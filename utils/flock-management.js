// utils/flock-management.js
// 鹅群入栏记录管理

/**
 * 添加鹅群入栏记录
 * @param {Object} flockData 鹅群数据
 * @returns {Object} 添加结果
 */
function addFlockEntry(flockData) {
  try {
    const { addFlockEntryRecord } = require('./task-data.js');
    
    const record = {
      id: flockData.id || `flock_${Date.now()}`,
      flockName: flockData.flockName,
      entryDate: flockData.entryDate,
      quantity: flockData.quantity,
      area: flockData.area,
      breed: flockData.breed || '狮头鹅',
      notes: flockData.notes || ''
    };
    
    const result = addFlockEntryRecord(record);
    
    if (result) {
      wx.showToast({
        title: '入栏记录添加成功',
        icon: 'success'
      });
      
      return {
        success: true,
        data: result
      };
    } else {
      return {
        success: false,
        message: '添加失败'
      };
    }
  } catch (error) {
    console.error('添加鹅群入栏记录失败:', error);
    return {
      success: false,
      message: '操作失败'
    };
  }
}

/**
 * 删除鹅群入栏记录
 * @param {string} flockId 鹅群ID
 * @returns {Object} 删除结果
 */
function removeFlockEntry(flockId) {
  try {
    const { removeFlockEntryRecord } = require('./task-data.js');
    
    const result = removeFlockEntryRecord(flockId);
    
    if (result) {
      wx.showToast({
        title: '入栏记录删除成功',
        icon: 'success'
      });
      
      return {
        success: true
      };
    } else {
      return {
        success: false,
        message: '删除失败'
      };
    }
  } catch (error) {
    console.error('删除鹅群入栏记录失败:', error);
    return {
      success: false,
      message: '操作失败'
    };
  }
}

/**
 * 获取所有入栏记录
 * @returns {Array} 入栏记录列表
 */
function getAllFlockEntries() {
  try {
    const { getFlockEntryRecords, calculateDayAge } = require('./task-data.js');
    const records = getFlockEntryRecords();
    
    // 为每个记录计算当前日龄
    return records.map(record => ({
      ...record,
      currentDayAge: calculateDayAge(record.entryDate)
    }));
  } catch (error) {
    console.error('获取入栏记录失败:', error);
    return [];
  }
}

/**
 * 模拟添加一个测试入栏记录（用于演示）
 */
function addTestFlockEntry() {
  const today = new Date();
  const entryDate = today.toISOString().split('T')[0];
  
  // 获取已有记录数量，生成不同的批次名称
  const existingRecords = wx.getStorageSync('flock_entry_records') || [];
  const batchNumber = existingRecords.length + 1;
  
  const testRecord = {
    flockName: `测试鹅群${String.fromCharCode(64 + batchNumber)}批`,
    entryDate: entryDate,
    quantity: 100 + batchNumber * 50, // 不同批次不同数量
    area: `${String.fromCharCode(64 + batchNumber)}区`,
    breed: '狮头鹅',
    notes: `测试入栏记录，第${batchNumber}批次，用于演示多批次防疫流程`
  };
  
  return addFlockEntry(testRecord);
}

module.exports = {
  addFlockEntry,
  removeFlockEntry,
  getAllFlockEntries,
  addTestFlockEntry
};