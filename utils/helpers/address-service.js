// utils/address-service.js - 地址管理服务
class AddressService {
  constructor() {
    this.storageKey = 'user_addresses';
  }

  /**
   * 初始化示例数据
   */
  initSampleData() {
    const existingData = wx.getStorageSync(this.storageKey) || [];
    if (existingData.length === 0) {
      const sampleAddresses = [
        {
          id: '1',
          name: '张三',
          phone: '13800138001',
          province: '浙江省',
          city: '杭州市',
          district: '西湖区',
          detail: '文三路138号东方通信大厦7楼701室',
          tag: '家',
          isDefault: true,
          createTime: new Date().toISOString()
        },
        {
          id: '2',
          name: '李四',
          phone: '13800138002',
          province: '浙江省',
          city: '宁波市',
          district: '鄞州区',
          detail: '钱湖北路1号宁波国际会展中心',
          tag: '公司',
          isDefault: false,
          createTime: new Date().toISOString()
        }
      ];
      wx.setStorageSync(this.storageKey, sampleAddresses);
    }
  }

  /**
   * 获取地址列表
   */
  getAddressList() {
    try {
      return wx.getStorageSync(this.storageKey) || [];
    } catch (error) {
      console.error('获取地址列表失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取地址
   */
  getAddressById(id) {
    try {
      const addresses = this.getAddressList();
      return addresses.find(addr => addr.id === id) || null;
    } catch (error) {
      console.error('获取地址失败:', error);
      return null;
    }
  }

  /**
   * 获取默认地址
   */
  getDefaultAddress() {
    try {
      const addresses = this.getAddressList();
      return addresses.find(addr => addr.isDefault) || null;
    } catch (error) {
      console.error('获取默认地址失败:', error);
      return null;
    }
  }

  /**
   * 添加地址
   */
  addAddress(addressData) {
    try {
      const addresses = this.getAddressList();
      
      // 生成新的ID
      const newId = Date.now().toString();
      
      // 如果是设置为默认地址，先清除其他默认地址
      if (addressData.isDefault) {
        addresses.forEach(addr => {
          addr.isDefault = false;
        });
      }

      // 创建新地址对象
      const newAddress = {
        id: newId,
        name: addressData.name,
        phone: addressData.phone,
        province: addressData.province,
        city: addressData.city,
        district: addressData.district,
        detail: addressData.detail,
        tag: addressData.tag || '',
        isDefault: addressData.isDefault || false,
        createTime: new Date().toISOString()
      };

      // 如果是第一个地址，自动设为默认
      if (addresses.length === 0) {
        newAddress.isDefault = true;
      }

      addresses.unshift(newAddress);
      wx.setStorageSync(this.storageKey, addresses);

      return {
        success: true,
        message: '地址添加成功',
        data: newAddress
      };
    } catch (error) {
      console.error('添加地址失败:', error);
      return {
        success: false,
        message: '添加地址失败'
      };
    }
  }

  /**
   * 更新地址
   */
  updateAddress(id, addressData) {
    try {
      const addresses = this.getAddressList();
      const addressIndex = addresses.findIndex(addr => addr.id === id);
      
      if (addressIndex === -1) {
        return {
          success: false,
          message: '地址不存在'
        };
      }

      // 如果要设置为默认地址，先清除其他默认地址
      if (addressData.isDefault) {
        addresses.forEach(addr => {
          addr.isDefault = false;
        });
      }

      // 更新地址信息
      addresses[addressIndex] = {
        ...addresses[addressIndex],
        name: addressData.name,
        phone: addressData.phone,
        province: addressData.province,
        city: addressData.city,
        district: addressData.district,
        detail: addressData.detail,
        tag: addressData.tag || '',
        isDefault: addressData.isDefault || false,
        updateTime: new Date().toISOString()
      };

      wx.setStorageSync(this.storageKey, addresses);

      return {
        success: true,
        message: '地址更新成功',
        data: addresses[addressIndex]
      };
    } catch (error) {
      console.error('更新地址失败:', error);
      return {
        success: false,
        message: '更新地址失败'
      };
    }
  }

  /**
   * 删除地址
   */
  deleteAddress(id) {
    try {
      const addresses = this.getAddressList();
      const addressIndex = addresses.findIndex(addr => addr.id === id);
      
      if (addressIndex === -1) {
        return {
          success: false,
          message: '地址不存在'
        };
      }

      const deletedAddress = addresses[addressIndex];
      addresses.splice(addressIndex, 1);

      // 如果删除的是默认地址，设置第一个地址为默认
      if (deletedAddress.isDefault && addresses.length > 0) {
        addresses[0].isDefault = true;
      }

      wx.setStorageSync(this.storageKey, addresses);

      return {
        success: true,
        message: '地址删除成功'
      };
    } catch (error) {
      console.error('删除地址失败:', error);
      return {
        success: false,
        message: '删除地址失败'
      };
    }
  }

  /**
   * 设置默认地址
   */
  setDefaultAddress(id) {
    try {
      const addresses = this.getAddressList();
      const addressIndex = addresses.findIndex(addr => addr.id === id);
      
      if (addressIndex === -1) {
        return {
          success: false,
          message: '地址不存在'
        };
      }

      // 清除所有默认标记
      addresses.forEach(addr => {
        addr.isDefault = false;
      });

      // 设置新的默认地址
      addresses[addressIndex].isDefault = true;
      addresses[addressIndex].updateTime = new Date().toISOString();

      wx.setStorageSync(this.storageKey, addresses);

      return {
        success: true,
        message: '默认地址设置成功'
      };
    } catch (error) {
      console.error('设置默认地址失败:', error);
      return {
        success: false,
        message: '设置默认地址失败'
      };
    }
  }

  /**
   * 验证地址数据
   */
  validateAddress(addressData) {
    const errors = [];

    if (!addressData.name || !addressData.name.trim()) {
      errors.push('请填写收货人姓名');
    }

    if (!addressData.phone || !addressData.phone.trim()) {
      errors.push('请填写手机号');
    } else {
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!phoneReg.test(addressData.phone)) {
        errors.push('请填写正确的手机号');
      }
    }

    if (!addressData.province || !addressData.city || !addressData.district) {
      errors.push('请选择省市区');
    }

    if (!addressData.detail || !addressData.detail.trim()) {
      errors.push('请填写详细地址');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 格式化地址显示
   */
  formatAddress(address) {
    if (!address) return '';
    return `${address.province}${address.city}${address.district}${address.detail}`;
  }

  /**
   * 清除所有地址（测试用）
   */
  clearAllAddresses() {
    try {
      wx.removeStorageSync(this.storageKey);
      return {
        success: true,
        message: '地址清除成功'
      };
    } catch (error) {
      console.error('清除地址失败:', error);
      return {
        success: false,
        message: '清除地址失败'
      };
    }
  }
}

// 导出单例实例
const addressService = new AddressService();

module.exports = addressService;