/**
 * 模拟用户数据和角色管理
 * 用于测试权限系统和开发阶段的用户切换
 */

// 导入角色常量
const { ROLES } = require('./permission-checker.js');

// 模拟用户数据
const MOCK_USERS = [
  {
    id: 'admin_001',
    name: '系统管理员',
    avatar: '/images/avatars/admin.png',
    role: ROLES.ADMIN,
    department: '管理部',
    position: '系统管理员',
    phone: '13800138001',
    email: '<EMAIL>',
    permissions: ['全部权限']
  },
  {
    id: 'manager_001',
    name: '李经理',
    avatar: '/images/avatars/manager.png',
    role: ROLES.MANAGER,
    department: '管理部',
    position: '总经理',
    phone: '13800138002',
    email: '<EMAIL>',
    permissions: ['经营管理', '财务查看', '生产管理', '人员管理']
  },
  {
    id: 'finance_001',
    name: '王会计',
    avatar: '/images/avatars/finance.png',
    role: ROLES.FINANCE,
    department: '财务部',
    position: '财务主管',
    phone: '13800138003',
    email: '<EMAIL>',
    permissions: ['财务管理', '报表分析', 'AI财务分析', '成本核算']
  },
  {
    id: 'finance_002',
    name: '张出纳',
    avatar: '/images/avatars/finance2.png',
    role: ROLES.FINANCE,
    department: '财务部',
    position: '出纳员',
    phone: '13800138004',
    email: '<EMAIL>',
    permissions: ['财务记录', '报表查看', '资金管理']
  },
  {
    id: 'employee_001',
    name: '刘员工',
    avatar: '/images/avatars/employee.png',
    role: ROLES.EMPLOYEE,
    department: '生产部',
    position: '养殖员',
    phone: '13800138005',
    email: '<EMAIL>',
    permissions: ['生产记录', '个人申请']
  },
  {
    id: 'employee_002',
    name: '陈技术员',
    avatar: '/images/avatars/employee2.png',
    role: ROLES.EMPLOYEE,
    department: '技术部',
    position: '技术员',
    phone: '13800138006',
    email: '<EMAIL>',
    permissions: ['技术记录', '设备维护', '个人申请']
  }
];

/**
 * 获取所有模拟用户
 * @returns {Array} 用户列表
 */
function getAllMockUsers() {
  return MOCK_USERS;
}

/**
 * 根据ID获取用户信息
 * @param {string} userId - 用户ID
 * @returns {Object|null} 用户信息
 */
function getMockUserById(userId) {
  return MOCK_USERS.find(user => user.id === userId) || null;
}

/**
 * 根据角色获取用户列表
 * @param {string} role - 用户角色
 * @returns {Array} 用户列表
 */
function getMockUsersByRole(role) {
  return MOCK_USERS.filter(user => user.role === role);
}

/**
 * 设置当前用户（用于测试）
 * @param {string} userId - 用户ID
 * @returns {boolean} 是否设置成功
 */
function setCurrentUser(userId) {
  const user = getMockUserById(userId);
  if (!user) {
    console.error('用户不存在:', userId);
    return false;
  }
  
  try {
    wx.setStorageSync('userInfo', user);
    console.log('已切换到用户:', user.name, '角色:', user.role);
    return true;
  } catch (error) {
    console.error('设置用户信息失败:', error);
    return false;
  }
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 当前用户信息
 */
function getCurrentMockUser() {
  try {
    const userInfo = wx.getStorageSync('userInfo');
    return userInfo || null;
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    return null;
  }
}

/**
 * 清除当前用户（退出登录）
 */
function clearCurrentUser() {
  try {
    wx.removeStorageSync('userInfo');
    console.log('用户已退出登录');
  } catch (error) {
    console.error('清除用户信息失败:', error);
  }
}

/**
 * 快速角色切换工具（开发阶段使用）
 * @param {string} targetRole - 目标角色
 * @returns {boolean} 是否切换成功
 */
function switchToRole(targetRole) {
  const users = getMockUsersByRole(targetRole);
  if (users.length === 0) {
    console.error('找不到指定角色的用户:', targetRole);
    return false;
  }
  
  // 选择该角色的第一个用户
  return setCurrentUser(users[0].id);
}

/**
 * 初始化默认用户（如果没有当前用户）
 */
function initializeDefaultUser() {
  const currentUser = getCurrentMockUser();
  if (!currentUser) {
    // 默认设置为管理员，方便测试所有功能
    console.log('[Mock Users] 未检测到当前用户，设置默认管理员用户');
    setCurrentUser('admin_001');
  } else {
    console.log('[Mock Users] 检测到当前用户:', currentUser.name, '角色:', currentUser.role);
  }
}

/**
 * 获取角色显示名称
 * @param {string} role - 角色代码
 * @returns {string} 角色显示名称
 */
function getRoleDisplayName(role) {
  const roleNames = {
    [ROLES.ADMIN]: '系统管理员',
    [ROLES.MANAGER]: '经理',
    [ROLES.FINANCE]: '财务人员',
    [ROLES.EMPLOYEE]: '普通员工'
  };
  
  return roleNames[role] || '未知角色';
}

/**
 * 检查指定用户是否有访问权限
 * @param {string} userId - 用户ID
 * @param {string} pageName - 页面名称
 * @returns {boolean} 是否有访问权限
 */
function checkUserPageAccess(userId, pageName) {
  const user = getMockUserById(userId);
  if (!user) {
    return false;
  }
  
  const { checkPageAccess } = require('./permission-checker.js');
  return checkPageAccess(pageName, user.role);
}

/**
 * 生成权限测试报告
 * @param {string} pageName - 页面名称
 * @returns {Object} 权限测试报告
 */
function generatePermissionReport(pageName) {
  const report = {
    pageName,
    testTime: new Date().toISOString(),
    results: []
  };
  
  MOCK_USERS.forEach(user => {
    const hasAccess = checkUserPageAccess(user.id, pageName);
    report.results.push({
      userId: user.id,
      userName: user.name,
      role: user.role,
      roleDisplayName: getRoleDisplayName(user.role),
      hasAccess,
      status: hasAccess ? '✅ 允许访问' : '❌ 禁止访问'
    });
  });
  
  return report;
}

module.exports = {
  MOCK_USERS,
  getAllMockUsers,
  getMockUserById,
  getMockUsersByRole,
  setCurrentUser,
  getCurrentMockUser,
  clearCurrentUser,
  switchToRole,
  initializeDefaultUser,
  getRoleDisplayName,
  checkUserPageAccess,
  generatePermissionReport
};