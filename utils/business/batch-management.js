// utils/batch-management.js - 批次管理服务
class BatchManagement {
  constructor() {
    this.storageKey = 'batch_management_data';
    this.batchesKey = 'active_batches';
    this.healthRecordsKey = 'batch_health_records';
  }

  /**
   * 获取活跃批次列表
   */
  async getActiveBatches() {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      
      // 如果没有数据，初始化示例数据
      if (batches.length === 0) {
        const sampleBatches = this.generateSampleBatches();
        wx.setStorageSync(this.batchesKey, sampleBatches);
        return sampleBatches;
      }
      
      // 过滤出活跃状态的批次
      return batches.filter(batch => ['active', 'growing', 'ready_for_slaughter'].includes(batch.status));
    } catch (error) {
      console.error('获取活跃批次失败:', error);
      return [];
    }
  }

  /**
   * 生成示例批次数据
   */
  generateSampleBatches() {
    const now = new Date();
    
    return [
      {
        id: 'batch_001',
        batchNumber: 'QY-20240301-001',
        breed: '太湖鹅',
        status: 'growing',
        entryDate: '2024-03-01',
        initialCount: 500,
        currentCount: 485,
        dayAge: 45,
        averageWeight: 2.8,
        costPerUnit: 15.5,
        supplier: '江南鹅苗场',
        source: '种鹅场直供',
        createTime: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'batch_002',
        batchNumber: 'QY-20240215-002',
        breed: '四川白鹅',
        status: 'ready_for_slaughter',
        entryDate: '2024-02-15',
        initialCount: 300,
        currentCount: 288,
        dayAge: 65,
        averageWeight: 3.5,
        costPerUnit: 16.2,
        supplier: '川西养殖合作社',
        source: '合作养殖',
        createTime: new Date(now.getTime() - 65 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'batch_003',
        batchNumber: 'QY-20240320-003',
        breed: '皖西白鹅',
        status: 'active',
        entryDate: '2024-03-20',
        initialCount: 400,
        currentCount: 400,
        dayAge: 25,
        averageWeight: 1.2,
        costPerUnit: 14.8,
        supplier: '安徽优鹅养殖场',
        source: '外购雏鹅',
        createTime: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      }
    ];
  }

  /**
   * 创建新批次
   */
  async createBatch(batchData) {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      
      const newBatch = {
        id: `batch_${Date.now()}`,
        batchNumber: batchData.batch,
        breed: batchData.breed,
        status: 'active',
        entryDate: batchData.date,
        initialCount: batchData.count,
        currentCount: batchData.count,
        dayAge: batchData.age || 0,
        averageWeight: 0,
        costPerUnit: batchData.costPerUnit,
        supplier: batchData.supplier,
        source: batchData.source,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        totalCost: batchData.count * batchData.costPerUnit,
        healthRecords: [],
        weightRecords: [],
        notes: []
      };

      batches.push(newBatch);
      wx.setStorageSync(this.batchesKey, batches);

      return {
        success: true,
        message: `批次 ${newBatch.batchNumber} 创建成功`,
        data: newBatch
      };
    } catch (error) {
      console.error('创建批次失败:', error);
      return {
        success: false,
        message: '创建批次失败: ' + error.message
      };
    }
  }

  /**
   * 处理批次出栏
   */
  async processBatchSale(batchNumber, saleData) {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      const batchIndex = batches.findIndex(b => b.batchNumber === batchNumber);
      
      if (batchIndex === -1) {
        return {
          success: false,
          message: '批次不存在'
        };
      }

      const batch = batches[batchIndex];
      
      // 检查出栏数量是否超过当前存栏
      if (saleData.count > batch.currentCount) {
        return {
          success: false,
          message: `出栏数量 ${saleData.count} 超过当前存栏 ${batch.currentCount}`
        };
      }

      // 更新批次信息
      batch.currentCount -= saleData.count;
      batch.updateTime = new Date().toISOString();
      
      // 添加出栏记录
      if (!batch.saleRecords) {
        batch.saleRecords = [];
      }
      
      batch.saleRecords.push({
        date: saleData.date,
        count: saleData.count,
        weight: saleData.weight,
        price: saleData.price,
        buyer: saleData.buyer,
        totalAmount: saleData.count * saleData.weight * saleData.price,
        notes: saleData.notes,
        recordTime: new Date().toISOString()
      });

      // 如果全部出栏，更新批次状态
      if (batch.currentCount === 0) {
        batch.status = 'completed';
      }

      batches[batchIndex] = batch;
      wx.setStorageSync(this.batchesKey, batches);

      return {
        success: true,
        message: `批次 ${batchNumber} 出栏 ${saleData.count} 只，剩余 ${batch.currentCount} 只`,
        data: batch
      };
    } catch (error) {
      console.error('处理批次出栏失败:', error);
      return {
        success: false,
        message: '处理批次出栏失败: ' + error.message
      };
    }
  }

  /**
   * 更新批次称重信息
   */
  async updateBatchWeight(batchNumber, weightData) {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      const batchIndex = batches.findIndex(b => b.batchNumber === batchNumber);
      
      if (batchIndex === -1) {
        return {
          success: false,
          message: '批次不存在'
        };
      }

      const batch = batches[batchIndex];
      
      // 更新平均重量
      batch.averageWeight = weightData.weight;
      batch.updateTime = new Date().toISOString();
      
      // 添加称重记录
      if (!batch.weightRecords) {
        batch.weightRecords = [];
      }
      
      batch.weightRecords.push({
        date: weightData.date,
        averageWeight: weightData.weight,
        sampleCount: weightData.count,
        notes: weightData.notes,
        recordTime: new Date().toISOString()
      });

      // 根据重量和日龄判断是否可以出栏
      if (batch.averageWeight >= 3.0 && batch.dayAge >= 60) {
        batch.status = 'ready_for_slaughter';
      } else if (batch.averageWeight >= 2.0) {
        batch.status = 'growing';
      }

      batches[batchIndex] = batch;
      wx.setStorageSync(this.batchesKey, batches);

      return {
        success: true,
        message: `批次 ${batchNumber} 称重记录已更新，平均重量 ${weightData.weight}kg`,
        data: batch
      };
    } catch (error) {
      console.error('更新批次称重失败:', error);
      return {
        success: false,
        message: '更新批次称重失败: ' + error.message
      };
    }
  }

  /**
   * 添加批次健康记录
   */
  async addBatchHealthRecord(batchNumber, healthData) {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      const batchIndex = batches.findIndex(b => b.batchNumber === batchNumber);
      
      if (batchIndex === -1) {
        return {
          success: false,
          message: '批次不存在'
        };
      }

      const batch = batches[batchIndex];
      
      // 添加健康记录
      if (!batch.healthRecords) {
        batch.healthRecords = [];
      }
      
      const healthRecord = {
        id: `health_${Date.now()}`,
        date: healthData.date,
        recordType: healthData.recordType,
        description: healthData.description,
        medicine: healthData.medicine,
        dosage: healthData.dosage,
        cost: healthData.cost,
        affectedCount: healthData.affectedCount,
        notes: healthData.notes,
        recordTime: new Date().toISOString()
      };
      
      batch.healthRecords.push(healthRecord);
      batch.updateTime = new Date().toISOString();

      // 如果是疾病或死亡记录，可能需要更新存栏数量
      if (healthData.recordType === 'death' && healthData.affectedCount > 0) {
        batch.currentCount = Math.max(0, batch.currentCount - healthData.affectedCount);
      }

      batches[batchIndex] = batch;
      wx.setStorageSync(this.batchesKey, batches);

      return {
        success: true,
        message: `批次 ${batchNumber} 健康记录已添加`,
        data: healthRecord
      };
    } catch (error) {
      console.error('添加批次健康记录失败:', error);
      return {
        success: false,
        message: '添加批次健康记录失败: ' + error.message
      };
    }
  }

  /**
   * 获取批次详细信息
   */
  async getBatchDetails(batchNumber) {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      const batch = batches.find(b => b.batchNumber === batchNumber);
      
      if (!batch) {
        return {
          success: false,
          message: '批次不存在'
        };
      }

      // 计算批次统计信息
      const stats = this.calculateBatchStats(batch);
      
      return {
        success: true,
        data: {
          ...batch,
          stats
        }
      };
    } catch (error) {
      console.error('获取批次详情失败:', error);
      return {
        success: false,
        message: '获取批次详情失败: ' + error.message
      };
    }
  }

  /**
   * 计算批次统计信息
   */
  calculateBatchStats(batch) {
    const now = new Date();
    const entryDate = new Date(batch.entryDate);
    const dayAge = Math.floor((now - entryDate) / (1000 * 60 * 60 * 24));
    
    // 存活率
    const survivalRate = ((batch.currentCount / batch.initialCount) * 100).toFixed(1);
    
    // 死亡数量
    const deathCount = batch.initialCount - batch.currentCount;
    
    // 总出栏数量
    const totalSold = (batch.saleRecords || []).reduce((sum, record) => sum + record.count, 0);
    
    // 总销售收入
    const totalRevenue = (batch.saleRecords || []).reduce((sum, record) => sum + record.totalAmount, 0);
    
    // 健康记录数量
    const healthRecordCount = (batch.healthRecords || []).length;
    
    // 称重记录数量
    const weightRecordCount = (batch.weightRecords || []).length;

    return {
      currentDayAge: dayAge,
      survivalRate: parseFloat(survivalRate),
      deathCount,
      totalSold,
      totalRevenue,
      healthRecordCount,
      weightRecordCount,
      costPerHead: batch.totalCost / batch.initialCount,
      currentValue: batch.currentCount * batch.averageWeight * 18 // 假设市场价18元/斤
    };
  }

  /**
   * 获取状态标签
   */
  static getStatusLabel(status) {
    const statusLabels = {
      'active': '活跃',
      'growing': '生长中',
      'ready_for_slaughter': '可出栏',
      'completed': '已完成',
      'suspended': '暂停'
    };
    
    return statusLabels[status] || '未知状态';
  }

  /**
   * 批次状态更新
   */
  async updateBatchStatus(batchNumber, newStatus, notes = '') {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      const batchIndex = batches.findIndex(b => b.batchNumber === batchNumber);
      
      if (batchIndex === -1) {
        return {
          success: false,
          message: '批次不存在'
        };
      }

      const batch = batches[batchIndex];
      const oldStatus = batch.status;
      
      batch.status = newStatus;
      batch.updateTime = new Date().toISOString();
      
      // 添加状态变更记录
      if (!batch.statusHistory) {
        batch.statusHistory = [];
      }
      
      batch.statusHistory.push({
        fromStatus: oldStatus,
        toStatus: newStatus,
        notes: notes,
        changeTime: new Date().toISOString()
      });

      batches[batchIndex] = batch;
      wx.setStorageSync(this.batchesKey, batches);

      return {
        success: true,
        message: `批次 ${batchNumber} 状态已更新为 ${BatchManagement.getStatusLabel(newStatus)}`,
        data: batch
      };
    } catch (error) {
      console.error('更新批次状态失败:', error);
      return {
        success: false,
        message: '更新批次状态失败: ' + error.message
      };
    }
  }

  /**
   * 获取批次统计概览
   */
  async getBatchOverview() {
    try {
      const batches = wx.getStorageSync(this.batchesKey) || [];
      
      const overview = {
        totalBatches: batches.length,
        activeBatches: batches.filter(b => ['active', 'growing', 'ready_for_slaughter'].includes(b.status)).length,
        totalGeese: batches.reduce((sum, b) => sum + b.currentCount, 0),
        totalInitialGeese: batches.reduce((sum, b) => sum + b.initialCount, 0),
        readyForSlaughter: batches.filter(b => b.status === 'ready_for_slaughter').length,
        completedBatches: batches.filter(b => b.status === 'completed').length
      };
      
      // 计算整体存活率
      if (overview.totalInitialGeese > 0) {
        overview.overallSurvivalRate = ((overview.totalGeese / overview.totalInitialGeese) * 100).toFixed(1);
      } else {
        overview.overallSurvivalRate = '0.0';
      }

      return {
        success: true,
        data: overview
      };
    } catch (error) {
      console.error('获取批次概览失败:', error);
      return {
        success: false,
        message: '获取批次概览失败: ' + error.message
      };
    }
  }

  /**
   * 清除所有批次数据（测试用）
   */
  clearAllBatches() {
    try {
      wx.removeStorageSync(this.batchesKey);
      wx.removeStorageSync(this.healthRecordsKey);
      return {
        success: true,
        message: '批次数据清除成功'
      };
    } catch (error) {
      console.error('清除批次数据失败:', error);
      return {
        success: false,
        message: '清除批次数据失败'
      };
    }
  }
}

// 导出类和单例实例
module.exports = {
  BatchManagement: new BatchManagement(),
  BatchManagementClass: BatchManagement
};