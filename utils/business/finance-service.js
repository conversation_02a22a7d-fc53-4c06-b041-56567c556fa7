// utils/finance-service.js - 财务服务管理
class FinanceService {
  constructor() {
    this.storageKey = 'finance_records';
    this.transactionKey = 'finance_transactions';
    this.categoryKey = 'finance_categories';
  }

  /**
   * 创建入栏费用记录
   */
  async createEntryExpense(entryData) {
    try {
      const totalCost = entryData.count * entryData.costPerUnit;
      
      const expenseRecord = {
        id: `expense_entry_${entryData.id}`,
        type: 'expense',
        category: '鹅苗采购',
        subCategory: '入栏费用',
        amount: totalCost,
        date: entryData.date,
        description: `批次 ${entryData.batch} 入栏 ${entryData.count} 只 ${entryData.breed}`,
        relatedBatch: entryData.batch,
        details: {
          breed: entryData.breed,
          count: entryData.count,
          costPerUnit: entryData.costPerUnit,
          supplier: entryData.supplier,
          source: entryData.source,
          age: entryData.age
        },
        createTime: new Date().toISOString()
      };

      const result = await this.saveFinanceRecord(expenseRecord);
      
      if (result.success) {
        return {
          success: true,
          message: `入栏费用记录已创建，金额 ¥${totalCost.toFixed(2)}`,
          data: expenseRecord
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('创建入栏费用记录失败:', error);
      return {
        success: false,
        message: '创建入栏费用记录失败: ' + error.message
      };
    }
  }

  /**
   * 创建销售收入记录
   */
  async createSaleIncome(saleData) {
    try {
      const totalIncome = saleData.count * saleData.weight * saleData.price;
      
      const incomeRecord = {
        id: `income_sale_${saleData.id}`,
        type: 'income',
        category: '鹅只销售',
        subCategory: '出栏收入',
        amount: totalIncome,
        date: saleData.date,
        description: `批次 ${saleData.batch} 出栏 ${saleData.count} 只 ${saleData.breed}`,
        relatedBatch: saleData.batch,
        details: {
          breed: saleData.breed,
          count: saleData.count,
          weight: saleData.weight,
          price: saleData.price,
          buyer: saleData.buyer,
          totalWeight: saleData.count * saleData.weight
        },
        createTime: new Date().toISOString()
      };

      const result = await this.saveFinanceRecord(incomeRecord);
      
      if (result.success) {
        return {
          success: true,
          message: `销售收入记录已创建，金额 ¥${totalIncome.toFixed(2)}`,
          data: incomeRecord
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('创建销售收入记录失败:', error);
      return {
        success: false,
        message: '创建销售收入记录失败: ' + error.message
      };
    }
  }

  /**
   * 创建健康管理费用记录
   */
  async createHealthExpense(healthData) {
    try {
      const expenseRecord = {
        id: `expense_health_${healthData.id}`,
        type: 'expense',
        category: '健康管理',
        subCategory: this.getHealthSubCategory(healthData.recordType),
        amount: healthData.cost,
        date: healthData.date,
        description: `批次 ${healthData.batch} ${healthData.description}`,
        relatedBatch: healthData.batch,
        details: {
          recordType: healthData.recordType,
          medicine: healthData.medicine,
          dosage: healthData.dosage,
          supplier: healthData.supplier,
          notes: healthData.notes
        },
        createTime: new Date().toISOString()
      };

      const result = await this.saveFinanceRecord(expenseRecord);
      
      if (result.success) {
        return {
          success: true,
          message: `健康管理费用记录已创建，金额 ¥${healthData.cost.toFixed(2)}`,
          data: expenseRecord
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('创建健康管理费用记录失败:', error);
      return {
        success: false,
        message: '创建健康管理费用记录失败: ' + error.message
      };
    }
  }

  /**
   * 获取健康记录的子分类
   */
  getHealthSubCategory(recordType) {
    const subCategories = {
      'vaccine': '疫苗接种',
      'treatment': '疾病治疗',
      'prevention': '预防保健',
      'medicine': '药品采购',
      'inspection': '健康检查',
      'other': '其他健康费用'
    };
    
    return subCategories[recordType] || '其他健康费用';
  }

  /**
   * 保存财务记录
   */
  async saveFinanceRecord(record) {
    try {
      const records = wx.getStorageSync(this.storageKey) || [];
      records.unshift(record); // 最新记录放在前面
      wx.setStorageSync(this.storageKey, records);

      // 同时更新交易记录（用于报表）
      await this.updateTransactionRecord(record);

      return {
        success: true,
        message: '财务记录保存成功',
        data: record
      };
    } catch (error) {
      console.error('保存财务记录失败:', error);
      return {
        success: false,
        message: '保存财务记录失败: ' + error.message
      };
    }
  }

  /**
   * 更新交易记录（用于报表查询）
   */
  async updateTransactionRecord(record) {
    try {
      const transactions = wx.getStorageSync(this.transactionKey) || [];
      
      const transaction = {
        id: record.id,
        type: record.type,
        amount: record.amount,
        category: record.category,
        subCategory: record.subCategory,
        description: record.description,
        date: record.date,
        relatedBatch: record.relatedBatch,
        createTime: record.createTime
      };
      
      transactions.unshift(transaction);
      wx.setStorageSync(this.transactionKey, transactions);
      
      return true;
    } catch (error) {
      console.error('更新交易记录失败:', error);
      return false;
    }
  }

  /**
   * 获取财务记录列表
   */
  async getFinanceRecords(filters = {}) {
    try {
      let records = wx.getStorageSync(this.storageKey) || [];
      
      // 应用过滤条件
      if (filters.type) {
        records = records.filter(record => record.type === filters.type);
      }
      
      if (filters.category) {
        records = records.filter(record => record.category === filters.category);
      }
      
      if (filters.batch) {
        records = records.filter(record => record.relatedBatch === filters.batch);
      }
      
      if (filters.startDate && filters.endDate) {
        records = records.filter(record => {
          const recordDate = new Date(record.date);
          return recordDate >= new Date(filters.startDate) && recordDate <= new Date(filters.endDate);
        });
      }
      
      // 分页
      if (filters.page && filters.pageSize) {
        const start = (filters.page - 1) * filters.pageSize;
        const end = start + filters.pageSize;
        records = records.slice(start, end);
      }

      return {
        success: true,
        data: records,
        total: records.length
      };
    } catch (error) {
      console.error('获取财务记录失败:', error);
      return {
        success: false,
        message: '获取财务记录失败: ' + error.message,
        data: []
      };
    }
  }

  /**
   * 获取财务统计数据
   */
  async getFinanceStatistics(startDate, endDate) {
    try {
      const records = wx.getStorageSync(this.storageKey) || [];
      
      // 过滤日期范围内的记录
      const filteredRecords = records.filter(record => {
        if (!startDate || !endDate) return true;
        const recordDate = new Date(record.date);
        return recordDate >= new Date(startDate) && recordDate <= new Date(endDate);
      });

      // 计算总收入和总支出
      const totalIncome = filteredRecords
        .filter(record => record.type === 'income')
        .reduce((sum, record) => sum + record.amount, 0);

      const totalExpense = filteredRecords
        .filter(record => record.type === 'expense')
        .reduce((sum, record) => sum + record.amount, 0);

      const netProfit = totalIncome - totalExpense;
      const profitMargin = totalIncome > 0 ? (netProfit / totalIncome * 100).toFixed(2) : '0.00';

      // 按分类统计
      const categoryStats = this.calculateCategoryStatistics(filteredRecords);

      // 按批次统计
      const batchStats = this.calculateBatchStatistics(filteredRecords);

      return {
        success: true,
        data: {
          totalIncome: totalIncome.toFixed(2),
          totalExpense: totalExpense.toFixed(2),
          netProfit: netProfit.toFixed(2),
          profitMargin: profitMargin,
          recordCount: filteredRecords.length,
          categoryStats,
          batchStats,
          dateRange: {
            startDate,
            endDate
          }
        }
      };
    } catch (error) {
      console.error('获取财务统计失败:', error);
      return {
        success: false,
        message: '获取财务统计失败: ' + error.message
      };
    }
  }

  /**
   * 计算分类统计
   */
  calculateCategoryStatistics(records) {
    const categoryMap = new Map();
    
    records.forEach(record => {
      const key = `${record.category}_${record.type}`;
      if (categoryMap.has(key)) {
        const existing = categoryMap.get(key);
        existing.amount += record.amount;
        existing.count += 1;
      } else {
        categoryMap.set(key, {
          category: record.category,
          type: record.type,
          amount: record.amount,
          count: 1
        });
      }
    });

    return Array.from(categoryMap.values()).map(stat => ({
      ...stat,
      amount: stat.amount.toFixed(2)
    }));
  }

  /**
   * 计算批次统计
   */
  calculateBatchStatistics(records) {
    const batchMap = new Map();
    
    records.forEach(record => {
      if (record.relatedBatch) {
        if (batchMap.has(record.relatedBatch)) {
          const existing = batchMap.get(record.relatedBatch);
          if (record.type === 'income') {
            existing.income += record.amount;
          } else {
            existing.expense += record.amount;
          }
          existing.recordCount += 1;
        } else {
          batchMap.set(record.relatedBatch, {
            batch: record.relatedBatch,
            income: record.type === 'income' ? record.amount : 0,
            expense: record.type === 'expense' ? record.amount : 0,
            recordCount: 1
          });
        }
      }
    });

    return Array.from(batchMap.values()).map(stat => ({
      ...stat,
      netProfit: (stat.income - stat.expense).toFixed(2),
      profitMargin: stat.income > 0 ? ((stat.income - stat.expense) / stat.income * 100).toFixed(2) : '0.00',
      income: stat.income.toFixed(2),
      expense: stat.expense.toFixed(2)
    }));
  }

  /**
   * 创建通用财务记录
   */
  async createCustomRecord(recordData) {
    try {
      const record = {
        id: `custom_${Date.now()}`,
        type: recordData.type, // income or expense
        category: recordData.category,
        subCategory: recordData.subCategory || '',
        amount: parseFloat(recordData.amount),
        date: recordData.date,
        description: recordData.description,
        relatedBatch: recordData.batch || '',
        details: recordData.details || {},
        createTime: new Date().toISOString()
      };

      const result = await this.saveFinanceRecord(record);
      
      if (result.success) {
        return {
          success: true,
          message: `${record.type === 'income' ? '收入' : '支出'}记录已创建，金额 ¥${record.amount.toFixed(2)}`,
          data: record
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('创建自定义财务记录失败:', error);
      return {
        success: false,
        message: '创建财务记录失败: ' + error.message
      };
    }
  }

  /**
   * 获取交易记录（用于报表）
   */
  async getTransactions(filters = {}) {
    try {
      let transactions = wx.getStorageSync(this.transactionKey) || [];
      
      // 如果没有交易记录，从财务记录中生成
      if (transactions.length === 0) {
        await this.rebuildTransactionRecords();
        transactions = wx.getStorageSync(this.transactionKey) || [];
      }
      
      // 应用过滤器
      if (filters.type) {
        transactions = transactions.filter(t => t.type === filters.type);
      }
      
      if (filters.category) {
        transactions = transactions.filter(t => t.category === filters.category);
      }
      
      if (filters.startDate && filters.endDate) {
        transactions = transactions.filter(t => {
          const transactionDate = new Date(t.date);
          return transactionDate >= new Date(filters.startDate) && transactionDate <= new Date(filters.endDate);
        });
      }

      return {
        success: true,
        data: transactions
      };
    } catch (error) {
      console.error('获取交易记录失败:', error);
      return {
        success: false,
        message: '获取交易记录失败: ' + error.message,
        data: []
      };
    }
  }

  /**
   * 重建交易记录（从财务记录）
   */
  async rebuildTransactionRecords() {
    try {
      const records = wx.getStorageSync(this.storageKey) || [];
      const transactions = records.map(record => ({
        id: record.id,
        type: record.type,
        amount: record.amount,
        category: record.category,
        subCategory: record.subCategory,
        description: record.description,
        date: record.date,
        relatedBatch: record.relatedBatch,
        createTime: record.createTime
      }));
      
      wx.setStorageSync(this.transactionKey, transactions);
      return true;
    } catch (error) {
      console.error('重建交易记录失败:', error);
      return false;
    }
  }

  /**
   * 删除财务记录
   */
  async deleteRecord(recordId) {
    try {
      const records = wx.getStorageSync(this.storageKey) || [];
      const filteredRecords = records.filter(record => record.id !== recordId);
      
      wx.setStorageSync(this.storageKey, filteredRecords);
      
      // 同时删除交易记录
      const transactions = wx.getStorageSync(this.transactionKey) || [];
      const filteredTransactions = transactions.filter(transaction => transaction.id !== recordId);
      wx.setStorageSync(this.transactionKey, filteredTransactions);

      return {
        success: true,
        message: '财务记录删除成功'
      };
    } catch (error) {
      console.error('删除财务记录失败:', error);
      return {
        success: false,
        message: '删除财务记录失败: ' + error.message
      };
    }
  }

  /**
   * 获取分类列表
   */
  getCategories() {
    const defaultCategories = {
      income: [
        '鹅只销售',
        '鹅蛋销售',
        '副产品销售',
        '政府补贴',
        '其他收入'
      ],
      expense: [
        '鹅苗采购',
        '饲料费用',
        '健康管理',
        '设备维护',
        '人工费用',
        '水电费',
        '其他支出'
      ]
    };

    try {
      const customCategories = wx.getStorageSync(this.categoryKey) || {};
      return {
        income: [...defaultCategories.income, ...(customCategories.income || [])],
        expense: [...defaultCategories.expense, ...(customCategories.expense || [])]
      };
    } catch (error) {
      console.error('获取分类列表失败:', error);
      return defaultCategories;
    }
  }

  /**
   * 清除所有财务数据（测试用）
   */
  clearAllFinanceData() {
    try {
      wx.removeStorageSync(this.storageKey);
      wx.removeStorageSync(this.transactionKey);
      wx.removeStorageSync(this.categoryKey);
      return {
        success: true,
        message: '财务数据清除成功'
      };
    } catch (error) {
      console.error('清除财务数据失败:', error);
      return {
        success: false,
        message: '清除财务数据失败'
      };
    }
  }
}

// 导出类和单例实例
module.exports = {
  FinanceService: new FinanceService(),
  FinanceServiceClass: FinanceService
};