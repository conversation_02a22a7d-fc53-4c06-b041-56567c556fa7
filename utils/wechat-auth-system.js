/**
 * 微信小程序完整的用户认证和注册系统
 * 包含养殖场注册、审批、用户管理等完整流程
 */

const request = require('./request.js');
const { ROLES } = require('./permission-checker.js');

// 用户状态枚举
const USER_STATUS = {
  PENDING: 'pending',           // 待审批
  APPROVED: 'approved',         // 已审批
  REJECTED: 'rejected',         // 已拒绝
  SUSPENDED: 'suspended',       // 已暂停
  ACTIVE: 'active'             // 活跃状态
};

// 养殖场状态枚举
const FARM_STATUS = {
  PENDING: 'pending',           // 待审批
  APPROVED: 'approved',         // 已审批
  REJECTED: 'rejected',         // 已拒绝
  SUSPENDED: 'suspended'        // 已暂停
};

/**
 * 完整的微信小程序认证流程类
 */
class WechatAuthSystem {
  constructor() {
    this.baseUrl = 'https://api.smartgoose.com'; // 生产环境API地址
    // this.baseUrl = 'http://localhost:3000'; // 开发环境API地址
    this.currentUser = null;
  }

  /**
   * 第一步：微信授权获取用户基本信息
   * @returns {Promise<Object>} 微信用户信息
   */
  async getWechatUserInfo() {
    return new Promise((resolve, reject) => {
      // 检查是否已经授权
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userInfo']) {
            // 已经授权，直接获取用户信息
            wx.getUserInfo({
              success: resolve,
              fail: reject
            });
          } else {
            // 需要用户授权
            wx.getUserProfile({
              desc: '用于完善养殖场管理员信息',
              success: resolve,
              fail: reject
            });
          }
        }
      });
    });
  }

  /**
   * 第二步：检查用户是否已注册
   * @param {string} code 微信登录code
   * @returns {Promise<Object>} 用户状态信息
   */
  async checkUserRegistration(code) {
    try {
      // 模拟检查逻辑，实际应用中调用真实API
      const response = await request.post(`${this.baseUrl}/api/auth/wechat/login`, {
        code: code
      });

      if (response.success) {
        return {
          isRegistered: true,
          userStatus: response.flow === 'LOGIN_SUCCESS' ? USER_STATUS.APPROVED : 
                     response.flow === 'PENDING_APPROVAL' ? USER_STATUS.PENDING :
                     response.flow === 'REJECTED' ? USER_STATUS.REJECTED : 
                     response.flow === 'SUSPENDED' ? USER_STATUS.SUSPENDED : null,
          user: response.user || null,
          farm: response.tenant || null
        };
      } else {
        return {
          isRegistered: false,
          userStatus: null,
          user: null,
          farm: null
        };
      }
    } catch (error) {
      console.error('检查用户注册状态失败:', error);
      
      // 如果是首次用户或网络错误，返回未注册状态
      return {
        isRegistered: false,
        userStatus: null,
        user: null,
        farm: null
      };
    }
  }

  /**
   * 第三步：养殖场注册申请
   * @param {Object} farmInfo 养殖场信息
   * @param {Object} adminInfo 管理员信息
   * @param {Object} wechatUserInfo 微信用户信息
   * @returns {Promise<Object>} 注册结果
   */
  async registerFarmApplication(farmInfo, adminInfo, wechatUserInfo) {
    try {
      // 先获取微信用户ID
      const loginCode = await this.getWechatLoginCode();
      
      // 获取或创建微信用户记录
      const wechatUserResponse = await request.post(`${this.baseUrl}/api/auth/wechat/get-or-create-user`, {
        code: loginCode.code,
        userInfo: wechatUserInfo.userInfo
      });

      if (!wechatUserResponse.success) {
        throw new Error('获取微信用户信息失败');
      }

      const wechatUserId = wechatUserResponse.data.wechatUserId;

      const applicationData = {
        wechatUserId: wechatUserId,
        
        // 养殖场信息
        farmName: farmInfo.farmName,
        legalRepresentative: farmInfo.legalRepresentative,
        businessLicense: farmInfo.businessLicense,
        contactPhone: farmInfo.contactPhone,
        email: farmInfo.email || '',
        province: farmInfo.province,
        city: farmInfo.city,
        district: farmInfo.district || '',
        detailedAddress: farmInfo.detailedAddress,
        farmScale: farmInfo.farmScale,
        scaleDescription: farmInfo.scaleDescription || '',
        breedTypes: farmInfo.breedTypes || [],
        breedingYears: farmInfo.breedingYears || 0,
        facilities: farmInfo.facilities || {},
        certifications: farmInfo.certifications || [],
        
        // 管理员信息
        adminRealName: adminInfo.realName,
        adminPhone: adminInfo.phone,
        adminEmail: adminInfo.email || '',
        adminIdCard: adminInfo.idCard || '',
        adminPosition: adminInfo.position || '场长'
      };

      const response = await request.post(`${this.baseUrl}/api/auth/register/farm`, applicationData);
      
      if (response.success) {
        // 保存申请信息到本地，等待审批
        wx.setStorageSync('pending_application', {
          applicationId: response.data.applicationId,
          status: USER_STATUS.PENDING,
          submitTime: new Date().toISOString(),
          farmName: farmInfo.farmName
        });

        return {
          success: true,
          applicationId: response.data.applicationId,
          message: '养殖场注册申请已提交，请等待平台审核'
        };
      } else {
        throw new Error(response.message || '提交申请失败');
      }
    } catch (error) {
      console.error('提交养殖场注册申请失败:', error);
      return {
        success: false,
        message: error.message || '提交申请失败，请重试'
      };
    }
  }

  /**
   * 第四步：检查申请审批状态
   * @param {string} applicationId 申请ID
   * @returns {Promise<Object>} 审批状态
   */
  async checkApplicationStatus(applicationId) {
    try {
      const response = await request.get(`${this.baseUrl}/api/auth/application/status/${applicationId}`);

      if (response.success) {
        return {
          status: response.data.status,
          farm: response.data.farm,
          user: response.data.user,
          reviewComment: response.data.reviewComment || '',
          reviewTime: response.data.reviewTime || null
        };
      } else {
        throw new Error(response.message || '查询失败');
      }
    } catch (error) {
      console.error('检查申请状态失败:', error);
      
      // 返回模拟数据用于开发测试
      return {
        status: USER_STATUS.PENDING,
        farm: {
          name: '智慧生态养鹅基地',
          address: '江苏省苏州市昆山市开发区',
          scale: '中型养殖场（500-2000只）',
          breedTypes: ['白鹅', '灰鹅']
        },
        user: {
          realName: '测试管理员',
          phone: '138****8888',
          position: '场长'
        },
        reviewComment: '',
        reviewTime: null
      };
    }
  }

  /**
   * 第五步：审批通过后的正式登录
   * @returns {Promise<Object>} 登录结果
   */
  async performOfficialLogin() {
    try {
      // 获取微信登录凭证
      const loginCode = await this.getWechatLoginCode();
      
      const response = await request.post(`${this.baseUrl}/api/auth/wechat/login`, {
        code: loginCode.code
      });

      if (response.success && response.flow === 'LOGIN_SUCCESS') {
        // 构建标准用户信息
        const userInfo = this.buildStandardUserInfo(response);
        
        // 保存登录信息
        wx.setStorageSync('access_token', response.tokens.accessToken);
        wx.setStorageSync('refresh_token', response.tokens.refreshToken);
        wx.setStorageSync('user_info', userInfo);
        
        // 清除待审批申请记录
        wx.removeStorageSync('pending_application');
        
        this.currentUser = userInfo;
        
        return {
          success: true,
          user: userInfo,
          message: '登录成功'
        };
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('正式登录失败:', error);
      return {
        success: false,
        message: error.message || '登录失败，请重试'
      };
    }
  }

  /**
   * 员工注册（由管理员发起）
   * @param {Object} employeeInfo 员工信息
   * @param {string} farmId 养殖场ID
   * @param {string} inviteCode 邀请码
   * @returns {Promise<Object>} 注册结果
   */
  async registerEmployee(employeeInfo, farmId, inviteCode) {
    try {
      const employeeData = {
        openid: employeeInfo.openid,
        wechatInfo: employeeInfo.wechatInfo,
        realName: employeeInfo.realName,
        phone: employeeInfo.phone,
        role: employeeInfo.role || ROLES.EMPLOYEE,
        department: employeeInfo.department || '生产部',
        position: employeeInfo.position || '员工',
        farmId: farmId,
        inviteCode: inviteCode,
        registrationTime: new Date().toISOString()
      };

      const response = await request.post(`${this.baseUrl}/api/auth/register-employee`, employeeData);

      if (response.success) {
        const userInfo = this.buildStandardUserInfo(response.data);
        
        // 保存员工登录信息
        wx.setStorageSync('access_token', response.data.accessToken);
        wx.setStorageSync('refresh_token', response.data.refreshToken);
        wx.setStorageSync('user_info', userInfo);
        
        return {
          success: true,
          user: userInfo,
          message: '员工注册成功'
        };
      } else {
        throw new Error(response.message || '员工注册失败');
      }
    } catch (error) {
      console.error('员工注册失败:', error);
      return {
        success: false,
        message: error.message || '员工注册失败，请重试'
      };
    }
  }

  /**
   * 一键登录（已注册用户）
   * @returns {Promise<Object>} 登录结果
   */
  async quickLogin() {
    try {
      // 获取微信登录凭证
      const loginCode = await this.getWechatLoginCode();
      
      const response = await request.post(`${this.baseUrl}/api/auth/quick-login`, {
        code: loginCode.code,
        loginTime: new Date().toISOString(),
        deviceInfo: this.getDeviceInfo()
      });

      if (response.success) {
        const userInfo = this.buildStandardUserInfo(response.data);
        
        // 更新登录信息
        wx.setStorageSync('access_token', response.data.accessToken);
        wx.setStorageSync('refresh_token', response.data.refreshToken);
        wx.setStorageSync('user_info', userInfo);
        
        this.currentUser = userInfo;
        
        return {
          success: true,
          user: userInfo,
          message: '登录成功'
        };
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('一键登录失败:', error);
      return {
        success: false,
        message: error.message || '登录失败，请重试'
      };
    }
  }

  /**
   * 获取微信登录凭证
   * @returns {Promise<Object>} 登录凭证
   */
  getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    const systemInfo = wx.getSystemInfoSync();
    return {
      platform: systemInfo.platform,
      model: systemInfo.model,
      version: systemInfo.version,
      system: systemInfo.system,
      brand: systemInfo.brand,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight
    };
  }

  /**
   * 构建标准用户信息格式
   * @param {Object} apiResponse API响应数据
   * @returns {Object} 标准用户信息
   */
  buildStandardUserInfo(apiResponse) {
    const user = apiResponse.user;
    const tenant = apiResponse.tenant;
    
    return {
      // 用户基本信息
      id: user.id,
      name: user.name,
      nickname: user.name,
      avatar: user.avatar || '/images/default_avatar.png',
      phone: user.phone,
      email: user.email || '',
      
      // 角色和权限
      role: user.role,                    // 中文角色名
      roleCode: user.roleCode,            // 英文角色代码
      department: user.department || '生产部',
      position: user.position || '员工',
      
      // 养殖场信息
      farmId: tenant?.id,
      farmName: user.farmName || tenant?.companyName,
      tenantCode: tenant?.tenantCode,
      
      // 状态信息
      userStatus: 'active',
      isActive: true,
      lastLoginTime: new Date().toISOString()
    };
  }

  /**
   * 统一认证入口方法
   * @returns {Promise<Object>} 认证结果
   */
  async authenticate() {
    try {
      // 1. 获取微信登录凭证
      const loginCode = await this.getWechatLoginCode();

      console.log('[认证系统] 获取微信登录凭证成功');

      // 2. 尝试登录检查用户状态
      const response = await request.post(`${this.baseUrl}/api/auth/wechat/login`, {
        code: loginCode.code
      });

      console.log('[认证系统] 登录检查结果:', response);

      // 3. 根据响应流程决定后续操作
      switch (response.flow) {
        case 'REGISTRATION':
          // 首次使用，需要注册
          return {
            flow: 'REGISTRATION',
            message: response.message,
            wechatUser: response.wechat_user,
            nextStep: 'FILL_FARM_INFO'
          };

        case 'PENDING_APPROVAL':
          // 等待审批
          return {
            flow: 'PENDING_APPROVAL',
            message: response.message,
            applicationId: response.application_id,
            nextStep: 'WAIT_APPROVAL'
          };

        case 'REJECTED':
          // 申请被拒绝
          return {
            flow: 'REJECTED',
            message: response.message,
            rejectionReason: response.rejection_reason,
            nextStep: 'REAPPLY_OR_CONTACT'
          };

        case 'SUSPENDED':
          // 账号被暂停
          return {
            flow: 'SUSPENDED',
            message: response.message,
            nextStep: 'CONTACT_ADMIN'
          };

        case 'LOGIN_SUCCESS':
          // 登录成功
          const userInfo = this.buildStandardUserInfo(response);
          
          // 保存登录信息
          wx.setStorageSync('access_token', response.tokens.accessToken);
          wx.setStorageSync('refresh_token', response.tokens.refreshToken);
          wx.setStorageSync('user_info', userInfo);
          
          this.currentUser = userInfo;
          
          return {
            flow: 'LOGIN_SUCCESS',
            message: response.message,
            user: userInfo,
            nextStep: 'ENTER_APP'
          };

        default:
          throw new Error(response.message || '认证流程异常');
      }

    } catch (error) {
      console.error('[认证系统] 认证失败:', error);
      return {
        flow: 'ERROR',
        message: error.message || '认证失败，请重试',
        error: error,
        nextStep: 'RETRY'
      };
    }
  }
}

// 导出认证系统实例和相关常量
module.exports = {
  WechatAuthSystem,
  USER_STATUS,
  FARM_STATUS
};