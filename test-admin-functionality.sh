#!/bin/bash

# SAAS后台管理系统功能验证脚本
# 基于Context7最佳实践

BASE_URL="http://localhost:4000"
TOTAL_TESTS=0
PASSED_TESTS=0

echo "🚀 开始SAAS后台管理系统功能验证"
echo "=====================================\n"

# 函数: 测试页面访问
test_page() {
    local path="$1"
    local name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "📄 测试${name}页面..."
    
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}${path}")
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "302" ]; then
        echo "✅ ${name}页面访问成功 (HTTP $http_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo "❌ ${name}页面访问失败 (HTTP $http_code)"
        return 1
    fi
}

# 函数: 测试API端点
test_api() {
    local path="$1"
    local name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "🔗 测试${name}API..."
    
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}${path}")
    
    if [ "$http_code" = "200" ]; then
        echo "✅ ${name}API访问成功 (HTTP $http_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo "⚠️ ${name}API状态: HTTP $http_code"
        if [ "$http_code" = "302" ] || [ "$http_code" = "401" ]; then
            echo "   (需要登录认证，这是正常的)"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        fi
        return 1
    fi
}

# 1. 测试登录功能
echo "🔐 测试登录功能..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))

login_response=$(curl -s -X POST -H "Content-Type: application/json" -d '{"username":"admin","password":"admin123"}' "${BASE_URL}/auth/login")

if echo "$login_response" | grep -q "success.*true"; then
    echo "✅ 登录功能正常"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ 登录功能异常: $login_response"
fi

# 2. 测试核心页面访问
echo "\n📱 测试核心页面访问..."
test_page "/" "首页"
test_page "/auth/login" "登录页面"
test_page "/dashboard" "仪表板"
test_page "/users" "用户管理"
test_page "/tenants" "租户管理"
test_page "/flocks" "鹅群管理"
test_page "/production" "生产记录"
test_page "/health" "健康管理"
test_page "/finance" "财务管理"
test_page "/inventory" "库存管理"
test_page "/reports" "报表中心"
test_page "/system" "系统设置"

# 3. 测试SAAS特色功能
echo "\n🏪 测试SAAS特色功能..."
test_page "/mall" "商城管理"
test_page "/goose-prices" "鹅价管理"
test_page "/announcements" "公告管理"
test_page "/knowledge" "知识库"

# 4. 测试子功能页面
echo "\n🔧 测试子功能页面..."
test_page "/tenants/create" "创建租户"
test_page "/goose-prices/trends" "价格趋势"
test_page "/mall/products" "商品管理"
test_page "/mall/orders" "订单管理"
test_page "/mall/categories" "分类管理"
test_page "/mall/inventory" "库存管理"

# 5. 测试API端点
echo "\n🔌 测试API端点..."
test_api "/health" "健康检查"
test_api "/api/dashboard/stats" "仪表板统计"

# 6. 输出测试结果
echo "\n====================================="
echo "📊 测试结果汇总:"
echo "✅ 通过测试: $PASSED_TESTS/$TOTAL_TESTS"

success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "📈 成功率: ${success_rate}%"

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    echo "\n🎉 所有功能测试通过！系统运行正常！"
    exit_code=0
elif [ "$success_rate" -ge 80 ]; then
    echo "\n✅ 系统基本功能正常，成功率较高"
    exit_code=0
else
    echo "\n⚠️ 系统存在一些问题，需要进一步检查"
    exit_code=1
fi

echo "📝 测试完成时间: $(date)"
echo "====================================="

exit $exit_code