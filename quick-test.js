// 快速测试租户管理功能
const axios = require('axios');

const baseURL = 'http://localhost:4000';
let cookies = '';

async function login() {
    try {
        // 模拟登录
        const loginData = {
            username: 'admin',
            password: 'admin123'
        };

        const loginResponse = await axios.post(`${baseURL}/auth/login`, loginData, {
            maxRedirects: 0,
            validateStatus: (status) => status < 400
        });

        // 提取cookies
        if (loginResponse.headers['set-cookie']) {
            cookies = loginResponse.headers['set-cookie']
                .map(cookie => cookie.split(';')[0])
                .join('; ');
        }

        console.log('✅ 登录成功');
        return true;
    } catch (error) {
        console.log('❌ 登录失败:', error.response?.status, error.response?.statusText);
        return false;
    }
}

async function testTenantList() {
    try {
        const response = await axios.get(`${baseURL}/tenants`, {
            headers: {
                'Cookie': cookies
            }
        });

        if (response.status === 200) {
            console.log('✅ 租户列表页面访问成功');
            
            // 简单检查页面内容
            const html = response.data;
            if (html.includes('租户管理') && html.includes('table')) {
                console.log('✅ 租户列表页面包含正确内容');
                
                // 查找租户数据
                if (html.includes('绿野生态') || html.includes('DEMO001')) {
                    console.log('✅ 找到测试租户数据');
                } else {
                    console.log('⚠️  未找到测试租户数据');
                }
            } else {
                console.log('⚠️  租户列表页面内容不完整');
            }
        }
    } catch (error) {
        console.log('❌ 租户列表访问失败:', error.response?.status, error.response?.statusText);
    }
}

async function testTenantDetails() {
    try {
        const response = await axios.get(`${baseURL}/tenants/1/details`, {
            headers: {
                'Cookie': cookies
            }
        });

        if (response.status === 200) {
            console.log('✅ 租户详情页面访问成功');
        }
    } catch (error) {
        console.log('❌ 租户详情访问失败:', error.response?.status, error.response?.statusText);
    }
}

async function runTests() {
    console.log('🚀 开始测试租户管理功能...\n');
    
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.log('无法继续测试，登录失败');
        return;
    }

    await testTenantList();
    await testTenantDetails();

    console.log('\n📊 测试完成');
}

runTests().catch(console.error);