#!/usr/bin/env node

/**
 * 数据库检查和修复脚本
 * 检查tenants表的字段结构，并提供修复建议
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goose_saas_platform',
    charset: 'utf8mb4'
};

async function checkDatabase() {
    let connection;
    
    try {
        console.log('🔍 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');

        // 检查数据库是否存在
        const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [dbConfig.database]);
        if (databases.length === 0) {
            console.log('❌ 数据库不存在:', dbConfig.database);
            console.log('请先创建数据库:', `CREATE DATABASE ${dbConfig.database};`);
            return;
        }

        // 检查tenants表是否存在
        const [tables] = await connection.execute('SHOW TABLES LIKE ?', ['tenants']);
        if (tables.length === 0) {
            console.log('❌ tenants表不存在');
            console.log('请运行迁移脚本创建表');
            return;
        }

        console.log('✅ tenants表存在');

        // 检查表结构
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'tenants'
            AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
        `, [dbConfig.database]);

        console.log('\n📋 当前表结构:');
        console.log('字段名\t\t\t类型\t\t可空\t默认值\t\t注释');
        console.log('─'.repeat(80));
        
        const fieldNames = [];
        columns.forEach(col => {
            fieldNames.push(col.COLUMN_NAME);
            console.log(`${col.COLUMN_NAME.padEnd(20)}\t${col.DATA_TYPE.padEnd(12)}\t${col.IS_NULLABLE}\t${(col.COLUMN_DEFAULT || 'NULL').toString().padEnd(12)}\t${col.COLUMN_COMMENT || ''}`);
        });

        // 检查关键字段
        const requiredFields = [
            'subscription_start',
            'subscription_end',
            'subscription_plan',
            'status',
            'max_users',
            'max_flocks'
        ];

        console.log('\n🔍 检查关键字段:');
        const missingFields = [];
        requiredFields.forEach(field => {
            if (fieldNames.includes(field)) {
                console.log(`✅ ${field} - 存在`);
            } else {
                console.log(`❌ ${field} - 缺失`);
                missingFields.push(field);
            }
        });

        // 检查可能的字段名变体
        const fieldVariants = {
            'subscription_end': ['subscription_end_date', 'subscriptionEndDate'],
            'subscription_start': ['subscription_start_date', 'subscriptionStartDate'],
            'company_name': ['tenant_name', 'companyName'],
            'contact_name': ['contact_person', 'contactName']
        };

        console.log('\n🔍 检查字段名变体:');
        Object.keys(fieldVariants).forEach(standardField => {
            const variants = fieldVariants[standardField];
            const foundVariant = variants.find(variant => fieldNames.includes(variant));
            if (foundVariant && !fieldNames.includes(standardField)) {
                console.log(`⚠️  发现字段变体: ${foundVariant} (建议重命名为 ${standardField})`);
            }
        });

        // 检查数据
        const [dataCount] = await connection.execute('SELECT COUNT(*) as count FROM tenants');
        console.log(`\n📊 数据统计: ${dataCount[0].count} 条记录`);

        if (dataCount[0].count > 0) {
            // 显示前几条数据
            const [sampleData] = await connection.execute('SELECT * FROM tenants LIMIT 3');
            console.log('\n📝 示例数据:');
            sampleData.forEach((row, index) => {
                console.log(`记录 ${index + 1}:`, {
                    id: row.id,
                    tenant_code: row.tenant_code || row.tenantCode,
                    company_name: row.company_name || row.tenant_name || row.companyName,
                    status: row.status,
                    subscription_plan: row.subscription_plan || row.subscriptionPlan
                });
            });
        }

        // 提供修复建议
        if (missingFields.length > 0) {
            console.log('\n🔧 修复建议:');
            console.log('1. 运行数据库迁移脚本:');
            console.log('   mysql -u root -p < backend/migrations/fix-tenants-subscription-fields.sql');
            console.log('\n2. 或者手动添加缺失字段:');
            missingFields.forEach(field => {
                switch(field) {
                    case 'subscription_start':
                        console.log('   ALTER TABLE tenants ADD COLUMN subscription_start DATE NULL COMMENT "订阅开始日期";');
                        break;
                    case 'subscription_end':
                        console.log('   ALTER TABLE tenants ADD COLUMN subscription_end DATE NULL COMMENT "订阅结束日期";');
                        break;
                    case 'max_users':
                        console.log('   ALTER TABLE tenants ADD COLUMN max_users INT DEFAULT 10 COMMENT "最大用户数";');
                        break;
                    case 'max_flocks':
                        console.log('   ALTER TABLE tenants ADD COLUMN max_flocks INT DEFAULT 10 COMMENT "最大鹅群数";');
                        break;
                }
            });
        } else {
            console.log('\n✅ 表结构检查通过，所有必需字段都存在');
        }

    } catch (error) {
        console.error('❌ 数据库检查失败:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('请检查数据库连接配置:');
            console.log('- DB_HOST:', dbConfig.host);
            console.log('- DB_USER:', dbConfig.user);
            console.log('- DB_NAME:', dbConfig.database);
        }
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 运行检查
if (require.main === module) {
    checkDatabase().then(() => {
        console.log('\n🏁 检查完成');
        process.exit(0);
    }).catch(error => {
        console.error('检查过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = { checkDatabase };
