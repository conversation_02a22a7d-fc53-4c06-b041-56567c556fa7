const jwt = require('jsonwebtoken');
const db = require('../database/connection');

/**
 * JWT Token验证中间件
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '缺少访问令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 验证用户是否仍然有效
    const [users] = await db.query(`
      SELECT u.*, t.tenant_code, t.status as tenant_status
      FROM users u 
      LEFT JOIN tenants t ON u.tenant_id = t.id 
      WHERE u.id = ?
    `, [decoded.userId]);

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    // 检查用户状态
    if (user.status !== 'approved') {
      return res.status(403).json({
        success: false,
        message: '用户账号未激活或已被禁用'
      });
    }

    // 检查租户状态
    if (user.tenant_status !== 'approved') {
      return res.status(403).json({
        success: false,
        message: '所属养殖场未通过审核或已被暂停'
      });
    }

    req.user = {
      userId: user.id,
      tenantId: user.tenant_id,
      role: user.role,
      name: user.real_name,
      farmName: user.farm_name,
      isAdmin: user.is_farm_admin
    };

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token已过期',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token无效',
        code: 'TOKEN_INVALID'
      });
    } else {
      console.error('Token验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
};

/**
 * 管理员Token验证中间件
 */
const authenticateAdmin = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '缺少管理员访问令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_ADMIN_SECRET || process.env.JWT_SECRET);
    
    // 验证管理员是否仍然有效
    const [admins] = await db.query(
      'SELECT * FROM platform_admins WHERE id = ? AND status = "active"',
      [decoded.adminId]
    );

    if (admins.length === 0) {
      return res.status(401).json({
        success: false,
        message: '管理员不存在或已被禁用'
      });
    }

    const admin = admins[0];

    req.user = {
      adminId: admin.id,
      username: admin.username,
      name: admin.name,
      role: admin.role,
      isAdmin: true
    };

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '管理员Token已过期',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '管理员Token无效',
        code: 'TOKEN_INVALID'
      });
    } else {
      console.error('管理员Token验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
};

/**
 * 角色权限验证中间件
 */
const requireRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      const userRole = req.user.role;

      if (!allowedRoles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          message: '权限不足，无法访问此资源'
        });
      }

      next();
    } catch (error) {
      console.error('角色验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '权限验证失败'
      });
    }
  };
};

/**
 * 页面权限验证中间件
 */
const requirePagePermission = (module, action) => {
  return async (req, res, next) => {
    try {
      const userRole = req.user.role;

      // 查询权限配置
      const [permissions] = await db.query(`
        SELECT 
          CASE 
            WHEN ? = 'admin' THEN role_admin
            WHEN ? = 'manager' THEN role_manager  
            WHEN ? = 'finance' THEN role_finance
            WHEN ? = 'employee' THEN role_employee
            ELSE FALSE
          END as has_permission
        FROM permission_configs
        WHERE module = ? AND action = ?
      `, [userRole, userRole, userRole, userRole, module, action]);

      const hasPermission = permissions.length > 0 ? !!permissions[0].has_permission : false;

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `您的角色(${userRole})无权访问此功能模块`,
          code: 'PERMISSION_DENIED',
          details: {
            module,
            action,
            userRole
          }
        });
      }

      next();
    } catch (error) {
      console.error('页面权限验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '权限验证失败'
      });
    }
  };
};

/**
 * 租户数据隔离中间件
 */
const requireTenant = (req, res, next) => {
  if (!req.user.tenantId) {
    return res.status(403).json({
      success: false,
      message: '用户未关联任何养殖场'
    });
  }

  req.tenantId = req.user.tenantId;
  next();
};

/**
 * API限流中间件 (简单实现)
 */
const rateLimit = (windowMs = 15 * 60 * 1000, max = 100) => {
  const requests = new Map();

  return (req, res, next) => {
    const identifier = req.ip || req.user?.userId || 'anonymous';
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期的请求记录
    if (!requests.has(identifier)) {
      requests.set(identifier, []);
    }

    const userRequests = requests.get(identifier).filter(time => time > windowStart);
    
    if (userRequests.length >= max) {
      return res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED'
      });
    }

    userRequests.push(now);
    requests.set(identifier, userRequests);

    next();
  };
};

/**
 * 操作日志记录中间件
 */
const logOperation = (operationType, operationModule) => {
  return async (req, res, next) => {
    const originalSend = res.send;

    res.send = function(data) {
      // 记录操作日志
      const logData = {
        operator_type: req.user.isAdmin ? 'admin' : 'user',
        operator_id: req.user.adminId || req.user.userId,
        operation_type: operationType,
        operation_module: operationModule,
        operation_desc: `${operationType} - ${req.method} ${req.path}`,
        request_data: JSON.stringify({
          method: req.method,
          path: req.path,
          params: req.params,
          query: req.query,
          body: req.body
        }),
        response_data: data,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      };

      // 异步记录日志，不影响响应
      db.query(`
        INSERT INTO operation_logs (
          operator_type, operator_id, operation_type, operation_module,
          operation_desc, request_data, response_data, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        logData.operator_type,
        logData.operator_id,
        logData.operation_type,
        logData.operation_module,
        logData.operation_desc,
        logData.request_data,
        logData.response_data,
        logData.ip_address,
        logData.user_agent
      ]).catch(error => {
        console.error('记录操作日志失败:', error);
      });

      originalSend.call(this, data);
    };

    next();
  };
};

module.exports = {
  authenticateToken,
  authenticateAdmin,
  requireRole,
  requirePagePermission,
  requireTenant,
  rateLimit,
  logOperation
};