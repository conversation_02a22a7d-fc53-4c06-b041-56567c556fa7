-- SAAS平台测试数据
-- 插入租户数据并关联现有业务数据

-- 1. 插入测试租户数据
INSERT IGNORE INTO tenants (tenant_code, tenant_name, tenant_type, contact_person, contact_phone, contact_email, address, subscription_plan, subscription_start, subscription_end, max_users, max_flocks) VALUES
('TENANT001', '阳光养鹅场', 'enterprise', '张经理', '***********', '<EMAIL>', '北京市昌平区阳光农业园区88号', 'premium', '2024-01-01', '2024-12-31', 20, 50),
('TENANT002', '绿野养鹅合作社', 'cooperative', '李小明', '***********', '<EMAIL>', '河北省承德市绿野合作社', 'standard', '2024-02-01', '2025-01-31', 10, 20),
('TENANT003', '山水养鹅场', 'individual', '王大华', '***********', '<EMAIL>', '河南省信阳市山水养鹅场', 'basic', '2024-03-01', '2024-11-30', 5, 10);

-- 2. 将现有用户关联到租户
UPDATE users SET tenant_id = 1 WHERE id = 1;  -- admin 关联到阳光养鹅场
UPDATE users SET tenant_id = 1 WHERE id = 2;  -- manager1 关联到阳光养鹅场
UPDATE users SET tenant_id = 2 WHERE id = 4;  -- user1 关联到绿野养鹅合作社
UPDATE users SET tenant_id = 3 WHERE id = 5;  -- user2 关联到山水养鹅场
UPDATE users SET tenant_id = 1 WHERE id = 6;  -- inactive_user 关联到阳光养鹅场

-- 3. 将现有鹅群关联到租户
UPDATE flocks SET tenant_id = 1 WHERE userId IN (1, 2);  -- admin和manager1的鹅群
UPDATE flocks SET tenant_id = 2 WHERE userId = 4;        -- user1的鹅群
UPDATE flocks SET tenant_id = 3 WHERE userId = 5;        -- user2的鹅群
UPDATE flocks SET tenant_id = 1 WHERE userId = 6;        -- inactive_user的鹅群

-- 4. 将现有生产记录关联到租户
UPDATE production_records SET tenant_id = 1 WHERE userId IN (1, 2);
UPDATE production_records SET tenant_id = 2 WHERE userId = 4;
UPDATE production_records SET tenant_id = 3 WHERE userId = 5;

-- 5. 将现有健康记录关联到租户
UPDATE health_records SET tenant_id = 1 WHERE userId IN (1, 2);
UPDATE health_records SET tenant_id = 2 WHERE userId = 4;
UPDATE health_records SET tenant_id = 3 WHERE userId = 5;

-- 6. 将现有财务记录关联到租户
UPDATE financial_records SET tenant_id = 1 WHERE userId = 2;
UPDATE financial_records SET tenant_id = 2 WHERE userId = 4;
UPDATE financial_records SET tenant_id = 3 WHERE userId = 5;

-- 7. 插入今日鹅价数据
INSERT IGNORE INTO goose_prices (date, region, breed, price_type, unit, min_price, max_price, avg_price, market_trend, source, is_published) VALUES
('2024-08-26', '北京', '大白鹅', 'live_goose', 'kg', 18.00, 22.00, 20.00, 'stable', '北京农产品批发市场', true),
('2024-08-26', '北京', '灰鹅', 'live_goose', 'kg', 16.00, 20.00, 18.00, 'up', '北京农产品批发市场', true),
('2024-08-26', '河北', '大白鹅', 'live_goose', 'kg', 17.00, 21.00, 19.00, 'stable', '河北省畜牧局', true),
('2024-08-26', '河南', '大白鹅', 'live_goose', 'kg', 16.50, 20.50, 18.50, 'down', '河南省农业厅', true),
('2024-08-26', '全国', '鹅蛋', 'egg', '个', 1.20, 1.80, 1.50, 'stable', '全国农产品价格监测系统', true);

-- 8. 插入商城商品数据
INSERT IGNORE INTO mall_products (product_code, name, category_id, brand, description, price, cost_price, market_price, stock_qty, status, is_featured) VALUES
('FEED001', '优质成鹅全价饲料', 1, '正大饲料', '适合成年鹅的全价配合饲料，营养均衡，提高产蛋率', 85.00, 70.00, 95.00, 500, 'active', true),
('FEED002', '幼鹅专用开食料', 2, '希望饲料', '专为1-4周龄幼鹅设计的开食料，易消化吸收', 120.00, 95.00, 135.00, 200, 'active', true),
('EQUIP001', '自动饮水器', 7, '畜牧王', '304不锈钢材质，自动控制水位，卫生耐用', 180.00, 140.00, 220.00, 100, 'active', false),
('EQUIP002', '料槽（3米长）', 8, '农设通', '镀锌钢板制作，防腐耐用，适合群体饲喂', 350.00, 280.00, 400.00, 50, 'active', false),
('MED001', '禽用多维', 3, '动物保健', '补充维生素A、D、E等，增强免疫力', 45.00, 35.00, 55.00, 300, 'active', false);

-- 9. 插入测试订单数据
INSERT IGNORE INTO mall_orders (order_no, tenant_id, user_id, contact_name, contact_phone, shipping_address, total_amount, shipping_fee, final_amount, payment_method, payment_status, order_status) VALUES
('ORD240826001', 1, 2, '张经理', '***********', '北京市昌平区阳光农业园区88号', 850.00, 0, 850.00, 'bank_transfer', 'paid', 'delivered'),
('ORD240826002', 2, 4, '李小明', '***********', '河北省承德市绿野合作社', 240.00, 15.00, 255.00, 'wechat', 'paid', 'shipped'),
('ORD240826003', 3, 5, '王大华', '***********', '河南省信阳市山水养鹅场', 530.00, 0, 530.00, 'alipay', 'pending', 'confirmed');

-- 10. 插入订单商品详情
INSERT IGNORE INTO mall_order_items (order_id, product_id, product_name, product_image, price, quantity, subtotal) VALUES
(1, 1, '优质成鹅全价饲料', '', 85.00, 10, 850.00),
(2, 2, '幼鹅专用开食料', '', 120.00, 2, 240.00),
(3, 1, '优质成鹅全价饲料', '', 85.00, 5, 425.00),
(3, 5, '禽用多维', '', 45.00, 2, 90.00),
(3, 3, '自动饮水器', '', 180.00, 1, 180.00);

-- 验证数据
SELECT 'SAAS平台数据统计' as info;
SELECT '租户统计' as title, subscription_plan, COUNT(*) as count FROM tenants GROUP BY subscription_plan;
SELECT '商品统计' as title, c.name as category, COUNT(p.id) as product_count FROM mall_products p JOIN mall_categories c ON p.category_id = c.id GROUP BY c.name;
SELECT '订单统计' as title, order_status, COUNT(*) as count FROM mall_orders GROUP BY order_status;
SELECT '今日鹅价统计' as title, region, COUNT(*) as price_count FROM goose_prices WHERE date = '2024-08-26' GROUP BY region;

-- 检查租户关联数据
SELECT '租户业务数据统计' as info;
SELECT t.tenant_name, 
       COUNT(DISTINCT u.id) as user_count,
       COUNT(DISTINCT f.id) as flock_count,
       COUNT(DISTINCT p.id) as production_count,
       COUNT(DISTINCT h.id) as health_count
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id
LEFT JOIN flocks f ON t.id = f.tenant_id  
LEFT JOIN production_records p ON t.id = p.tenant_id
LEFT JOIN health_records h ON t.id = h.tenant_id
GROUP BY t.id, t.tenant_name;