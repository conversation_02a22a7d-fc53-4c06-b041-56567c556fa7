/**
 * 数据库查询优化工具
 * Database Query Optimization Utils
 */

class QueryOptimizer {
  
  /**
   * 构建分页查询选项
   * @param {Object} params 查询参数
   * @returns {Object} Sequelize查询选项
   */
  static buildPaginationOptions(params) {
    const { page = 1, limit = 20, orderBy = 'createdAt', orderDir = 'DESC' } = params;
    
    return {
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit),
      order: [[orderBy, orderDir.toUpperCase()]]
    };
  }

  /**
   * 构建搜索条件
   * @param {Object} searchParams 搜索参数
   * @param {Array} searchFields 可搜索字段
   * @returns {Object} WHERE条件
   */
  static buildSearchConditions(searchParams, searchFields = []) {
    const { Op } = require('sequelize');
    const conditions = {};
    
    // 关键词搜索
    if (searchParams.keyword && searchFields.length > 0) {
      conditions[Op.or] = searchFields.map(field => ({
        [field]: {
          [Op.like]: `%${searchParams.keyword}%`
        }
      }));
    }
    
    // 日期范围搜索
    if (searchParams.startDate || searchParams.endDate) {
      const dateCondition = {};
      if (searchParams.startDate) {
        dateCondition[Op.gte] = new Date(searchParams.startDate);
      }
      if (searchParams.endDate) {
        dateCondition[Op.lte] = new Date(searchParams.endDate);
      }
      conditions.createdAt = dateCondition;
    }
    
    // 状态筛选
    if (searchParams.status) {
      conditions.status = searchParams.status;
    }
    
    return conditions;
  }

  /**
   * 构建多租户查询条件
   * @param {string} tenantId 租户ID
   * @param {Object} additionalConditions 额外条件
   * @returns {Object} WHERE条件
   */
  static buildTenantConditions(tenantId, additionalConditions = {}) {
    return {
      tenantId,
      ...additionalConditions
    };
  }

  /**
   * 构建包含关联的查询选项
   * @param {Array} includes 关联配置
   * @returns {Array} include配置
   */
  static buildIncludes(includes = []) {
    return includes.map(include => {
      if (typeof include === 'string') {
        return { association: include, required: false };
      }
      return { required: false, ...include };
    });
  }
}

/**
 * 缓存管理器
 */
class CacheManager {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5分钟TTL
  }

  /**
   * 生成缓存键
   * @param {string} prefix 前缀
   * @param {string} tenantId 租户ID
   * @param {Object} params 参数
   * @returns {string} 缓存键
   */
  generateKey(prefix, tenantId, params = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});
    
    return `${prefix}:${tenantId}:${JSON.stringify(sortedParams)}`;
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {*} 缓存值
   */
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {*} value 缓存值
   * @param {number} ttl 过期时间(毫秒)
   */
  set(key, value, ttl = this.ttl) {
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl
    });
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxAge: this.ttl
    };
  }
}

/**
 * 数据库连接池优化配置
 */
const optimizedPoolConfig = {
  max: 20,          // 最大连接数
  min: 5,           // 最小连接数
  acquire: 30000,   // 获取连接超时时间 (30秒)
  idle: 10000,      // 连接空闲超时时间 (10秒)
  evict: 1000,      // 检查空闲连接间隔时间 (1秒)
  validate: true    // 验证连接有效性
};

// 创建全局缓存实例
const globalCache = new CacheManager();

module.exports = {
  QueryOptimizer,
  CacheManager,
  globalCache,
  optimizedPoolConfig
};