/**
 * 响应函数迁移工具 - 兼容旧的generateSuccessResponse和generateErrorResponse
 */

/**
 * 生成成功响应 (兼容函数)
 * @param {string} message 响应消息
 * @param {*} data 响应数据
 * @param {Object} metadata 元数据
 * @returns {Object} 统一响应格式
 */
function generateSuccessResponse(message = '操作成功', data = null, metadata = {}) {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
    ...metadata
  };
}

/**
 * 生成错误响应 (兼容函数)
 * @param {string} message 错误消息
 * @param {Object} errorDetails 错误详情
 * @returns {Object} 统一响应格式
 */
function generateErrorResponse(message = '操作失败', errorDetails = {}) {
  return {
    success: false,
    message,
    error: errorDetails,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  generateSuccessResponse,
  generateErrorResponse
};