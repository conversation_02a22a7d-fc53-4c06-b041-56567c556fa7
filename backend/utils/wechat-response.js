/**
 * 微信小程序API响应格式标准化工具
 * 基于微信官方开发规范统一API响应格式
 */

/**
 * 微信API标准错误码
 * 参照微信官方文档定义
 */
const WECHAT_ERROR_CODES = {
  // 成功
  SUCCESS: 0,
  
  // 系统错误
  SYSTEM_ERROR: -1,
  SYSTEM_BUSY: -1000,
  
  // 参数错误 (40001-40099)
  ACCESS_TOKEN_INVALID: 40014,
  PARAMETER_MISSING: 40101,
  ACCESS_TOKEN_MISSING: 41001,
  ACCESS_TOKEN_EXPIRED: 42001,
  
  // 权限错误 (43001-43999)
  HTTP_METHOD_ERROR: 43002,
  PERMISSION_DENIED: 43003,
  
  // 业务逻辑错误 (44001-44999)  
  USER_NOT_FOUND: 44001,
  RESOURCE_NOT_FOUND: 44002,
  RESOURCE_CONFLICT: 44003,
  VALIDATION_ERROR: 44004,
  BUSINESS_ERROR: 44005,
  
  // 网络错误 (45001-45999)
  NETWORK_ERROR: 45001,
  TIMEOUT_ERROR: 45002,
  
  // 自定义业务错误 (50001-59999)
  AUTHENTICATION_FAILED: 50001,
  AUTHORIZATION_FAILED: 50002,
  TENANT_ACCESS_DENIED: 50003,
  DATA_VALIDATION_ERROR: 50004,
  INSUFFICIENT_PERMISSIONS: 50005
};

/**
 * 错误码对应的标准错误消息
 */
const ERROR_MESSAGES = {
  [WECHAT_ERROR_CODES.SUCCESS]: 'ok',
  [WECHAT_ERROR_CODES.SYSTEM_ERROR]: '系统错误',
  [WECHAT_ERROR_CODES.SYSTEM_BUSY]: '系统繁忙，此时请开发者稍候再试',
  [WECHAT_ERROR_CODES.ACCESS_TOKEN_INVALID]: '不合法的access_token',
  [WECHAT_ERROR_CODES.PARAMETER_MISSING]: '缺少必要参数',
  [WECHAT_ERROR_CODES.ACCESS_TOKEN_MISSING]: '缺少access_token参数',
  [WECHAT_ERROR_CODES.ACCESS_TOKEN_EXPIRED]: 'access_token超时',
  [WECHAT_ERROR_CODES.HTTP_METHOD_ERROR]: 'HTTP方法不正确',
  [WECHAT_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [WECHAT_ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
  [WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND]: '资源不存在',
  [WECHAT_ERROR_CODES.RESOURCE_CONFLICT]: '资源冲突',
  [WECHAT_ERROR_CODES.VALIDATION_ERROR]: '数据验证失败',
  [WECHAT_ERROR_CODES.BUSINESS_ERROR]: '业务逻辑错误',
  [WECHAT_ERROR_CODES.NETWORK_ERROR]: '网络错误',
  [WECHAT_ERROR_CODES.TIMEOUT_ERROR]: '请求超时',
  [WECHAT_ERROR_CODES.AUTHENTICATION_FAILED]: '身份验证失败',
  [WECHAT_ERROR_CODES.AUTHORIZATION_FAILED]: '授权失败',
  [WECHAT_ERROR_CODES.TENANT_ACCESS_DENIED]: '租户访问被拒绝',
  [WECHAT_ERROR_CODES.DATA_VALIDATION_ERROR]: '数据校验错误',
  [WECHAT_ERROR_CODES.INSUFFICIENT_PERMISSIONS]: '权限不足'
};

/**
 * 创建成功响应
 * @param {*} data - 响应数据
 * @param {string} errmsg - 自定义成功消息，默认为 'ok'
 * @returns {Object} 标准化的微信API响应格式
 */
function createSuccessResponse(data = null, errmsg = 'ok') {
  const response = {
    errcode: WECHAT_ERROR_CODES.SUCCESS,
    errmsg: errmsg
  };
  
  // 只有在有数据时才添加data字段
  if (data !== null && data !== undefined) {
    response.data = data;
  }
  
  return response;
}

/**
 * 创建错误响应
 * @param {number} errcode - 错误码
 * @param {string} errmsg - 自定义错误消息
 * @param {*} data - 额外的错误数据（可选）
 * @returns {Object} 标准化的微信API错误响应格式
 */
function createErrorResponse(errcode, errmsg = null, data = null) {
  const response = {
    errcode: errcode,
    errmsg: errmsg || ERROR_MESSAGES[errcode] || '未知错误'
  };
  
  // 只有在有额外数据时才添加data字段
  if (data !== null && data !== undefined) {
    response.data = data;
  }
  
  return response;
}

/**
 * 创建分页响应
 * @param {Array} items - 数据列表
 * @param {Object} pagination - 分页信息 {page, limit, total, pages}
 * @param {string} errmsg - 成功消息
 * @returns {Object} 带分页信息的标准响应
 */
function createPaginatedResponse(items, pagination, errmsg = 'ok') {
  return createSuccessResponse({
    items: items,
    pagination: {
      page: parseInt(pagination.page) || 1,
      limit: parseInt(pagination.limit) || 10,
      total: parseInt(pagination.total) || 0,
      pages: Math.ceil((parseInt(pagination.total) || 0) / (parseInt(pagination.limit) || 10))
    }
  }, errmsg);
}

/**
 * Express中间件：自动包装响应格式
 */
function wechatResponseMiddleware(req, res, next) {
  // 保存原始的json方法
  const originalJson = res.json.bind(res);
  
  // 重写json方法
  res.json = function(data) {
    // 如果已经是微信API格式，直接返回
    if (data && typeof data === 'object' && 'errcode' in data) {
      return originalJson(data);
    }
    
    // 如果是旧格式，转换为微信格式
    if (data && typeof data === 'object' && 'success' in data) {
      if (data.success) {
        return originalJson(createSuccessResponse(data.data, data.message));
      } else {
        return originalJson(createErrorResponse(
          WECHAT_ERROR_CODES.BUSINESS_ERROR,
          data.message,
          data.data
        ));
      }
    }
    
    // 默认作为成功响应处理
    return originalJson(createSuccessResponse(data));
  };
  
  // 添加便捷方法
  res.wechatSuccess = function(data, message = 'ok') {
    return res.json(createSuccessResponse(data, message));
  };
  
  res.wechatError = function(errcode, errmsg, data = null) {
    return res.status(getHttpStatusFromErrorCode(errcode)).json(
      createErrorResponse(errcode, errmsg, data)
    );
  };
  
  res.wechatPaginated = function(items, pagination, message = 'ok') {
    return res.json(createPaginatedResponse(items, pagination, message));
  };
  
  next();
}

/**
 * 根据微信错误码获取对应的HTTP状态码
 * @param {number} errcode - 微信错误码
 * @returns {number} HTTP状态码
 */
function getHttpStatusFromErrorCode(errcode) {
  // 成功
  if (errcode === WECHAT_ERROR_CODES.SUCCESS) {
    return 200;
  }
  
  // 参数错误
  if (errcode >= 40001 && errcode <= 40999) {
    return 400;
  }
  
  // 认证/授权错误  
  if (errcode >= 41001 && errcode <= 42999 || 
      errcode === WECHAT_ERROR_CODES.AUTHENTICATION_FAILED ||
      errcode === WECHAT_ERROR_CODES.AUTHORIZATION_FAILED) {
    return 401;
  }
  
  // 权限错误
  if (errcode >= 43001 && errcode <= 43999 ||
      errcode === WECHAT_ERROR_CODES.INSUFFICIENT_PERMISSIONS ||
      errcode === WECHAT_ERROR_CODES.TENANT_ACCESS_DENIED) {
    return 403;
  }
  
  // 资源不存在
  if (errcode === WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND ||
      errcode === WECHAT_ERROR_CODES.USER_NOT_FOUND) {
    return 404;
  }
  
  // 资源冲突
  if (errcode === WECHAT_ERROR_CODES.RESOURCE_CONFLICT) {
    return 409;
  }
  
  // 验证错误
  if (errcode === WECHAT_ERROR_CODES.VALIDATION_ERROR ||
      errcode === WECHAT_ERROR_CODES.DATA_VALIDATION_ERROR) {
    return 422;
  }
  
  // 默认服务器错误
  return 500;
}

/**
 * 错误处理中间件
 */
function wechatErrorHandler(error, req, res, next) {
  try { const { Logger } = require('../middleware/errorHandler'); Logger.error('API错误', { error: error.message, stack: error.stack, path: req.originalUrl }); } catch(_) {}
  
  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.wechatError(
      WECHAT_ERROR_CODES.ACCESS_TOKEN_INVALID,
      'Token无效'
    );
  }
  
  if (error.name === 'TokenExpiredError') {
    return res.wechatError(
      WECHAT_ERROR_CODES.ACCESS_TOKEN_EXPIRED,
      'Token已过期'
    );
  }
  
  // 验证错误
  if (error.name === 'ValidationError') {
    return res.wechatError(
      WECHAT_ERROR_CODES.VALIDATION_ERROR,
      error.message
    );
  }
  
  // 自定义业务错误
  if (error.wechatErrorCode) {
    return res.wechatError(
      error.wechatErrorCode,
      error.message
    );
  }
  
  // 默认系统错误
  return res.wechatError(
    WECHAT_ERROR_CODES.SYSTEM_ERROR,
    process.env.NODE_ENV === 'development' ? error.message : '系统内部错误'
  );
}

/**
 * 创建自定义业务错误
 */
class WechatApiError extends Error {
  constructor(errcode, errmsg, data = null) {
    super(errmsg);
    this.wechatErrorCode = errcode;
    this.data = data;
    this.name = 'WechatApiError';
  }
}

module.exports = {
  WECHAT_ERROR_CODES,
  ERROR_MESSAGES,
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  wechatResponseMiddleware,
  wechatErrorHandler,
  getHttpStatusFromErrorCode,
  WechatApiError
};