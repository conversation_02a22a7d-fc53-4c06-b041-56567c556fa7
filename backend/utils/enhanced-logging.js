/**
 * 增强日志记录和监控系统
 * Enhanced Logging and Monitoring System
 */

const fs = require('fs');
const path = require('path');

class EnhancedLogger {
  constructor(options = {}) {
    this.logDir = options.logDir || path.join(__dirname, '../logs');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    this.logLevel = options.logLevel || 'info';
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.ensureLogDir();
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * 格式化日志消息
   * @param {string} level 日志级别
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   * @returns {string} 格式化后的日志
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaString = Object.keys(meta).length > 0 ? JSON.stringify(meta) : '';
    
    return `[${timestamp}] [${level.toUpperCase()}] ${message} ${metaString}\n`;
  }

  /**
   * 写入日志文件
   * @param {string} filename 文件名
   * @param {string} content 日志内容
   */
  writeToFile(filename, content) {
    const filepath = path.join(this.logDir, filename);
    
    try {
      // 检查文件大小，如果超过限制则轮转
      if (fs.existsSync(filepath)) {
        const stats = fs.statSync(filepath);
        if (stats.size >= this.maxFileSize) {
          this.rotateLogFile(filepath);
        }
      }
      
      fs.appendFileSync(filepath, content, 'utf8');
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 轮转日志文件
   * @param {string} filepath 文件路径
   */
  rotateLogFile(filepath) {
    try {
      const ext = path.extname(filepath);
      const basename = path.basename(filepath, ext);
      const dir = path.dirname(filepath);
      
      // 移动现有文件
      for (let i = this.maxFiles - 1; i >= 1; i--) {
        const oldFile = path.join(dir, `${basename}.${i}${ext}`);
        const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxFiles - 1) {
            fs.unlinkSync(oldFile); // 删除最旧的文件
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }
      
      // 重命名当前文件
      const archivedFile = path.join(dir, `${basename}.1${ext}`);
      fs.renameSync(filepath, archivedFile);
      
    } catch (error) {
      console.error('轮转日志文件失败:', error);
    }
  }

  /**
   * 记录日志
   * @param {string} level 日志级别
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   */
  log(level, message, meta = {}) {
    if (this.levels[level] > this.levels[this.logLevel]) {
      return; // 跳过低级别日志
    }

    const formattedMessage = this.formatMessage(level, message, meta);
    
    // 输出到控制台
    console.log(formattedMessage.trim());
    
    // 写入对应级别的日志文件
    this.writeToFile(`${level}.log`, formattedMessage);
    
    // 所有日志都写入综合日志文件
    this.writeToFile('combined.log', formattedMessage);
  }

  /**
   * 错误日志
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   */
  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  /**
   * 警告日志
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   */
  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  /**
   * 信息日志
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   */
  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  /**
   * 调试日志
   * @param {string} message 消息内容
   * @param {Object} meta 元数据
   */
  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }
}

/**
 * API请求日志中间件
 */
const requestLogger = (logger) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const originalSend = res.send;
    
    // 记录请求信息
    logger.info('API请求开始', {
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      tenantId: req.tenantId,
      userId: req.userId
    });

    // 拦截响应
    res.send = function(data) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      logger.info('API请求结束', {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        responseSize: Buffer.byteLength(data || ''),
        tenantId: req.tenantId,
        userId: req.userId
      });

      // 如果响应时间过长，记录警告
      if (duration > 3000) {
        logger.warn('API响应时间过长', {
          method: req.method,
          url: req.originalUrl,
          duration: `${duration}ms`
        });
      }

      originalSend.call(this, data);
    };

    next();
  };
};

/**
 * 错误监控
 */
class ErrorMonitor {
  constructor(logger) {
    this.logger = logger;
    this.errorCounts = new Map();
    this.alertThreshold = 10; // 10分钟内错误次数阈值
    this.timeWindow = 10 * 60 * 1000; // 10分钟
  }

  /**
   * 记录错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文信息
   */
  recordError(error, context = {}) {
    const errorKey = error.message || 'Unknown Error';
    const currentTime = Date.now();
    
    // 记录错误日志
    this.logger.error('应用错误', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      context
    });

    // 更新错误计数
    if (!this.errorCounts.has(errorKey)) {
      this.errorCounts.set(errorKey, []);
    }
    
    const errorTimes = this.errorCounts.get(errorKey);
    errorTimes.push(currentTime);
    
    // 清理过期的错误记录
    const validTimes = errorTimes.filter(time => 
      currentTime - time < this.timeWindow
    );
    this.errorCounts.set(errorKey, validTimes);
    
    // 检查是否需要告警
    if (validTimes.length >= this.alertThreshold) {
      this.triggerAlert(errorKey, validTimes.length);
    }
  }

  /**
   * 触发告警
   * @param {string} errorKey 错误键
   * @param {number} count 错误次数
   */
  triggerAlert(errorKey, count) {
    this.logger.error('错误告警触发', {
      errorType: errorKey,
      count,
      timeWindow: this.timeWindow / 60000 + '分钟',
      level: 'CRITICAL'
    });

    // 这里可以集成告警通知系统
    // 例如：发送邮件、短信、钉钉等
  }
}

/**
 * 系统性能监控
 */
class PerformanceMonitor {
  constructor(logger) {
    this.logger = logger;
    this.metrics = {
      cpuUsage: [],
      memoryUsage: [],
      responseTime: []
    };
    this.startMonitoring();
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    setInterval(() => {
      this.collectMetrics();
    }, 30000); // 每30秒收集一次
  }

  /**
   * 收集系统指标
   */
  collectMetrics() {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      const metrics = {
        timestamp: new Date().toISOString(),
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
          external: Math.round(memUsage.external / 1024 / 1024) + 'MB'
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: Math.round(process.uptime()) + 's'
      };

      this.logger.info('系统性能指标', metrics);
      
      // 检查内存使用率
      if (memUsage.heapUsed / memUsage.heapTotal > 0.85) {
        this.logger.warn('内存使用率过高', {
          usage: Math.round(memUsage.heapUsed / memUsage.heapTotal * 100) + '%'
        });
      }
      
    } catch (error) {
      this.logger.error('收集性能指标失败', { error: error.message });
    }
  }
}

// 创建全局日志实例
const logger = new EnhancedLogger({
  logLevel: process.env.LOG_LEVEL || 'info'
});

// 创建监控实例
const errorMonitor = new ErrorMonitor(logger);
const performanceMonitor = new PerformanceMonitor(logger);

module.exports = {
  EnhancedLogger,
  logger,
  requestLogger,
  errorMonitor,
  performanceMonitor,
  ErrorMonitor,
  PerformanceMonitor
};