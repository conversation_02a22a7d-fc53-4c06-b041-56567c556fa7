#!/bin/bash

# 智慧养鹅系统全面联调测试脚本
# Full Integration Test Script for Smart Goose System

echo "🧪 开始智慧养鹅系统全面联调测试..."
echo "=================================================="

# 测试配置
BACKEND_URL="http://localhost:3000"
ADMIN_URL="http://localhost:4000"

echo "📡 1. 测试服务器连通性"
echo "----------------------------------------"

# 测试主后端API服务器
echo -n "主后端API (port 3000): "
if curl -s --max-time 5 "$BACKEND_URL/api/health" >/dev/null; then
    echo "✅ 正常"
else
    echo "❌ 失败"
    exit 1
fi

# 测试SAAS管理后台
echo -n "SAAS管理后台 (port 4000): "
if curl -s --max-time 5 -I "$ADMIN_URL" >/dev/null; then
    echo "✅ 正常"
else
    echo "❌ 失败"
    exit 1
fi

echo ""
echo "🗄️ 2. 测试数据库连接"
echo "----------------------------------------"

# 测试数据库连接
echo -n "数据库连接: "
DB_TEST=$(mysql -h localhost -u root smart_goose -e "SELECT COUNT(*) as count FROM users;" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 正常"
    echo "$DB_TEST" | tail -1 | sed 's/^/   用户总数: /'
else
    echo "❌ 失败"
    exit 1
fi

echo ""
echo "📊 3. 测试数据完整性"
echo "----------------------------------------"

# 检查各表数据量
echo "数据统计:"
mysql -h localhost -u root smart_goose -e "
    SELECT '用户' as 类型, COUNT(*) as 数量 FROM users
    UNION ALL
    SELECT '鹅群', COUNT(*) FROM flocks
    UNION ALL
    SELECT '生产记录', COUNT(*) FROM production_records
    UNION ALL
    SELECT '健康记录', COUNT(*) FROM health_records
    UNION ALL
    SELECT '财务记录', COUNT(*) FROM financial_records;" 2>/dev/null

echo ""
echo "🔐 4. 测试API认证"
echo "----------------------------------------"

# 测试健康检查API
echo -n "健康检查API: "
HEALTH_RESPONSE=$(curl -s "$BACKEND_URL/api/health")
if echo "$HEALTH_RESPONSE" | grep -q "success.*true"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

echo ""
echo "🎯 5. 测试管理后台功能"
echo "----------------------------------------"

echo -n "登录页面: "
if curl -s "$ADMIN_URL/auth/login" | grep -q "智慧养鹅SAAS"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

echo -n "静态资源: "
if curl -s "$ADMIN_URL/css/admin-custom.css" | grep -q "Custom CSS"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

echo ""
echo "📈 6. 测试仪表盘数据"
echo "----------------------------------------"

# 显示最新的生产数据
echo "最新生产记录:"
mysql -h localhost -u root smart_goose -e "
    SELECT 
        f.name as '鹅群名称',
        p.recordedDate as '记录日期',
        p.eggCount as '产蛋数量',
        u.name as '操作员'
    FROM production_records p
    JOIN flocks f ON p.flockId = f.id
    JOIN users u ON p.userId = u.id
    ORDER BY p.recordedDate DESC, p.createdAt DESC
    LIMIT 5;" 2>/dev/null

echo ""
echo "🏥 7. 测试健康监控"
echo "----------------------------------------"

# 显示健康状态
echo "健康状态统计:"
mysql -h localhost -u root smart_goose -e "
    SELECT 
        healthStatus as '健康状态',
        COUNT(*) as '记录数量'
    FROM health_records
    GROUP BY healthStatus;" 2>/dev/null

echo ""
echo "💰 8. 测试财务统计"
echo "----------------------------------------"

# 显示财务摘要
mysql -h localhost -u root smart_goose -e "
    SELECT 
        type as '类型',
        SUM(amount) as '总金额',
        COUNT(*) as '记录数'
    FROM financial_records
    GROUP BY type;" 2>/dev/null

echo ""
echo "🎉 联调测试完成!"
echo "=================================================="

echo ""
echo "📋 系统访问信息:"
echo "• 主后端API: http://localhost:3000"
echo "• SAAS管理后台: http://localhost:4000"
echo "• 默认管理员账号: admin / admin123"
echo ""
echo "✨ 所有服务均已就绪，可以进行完整功能测试！"