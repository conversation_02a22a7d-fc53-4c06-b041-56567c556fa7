const express = require('express');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 应用认证中间件到需要认证的路由
router.use('/records', authenticateToken);

/**
 * 健康管理路由
 */

// 基础健康检查（无需认证）
router.get('/', (req, res) => {
  res.json({
    status: 'ok',
    message: '智慧养鹅SAAS平台运行正常',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '2.9.2',
    environment: process.env.NODE_ENV || 'development'
  });
});

// 获取健康记录列表
router.get('/records', async (req, res) => {
  try {
    const { page = 1, limit = 20, flock_id, status } = req.query;
    const userId = req.user.userId;

    // 模拟健康记录数据
    const mockHealthRecords = [
      {
        id: 1,
        flock_id: 1,
        flock_name: '鹅群A',
        check_date: '2024-08-26T09:00:00Z',
        check_type: 'routine',
        health_status: 'healthy',
        temperature: 41.5,
        weight_avg: 3.2,
        symptoms: null,
        treatment: null,
        veterinarian: '张医生',
        notes: '整体健康状况良好，无异常症状',
        created_at: '2024-08-26T09:30:00Z',
        user_id: userId
      },
      {
        id: 2,
        flock_id: 2,
        flock_name: '鹅群B',
        check_date: '2024-08-25T14:00:00Z',
        check_type: 'vaccination',
        health_status: 'healthy',
        temperature: 41.2,
        weight_avg: 2.8,
        symptoms: null,
        treatment: '禽流感疫苗接种',
        veterinarian: '李医生',
        notes: '疫苗接种完成，观察期内无不良反应',
        created_at: '2024-08-25T15:00:00Z',
        user_id: userId
      },
      {
        id: 3,
        flock_id: 1,
        flock_name: '鹅群A',
        check_date: '2024-08-24T11:00:00Z',
        check_type: 'treatment',
        health_status: 'recovering',
        temperature: 42.1,
        weight_avg: 3.0,
        symptoms: '轻微腹泻',
        treatment: '口服抗生素，调整饲料配方',
        veterinarian: '王医生',
        notes: '症状已有好转，继续观察',
        created_at: '2024-08-24T12:00:00Z',
        user_id: userId
      }
    ];

    // 过滤条件
    let filteredRecords = mockHealthRecords;
    if (flock_id) {
      filteredRecords = filteredRecords.filter(record => record.flock_id == flock_id);
    }
    if (status) {
      filteredRecords = filteredRecords.filter(record => record.health_status === status);
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        records: paginatedRecords,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: filteredRecords.length,
          total_pages: Math.ceil(filteredRecords.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取健康记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取健康记录失败',
      error: error.message
    });
  }
});

// 创建健康记录
router.post('/records', async (req, res) => {
  try {
    const { flock_id, check_type, health_status, temperature, weight_avg, symptoms, treatment, veterinarian, notes } = req.body;
    const userId = req.user.userId;

    // 验证必填字段
    if (!flock_id || !check_type || !health_status) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：flock_id, check_type, health_status'
      });
    }

    // 模拟创建健康记录
    const newRecord = {
      id: Date.now(),
      flock_id: parseInt(flock_id),
      flock_name: `鹅群${flock_id}`,
      check_date: new Date().toISOString(),
      check_type,
      health_status,
      temperature: temperature || null,
      weight_avg: weight_avg || null,
      symptoms: symptoms || null,
      treatment: treatment || null,
      veterinarian: veterinarian || null,
      notes: notes || null,
      created_at: new Date().toISOString(),
      user_id: userId
    };

    res.status(201).json({
      success: true,
      message: '健康记录创建成功',
      data: newRecord
    });

  } catch (error) {
    console.error('创建健康记录失败:', error);
    res.status(500).json({
      success: false,
      message: '创建健康记录失败',
      error: error.message
    });
  }
});

// 获取健康记录详情
router.get('/records/:id', async (req, res) => {
  try {
    const recordId = parseInt(req.params.id);
    const userId = req.user.userId;

    // 模拟健康记录详情
    const mockRecord = {
      id: recordId,
      flock_id: 1,
      flock_name: '鹅群A',
      check_date: '2024-08-26T09:00:00Z',
      check_type: 'routine',
      health_status: 'healthy',
      temperature: 41.5,
      weight_avg: 3.2,
      symptoms: null,
      treatment: null,
      veterinarian: '张医生',
      notes: '整体健康状况良好，无异常症状',
      created_at: '2024-08-26T09:30:00Z',
      updated_at: '2024-08-26T09:30:00Z',
      user_id: userId,
      additional_data: {
        mortality_count: 0,
        sick_count: 2,
        recovered_count: 1,
        medication_used: [],
        follow_up_required: false,
        next_check_date: '2024-09-02T09:00:00Z'
      }
    };

    res.json({
      success: true,
      data: mockRecord
    });

  } catch (error) {
    console.error('获取健康记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取健康记录详情失败',
      error: error.message
    });
  }
});

// 详细健康检查（系统状态）
router.get('/detailed', async (req, res) => {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: 'ok',
        redis: 'not_configured',
        cache: 'ok'
      },
      memory: process.memoryUsage(),
      uptime: process.uptime()
    };

    res.json(health);
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: '服务检查失败',
      error: error.message
    });
  }
});

module.exports = router;
