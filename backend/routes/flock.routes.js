const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');

// 应用认证中间件
router.use(authenticateToken);

/**
 * 鹅群管理路由
 * 提供鹅群的CRUD操作
 */

// 获取鹅群列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const userId = req.user.userId;

    // 模拟数据 - 实际应该从数据库获取
    const mockFlocks = [
      {
        id: 1,
        name: '鹅群A',
        breed: '白鹅',
        count: 150,
        age_months: 6,
        health_status: 'healthy',
        location: '养殖区A',
        created_at: '2024-01-15T08:00:00Z',
        updated_at: '2024-08-26T10:30:00Z',
        user_id: userId
      },
      {
        id: 2,
        name: '鹅群B',
        breed: '灰鹅',
        count: 200,
        age_months: 4,
        health_status: 'healthy',
        location: '养殖区B',
        created_at: '2024-02-20T09:00:00Z',
        updated_at: '2024-08-26T11:15:00Z',
        user_id: userId
      },
      {
        id: 3,
        name: '鹅群C',
        breed: '白鹅',
        count: 120,
        age_months: 8,
        health_status: 'attention',
        location: '养殖区C',
        created_at: '2024-03-10T07:30:00Z',
        updated_at: '2024-08-26T09:45:00Z',
        user_id: userId
      }
    ];

    // 简单的搜索过滤
    let filteredFlocks = mockFlocks;
    if (search) {
      filteredFlocks = mockFlocks.filter(flock => 
        flock.name.toLowerCase().includes(search.toLowerCase()) ||
        flock.breed.toLowerCase().includes(search.toLowerCase()) ||
        flock.location.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedFlocks = filteredFlocks.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        flocks: paginatedFlocks,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: filteredFlocks.length,
          total_pages: Math.ceil(filteredFlocks.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取鹅群列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取鹅群列表失败',
      error: error.message
    });
  }
});

// 获取鹅群详情
router.get('/:id', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const userId = req.user.userId;

    // 模拟数据
    const mockFlock = {
      id: flockId,
      name: `鹅群${flockId}`,
      breed: '白鹅',
      count: 150,
      age_months: 6,
      health_status: 'healthy',
      location: `养殖区${String.fromCharCode(64 + flockId)}`,
      description: '这是一个健康的鹅群，定期进行健康检查和疫苗接种。',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: '2024-08-26T10:30:00Z',
      user_id: userId,
      stats: {
        avg_weight: 3.2,
        mortality_rate: 0.02,
        feed_consumption: 450,
        egg_production: 120
      }
    };

    res.json({
      success: true,
      data: mockFlock
    });

  } catch (error) {
    console.error('获取鹅群详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取鹅群详情失败',
      error: error.message
    });
  }
});

// 创建鹅群
router.post('/', async (req, res) => {
  try {
    const { name, breed, count, location, description } = req.body;
    const userId = req.user.userId;

    // 验证必填字段
    if (!name || !breed || !count || !location) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：name, breed, count, location'
      });
    }

    // 模拟创建鹅群
    const newFlock = {
      id: Date.now(), // 简单的ID生成
      name,
      breed,
      count: parseInt(count),
      age_months: 0,
      health_status: 'healthy',
      location,
      description: description || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      user_id: userId
    };

    res.status(201).json({
      success: true,
      message: '鹅群创建成功',
      data: newFlock
    });

  } catch (error) {
    console.error('创建鹅群失败:', error);
    res.status(500).json({
      success: false,
      message: '创建鹅群失败',
      error: error.message
    });
  }
});

// 更新鹅群
router.put('/:id', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const { name, breed, count, location, description, health_status } = req.body;
    const userId = req.user.userId;

    // 模拟更新鹅群
    const updatedFlock = {
      id: flockId,
      name: name || `鹅群${flockId}`,
      breed: breed || '白鹅',
      count: count ? parseInt(count) : 150,
      age_months: 6,
      health_status: health_status || 'healthy',
      location: location || `养殖区${String.fromCharCode(64 + flockId)}`,
      description: description || '',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: new Date().toISOString(),
      user_id: userId
    };

    res.json({
      success: true,
      message: '鹅群更新成功',
      data: updatedFlock
    });

  } catch (error) {
    console.error('更新鹅群失败:', error);
    res.status(500).json({
      success: false,
      message: '更新鹅群失败',
      error: error.message
    });
  }
});

// 删除鹅群
router.delete('/:id', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const userId = req.user.userId;

    // 模拟删除操作
    res.json({
      success: true,
      message: '鹅群删除成功',
      data: {
        id: flockId,
        deleted_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('删除鹅群失败:', error);
    res.status(500).json({
      success: false,
      message: '删除鹅群失败',
      error: error.message
    });
  }
});

// 获取鹅群统计信息
router.get('/:id/stats', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const userId = req.user.userId;

    // 模拟统计数据
    const stats = {
      flock_id: flockId,
      total_count: 150,
      healthy_count: 147,
      sick_count: 2,
      dead_count: 1,
      avg_weight: 3.2,
      weight_gain_rate: 0.15,
      feed_consumption_daily: 45,
      feed_conversion_ratio: 2.8,
      egg_production_daily: 12,
      mortality_rate: 0.007,
      health_score: 95,
      last_health_check: '2024-08-25T14:30:00Z',
      next_vaccination: '2024-09-15T09:00:00Z'
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取鹅群统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取鹅群统计失败',
      error: error.message
    });
  }
});

module.exports = router;
