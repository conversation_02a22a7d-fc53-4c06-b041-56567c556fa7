const express = require('express');
const router = express.Router();
const db = require('../database/connection');
const { authenticateToken, requireRole } = require('../middleware/auth');

/**
 * 获取待审批列表
 */
router.get('/pending', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { type = 'all', page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'ar.status = "pending"';
    let params = [];

    if (type === 'tenant') {
      whereClause += ' AND ar.application_type = "tenant"';
    } else if (type === 'user') {
      whereClause += ' AND ar.application_type = "user"';
    }

    // 查询待审批列表
    const [results] = await db.query(`
      SELECT 
        ar.id as approval_id,
        ar.application_type,
        ar.application_id,
        ar.applicant_type,
        ar.applicant_id,
        ar.status,
        ar.created_at,
        -- 租户信息
        t.farm_name,
        t.legal_representative,
        t.contact_phone as tenant_phone,
        t.province,
        t.city,
        t.detailed_address,
        t.farm_scale,
        -- 用户信息
        u.real_name,
        u.phone as user_phone,
        u.email,
        u.position,
        u.role,
        -- 微信用户信息
        wu.nickname,
        wu.avatar_url
      FROM approval_records ar
      LEFT JOIN tenants t ON ar.application_type = 'tenant' AND ar.application_id = t.id
      LEFT JOIN users u ON ar.applicant_type = 'user' AND ar.applicant_id = u.id
      LEFT JOIN wechat_users wu ON u.wechat_user_id = wu.id
      WHERE ${whereClause}
      ORDER BY ar.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // 查询总数
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total
      FROM approval_records ar
      WHERE ${whereClause}
    `, params);

    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        list: results,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取待审批列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取待审批列表失败',
      error: error.message
    });
  }
});

/**
 * 获取审批详情
 */
router.get('/:approvalId/detail', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { approvalId } = req.params;

    const [results] = await db.query(`
      SELECT 
        ar.*,
        -- 租户详细信息
        t.tenant_code,
        t.farm_name,
        t.legal_representative,
        t.business_license,
        t.contact_phone as tenant_phone,
        t.email as tenant_email,
        t.province,
        t.city,
        t.district,
        t.detailed_address,
        t.farm_scale,
        t.scale_description,
        t.breed_types,
        t.breeding_years,
        t.facilities,
        t.certifications,
        -- 申请用户信息
        u.real_name,
        u.phone as user_phone,
        u.email as user_email,
        u.id_card,
        u.position,
        u.role,
        -- 微信用户信息
        wu.nickname,
        wu.avatar_url,
        wu.openid
      FROM approval_records ar
      LEFT JOIN tenants t ON ar.application_type = 'tenant' AND ar.application_id = t.id
      LEFT JOIN users u ON ar.applicant_type = 'user' AND ar.applicant_id = u.id
      LEFT JOIN wechat_users wu ON u.wechat_user_id = wu.id
      WHERE ar.id = ?
    `, [approvalId]);

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: '审批记录不存在'
      });
    }

    const record = results[0];

    // 构建返回数据
    const data = {
      approvalInfo: {
        id: record.id,
        applicationType: record.application_type,
        applicationId: record.application_id,
        status: record.status,
        comment: record.comment,
        createdAt: record.created_at,
        approvedAt: record.approved_at
      }
    };

    if (record.application_type === 'tenant') {
      data.tenant = {
        tenantCode: record.tenant_code,
        farmName: record.farm_name,
        legalRepresentative: record.legal_representative,
        businessLicense: record.business_license,
        contactPhone: record.tenant_phone,
        email: record.tenant_email,
        province: record.province,
        city: record.city,
        district: record.district,
        detailedAddress: record.detailed_address,
        farmScale: record.farm_scale,
        scaleDescription: record.scale_description,
        breedTypes: JSON.parse(record.breed_types || '[]'),
        breedingYears: record.breeding_years,
        facilities: JSON.parse(record.facilities || '{}'),
        certifications: JSON.parse(record.certifications || '[]')
      };
    }

    data.applicant = {
      realName: record.real_name,
      phone: record.user_phone,
      email: record.user_email,
      idCard: record.id_card,
      position: record.position,
      role: record.role,
      wechat: {
        nickname: record.nickname,
        avatarUrl: record.avatar_url,
        openid: record.openid
      }
    };

    res.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('获取审批详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取审批详情失败',
      error: error.message
    });
  }
});

/**
 * 审批申请（通过/拒绝）
 */
router.post('/:approvalId/approve', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { approvalId } = req.params;
    const { action, comment = '' } = req.body; // action: 'approve' | 'reject'
    const adminId = req.user.adminId || req.user.userId;

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: '无效的审批操作'
      });
    }

    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // 1. 获取审批记录
      const [approvalRecords] = await connection.query(
        'SELECT * FROM approval_records WHERE id = ? AND status = "pending"',
        [approvalId]
      );

      if (approvalRecords.length === 0) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          message: '审批记录不存在或已处理'
        });
      }

      const record = approvalRecords[0];
      const status = action === 'approve' ? 'approved' : 'rejected';

      // 2. 更新审批记录
      await connection.query(`
        UPDATE approval_records 
        SET status = ?, comment = ?, approver_id = ?, approved_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [status, comment, adminId, approvalId]);

      // 3. 更新对应的申请记录状态
      if (record.application_type === 'tenant') {
        // 更新租户状态
        await connection.query(
          'UPDATE tenants SET status = ?, approved_by = ?, approved_at = CURRENT_TIMESTAMP WHERE id = ?',
          [status, adminId, record.application_id]
        );

        if (status === 'approved') {
          // 同时审批通过该租户的管理员用户
          await connection.query(`
            UPDATE users 
            SET status = 'approved', approved_by = ?, approved_at = CURRENT_TIMESTAMP
            WHERE tenant_id = ? AND is_farm_admin = TRUE
          `, [adminId, record.application_id]);
        } else {
          // 拒绝租户时，同时拒绝管理员用户
          await connection.query(`
            UPDATE users 
            SET status = 'rejected', approved_by = ?, approved_at = CURRENT_TIMESTAMP
            WHERE tenant_id = ? AND is_farm_admin = TRUE
          `, [adminId, record.application_id]);
        }
      } else if (record.application_type === 'user') {
        // 更新用户状态
        await connection.query(
          'UPDATE users SET status = ?, approved_by = ?, approved_at = CURRENT_TIMESTAMP WHERE id = ?',
          [status, adminId, record.application_id]
        );
      }

      // 4. 记录操作日志
      await connection.query(`
        INSERT INTO operation_logs (
          operator_type, operator_id, operation_type, operation_module,
          operation_desc, request_data, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        adminId,
        action === 'approve' ? 'approve_application' : 'reject_application',
        'approval',
        `${action === 'approve' ? '审批通过' : '审批拒绝'}${record.application_type === 'tenant' ? '租户' : '用户'}申请`,
        JSON.stringify({ approvalId, action, comment }),
        req.ip,
        req.get('User-Agent')
      ]);

      await connection.commit();

      res.json({
        success: true,
        message: `${action === 'approve' ? '审批通过' : '审批拒绝'}成功`,
        data: {
          approvalId: parseInt(approvalId),
          action,
          status,
          comment
        }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('审批操作失败:', error);
    res.status(500).json({
      success: false,
      message: '审批操作失败',
      error: error.message
    });
  }
});

/**
 * 批量审批
 */
router.post('/batch-approve', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { approvalIds, action, comment = '' } = req.body;
    const adminId = req.user.adminId || req.user.userId;

    if (!Array.isArray(approvalIds) || approvalIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要处理的审批记录'
      });
    }

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: '无效的审批操作'
      });
    }

    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      const results = [];
      const status = action === 'approve' ? 'approved' : 'rejected';

      for (const approvalId of approvalIds) {
        try {
          // 获取审批记录
          const [approvalRecords] = await connection.query(
            'SELECT * FROM approval_records WHERE id = ? AND status = "pending"',
            [approvalId]
          );

          if (approvalRecords.length === 0) {
            results.push({
              approvalId,
              success: false,
              message: '记录不存在或已处理'
            });
            continue;
          }

          const record = approvalRecords[0];

          // 更新审批记录
          await connection.query(`
            UPDATE approval_records 
            SET status = ?, comment = ?, approver_id = ?, approved_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `, [status, comment, adminId, approvalId]);

          // 更新对应的申请记录
          if (record.application_type === 'tenant') {
            await connection.query(
              'UPDATE tenants SET status = ?, approved_by = ?, approved_at = CURRENT_TIMESTAMP WHERE id = ?',
              [status, adminId, record.application_id]
            );

            if (status === 'approved') {
              await connection.query(`
                UPDATE users 
                SET status = 'approved', approved_by = ?, approved_at = CURRENT_TIMESTAMP
                WHERE tenant_id = ? AND is_farm_admin = TRUE
              `, [adminId, record.application_id]);
            }
          } else if (record.application_type === 'user') {
            await connection.query(
              'UPDATE users SET status = ?, approved_by = ?, approved_at = CURRENT_TIMESTAMP WHERE id = ?',
              [status, adminId, record.application_id]
            );
          }

          results.push({
            approvalId,
            success: true,
            message: '处理成功'
          });

        } catch (error) {
          results.push({
            approvalId,
            success: false,
            message: error.message
          });
        }
      }

      // 记录批量操作日志
      await connection.query(`
        INSERT INTO operation_logs (
          operator_type, operator_id, operation_type, operation_module,
          operation_desc, request_data, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        adminId,
        'batch_approve',
        'approval',
        `批量${action === 'approve' ? '审批通过' : '审批拒绝'}`,
        JSON.stringify({ approvalIds, action, comment, results }),
        req.ip,
        req.get('User-Agent')
      ]);

      await connection.commit();

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      res.json({
        success: true,
        message: `批量处理完成，成功${successCount}条，失败${failCount}条`,
        data: {
          results,
          summary: {
            total: approvalIds.length,
            success: successCount,
            fail: failCount
          }
        }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('批量审批失败:', error);
    res.status(500).json({
      success: false,
      message: '批量审批失败',
      error: error.message
    });
  }
});

/**
 * 获取审批统计信息
 */
router.get('/statistics', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // 获取待审批数量统计
    const [pendingStats] = await db.query(`
      SELECT 
        application_type,
        COUNT(*) as count
      FROM approval_records 
      WHERE status = 'pending'
      GROUP BY application_type
    `);

    // 获取今日审批统计
    const [todayStats] = await db.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM approval_records 
      WHERE DATE(approved_at) = CURDATE()
      GROUP BY status
    `);

    // 获取近7天审批趋势
    const [weeklyTrend] = await db.query(`
      SELECT 
        DATE(approved_at) as date,
        status,
        COUNT(*) as count
      FROM approval_records 
      WHERE approved_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(approved_at), status
      ORDER BY date DESC
    `);

    const statistics = {
      pending: {
        tenant: pendingStats.find(s => s.application_type === 'tenant')?.count || 0,
        user: pendingStats.find(s => s.application_type === 'user')?.count || 0,
        total: pendingStats.reduce((sum, s) => sum + s.count, 0)
      },
      today: {
        approved: todayStats.find(s => s.status === 'approved')?.count || 0,
        rejected: todayStats.find(s => s.status === 'rejected')?.count || 0
      },
      weeklyTrend: weeklyTrend
    };

    res.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('获取审批统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
});

module.exports = router;