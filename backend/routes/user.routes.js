const express = require('express');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * 用户管理路由
 * 遵循RESTful API设计规范
 */

// 所有路由都需要验证令牌
router.use(authenticateToken);

// 获取用户列表（管理员权限）
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const userId = req.user.userId;

    // 模拟用户数据
    const mockUsers = [
      {
        id: 1,
        username: 'admin',
        real_name: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        role: 'admin',
        status: 'active',
        farm_name: '示例养殖场',
        department: '管理部',
        position: '系统管理员',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-08-26T10:00:00Z'
      },
      {
        id: 2,
        username: 'manager',
        real_name: '养殖场经理',
        email: '<EMAIL>',
        phone: '13800138001',
        role: 'manager',
        status: 'active',
        farm_name: '示例养殖场',
        department: '生产部',
        position: '养殖场经理',
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-08-26T09:30:00Z'
      },
      {
        id: 3,
        username: 'employee',
        real_name: '养殖员工',
        email: '<EMAIL>',
        phone: '13800138002',
        role: 'employee',
        status: 'active',
        farm_name: '示例养殖场',
        department: '生产部',
        position: '养殖员',
        created_at: '2024-02-01T00:00:00Z',
        updated_at: '2024-08-26T08:45:00Z'
      }
    ];

    // 简单搜索过滤
    let filteredUsers = mockUsers;
    if (search) {
      filteredUsers = mockUsers.filter(user =>
        user.username.toLowerCase().includes(search.toLowerCase()) ||
        user.real_name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        users: paginatedUsers,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: filteredUsers.length,
          total_pages: Math.ceil(filteredUsers.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 创建用户（管理员权限）
router.post('/', async (req, res) => {
  try {
    const { username, real_name, email, phone, role, department, position } = req.body;

    if (!username || !real_name || !email) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段：username, real_name, email'
      });
    }

    const newUser = {
      id: Date.now(),
      username,
      real_name,
      email,
      phone: phone || null,
      role: role || 'employee',
      status: 'active',
      farm_name: '示例养殖场',
      department: department || '生产部',
      position: position || '员工',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: newUser
    });

  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
});

// 获取指定用户详情
router.get('/:id', async (req, res) => {
  try {
    const userId = parseInt(req.params.id);

    const mockUser = {
      id: userId,
      username: `user${userId}`,
      real_name: `用户${userId}`,
      email: `user${userId}@example.com`,
      phone: '13800138000',
      role: 'employee',
      status: 'active',
      farm_name: '示例养殖场',
      department: '生产部',
      position: '员工',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-08-26T10:00:00Z'
    };

    res.json({
      success: true,
      data: mockUser
    });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
});

// 简化其他路由为基本响应
router.put('/:id', (req, res) => {
  res.json({ success: true, message: '用户更新成功' });
});

router.patch('/:id', (req, res) => {
  res.json({ success: true, message: '用户部分更新成功' });
});

router.delete('/:id', (req, res) => {
  res.json({ success: true, message: '用户删除成功' });
});

router.get('/stats/overview', (req, res) => {
  res.json({
    success: true,
    data: {
      total_users: 3,
      active_users: 3,
      inactive_users: 0,
      admin_users: 1,
      manager_users: 1,
      employee_users: 1
    }
  });
});

module.exports = router;
