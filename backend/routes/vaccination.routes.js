// 防疫流程路由
const express = require('express');
const router = express.Router();
const VaccinationController = require('../controllers/vaccination.controller');

// 获取防疫模板列表
router.get('/templates', VaccinationController.getTemplates);

// 获取模板的防疫步骤
router.get('/templates/:templateId/steps', VaccinationController.getTemplateSteps);

// 为鹅群创建防疫计划
router.post('/flocks/vaccination', VaccinationController.createFlockVaccination);

// 获取鹅群的防疫任务列表
router.get('/flocks/:flockId/tasks', VaccinationController.getFlockTasks);

// 获取今天的待办事项
router.get('/today-tasks/:userId', VaccinationController.getTodayTasks);

// 更新任务状态
router.patch('/tasks/:taskId/status', VaccinationController.updateTaskStatus);

// 获取防疫统计报告
router.get('/stats', VaccinationController.getVaccinationStats);

module.exports = router;