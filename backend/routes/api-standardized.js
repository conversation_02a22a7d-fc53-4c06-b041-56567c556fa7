/**
 * 智慧养鹅小程序 - 标准化API路由配置
 * 基于微信小程序开发规范和RESTful API最佳实践
 */

const express = require('express');
const { 
  wechatResponseMiddleware,
  wechatErrorHandler,
  WECHAT_ERROR_CODES,
  createSuccessResponse,
  createErrorResponse,
  WechatApiError
} = require('../utils/wechat-response');

const router = express.Router();

// 应用微信API响应格式中间件
router.use(wechatResponseMiddleware);

/**
 * API版本和路径规范：
 * 
 * 1. 版本管理：
 *    - v1: 兼容旧版接口
 *    - v2: 新版标准化接口（推荐）
 * 
 * 2. 路径命名规范：
 *    - 使用小写字母和连字符
 *    - RESTful风格：GET /users, POST /users, GET /users/:id
 *    - 嵌套资源：GET /users/:id/orders
 * 
 * 3. 响应格式统一：
 *    - 成功：{ errcode: 0, errmsg: "ok", data: {...} }
 *    - 失败：{ errcode: 40001, errmsg: "参数错误" }
 *    - 分页：{ errcode: 0, errmsg: "ok", data: { items: [], pagination: {} } }
 */

// ============================================================================
// 认证模块 (Authentication)
// ============================================================================

/**
 * 微信小程序登录
 * POST /api/v2/auth/wechat-login
 */
router.post('/v2/auth/wechat-login', async (req, res) => {
  try {
    const authController = require('../controllers/auth.controller');
    await authController.wechatLogin(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.AUTHENTICATION_FAILED, error.message);
  }
});

/**
 * 获取用户信息
 * GET /api/v2/auth/user-info
 */
router.get('/v2/auth/user-info', async (req, res) => {
  try {
    const authController = require('../controllers/auth.controller');
    await authController.getUserInfo(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.USER_NOT_FOUND, error.message);
  }
});

/**
 * 刷新访问令牌
 * POST /api/v2/auth/refresh-token
 */
router.post('/v2/auth/refresh-token', async (req, res) => {
  try {
    const authController = require('../controllers/auth.controller');
    await authController.refreshToken(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.ACCESS_TOKEN_EXPIRED, error.message);
  }
});

// ============================================================================
// 首页模块 (Home)
// ============================================================================

/**
 * 获取首页数据
 * GET /api/v2/home/<USER>
 */
router.get('/v2/home/<USER>', async (req, res) => {
  try {
    const homeController = require('../controllers/home.controller');
    await homeController.getDashboardData(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取公告列表
 * GET /api/v2/home/<USER>
 */
router.get('/v2/home/<USER>', async (req, res) => {
  try {
    const homeController = require('../controllers/home.controller');
    await homeController.getAnnouncements(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

// ============================================================================
// 生产管理模块 (Production)
// ============================================================================

/**
 * 获取生产记录列表
 * GET /api/v2/production/records
 * Query参数: page, limit, type, batch, startDate, endDate
 */
router.get('/v2/production/records', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.getRecords(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 创建生产记录
 * POST /api/v2/production/records
 */
router.post('/v2/production/records', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.createRecord(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.VALIDATION_ERROR, error.message);
  }
});

/**
 * 获取生产记录详情
 * GET /api/v2/production/records/:id
 */
router.get('/v2/production/records/:id', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.getRecordById(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND, error.message);
  }
});

/**
 * 更新生产记录
 * PUT /api/v2/production/records/:id
 */
router.put('/v2/production/records/:id', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.updateRecord(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.VALIDATION_ERROR, error.message);
  }
});

/**
 * 删除生产记录
 * DELETE /api/v2/production/records/:id
 */
router.delete('/v2/production/records/:id', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.deleteRecord(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取生产统计数据
 * GET /api/v2/production/statistics
 */
router.get('/v2/production/statistics', async (req, res) => {
  try {
    const productionController = require('../controllers/production.controller');
    await productionController.getStatistics(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

// ============================================================================
// 物料管理模块 (Materials)
// ============================================================================

/**
 * 获取物料列表
 * GET /api/v2/materials
 */
router.get('/v2/materials', async (req, res) => {
  try {
    const materialsController = require('../controllers/materials.controller');
    await materialsController.getMaterials(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 创建物料
 * POST /api/v2/materials
 */
router.post('/v2/materials', async (req, res) => {
  try {
    const materialsController = require('../controllers/materials.controller');
    await materialsController.createMaterial(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.VALIDATION_ERROR, error.message);
  }
});

/**
 * 更新物料
 * PUT /api/v2/materials/:id
 */
router.put('/v2/materials/:id', async (req, res) => {
  try {
    const materialsController = require('../controllers/materials.controller');
    await materialsController.updateMaterial(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND, error.message);
  }
});

// ============================================================================
// OA办公模块 (Office Automation)
// ============================================================================

/**
 * 获取OA统计信息
 * GET /api/v2/oa/statistics
 */
router.get('/v2/oa/statistics', async (req, res) => {
  try {
    const oaController = require('../controllers/oa.controller');
    await oaController.getStatistics(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取待处理审批列表
 * GET /api/v2/oa/approvals/pending
 */
router.get('/v2/oa/approvals/pending', async (req, res) => {
  try {
    const oaController = require('../controllers/oa.controller');
    await oaController.getPendingApprovals(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取审批历史
 * GET /api/v2/oa/approvals/history
 */
router.get('/v2/oa/approvals/history', async (req, res) => {
  try {
    const oaController = require('../controllers/oa.controller');
    await oaController.getApprovalHistory(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取紧急审批
 * GET /api/v2/oa/approvals/urgent
 */
router.get('/v2/oa/approvals/urgent', async (req, res) => {
  try {
    const oaController = require('../controllers/oa.controller');
    await oaController.getUrgentApprovals(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

/**
 * 获取权限用户列表
 * GET /api/v2/oa/permissions/users
 */
router.get('/v2/oa/permissions/users', async (req, res) => {
  try {
    const oaController = require('../controllers/oa.controller');
    await oaController.getPermissionUsers(req, res);
  } catch (error) {
    throw new WechatApiError(WECHAT_ERROR_CODES.BUSINESS_ERROR, error.message);
  }
});

// ============================================================================
// V1 API 兼容性支持
// ============================================================================

/**
 * V1 API 重定向到 V2
 * 提供向后兼容性，同时引导开发者使用新版API
 */
router.all('/v1/*', (req, res) => {
  const v1Path = req.originalUrl;
  const v2Path = v1Path.replace('/api/v1/', '/api/v2/');
  
  // 特殊路径映射
  const pathMappings = {
    '/api/v1/auth/userinfo': '/api/v2/auth/user-info',
    '/api/v1/home/<USER>': '/api/v2/home/<USER>',
    '/api/v1/production-records': '/api/v2/production/records',
    '/api/v1/materials': '/api/v2/materials'
  };
  
  const newPath = pathMappings[v1Path] || v2Path;
  
  res.wechatError(
    WECHAT_ERROR_CODES.BUSINESS_ERROR,
    'API版本已升级，请使用V2版本',
    {
      deprecated_path: v1Path,
      new_path: newPath,
      migration_guide: 'https://docs.zhihuiyange.com/api/migration-v1-to-v2'
    }
  );
});

// ============================================================================
// 错误处理
// ============================================================================

// 404 处理
router.use('*', (req, res) => {
  res.wechatError(
    WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND,
    'API接口不存在',
    {
      path: req.originalUrl,
      method: req.method,
      available_versions: ['v2'],
      documentation: 'https://docs.zhihuiyange.com/api'
    }
  );
});

// 统一错误处理
router.use(wechatErrorHandler);

module.exports = router;