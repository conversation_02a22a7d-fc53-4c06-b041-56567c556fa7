const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const router = express.Router();
const db = require('../database/connection');
const { authenticateToken, requireRole } = require('../middleware/auth');

/**
 * 微信小程序登录接口
 */
router.post('/wechat/login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少微信登录凭证'
      });
    }

    // 1. 通过code获取openid和session_key
    const wxConfig = await db.query(
      'SELECT config_value FROM system_configs WHERE config_key IN (?, ?)',
      ['wechat.app_id', 'wechat.app_secret']
    );
    
    const appId = wxConfig.find(c => c.config_key === 'wechat.app_id')?.config_value;
    const appSecret = wxConfig.find(c => c.config_key === 'wechat.app_secret')?.config_value;
    
    if (!appId || !appSecret) {
      return res.status(500).json({
        success: false,
        message: '微信配置未完成'
      });
    }

    const wxResponse = await axios.get(`https://api.weixin.qq.com/sns/jscode2session`, {
      params: {
        appid: appId,
        secret: appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });

    if (wxResponse.data.errcode) {
      return res.status(400).json({
        success: false,
        message: '微信登录失败',
        error: wxResponse.data.errmsg
      });
    }

    const { openid, session_key, unionid } = wxResponse.data;

    // 2. 查询或创建微信用户记录
    let [wechatUsers] = await db.query('SELECT * FROM wechat_users WHERE openid = ?', [openid]);
    let wechatUser = wechatUsers[0];

    if (!wechatUser) {
      // 创建新的微信用户记录
      const [result] = await db.query(
        'INSERT INTO wechat_users (openid, unionid, nickname, avatar_url, gender, language, city, province, country) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          openid,
          unionid,
          userInfo?.nickName || '',
          userInfo?.avatarUrl || '',
          userInfo?.gender || 0,
          userInfo?.language || '',
          userInfo?.city || '',
          userInfo?.province || '',
          userInfo?.country || ''
        ]
      );
      
      wechatUser = {
        id: result.insertId,
        openid,
        unionid,
        nickname: userInfo?.nickName || '',
        avatar_url: userInfo?.avatarUrl || ''
      };
    } else {
      // 更新用户信息
      await db.query(
        'UPDATE wechat_users SET nickname = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [userInfo?.nickName || wechatUser.nickname, userInfo?.avatarUrl || wechatUser.avatar_url, wechatUser.id]
      );
    }

    // 3. 检查是否已关联平台用户
    const [users] = await db.query(`
      SELECT u.*, t.farm_name, t.tenant_code, t.status as tenant_status 
      FROM users u 
      LEFT JOIN tenants t ON u.tenant_id = t.id 
      WHERE u.wechat_user_id = ?
    `, [wechatUser.id]);

    if (users.length === 0) {
      // 未注册用户，返回注册流程
      return res.json({
        success: true,
        flow: 'REGISTRATION',
        message: '欢迎使用智慧养鹅平台！请先完成养殖场注册申请。',
        wechat_user: {
          id: wechatUser.id,
          openid: wechatUser.openid,
          nickname: wechatUser.nickname,
          avatar_url: wechatUser.avatar_url
        }
      });
    }

    const user = users[0];

    // 4. 检查租户状态
    if (user.tenant_status === 'pending') {
      return res.json({
        success: true,
        flow: 'PENDING_APPROVAL',
        message: '您的养殖场申请正在审核中，请耐心等待审批结果。',
        application_id: user.tenant_id
      });
    }

    if (user.tenant_status === 'rejected') {
      return res.json({
        success: true,
        flow: 'REJECTED',
        message: '很遗憾，您的养殖场申请未通过审核。',
        rejection_reason: '请联系平台客服了解详情'
      });
    }

    if (user.tenant_status === 'suspended') {
      return res.json({
        success: true,
        flow: 'SUSPENDED',
        message: '您的账号已被暂停使用，如有疑问请联系客服。'
      });
    }

    // 5. 检查用户状态
    if (user.status === 'pending') {
      return res.json({
        success: true,
        flow: 'PENDING_APPROVAL',
        message: '您的用户申请正在审核中，请耐心等待审批结果。',
        application_id: user.id
      });
    }

    if (user.status === 'rejected') {
      return res.json({
        success: true,
        flow: 'REJECTED',
        message: '很遗憾，您的用户申请未通过审核。'
      });
    }

    if (user.status === 'suspended') {
      return res.json({
        success: true,
        flow: 'SUSPENDED',
        message: '您的账号已被暂停使用，如有疑问请联系客服。'
      });
    }

    // 6. 登录成功，生成JWT token
    const tokenPayload = {
      userId: user.id,
      tenantId: user.tenant_id,
      role: user.role,
      wechatUserId: wechatUser.id
    };

    const accessToken = jwt.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: '7d' });
    const refreshToken = jwt.sign({ userId: user.id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });

    // 7. 记录登录日志
    await db.query(
      'INSERT INTO user_login_logs (user_id, login_type, ip_address, user_agent) VALUES (?, ?, ?, ?)',
      [user.id, 'wechat', req.ip, req.get('User-Agent')]
    );

    // 8. 返回登录成功信息
    res.json({
      success: true,
      flow: 'LOGIN_SUCCESS',
      message: '登录成功',
      tokens: {
        accessToken,
        refreshToken
      },
      user: {
        id: user.id,
        name: user.real_name,
        farmName: user.farm_name,
        role: user.role,
        roleCode: user.role,
        avatar: wechatUser.avatar_url || '/images/default_avatar.png',
        phone: user.phone,
        email: user.email,
        department: user.department,
        position: user.position
      },
      tenant: {
        id: user.tenant_id,
        companyName: user.farm_name,
        tenantCode: user.tenant_code
      }
    });

  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请重试',
      error: error.message
    });
  }
});

/**
 * 养殖场注册申请
 */
router.post('/register/farm', async (req, res) => {
  try {
    const {
      wechatUserId,
      // 养殖场信息
      farmName,
      legalRepresentative,
      businessLicense,
      contactPhone,
      email,
      province,
      city,
      district,
      detailedAddress,
      farmScale,
      scaleDescription,
      breedTypes,
      breedingYears,
      facilities,
      certifications,
      // 管理员信息
      adminRealName,
      adminPhone,
      adminEmail,
      adminIdCard,
      adminPosition
    } = req.body;

    // 验证必填字段
    if (!wechatUserId || !farmName || !legalRepresentative || !contactPhone || 
        !province || !city || !detailedAddress || !farmScale || !adminRealName || !adminPhone) {
      return res.status(400).json({
        success: false,
        message: '请填写所有必填信息'
      });
    }

    // 生成租户代码
    const tenantCode = 'T' + Date.now().toString(36).toUpperCase();

    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // 1. 创建租户记录
      const [tenantResult] = await connection.query(`
        INSERT INTO tenants (
          tenant_code, farm_name, legal_representative, business_license,
          contact_phone, email, province, city, district, detailed_address,
          farm_scale, scale_description, breed_types, breeding_years,
          facilities, certifications, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
      `, [
        tenantCode, farmName, legalRepresentative, businessLicense,
        contactPhone, email, province, city, district, detailedAddress,
        farmScale, scaleDescription, JSON.stringify(breedTypes), breedingYears,
        JSON.stringify(facilities), JSON.stringify(certifications)
      ]);

      const tenantId = tenantResult.insertId;

      // 2. 创建管理员用户记录
      const userCode = 'U' + Date.now().toString(36).toUpperCase();
      const [userResult] = await connection.query(`
        INSERT INTO users (
          tenant_id, wechat_user_id, user_code, real_name, phone, email,
          id_card, position, role, status, is_farm_admin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'admin', 'pending', TRUE)
      `, [
        tenantId, wechatUserId, userCode, adminRealName, adminPhone,
        adminEmail, adminIdCard, adminPosition
      ]);

      const userId = userResult.insertId;

      // 3. 创建审批记录
      await connection.query(`
        INSERT INTO approval_records (
          application_type, application_id, applicant_type, applicant_id, status
        ) VALUES ('tenant', ?, 'user', ?, 'pending')
      `, [tenantId, userId]);

      await connection.commit();

      res.json({
        success: true,
        message: '注册申请提交成功，请等待平台审核',
        data: {
          applicationId: tenantId,
          tenantCode: tenantCode,
          status: 'pending'
        }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('养殖场注册失败:', error);
    res.status(500).json({
      success: false,
      message: '注册失败，请重试',
      error: error.message
    });
  }
});

/**
 * 查询申请状态
 */
router.get('/application/status/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;

    // 查询租户和用户信息
    const [results] = await db.query(`
      SELECT 
        t.id as tenant_id,
        t.tenant_code,
        t.farm_name,
        t.legal_representative,
        t.contact_phone,
        t.email,
        t.province,
        t.city,
        t.district,
        t.detailed_address,
        t.farm_scale,
        t.scale_description,
        t.breed_types,
        t.breeding_years,
        t.facilities,
        t.certifications,
        t.status as tenant_status,
        t.rejection_reason,
        t.approved_at,
        u.id as user_id,
        u.real_name,
        u.phone as user_phone,
        u.email as user_email,
        u.position,
        u.status as user_status,
        ar.comment as review_comment,
        ar.approved_at as review_time
      FROM tenants t
      LEFT JOIN users u ON t.id = u.tenant_id AND u.is_farm_admin = TRUE
      LEFT JOIN approval_records ar ON t.id = ar.application_id AND ar.application_type = 'tenant'
      WHERE t.id = ?
    `, [applicationId]);

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: '申请记录不存在'
      });
    }

    const result = results[0];

    res.json({
      success: true,
      data: {
        status: result.tenant_status,
        farm: {
          name: result.farm_name,
          legalRepresentative: result.legal_representative,
          contactPhone: result.contact_phone,
          email: result.email,
          address: `${result.province}${result.city}${result.district}${result.detailed_address}`,
          scale: result.farm_scale,
          scaleDescription: result.scale_description,
          breedTypes: JSON.parse(result.breed_types || '[]'),
          breedingYears: result.breeding_years,
          facilities: JSON.parse(result.facilities || '{}'),
          certifications: JSON.parse(result.certifications || '[]')
        },
        user: {
          realName: result.real_name,
          phone: result.user_phone,
          email: result.user_email,
          position: result.position
        },
        reviewComment: result.review_comment,
        reviewTime: result.review_time,
        submissionTime: result.created_at
      }
    });

  } catch (error) {
    console.error('查询申请状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询失败，请重试',
      error: error.message
    });
  }
});

/**
 * 获取用户信息
 */
router.get('/userinfo', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取用户详细信息
    const [users] = await db.query(`
      SELECT u.*, t.farm_name, t.tenant_code, wu.nickname, wu.avatar_url
      FROM users u
      LEFT JOIN tenants t ON u.tenant_id = t.id
      LEFT JOIN wechat_users wu ON u.wechat_user_id = wu.id
      WHERE u.id = ?
    `, [userId]);

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    res.json({
      success: true,
      data: {
        id: user.id,
        name: user.real_name || user.nickname,
        farmName: user.farm_name,
        role: user.role,
        avatar: user.avatar_url || '/images/default_avatar.png',
        phone: user.phone,
        email: user.email,
        department: user.department,
        position: user.position,
        tenantCode: user.tenant_code,
        inventoryCount: 1250,  // 模拟数据
        healthRate: 95,        // 模拟数据
        environmentStatus: '优' // 模拟数据
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
});

/**
 * 获取用户权限信息
 */
router.get('/user/permissions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const role = req.user.role;

    // 获取角色权限配置
    const [permissions] = await db.query(`
      SELECT module, action, 
        CASE 
          WHEN ? = 'admin' THEN role_admin
          WHEN ? = 'manager' THEN role_manager  
          WHEN ? = 'finance' THEN role_finance
          WHEN ? = 'employee' THEN role_employee
          ELSE FALSE
        END as has_permission
      FROM permission_configs
    `, [role, role, role, role]);

    // 构建权限矩阵
    const permissionMatrix = {};
    permissions.forEach(p => {
      if (!permissionMatrix[p.module]) {
        permissionMatrix[p.module] = {};
      }
      permissionMatrix[p.module][p.action] = !!p.has_permission;
    });

    // 获取用户详细信息
    const [users] = await db.query(`
      SELECT u.*, t.farm_name, t.tenant_code
      FROM users u
      LEFT JOIN tenants t ON u.tenant_id = t.id
      WHERE u.id = ?
    `, [userId]);

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.real_name,
          role: user.role,
          department: user.department,
          position: user.position,
          farmName: user.farm_name,
          tenantCode: user.tenant_code
        },
        permissions: permissionMatrix
      }
    });

  } catch (error) {
    console.error('获取权限信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取权限失败',
      error: error.message
    });
  }
});

/**
 * 检查页面访问权限
 */
router.post('/permission/check', authenticateToken, async (req, res) => {
  try {
    const { module, action } = req.body;
    const role = req.user.role;

    const [results] = await db.query(`
      SELECT 
        CASE 
          WHEN ? = 'admin' THEN role_admin
          WHEN ? = 'manager' THEN role_manager  
          WHEN ? = 'finance' THEN role_finance
          WHEN ? = 'employee' THEN role_employee
          ELSE FALSE
        END as has_permission
      FROM permission_configs
      WHERE module = ? AND action = ?
    `, [role, role, role, role, module, action]);

    const hasPermission = results.length > 0 ? !!results[0].has_permission : false;

    res.json({
      success: true,
      data: {
        hasPermission,
        role,
        module,
        action
      }
    });

  } catch (error) {
    console.error('权限检查失败:', error);
    res.status(500).json({
      success: false,
      message: '权限检查失败',
      error: error.message
    });
  }
});

/**
 * 刷新token
 */
router.post('/token/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: '缺少刷新令牌'
      });
    }

    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    // 查询用户信息
    const [users] = await db.query(`
      SELECT u.*, t.tenant_code 
      FROM users u 
      LEFT JOIN tenants t ON u.tenant_id = t.id 
      WHERE u.id = ? AND u.status = 'approved'
    `, [decoded.userId]);

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用'
      });
    }

    const user = users[0];

    // 生成新的访问令牌
    const tokenPayload = {
      userId: user.id,
      tenantId: user.tenant_id,
      role: user.role
    };

    const newAccessToken = jwt.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: '7d' });

    res.json({
      success: true,
      data: {
        accessToken: newAccessToken
      }
    });

  } catch (error) {
    console.error('刷新token失败:', error);
    res.status(401).json({
      success: false,
      message: 'Token无效或已过期'
    });
  }
});

module.exports = router;