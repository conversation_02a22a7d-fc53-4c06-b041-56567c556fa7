/**
 * 企业级用户管理系统 JavaScript
 * 功能：用户列表、搜索、筛选、排序、分页、CRUD操作
 */

class UserManagement {
  constructor() {
    this.currentPage = 1;
    this.pageSize = 10;
    this.sortBy = 'createdAt';
    this.sortOrder = 'DESC';
    this.filters = {
      search: '',
      role: '',
      status: '',
      dateRange: ''
    };
    this.selectedUsers = new Set();
    this.users = [];
    this.totalCount = 0;

    this.init();
  }

  init() {
    this.bindEvents();
    this.loadUsers();
    this.loadStats();
  }

  bindEvents() {
    // 搜索
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.filters.search = e.target.value;
          this.currentPage = 1;
          this.loadUsers();
        }, 300);
      });
    }

    // 筛选器
    document.getElementById('roleFilter')?.addEventListener('change', (e) => {
      this.filters.role = e.target.value;
      this.currentPage = 1;
      this.loadUsers();
    });

    document.getElementById('statusFilter')?.addEventListener('change', (e) => {
      this.filters.status = e.target.value;
      this.currentPage = 1;
      this.loadUsers();
    });

    document.getElementById('dateFilter')?.addEventListener('change', (e) => {
      this.filters.dateRange = e.target.value;
      this.currentPage = 1;
      this.loadUsers();
    });

    // 分页大小
    document
      .getElementById('pageSizeSelect')
      ?.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadUsers();
      });

    // 全选
    document.getElementById('selectAll')?.addEventListener('change', (e) => {
      this.toggleSelectAll(e.target.checked);
    });

    // 排序
    document.querySelectorAll('.sortable').forEach((th) => {
      th.addEventListener('click', () => {
        const sortField = th.dataset.sort;
        if (this.sortBy === sortField) {
          this.sortOrder = this.sortOrder === 'ASC' ? 'DESC' : 'ASC';
        } else {
          this.sortBy = sortField;
          this.sortOrder = 'DESC';
        }
        this.updateSortIcons();
        this.loadUsers();
      });
    });
  }

  async loadUsers() {
    try {
      this.showLoading();

      const params = new URLSearchParams({
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder,
        ...this.filters
      });

      const response = await fetch(`/api/users?${params}`);
      const result = await response.json();

      if (result.success) {
        this.users = result.data.items;
        this.totalCount = result.data.pagination.total;
        this.renderUsers();
        this.renderPagination(result.data.pagination);
        this.updatePaginationInfo(result.data.pagination);
      } else {
        this.showError(result.message || '加载用户数据失败');
      }
    } catch (error) {
      console.error('加载用户失败:', error);
      this.showError('网络错误，请稍后重试');
    } finally {
      this.hideLoading();
    }
  }

  async loadStats() {
    try {
      const response = await fetch('/api/users/stats');
      const result = await response.json();

      if (result.success) {
        this.updateStats(result.data);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  }

  renderUsers() {
    const tbody = document.getElementById('usersTableBody');
    if (!tbody) return;

    if (this.users.length === 0) {
      this.showEmpty();
      return;
    }

    tbody.innerHTML = this.users
      .map(
        (user) => `
            <tr data-user-id="${user.id}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input user-checkbox" type="checkbox" 
                               value="${user.id}" onchange="userManager.toggleUserSelection(${user.id}, this.checked)">
                    </div>
                </td>
                <td>
                    <div class="user-avatar-container">
                        ${
  user.avatar
    ? `<img src="${user.avatar}" class="user-avatar" alt="${user.username}">`
    : `<div class="user-avatar-placeholder">${user.username.charAt(0).toUpperCase()}</div>`
}
                    </div>
                </td>
                <td>
                    <div class="user-info">
                        <div class="user-name">${user.username}</div>
                        <div class="user-id">#${user.id}</div>
                    </div>
                </td>
                <td>
                    <div class="user-email">${user.email || '-'}</div>
                </td>
                <td>
                    <div class="user-real-name">${user.name || '-'}</div>
                </td>
                <td>
                    <div class="user-phone">${user.phone || '-'}</div>
                </td>
                <td>
                    <div class="user-farm">${user.farmName || '-'}</div>
                </td>
                <td>
                    <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                </td>
                <td>
                    <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                </td>
                <td>
                    <div class="user-last-login">
                        ${user.lastLoginAt ? this.formatDateTime(user.lastLoginAt) : '从未登录'}
                    </div>
                </td>
                <td>
                    <div class="user-created-at">${this.formatDateTime(user.createdAt)}</div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-outline-primary btn-action" 
                                onclick="userManager.viewUser(${user.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success btn-action" 
                                onclick="userManager.editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                onclick="userManager.deleteUser(${user.id}, '${user.username}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `
      )
      .join('');

    this.hideEmpty();
    this.hideError();
  }

  renderPagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    if (!paginationEl) return;

    const { page, pages, hasNext, hasPrev } = pagination;
    let html = '';

    // 上一页
    html += `
            <li class="page-item ${!hasPrev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${page - 1})" ${!hasPrev ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);

    if (startPage > 1) {
      html += '<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(1)">1</a></li>';
      if (startPage > 2) {
        html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManager.goToPage(${i})">${i}</a>
                </li>
            `;
    }

    if (endPage < pages) {
      if (endPage < pages - 1) {
        html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
      }
      html += `<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(${pages})">${pages}</a></li>`;
    }

    // 下一页
    html += `
            <li class="page-item ${!hasNext ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${page + 1})" ${!hasNext ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

    paginationEl.innerHTML = html;
  }

  updatePaginationInfo(pagination) {
    const infoEl = document.getElementById('paginationInfo');
    if (!infoEl) return;

    const { page, limit, total } = pagination;
    const start = (page - 1) * limit + 1;
    const end = Math.min(page * limit, total);

    infoEl.textContent = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
  }

  updateStats(stats) {
    document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
    document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
    document.getElementById('adminUsers').textContent = stats.adminUsers || 0;
    document.getElementById('onlineUsers').textContent = stats.onlineUsers || 0;
  }

  showLoading() {
    document
      .getElementById('loadingState')
      ?.style.setProperty('display', 'flex');
    document.getElementById('emptyState')?.style.setProperty('display', 'none');
    document.getElementById('errorState')?.style.setProperty('display', 'none');
  }

  hideLoading() {
    document
      .getElementById('loadingState')
      ?.style.setProperty('display', 'none');
  }

  showEmpty() {
    document.getElementById('emptyState')?.style.setProperty('display', 'flex');
    document
      .getElementById('loadingState')
      ?.style.setProperty('display', 'none');
    document.getElementById('errorState')?.style.setProperty('display', 'none');
    document.getElementById('usersTableBody').innerHTML = '';
  }

  hideEmpty() {
    document.getElementById('emptyState')?.style.setProperty('display', 'none');
  }

  showError(message) {
    const errorState = document.getElementById('errorState');
    const errorMessage = document.getElementById('errorMessage');
    if (errorState && errorMessage) {
      errorMessage.textContent = message;
      errorState.style.setProperty('display', 'flex');
      document
        .getElementById('loadingState')
        ?.style.setProperty('display', 'none');
      document
        .getElementById('emptyState')
        ?.style.setProperty('display', 'none');
    }
  }

  hideError() {
    document.getElementById('errorState')?.style.setProperty('display', 'none');
  }

  goToPage(page) {
    if (page < 1) return;
    this.currentPage = page;
    this.loadUsers();
  }

  getRoleText(role) {
    const roleMap = {
      admin: '管理员',
      manager: '经理',
      user: '普通用户'
    };
    return roleMap[role] || role;
  }

  getStatusText(status) {
    const statusMap = {
      active: '活跃',
      inactive: '非活跃',
      suspended: '已暂停'
    };
    return statusMap[status] || status;
  }

  formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  updateSortIcons() {
    document.querySelectorAll('.sortable').forEach((th) => {
      const icon = th.querySelector('.sort-icon');
      if (th.dataset.sort === this.sortBy) {
        th.classList.add('active');
        icon.className =
          this.sortOrder === 'ASC'
            ? 'bi bi-arrow-up sort-icon'
            : 'bi bi-arrow-down sort-icon';
      } else {
        th.classList.remove('active');
        icon.className = 'bi bi-arrow-down-up sort-icon';
      }
    });
  }

  toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach((checkbox) => {
      checkbox.checked = checked;
      this.toggleUserSelection(parseInt(checkbox.value), checked);
    });
  }

  toggleUserSelection(userId, selected) {
    if (selected) {
      this.selectedUsers.add(userId);
    } else {
      this.selectedUsers.delete(userId);
    }
    this.updateBatchActions();
  }

  updateBatchActions() {
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');

    if (this.selectedUsers.size > 0) {
      batchActions.style.display = 'flex';
      selectedCount.textContent = this.selectedUsers.size;
    } else {
      batchActions.style.display = 'none';
    }
  }

  clearSelection() {
    this.selectedUsers.clear();
    document.querySelectorAll('.user-checkbox').forEach((checkbox) => {
      checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    this.updateBatchActions();
  }
}

// 全局实例
let userManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
  userManager = new UserManagement();
});

// 辅助函数
function showToast(message, type = 'info') {
  // 创建Toast元素
  const toastContainer = document.getElementById('toastContainer') || createToastContainer();

  const toastId = 'toast-' + Date.now();
  const toastHtml = `
    <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0"
         id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
  toast.show();

  // 自动移除
  toastElement.addEventListener('hidden.bs.toast', () => {
    toastElement.remove();
  });
}

function createToastContainer() {
  const container = document.createElement('div');
  container.id = 'toastContainer';
  container.className = 'toast-container position-fixed top-0 end-0 p-3';
  container.style.zIndex = '9999';
  document.body.appendChild(container);
  return container;
}

function togglePassword() {
  const passwordInput = document.getElementById('password');
  const toggleIcon = document.getElementById('passwordToggleIcon');

  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    toggleIcon.className = 'bi bi-eye-slash';
  } else {
    passwordInput.type = 'password';
    toggleIcon.className = 'bi bi-eye';
  }
}

// 全局函数
function showCreateUserModal() {
  // 重置表单
  document.getElementById('userForm').reset();
  document.getElementById('userId').value = '';
  document.getElementById('userModalTitle').innerHTML = '<i class="bi bi-person-plus"></i> 新增用户';
  document.getElementById('passwordRequired').style.display = 'inline';
  document.getElementById('password').required = true;

  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('userModal'));
  modal.show();
}

function resetFilters() {
  document.getElementById('searchInput').value = '';
  document.getElementById('roleFilter').value = '';
  document.getElementById('statusFilter').value = '';
  document.getElementById('dateFilter').value = '';

  userManager.filters = {
    search: '',
    role: '',
    status: '',
    dateRange: ''
  };
  userManager.currentPage = 1;
  userManager.loadUsers();
}

function saveUser() {
  const form = document.getElementById('userForm');
  const formData = new FormData(form);
  const userId = document.getElementById('userId').value;
  const isEdit = userId !== '';

  // 表单验证
  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return;
  }

  // 显示加载状态
  const saveBtn = document.querySelector('#userModal .btn-primary');
  const btnLoading = saveBtn.querySelector('.btn-loading');
  const btnText = saveBtn.querySelector('.btn-text');

  btnLoading.style.display = 'inline-block';
  btnText.style.display = 'none';
  saveBtn.disabled = true;

  // 准备数据
  const userData = Object.fromEntries(formData);

  // API调用
  const url = isEdit ? `/api/users/${userId}` : '/api/users';
  const method = isEdit ? 'PUT' : 'POST';

  fetch(url, {
    method: method,
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(userData)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // 成功提示
      showToast(isEdit ? '用户更新成功' : '用户创建成功', 'success');

      // 关闭模态框
      const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
      modal.hide();

      // 重新加载用户列表
      userManager.loadUsers();
    } else {
      showToast(data.message || '操作失败', 'error');
    }
  })
  .catch(error => {
    console.error('保存用户失败:', error);
    showToast('网络错误，请稍后重试', 'error');
  })
  .finally(() => {
    // 恢复按钮状态
    btnLoading.style.display = 'none';
    btnText.style.display = 'inline';
    saveBtn.disabled = false;
  });
}

function exportUsers() {
  // 实现导出用户功能
  const selectedUsers = userManager.getSelectedUsers();

  if (selectedUsers.length === 0) {
    alert('请选择要导出的用户');
    return;
  }

  // 显示导出格式选择
  const format = prompt(
    '请选择导出格式:\n1. Excel (.xlsx)\n2. CSV (.csv)\n3. JSON (.json)\n\n输入格式编号 (1-3):',
    '1'
  );

  if (!format || !['1', '2', '3'].includes(format)) {
    return;
  }

  const formatMap = { 1: 'excel', 2: 'csv', 3: 'json' };
  const selectedFormat = formatMap[format];

  // 调用导出API
  const userIds = selectedUsers.map((user) => user.id).join(',');
  const exportUrl = `/api/users/export?format=${selectedFormat}&userIds=${userIds}`;

  // 创建下载链接
  const link = document.createElement('a');
  link.href = exportUrl;
  link.download = `users_export_${new Date().toISOString().split('T')[0]}.${selectedFormat === 'excel' ? 'xlsx' : selectedFormat}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

}

// 用户相关的其他功能
userManager.viewUser = function (userId) {
  // 实现查看用户详情
  fetch(`/api/users/${userId}`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        this.showUserDetailModal(data.data);
      } else {
        alert('获取用户详情失败: ' + data.message);
      }
    })
    .catch((error) => {
      console.error('获取用户详情失败:', error);
      alert('获取用户详情失败，请稍后重试');
    });
};

userManager.editUser = function (userId) {
  // 实现编辑用户
  fetch(`/api/users/${userId}`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        this.showUserEditModal(data.data);
      } else {
        alert('获取用户信息失败: ' + data.message);
      }
    })
    .catch((error) => {
      console.error('获取用户信息失败:', error);
      alert('获取用户信息失败，请稍后重试');
    });
};

userManager.deleteUser = function (userId, username) {
  if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
    fetch(`/api/users/${userId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert('用户删除成功');
          this.loadUsers(); // 重新加载用户列表
        } else {
          alert('删除用户失败: ' + data.message);
        }
      })
      .catch((error) => {
        console.error('删除用户失败:', error);
        alert('删除用户失败，请稍后重试');
      });
  }
};

// 获取选中的用户
userManager.getSelectedUsers = function () {
  const checkboxes = document.querySelectorAll(
    'input[name="userSelect"]:checked'
  );
  const selectedUsers = [];

  checkboxes.forEach((checkbox) => {
    const userId = checkbox.value;
    const userRow = checkbox.closest('tr');
    const username = userRow.querySelector('td:nth-child(2)').textContent;
    const email = userRow.querySelector('td:nth-child(3)').textContent;
    const role = userRow.querySelector('td:nth-child(4)').textContent;

    selectedUsers.push({
      id: userId,
      username: username,
      email: email,
      role: role
    });
  });

  return selectedUsers;
};

// 显示用户详情模态框
userManager.showUserDetailModal = function (user) {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户详情 - ${user.username}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <p><strong>用户ID:</strong> ${user.id}</p>
                            <p><strong>用户名:</strong> ${user.username}</p>
                            <p><strong>邮箱:</strong> ${user.email}</p>
                            <p><strong>手机号:</strong> ${user.phone || '未设置'}</p>
                            <p><strong>角色:</strong> ${user.role}</p>
                            <p><strong>状态:</strong> <span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-danger'}">${user.status === 'active' ? '正常' : '禁用'}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>统计信息</h6>
                            <p><strong>注册时间:</strong> ${new Date(user.created_at).toLocaleDateString()}</p>
                            <p><strong>最后登录:</strong> ${user.last_login ? new Date(user.last_login).toLocaleDateString() : '从未登录'}</p>
                            <p><strong>登录次数:</strong> ${user.login_count || 0}</p>
                            <p><strong>鹅群数量:</strong> ${user.flock_count || 0}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="userManager.editUser(${user.id})">编辑用户</button>
                </div>
            </div>
        </div>
    `;

  document.body.appendChild(modal);
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // 模态框关闭后移除DOM元素
  modal.addEventListener('hidden.bs.modal', () => {
    document.body.removeChild(modal);
  });
};

// 显示用户编辑模态框
userManager.showUserEditModal = function (user) {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="editUserForm">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑用户 - ${user.username}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username" value="${user.username}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" name="email" value="${user.email}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-control" name="phone" value="${user.phone || ''}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-control" name="role" required>
                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>普通用户</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
                                <option value="superadmin" ${user.role === 'superadmin' ? 'selected' : ''}>超级管理员</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <select class="form-control" name="status" required>
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>正常</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存更改</button>
                    </div>
                </form>
            </div>
        </div>
    `;

  document.body.appendChild(modal);
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // 处理表单提交
  document.getElementById('editUserForm').addEventListener('submit', (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const userData = Object.fromEntries(formData);

    fetch(`/api/users/${user.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert('用户信息更新成功');
          bsModal.hide();
          userManager.loadUsers(); // 重新加载用户列表
        } else {
          alert('更新用户信息失败: ' + data.message);
        }
      })
      .catch((error) => {
        console.error('更新用户信息失败:', error);
        alert('更新用户信息失败，请稍后重试');
      });
  });

  // 模态框关闭后移除DOM元素
  modal.addEventListener('hidden.bs.modal', () => {
    document.body.removeChild(modal);
  });
};
