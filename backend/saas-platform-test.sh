#!/bin/bash

# 智慧养鹅SAAS平台全面测试脚本
# Smart Goose SAAS Platform Comprehensive Test Script

echo "🧪 开始智慧养鹅SAAS平台全面测试..."
echo "=================================================="

# 测试配置
BACKEND_URL="http://localhost:3000"
ADMIN_URL="http://localhost:4000"

echo "📡 1. 测试服务器连通性"
echo "----------------------------------------"

# 测试主后端API服务器
echo -n "主后端API (port 3000): "
if curl -s --max-time 5 "$BACKEND_URL/api/health" >/dev/null; then
    echo "✅ 正常"
else
    echo "❌ 失败"
    exit 1
fi

# 测试SAAS管理后台
echo -n "SAAS管理后台 (port 4000): "
if curl -s --max-time 5 -I "$ADMIN_URL" >/dev/null; then
    echo "✅ 正常"
else
    echo "❌ 失败"
    exit 1
fi

echo ""
echo "🗄️ 2. 测试数据库连接和SAAS表结构"
echo "----------------------------------------"

# 测试数据库连接
echo -n "数据库连接: "
DB_TEST=$(mysql -h localhost -u root smart_goose -e "SELECT COUNT(*) as count FROM users;" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 正常"
    echo "$DB_TEST" | tail -1 | sed 's/^/   用户总数: /'
else
    echo "❌ 失败"
    exit 1
fi

# 检查SAAS平台表
echo "检查SAAS平台表结构:"
mysql -h localhost -u root smart_goose -e "
    SELECT 'SAAS平台表' as 类型, 
           CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'MISSING' END as 状态
    FROM information_schema.tables 
    WHERE table_schema = 'smart_goose' 
    AND table_name IN ('tenants', 'goose_prices', 'mall_products', 'mall_orders', 'platform_admins')
    UNION ALL
    SELECT '租户表', CASE WHEN COUNT(*) > 0 THEN '✅' ELSE '❌' END FROM tenants
    UNION ALL  
    SELECT '今日鹅价表', CASE WHEN COUNT(*) > 0 THEN '✅' ELSE '❌' END FROM goose_prices
    UNION ALL
    SELECT '商城商品表', CASE WHEN COUNT(*) > 0 THEN '✅' ELSE '❌' END FROM mall_products
    UNION ALL
    SELECT '订单表', CASE WHEN COUNT(*) > 0 THEN '✅' ELSE '❌' END FROM mall_orders;
" 2>/dev/null

echo ""
echo "📊 3. 测试SAAS平台数据完整性"
echo "----------------------------------------"

# 检查各表数据量
echo "SAAS平台数据统计:"
mysql -h localhost -u root smart_goose -e "
    SELECT '租户' as 类型, COUNT(*) as 数量, 
           SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as 活跃数量
    FROM tenants
    UNION ALL
    SELECT '平台用户', COUNT(*), 
           SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END)
    FROM users
    UNION ALL
    SELECT '鹅群', COUNT(*), 
           SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END)
    FROM flocks
    UNION ALL
    SELECT '商城商品', COUNT(*), 
           SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END)
    FROM mall_products
    UNION ALL
    SELECT '订单', COUNT(*), 
           SUM(CASE WHEN order_status != 'cancelled' THEN 1 ELSE 0 END)
    FROM mall_orders
    UNION ALL
    SELECT '今日鹅价', COUNT(*), 
           SUM(CASE WHEN is_published = 1 THEN 1 ELSE 0 END)
    FROM goose_prices;
" 2>/dev/null

echo ""
echo "🔐 4. 测试SAAS管理后台登录和认证"
echo "----------------------------------------"

# 测试登录API
echo -n "管理后台登录: "
LOGIN_RESPONSE=$(curl -s -X POST "$ADMIN_URL/auth/login" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=admin123" \
    -c /tmp/saas_cookies.txt)

if echo "$LOGIN_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 正常"
else
    echo "❌ 失败"
    echo "$LOGIN_RESPONSE"
fi

echo ""
echo "🎯 5. 测试SAAS管理后台核心功能"
echo "----------------------------------------"

# 测试平台仪表盘
echo -n "平台仪表盘: "
if curl -s "$ADMIN_URL/dashboard" -b /tmp/saas_cookies.txt | grep -q "平台仪表盘"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

# 测试租户管理
echo -n "租户管理页面: "
if curl -s "$ADMIN_URL/tenants" -b /tmp/saas_cookies.txt | grep -q "租户管理"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

# 测试今日鹅价管理
echo -n "今日鹅价管理: "
if curl -s "$ADMIN_URL/goose-prices" -b /tmp/saas_cookies.txt | grep -q "今日鹅价"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

# 测试商城管理
echo -n "商城商品管理: "
if curl -s "$ADMIN_URL/mall/products" -b /tmp/saas_cookies.txt | grep -q "商品管理"; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

echo ""
echo "📈 6. 测试租户业务数据管理"
echo "----------------------------------------"

# 显示租户及其业务数据统计
echo "租户业务数据关联:"
mysql -h localhost -u root smart_goose -e "
    SELECT 
        t.tenant_name as '租户名称',
        t.tenant_code as '租户代码',
        t.subscription_plan as '订阅计划',
        COUNT(DISTINCT u.id) as '用户数',
        COUNT(DISTINCT f.id) as '鹅群数',
        COUNT(DISTINCT p.id) as '生产记录',
        COUNT(DISTINCT h.id) as '健康记录'
    FROM tenants t
    LEFT JOIN users u ON t.id = u.tenant_id
    LEFT JOIN flocks f ON t.id = f.tenant_id  
    LEFT JOIN production_records p ON t.id = p.tenant_id
    LEFT JOIN health_records h ON t.id = h.tenant_id
    GROUP BY t.id, t.tenant_name, t.tenant_code, t.subscription_plan
    ORDER BY t.created_at DESC;
" 2>/dev/null

echo ""
echo "🛒 7. 测试商城功能"
echo "----------------------------------------"

# 显示商城数据统计
echo "商城数据统计:"
mysql -h localhost -u root smart_goose -e "
    SELECT 
        '商品分类' as 项目,
        COUNT(*) as 数量,
        '个' as 单位
    FROM mall_categories
    UNION ALL
    SELECT '商品总数', COUNT(*), '个'
    FROM mall_products  
    UNION ALL
    SELECT '在售商品', COUNT(*), '个'
    FROM mall_products WHERE status = 'active'
    UNION ALL
    SELECT '订单总数', COUNT(*), '个'
    FROM mall_orders
    UNION ALL
    SELECT '本月订单', COUNT(*), '个'
    FROM mall_orders WHERE MONTH(created_at) = MONTH(CURDATE())
    UNION ALL
    SELECT '本月收入', COALESCE(SUM(final_amount), 0), '元'
    FROM mall_orders WHERE order_status != 'cancelled' AND MONTH(created_at) = MONTH(CURDATE());
" 2>/dev/null

echo ""
echo "💰 8. 测试今日鹅价功能"
echo "----------------------------------------"

# 显示鹅价统计
echo "今日鹅价统计:"
mysql -h localhost -u root smart_goose -e "
    SELECT 
        region as '地区',
        breed as '品种',
        price_type as '价格类型',
        avg_price as '平均价格',
        market_trend as '趋势',
        CASE WHEN is_published = 1 THEN '已发布' ELSE '未发布' END as '状态'
    FROM goose_prices 
    WHERE date = CURDATE()
    ORDER BY region, breed;
" 2>/dev/null

echo ""
echo "🔌 9. 测试API端点"
echo "----------------------------------------"

# 测试健康检查API
echo -n "健康检查API: "
HEALTH_RESPONSE=$(curl -s "$BACKEND_URL/api/health")
if echo "$HEALTH_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 正常"
else
    echo "❌ 失败"
fi

# 测试主要API端点
echo "API端点测试:"
API_ENDPOINTS=(
    "/api/health::健康检查"
    "/api/users::用户API"
    "/api/flocks::鹅群API"
    "/api/production::生产记录API"
)

for endpoint_info in "${API_ENDPOINTS[@]}"; do
    IFS='::' read -r endpoint name <<< "$endpoint_info"
    echo -n "  $name: "
    if curl -s --max-time 5 "$BACKEND_URL$endpoint" >/dev/null; then
        echo "✅ 可访问"
    else
        echo "❌ 不可访问"
    fi
done

echo ""
echo "🎉 SAAS平台测试完成!"
echo "=================================================="

echo ""
echo "📋 SAAS平台访问信息:"
echo "• 主后端API: http://localhost:3000"
echo "• SAAS管理后台: http://localhost:4000"
echo "• 默认管理员账号: admin / admin123"
echo ""
echo "📊 SAAS平台架构特点:"
echo "• ✅ 多租户架构 - 支持不同养殖场独立管理"
echo "• ✅ 平台级管理 - 租户、商城、鹅价统一管理"
echo "• ✅ 业务数据隔离 - 每个租户的数据完全隔离"
echo "• ✅ 订阅计划管理 - 支持不同等级的服务"
echo "• ✅ 商城功能完整 - 商品、订单、库存管理"
echo "• ✅ 价格信息服务 - 今日鹅价发布管理"
echo ""
echo "✨ SAAS平台已就绪，可以进行完整功能测试和演示！"

# 清理临时文件
rm -f /tmp/saas_cookies.txt