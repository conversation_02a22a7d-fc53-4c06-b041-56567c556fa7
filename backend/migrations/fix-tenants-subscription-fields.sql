-- 修复租户表订阅字段
-- 确保tenants表有正确的订阅相关字段

USE `goose_saas_platform`;

-- 检查并添加缺失的字段
-- 如果字段不存在则添加

-- 添加订阅开始日期字段
ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS subscription_start DATE NULL COMMENT '订阅开始日期' AFTER subscription_plan;

-- 添加订阅结束日期字段
ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS subscription_end DATE NULL COMMENT '订阅结束日期' AFTER subscription_start;

-- 如果存在旧的字段名，进行数据迁移
-- 检查是否存在 subscription_start_date 字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'tenants' 
AND COLUMN_NAME = 'subscription_start_date';

-- 如果存在旧字段，迁移数据并删除旧字段
SET @sql = IF(@col_exists > 0, 
    'UPDATE tenants SET subscription_start = subscription_start_date WHERE subscription_start_date IS NOT NULL; ALTER TABLE tenants DROP COLUMN subscription_start_date;',
    'SELECT "subscription_start_date field does not exist" as message;'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否存在 subscription_end_date 字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'tenants' 
AND COLUMN_NAME = 'subscription_end_date';

-- 如果存在旧字段，迁移数据并删除旧字段
SET @sql = IF(@col_exists > 0, 
    'UPDATE tenants SET subscription_end = subscription_end_date WHERE subscription_end_date IS NOT NULL; ALTER TABLE tenants DROP COLUMN subscription_end_date;',
    'SELECT "subscription_end_date field does not exist" as message;'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他可能缺失的字段
ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS max_users INT DEFAULT 10 COMMENT '最大用户数' AFTER subscription_end;

ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS max_flocks INT DEFAULT 10 COMMENT '最大鹅群数' AFTER max_users;

ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS max_storage_gb INT DEFAULT 5 COMMENT '最大存储空间(GB)' AFTER max_flocks;

ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS max_api_calls_per_month INT DEFAULT 10000 COMMENT '每月API调用限制' AFTER max_storage_gb;

ALTER TABLE tenants 
ADD COLUMN IF NOT EXISTS last_active_at TIMESTAMP NULL COMMENT '最后活跃时间' AFTER max_api_calls_per_month;

-- 添加索引
ALTER TABLE tenants 
ADD INDEX IF NOT EXISTS idx_subscription_end (subscription_end);

ALTER TABLE tenants 
ADD INDEX IF NOT EXISTS idx_subscription_plan (subscription_plan);

ALTER TABLE tenants 
ADD INDEX IF NOT EXISTS idx_status (status);

-- 插入一些示例数据（如果表为空）
INSERT IGNORE INTO tenants (
    tenant_code, 
    company_name, 
    contact_name, 
    contact_phone, 
    contact_email,
    subscription_plan, 
    subscription_start, 
    subscription_end, 
    status,
    max_users,
    max_flocks,
    created_at,
    updated_at
) VALUES 
('DEMO001', '示例养殖场A', '张三', '13800138001', '<EMAIL>', 'standard', '2024-01-01', '2024-12-31', 'active', 20, 50, NOW(), NOW()),
('DEMO002', '绿色养殖场', '李四', '13800138002', '<EMAIL>', 'premium', '2024-02-01', '2024-11-30', 'active', 50, 100, NOW(), NOW()),
('DEMO003', '智慧农业科技', '王五', '13800138003', '<EMAIL>', 'enterprise', '2023-12-01', '2024-01-15', 'active', 100, 200, NOW(), NOW());

-- 显示表结构确认
DESCRIBE tenants;

-- 显示数据确认
SELECT 
    id,
    tenant_code,
    company_name,
    subscription_plan,
    subscription_start,
    subscription_end,
    status,
    DATEDIFF(subscription_end, CURDATE()) as days_remaining,
    CASE
        WHEN subscription_end IS NULL THEN 'no_subscription'
        WHEN subscription_end < CURDATE() THEN 'expired'
        WHEN DATEDIFF(subscription_end, CURDATE()) <= 30 THEN 'expiring'
        ELSE 'active'
    END as subscription_status
FROM tenants 
ORDER BY subscription_end ASC;
