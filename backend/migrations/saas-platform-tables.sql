-- SAAS平台架构数据库设计
-- 创建时间: 2024-08-26
-- 描述: 重构为真正的SAAS平台架构，分离平台管理和租户管理

-- 1. 租户表（养殖场/企业）
CREATE TABLE IF NOT EXISTS tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户代码',
    tenant_name VARCHAR(200) NOT NULL COMMENT '租户名称（养殖场/企业名称）',
    tenant_type ENUM('individual', 'enterprise', 'cooperative') DEFAULT 'individual' COMMENT '租户类型',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '地址',
    business_license VARCHAR(100) COMMENT '营业执照号',
    tax_number VARCHAR(100) COMMENT '税号',
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active' COMMENT '状态',
    subscription_plan ENUM('basic', 'standard', 'premium', 'enterprise') DEFAULT 'basic' COMMENT '订阅计划',
    subscription_start DATE COMMENT '订阅开始日期',
    subscription_end DATE COMMENT '订阅结束日期',
    max_users INT DEFAULT 5 COMMENT '最大用户数',
    max_flocks INT DEFAULT 10 COMMENT '最大鹅群数',
    features JSON COMMENT '可用功能列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT COMMENT '创建者（平台管理员）',
    INDEX idx_tenant_code (tenant_code),
    INDEX idx_status (status),
    INDEX idx_subscription_end (subscription_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- 2. 今日鹅价管理表
CREATE TABLE IF NOT EXISTS goose_prices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL COMMENT '日期',
    region VARCHAR(100) NOT NULL COMMENT '地区',
    breed VARCHAR(100) COMMENT '品种',
    price_type ENUM('live_goose', 'processed', 'egg', 'feather', 'down') NOT NULL COMMENT '价格类型',
    unit VARCHAR(20) DEFAULT 'kg' COMMENT '单位',
    min_price DECIMAL(10,2) COMMENT '最低价',
    max_price DECIMAL(10,2) COMMENT '最高价',
    avg_price DECIMAL(10,2) NOT NULL COMMENT '平均价',
    market_trend ENUM('up', 'down', 'stable') COMMENT '市场趋势',
    source VARCHAR(200) COMMENT '数据来源',
    notes TEXT COMMENT '备注',
    is_published BOOLEAN DEFAULT false COMMENT '是否发布',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT COMMENT '发布者',
    UNIQUE KEY uk_date_region_breed_type (date, region, breed, price_type),
    INDEX idx_date (date),
    INDEX idx_region (region),
    INDEX idx_published (is_published)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='今日鹅价表';

-- 3. 商城商品表
CREATE TABLE IF NOT EXISTS mall_products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_code VARCHAR(50) UNIQUE NOT NULL COMMENT '商品代码',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id INT COMMENT '分类ID',
    brand VARCHAR(100) COMMENT '品牌',
    description LONGTEXT COMMENT '商品描述',
    specifications JSON COMMENT '规格参数',
    images JSON COMMENT '商品图片数组',
    video_url VARCHAR(500) COMMENT '商品视频',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    cost_price DECIMAL(10,2) COMMENT '成本价',
    market_price DECIMAL(10,2) COMMENT '市场价',
    unit VARCHAR(20) DEFAULT '件' COMMENT '单位',
    min_order_qty INT DEFAULT 1 COMMENT '起订量',
    stock_qty INT DEFAULT 0 COMMENT '库存数量',
    sales_count INT DEFAULT 0 COMMENT '销量',
    weight DECIMAL(8,2) COMMENT '重量(kg)',
    status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active' COMMENT '状态',
    is_featured BOOLEAN DEFAULT false COMMENT '是否推荐',
    sort_order INT DEFAULT 0 COMMENT '排序',
    seo_title VARCHAR(200) COMMENT 'SEO标题',
    seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
    seo_description TEXT COMMENT 'SEO描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    FULLTEXT idx_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城商品表';

-- 4. 商城分类表
CREATE TABLE IF NOT EXISTS mall_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    parent_id INT DEFAULT 0 COMMENT '父分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    image VARCHAR(500) COMMENT '分类图片',
    icon VARCHAR(100) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT true COMMENT '是否启用',
    level TINYINT DEFAULT 1 COMMENT '分类级别',
    path VARCHAR(500) COMMENT '分类路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城分类表';

-- 5. 商城订单表
CREATE TABLE IF NOT EXISTS mall_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL COMMENT '订单号',
    tenant_id INT NOT NULL COMMENT '租户ID',
    user_id INT NOT NULL COMMENT '用户ID',
    contact_name VARCHAR(100) NOT NULL COMMENT '收货人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
    shipping_address TEXT NOT NULL COMMENT '收货地址',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    shipping_fee DECIMAL(10,2) DEFAULT 0 COMMENT '运费',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    payment_method ENUM('wechat', 'alipay', 'bank_transfer', 'cod') COMMENT '支付方式',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    order_status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '订单状态',
    shipping_method VARCHAR(100) COMMENT '配送方式',
    shipping_no VARCHAR(100) COMMENT '物流单号',
    notes TEXT COMMENT '订单备注',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    shipped_at TIMESTAMP NULL COMMENT '发货时间',
    delivered_at TIMESTAMP NULL COMMENT '收货时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_payment_status (payment_status),
    INDEX idx_order_status (order_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城订单表';

-- 6. 订单商品详情表
CREATE TABLE IF NOT EXISTS mall_order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '订单ID',
    product_id INT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(500) COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL COMMENT '数量',
    subtotal DECIMAL(10,2) NOT NULL COMMENT '小计',
    specifications JSON COMMENT '商品规格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES mall_orders(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品详情表';

-- 7. API管理表
CREATE TABLE IF NOT EXISTS api_endpoints (
    id INT PRIMARY KEY AUTO_INCREMENT,
    endpoint_name VARCHAR(100) NOT NULL COMMENT 'API名称',
    endpoint_path VARCHAR(200) NOT NULL COMMENT 'API路径',
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL COMMENT 'HTTP方法',
    description TEXT COMMENT 'API描述',
    is_public BOOLEAN DEFAULT false COMMENT '是否公开API',
    requires_auth BOOLEAN DEFAULT true COMMENT '是否需要认证',
    rate_limit INT DEFAULT 100 COMMENT '请求限制（每小时）',
    status ENUM('active', 'deprecated', 'maintenance') DEFAULT 'active' COMMENT '状态',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_path_method (endpoint_path, method),
    INDEX idx_status (status),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API端点表';

-- 8. API调用统计表
CREATE TABLE IF NOT EXISTS api_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    endpoint_id INT NOT NULL COMMENT 'API端点ID',
    tenant_id INT COMMENT '租户ID',
    date DATE NOT NULL COMMENT '统计日期',
    total_calls INT DEFAULT 0 COMMENT '总调用次数',
    success_calls INT DEFAULT 0 COMMENT '成功调用次数',
    error_calls INT DEFAULT 0 COMMENT '错误调用次数',
    avg_response_time DECIMAL(8,2) COMMENT '平均响应时间(ms)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (endpoint_id) REFERENCES api_endpoints(id) ON DELETE CASCADE,
    UNIQUE KEY uk_endpoint_tenant_date (endpoint_id, tenant_id, date),
    INDEX idx_date (date),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表';

-- 9. 系统配置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL COMMENT '配置分类',
    setting_key VARCHAR(100) NOT NULL COMMENT '配置键',
    setting_value TEXT COMMENT '配置值',
    data_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string' COMMENT '数据类型',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT false COMMENT '是否公开（前端可访问）',
    is_system BOOLEAN DEFAULT false COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_category_key (category, setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 10. 平台管理员表
CREATE TABLE IF NOT EXISTS platform_admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    avatar VARCHAR(500) COMMENT '头像',
    role ENUM('super_admin', 'admin', 'operator', 'viewer') DEFAULT 'admin' COMMENT '角色',
    permissions JSON COMMENT '权限列表',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台管理员表';

-- 修改现有用户表，增加租户关联
ALTER TABLE users ADD COLUMN tenant_id INT COMMENT '所属租户ID' AFTER id;
ALTER TABLE users ADD INDEX idx_tenant_id (tenant_id);

-- 修改现有鹅群表，增加租户关联
ALTER TABLE flocks ADD COLUMN tenant_id INT COMMENT '所属租户ID' AFTER userId;
ALTER TABLE flocks ADD INDEX idx_tenant_id (tenant_id);

-- 修改现有生产记录表，增加租户关联
ALTER TABLE production_records ADD COLUMN tenant_id INT COMMENT '所属租户ID' AFTER userId;
ALTER TABLE production_records ADD INDEX idx_tenant_id (tenant_id);

-- 修改现有健康记录表，增加租户关联
ALTER TABLE health_records ADD COLUMN tenant_id INT COMMENT '所属租户ID' AFTER userId;
ALTER TABLE health_records ADD INDEX idx_tenant_id (tenant_id);

-- 修改现有财务记录表，增加租户关联
ALTER TABLE financial_records ADD COLUMN tenant_id INT COMMENT '所属租户ID' AFTER userId;
ALTER TABLE financial_records ADD INDEX idx_tenant_id (tenant_id);

-- 插入默认商城分类
INSERT INTO mall_categories (name, description, sort_order, level, path) VALUES
('饲料用品', '各类鹅饲料和营养补充剂', 1, 1, '1'),
('养殖设备', '养殖场所需的各种设备器材', 2, 1, '2'),
('医疗用品', '疫苗、药品、医疗器械等', 3, 1, '3'),
('孵化设备', '孵化器、孵化相关用品', 4, 1, '4'),
('环境控制', '温控、湿控、通风设备等', 5, 1, '5');

-- 插入子分类
INSERT INTO mall_categories (parent_id, name, description, sort_order, level, path) VALUES
(1, '成鹅饲料', '适合成年鹅的各种饲料', 1, 2, '1/1'),
(1, '幼鹅饲料', '适合幼鹅的专用饲料', 2, 2, '1/2'),
(1, '营养添加剂', '维生素、矿物质等营养补充', 3, 2, '1/3'),
(2, '饮水设备', '饮水器、水槽等', 1, 2, '2/1'),
(2, '饲喂设备', '料槽、自动饲喂器等', 2, 2, '2/2'),
(2, '围栏设备', '围网、栅栏等', 3, 2, '2/3');

-- 插入默认系统设置
INSERT INTO system_settings (category, setting_key, setting_value, data_type, description, is_public) VALUES
('platform', 'platform_name', '智慧养鹅SAAS平台', 'string', '平台名称', true),
('platform', 'contact_phone', '************', 'string', '客服电话', true),
('platform', 'contact_email', '<EMAIL>', 'string', '客服邮箱', true),
('platform', 'icp_license', '京ICP备12345678号', 'string', 'ICP备案号', true),
('mall', 'free_shipping_amount', '199', 'number', '免运费金额', true),
('mall', 'default_shipping_fee', '15', 'number', '默认运费', true),
('api', 'default_rate_limit', '100', 'number', '默认API限制（每小时）', false),
('api', 'enable_rate_limiting', 'true', 'boolean', '是否启用API限制', false);

-- 插入平台管理员账号
INSERT INTO platform_admins (username, email, password, name, role) VALUES
('platform_admin', '<EMAIL>', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', '平台管理员', 'super_admin');

-- 插入默认API端点
INSERT INTO api_endpoints (endpoint_name, endpoint_path, method, description, is_public, requires_auth) VALUES
('健康检查', '/api/health', 'GET', '系统健康检查', true, false),
('用户认证', '/api/auth/login', 'POST', '用户登录认证', true, false),
('获取用户信息', '/api/users/profile', 'GET', '获取当前用户信息', false, true),
('鹅群列表', '/api/flocks', 'GET', '获取鹅群列表', false, true),
('生产记录', '/api/production', 'GET', '获取生产记录', false, true),
('健康记录', '/api/health-records', 'GET', '获取健康记录', false, true),
('财务记录', '/api/financial', 'GET', '获取财务记录', false, true),
('今日鹅价', '/api/goose-prices/today', 'GET', '获取今日鹅价', true, false),
('知识库', '/api/knowledge', 'GET', '获取知识库内容', true, false),
('商城商品', '/api/mall/products', 'GET', '获取商城商品列表', true, false);