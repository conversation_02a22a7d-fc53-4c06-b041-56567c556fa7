<!DOCTYPE html>
<html>
<head>
    <title>联调测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>智慧养鹅系统联调测试</h1>
    <div id="results"></div>
    
    <script>
    async function runTests() {
        const results = document.getElementById('results');
        results.innerHTML = '<p>开始运行联调测试...</p>';
        
        const tests = [
            {
                name: '主后端健康检查',
                url: 'http://localhost:3000/api/health',
                method: 'GET'
            },
            {
                name: 'SAAS管理后台状态',
                url: 'http://localhost:4000/auth/login',
                method: 'GET'
            }
        ];
        
        for (const test of tests) {
            try {
                console.log(`Testing ${test.name}...`);
                const response = await axios({
                    method: test.method,
                    url: test.url,
                    timeout: 5000
                });
                results.innerHTML += `<p>✅ ${test.name}: 成功 (状态码: ${response.status})</p>`;
            } catch (error) {
                results.innerHTML += `<p>❌ ${test.name}: 失败 - ${error.message}</p>`;
            }
        }
        
        results.innerHTML += '<p>测试完成！</p>';
    }
    
    // 页面加载后运行测试
    window.onload = runTests;
    </script>
</body>
</html>