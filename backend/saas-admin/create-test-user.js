const bcrypt = require('bcrypt');
const mysql = require('mysql2/promise');

async function createTestUser() {
    const dbConfig = {
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'smart_goose',
        charset: 'utf8mb4'
    };
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        const hashedPassword = await bcrypt.hash('admin123', 10);
        
        // 检查admin用户是否已存在
        const [existingUsers] = await connection.execute(
            'SELECT id FROM users WHERE username = ?', 
            ['admin']
        );
        
        if (existingUsers.length > 0) {
            // 更新现有用户的密码
            await connection.execute(
                'UPDATE users SET password = ?, status = "active" WHERE username = ?',
                [hashedPassword, 'admin']
            );
            console.log('✅ 更新admin用户密码成功');
        } else {
            // 创建新用户
            await connection.execute(`
                INSERT INTO users (username, email, name, password, role, status, farmName, createdAt) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            `, ['admin', '<EMAIL>', 'System Admin', hashedPassword, 'admin', 'active', 'Test Farm']);
            console.log('✅ 创建admin用户成功');
        }
        
        await connection.end();
        console.log('✅ 数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('❌ 创建用户失败:', error.message);
        process.exit(1);
    }
}

createTestUser();