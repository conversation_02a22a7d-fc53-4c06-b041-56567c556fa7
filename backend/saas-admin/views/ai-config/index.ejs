<!-- AI配置页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-robot me-2"></i>
                    AI配置管理
                </h1>
                <p class="page-subtitle">配置和管理AI服务参数</p>
            </div>
        </div>
    </div>

    <!-- AI服务状态 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                AI诊断服务
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">运行中</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                今日调用次数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">1,247</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                响应时间
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">1.2s</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                准确率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">94.5%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullseye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI配置表单 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">AI服务配置</h6>
                </div>
                <div class="card-body">
                    <form id="aiConfigForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="aiProvider">AI服务提供商</label>
                                    <select class="form-control" id="aiProvider" name="provider">
                                        <option value="openai">OpenAI</option>
                                        <option value="baidu" selected>百度文心一言</option>
                                        <option value="alibaba">阿里通义千问</option>
                                        <option value="tencent">腾讯混元</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="aiModel">AI模型</label>
                                    <select class="form-control" id="aiModel" name="model">
                                        <option value="ernie-bot-turbo" selected>ERNIE-Bot-turbo</option>
                                        <option value="ernie-bot">ERNIE-Bot</option>
                                        <option value="ernie-bot-4">ERNIE-Bot-4</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="apiKey">API密钥</label>
                                    <input type="password" class="form-control" id="apiKey" name="apiKey" 
                                           value="sk-****************************" placeholder="请输入API密钥">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="apiEndpoint">API端点</label>
                                    <input type="url" class="form-control" id="apiEndpoint" name="endpoint" 
                                           value="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="temperature">温度参数</label>
                                    <input type="range" class="form-control-range" id="temperature" name="temperature" 
                                           min="0" max="1" step="0.1" value="0.7">
                                    <small class="form-text text-muted">当前值: <span id="tempValue">0.7</span></small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="maxTokens">最大令牌数</label>
                                    <input type="number" class="form-control" id="maxTokens" name="maxTokens" 
                                           value="2048" min="1" max="4096">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="timeout">超时时间(秒)</label>
                                    <input type="number" class="form-control" id="timeout" name="timeout" 
                                           value="30" min="5" max="120">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="systemPrompt">系统提示词</label>
                            <textarea class="form-control" id="systemPrompt" name="systemPrompt" rows="4" 
                                      placeholder="请输入系统提示词">你是一个专业的鹅类养殖专家，具有丰富的鹅类疾病诊断和养殖管理经验。请根据用户提供的症状描述，给出专业的诊断建议和治疗方案。</textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="enableAI" name="enabled" checked>
                                <label class="custom-control-label" for="enableAI">启用AI服务</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="enableLogging" name="logging" checked>
                                <label class="custom-control-label" for="enableLogging">启用请求日志</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            保存配置
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="testAIConnection()">
                            <i class="fas fa-plug me-1"></i>
                            测试连接
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">使用统计</h6>
                </div>
                <div class="card-body">
                    <canvas id="aiUsageChart"></canvas>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">最近诊断记录</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">鹅群健康诊断</h6>
                                <small>3分钟前</small>
                            </div>
                            <p class="mb-1">症状：食欲不振，精神萎靡</p>
                            <small>诊断结果：疑似消化系统疾病</small>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">生产效率分析</h6>
                                <small>15分钟前</small>
                            </div>
                            <p class="mb-1">产蛋率下降分析</p>
                            <small>建议：调整饲料配比</small>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">环境监测建议</h6>
                                <small>1小时前</small>
                            </div>
                            <p class="mb-1">温湿度异常处理</p>
                            <small>建议：增加通风设备</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 温度参数滑块
document.getElementById('temperature').addEventListener('input', function() {
    document.getElementById('tempValue').textContent = this.value;
});

// AI使用统计图表
const ctx = document.getElementById('aiUsageChart').getContext('2d');
const aiUsageChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
            label: 'API调用次数',
            data: [12, 19, 15, 25, 22, 30, 28],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 测试AI连接
function testAIConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>测试中...';
    btn.disabled = true;
    
    // 模拟测试
    setTimeout(() => {
        alert('AI服务连接测试成功！');
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

// 表单提交
document.getElementById('aiConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('AI配置保存成功！');
});
</script>
