<!-- 价格管理页面 -->
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-header">
        <h1 class="page-title">
          <i class="fas fa-dollar-sign me-2"></i>
          价格管理
        </h1>
        <p class="page-subtitle">管理产品价格和市场趋势分析</p>
      </div>
    </div>
  </div>

  <!-- 价格概览卡片 -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                鹅肉均价
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">¥28.5/斤</div>
              <div class="text-xs text-success">
                <i class="fas fa-arrow-up"></i> ****%
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-drumstick-bite fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                鹅蛋均价
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">¥3.2/个</div>
              <div class="text-xs text-danger">
                <i class="fas fa-arrow-down"></i> -1.5%
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-egg fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                饲料价格
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">¥2,850/吨</div>
              <div class="text-xs text-success">
                <i class="fas fa-arrow-up"></i> +0.8%
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-seedling fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                利润率
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">23.5%</div>
              <div class="text-xs text-success">
                <i class="fas fa-arrow-up"></i> +1.2%
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-chart-line fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 价格趋势图表 -->
  <div class="row">
    <div class="col-xl-8 col-lg-7">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">价格趋势分析</h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow">
              <div class="dropdown-header">时间范围:</div>
              <a class="dropdown-item" href="#">最近7天</a>
              <a class="dropdown-item" href="#">最近30天</a>
              <a class="dropdown-item" href="#">最近90天</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-area">
            <canvas id="priceChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-4 col-lg-5">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">价格预测</h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <div class="small text-gray-500">鹅肉价格预测 (未来7天)</div>
            <div class="h5 mb-0 font-weight-bold text-gray-800">¥29.2/斤</div>
            <div class="text-xs text-success">
              <i class="fas fa-arrow-up"></i> 预计上涨 2.5%
            </div>
          </div>
          <div class="mb-3">
            <div class="small text-gray-500">鹅蛋价格预测 (未来7天)</div>
            <div class="h5 mb-0 font-weight-bold text-gray-800">¥3.1/个</div>
            <div class="text-xs text-danger">
              <i class="fas fa-arrow-down"></i> 预计下跌 3.1%
            </div>
          </div>
          <div class="mb-3">
            <div class="small text-gray-500">市场建议</div>
            <div class="alert alert-info" role="alert">
              <i class="fas fa-info-circle"></i>
              建议适当增加鹅肉库存，减少鹅蛋囤积
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 价格管理表格 -->
  <div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
      <h6 class="m-0 font-weight-bold text-primary">产品价格管理</h6>
      <button class="btn btn-primary btn-sm" onclick="showAddPriceModal()">
        <i class="fas fa-plus"></i> 添加价格记录
      </button>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th>产品名称</th>
              <th>当前价格</th>
              <th>昨日价格</th>
              <th>涨跌幅</th>
              <th>最高价</th>
              <th>最低价</th>
              <th>更新时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <i class="fas fa-drumstick-bite text-primary me-2"></i>
                  鹅肉 (活重)
                </div>
              </td>
              <td><strong>¥28.50</strong></td>
              <td>¥27.85</td>
              <td><span class="text-success">****%</span></td>
              <td>¥32.00</td>
              <td>¥25.00</td>
              <td>2024-01-15 14:30</td>
              <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editPrice(1)">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="viewHistory(1)">
                  <i class="fas fa-history"></i>
                </button>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <i class="fas fa-egg text-warning me-2"></i>
                  鹅蛋 (新鲜)
                </div>
              </td>
              <td><strong>¥3.20</strong></td>
              <td>¥3.25</td>
              <td><span class="text-danger">-1.5%</span></td>
              <td>¥3.80</td>
              <td>¥2.80</td>
              <td>2024-01-15 14:30</td>
              <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editPrice(2)">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="viewHistory(2)">
                  <i class="fas fa-history"></i>
                </button>
              </td>
            </tr>
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <i class="fas fa-seedling text-success me-2"></i>
                  鹅饲料 (配合料)
                </div>
              </td>
              <td><strong>¥2,850</strong></td>
              <td>¥2,828</td>
              <td><span class="text-success">+0.8%</span></td>
              <td>¥3,200</td>
              <td>¥2,600</td>
              <td>2024-01-15 14:30</td>
              <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editPrice(3)">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="viewHistory(3)">
                  <i class="fas fa-history"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  // 价格趋势图表
  const ctx = document.getElementById('priceChart').getContext('2d');
  const priceChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: ['1月1日', '1月3日', '1月5日', '1月7日', '1月9日', '1月11日', '1月13日', '1月15日'],
      datasets: [{
        label: '鹅肉价格',
        data: [26.5, 27.2, 26.8, 28.1, 27.9, 28.3, 27.8, 28.5],
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.1)',
        tension: 0.1
      }, {
        label: '鹅蛋价格',
        data: [3.1, 3.3, 3.2, 3.4, 3.3, 3.2, 3.25, 3.2],
        borderColor: 'rgb(255, 206, 86)',
        backgroundColor: 'rgba(255, 206, 86, 0.1)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: false
        }
      }
    }
  });

  // 添加价格记录
  function showAddPriceModal() {
    alert('添加价格记录功能开发中...');
  }

  // 编辑价格
  function editPrice(id) {
    alert(`编辑价格记录 ${id} 功能开发中...`);
  }

  // 查看历史
  function viewHistory(id) {
    alert(`查看价格历史 ${id} 功能开发中...`);
  }

  // 导出价格数据
  function exportPriceData() {
    if (confirm('确定要导出价格数据吗？')) {
      // 创建CSV数据
      const csvData = [
        ['日期', '鹅肉价格(元/斤)', '鹅蛋价格(元/个)', '变化趋势'],
        ['2024-01-01', '26.5', '3.1', '稳定'],
        ['2024-01-03', '27.2', '3.3', '上涨'],
        ['2024-01-05', '26.8', '3.2', '下跌'],
        ['2024-01-07', '28.1', '3.4', '上涨'],
        ['2024-01-09', '27.9', '3.3', '下跌'],
        ['2024-01-11', '28.3', '3.2', '上涨'],
        ['2024-01-13', '27.8', '3.25', '下跌'],
        ['2024-01-15', '28.5', '3.2', '上涨']
      ];

      // 转换为CSV格式
      const csvContent = csvData.map(row => row.join(',')).join('\n');

      // 创建下载链接
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `价格数据_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  // 价格预测分析
  function analyzePriceTrend() {
    // 模拟价格预测数据
    const currentData = [26.5, 27.2, 26.8, 28.1, 27.9, 28.3, 27.8, 28.5];
    const average = currentData.reduce((sum, price) => sum + price, 0) / currentData.length;
    const trend = currentData[currentData.length - 1] - currentData[0];

    let trendText = '';
    let trendColor = '';

    if (trend > 0.5) {
      trendText = '上涨趋势';
      trendColor = 'text-success';
    } else if (trend < -0.5) {
      trendText = '下跌趋势';
      trendColor = 'text-danger';
    } else {
      trendText = '稳定趋势';
      trendColor = 'text-info';
    }

    // 显示分析结果
    const analysisHtml = `
        <div class="alert alert-info">
            <h5><i class="fas fa-chart-line me-2"></i>价格趋势分析</h5>
            <div class="row">
                <div class="col-md-4">
                    <strong>平均价格:</strong> ¥${average.toFixed(2)}/斤
                </div>
                <div class="col-md-4">
                    <strong>价格变化:</strong> <span class="${trendColor}">¥${trend.toFixed(2)}</span>
                </div>
                <div class="col-md-4">
                    <strong>趋势判断:</strong> <span class="${trendColor}">${trendText}</span>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <strong>最高价格:</strong> ¥${Math.max(...currentData).toFixed(2)}/斤
                </div>
                <div class="col-md-6">
                    <strong>最低价格:</strong> ¥${Math.min(...currentData).toFixed(2)}/斤
                </div>
            </div>
            <div class="mt-3">
                <strong>预测建议:</strong>
                ${trend > 0 ? '价格呈上涨趋势，建议适当增加库存' :
        trend < 0 ? '价格呈下跌趋势，建议控制库存规模' :
          '价格相对稳定，维持正常库存水平'}
            </div>
        </div>
    `;

    // 创建模态框显示分析结果
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">价格趋势分析报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${analysisHtml}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="exportAnalysisReport()">导出报告</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
      document.body.removeChild(modal);
    });
  }

  // 导出分析报告
  function exportAnalysisReport() {
    const reportData = {
      reportDate: new Date().toLocaleDateString(),
      averagePrice: 27.51,
      priceChange: 2.0,
      trend: '上涨趋势',
      maxPrice: 28.5,
      minPrice: 26.5,
      recommendation: '价格呈上涨趋势，建议适当增加库存'
    };

    const reportContent = `
价格趋势分析报告
生成日期: ${reportData.reportDate}

=== 价格统计 ===
平均价格: ¥${reportData.averagePrice}/斤
价格变化: ¥${reportData.priceChange}
趋势判断: ${reportData.trend}
最高价格: ¥${reportData.maxPrice}/斤
最低价格: ¥${reportData.minPrice}/斤

=== 预测建议 ===
${reportData.recommendation}

=== 数据来源 ===
智慧养鹅管理系统 - 价格管理模块
    `;

    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `价格趋势分析报告_${new Date().toISOString().split('T')[0]}.txt`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
</script>