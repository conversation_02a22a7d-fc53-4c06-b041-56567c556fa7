<%- contentFor('title') %>
健康管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">健康管理</h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#vaccinationModal">
                <i class="bi bi-shield-plus"></i> 疫苗接种
            </button>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#treatmentModal">
                <i class="bi bi-capsule"></i> 疾病治疗
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inspectionModal">
                <i class="bi bi-clipboard-check"></i> 健康检查
            </button>
        </div>
    </div>

    <!-- 健康状态统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">健康鹅群</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="healthyFlocks">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-heart-fill text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">治疗中</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="treatmentFlocks">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-bandaid text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">疫苗接种率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="vaccinationRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-shield-check text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">死亡率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="mortalityRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 健康监控图表 -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">健康趋势分析</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-calendar"></i> 时间范围
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-period="7">最近7天</a></li>
                            <li><a class="dropdown-item" href="#" data-period="30">最近30天</a></li>
                            <li><a class="dropdown-item" href="#" data-period="90">最近90天</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="healthTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">疾病类型分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="diseaseDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 健康预警 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">健康预警</h6>
                </div>
                <div class="card-body">
                    <div id="healthAlerts" class="row">
                        <!-- 预警信息将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">健康记录查询</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label for="recordTypeFilter" class="form-label">记录类型</label>
                    <select class="form-select" id="recordTypeFilter">
                        <option value="">全部类型</option>
                        <option value="inspection">健康检查</option>
                        <option value="vaccination">疫苗接种</option>
                        <option value="treatment">疾病治疗</option>
                        <option value="mortality">死亡记录</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="flockFilter" class="form-label">选择鹅群</label>
                    <select class="form-select" id="flockFilter">
                        <option value="">全部鹅群</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">健康状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="healthy">健康</option>
                        <option value="sick">生病</option>
                        <option value="recovered">已康复</option>
                        <option value="dead">死亡</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="startDate" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="searchBtn">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 健康记录列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">健康记录列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i> 导出数据
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" id="exportExcel"><i class="bi bi-file-earmark-excel"></i> Excel</a></li>
                    <li><a class="dropdown-item" href="#" id="exportPdf"><i class="bi bi-file-earmark-pdf"></i> PDF</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="healthTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>鹅群</th>
                            <th>记录类型</th>
                            <th>状态/疾病</th>
                            <th>处理措施</th>
                            <th>兽医</th>
                            <th>费用</th>
                            <th>下次复查</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="healthTableBody">
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="健康记录分页" class="mt-3">
                <ul class="pagination justify-content-center" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 健康检查模态框 -->
<div class="modal fade" id="inspectionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">健康检查记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="inspectionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionFlock" class="form-label">选择鹅群 <span class="text-danger">*</span></label>
                                <select class="form-select" id="inspectionFlock" name="flock_id" required>
                                    <option value="">请选择鹅群</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionDate" class="form-label">检查日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="inspectionDate" name="record_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionVet" class="form-label">检查兽医</label>
                                <input type="text" class="form-control" id="inspectionVet" name="veterinarian" placeholder="兽医姓名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionStatus" class="form-label">健康状态 <span class="text-danger">*</span></label>
                                <select class="form-select" id="inspectionStatus" name="health_status" required>
                                    <option value="">请选择状态</option>
                                    <option value="healthy">健康</option>
                                    <option value="sick">生病</option>
                                    <option value="suspected">疑似患病</option>
                                    <option value="quarantine">需要隔离</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionCount" class="form-label">检查数量</label>
                                <input type="number" class="form-control" id="inspectionCount" name="checked_count" min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="abnormalCount" class="form-label">异常数量</label>
                                <input type="number" class="form-control" id="abnormalCount" name="abnormal_count" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectionCost" class="form-label">检查费用 (元)</label>
                                <input type="number" class="form-control" id="inspectionCost" name="cost" min="0" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nextInspection" class="form-label">下次检查日期</label>
                                <input type="date" class="form-control" id="nextInspection" name="next_check_date">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="inspectionSymptoms" class="form-label">症状描述</label>
                        <textarea class="form-control" id="inspectionSymptoms" name="symptoms" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="inspectionTreatment" class="form-label">处理建议</label>
                        <textarea class="form-control" id="inspectionTreatment" name="treatment" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="inspectionNotes" class="form-label">备注说明</label>
                        <textarea class="form-control" id="inspectionNotes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存记录</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 疫苗接种模态框 -->
<div class="modal fade" id="vaccinationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">疫苗接种记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="vaccinationForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinationFlock" class="form-label">选择鹅群 <span class="text-danger">*</span></label>
                                <select class="form-select" id="vaccinationFlock" name="flock_id" required>
                                    <option value="">请选择鹅群</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinationDate" class="form-label">接种日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="vaccinationDate" name="record_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccineType" class="form-label">疫苗类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="vaccineType" name="vaccine_type" required>
                                    <option value="">请选择疫苗</option>
                                    <option value="Newcastle">新城疫疫苗</option>
                                    <option value="Avian_Flu">禽流感疫苗</option>
                                    <option value="Duck_Plague">鸭瘟疫苗</option>
                                    <option value="Duck_Hepatitis">鸭肝炎疫苗</option>
                                    <option value="Other">其他疫苗</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccineBatch" class="form-label">疫苗批号</label>
                                <input type="text" class="form-control" id="vaccineBatch" name="batch_number" placeholder="疫苗批次号">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinationMethod" class="form-label">接种方式</label>
                                <select class="form-select" id="vaccinationMethod" name="method">
                                    <option value="injection">肌肉注射</option>
                                    <option value="drinking_water">饮水免疫</option>
                                    <option value="spray">喷雾免疫</option>
                                    <option value="eye_drop">滴眼免疫</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinatedCount" class="form-label">接种数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="vaccinatedCount" name="vaccinated_count" min="1" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinationVet" class="form-label">接种兽医</label>
                                <input type="text" class="form-control" id="vaccinationVet" name="veterinarian" placeholder="兽医姓名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccinationCost" class="form-label">接种费用 (元)</label>
                                <input type="number" class="form-control" id="vaccinationCost" name="cost" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nextVaccination" class="form-label">下次接种日期</label>
                                <input type="date" class="form-control" id="nextVaccination" name="next_vaccination_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vaccineExpiry" class="form-label">疫苗有效期</label>
                                <input type="date" class="form-control" id="vaccineExpiry" name="expiry_date">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="vaccinationNotes" class="form-label">接种备注</label>
                        <textarea class="form-control" id="vaccinationNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">保存记录</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 疾病治疗模态框 -->
<div class="modal fade" id="treatmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">疾病治疗记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="treatmentForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="treatmentFlock" class="form-label">选择鹅群 <span class="text-danger">*</span></label>
                                <select class="form-select" id="treatmentFlock" name="flock_id" required>
                                    <option value="">请选择鹅群</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="treatmentDate" class="form-label">治疗日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="treatmentDate" name="record_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="diseaseType" class="form-label">疾病类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="diseaseType" name="disease_type" required>
                                    <option value="">请选择疾病</option>
                                    <option value="respiratory">呼吸道疾病</option>
                                    <option value="digestive">消化道疾病</option>
                                    <option value="parasitic">寄生虫病</option>
                                    <option value="infectious">传染病</option>
                                    <option value="nutritional">营养性疾病</option>
                                    <option value="other">其他疾病</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="affectedCount" class="form-label">患病数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="affectedCount" name="affected_count" min="1" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="treatmentVet" class="form-label">治疗兽医</label>
                                <input type="text" class="form-control" id="treatmentVet" name="veterinarian" placeholder="兽医姓名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="treatmentStatus" class="form-label">治疗状态</label>
                                <select class="form-select" id="treatmentStatus" name="treatment_status">
                                    <option value="ongoing">治疗中</option>
                                    <option value="recovered">已康复</option>
                                    <option value="died">死亡</option>
                                    <option value="quarantined">隔离中</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="treatmentCost" class="form-label">治疗费用 (元)</label>
                                <input type="number" class="form-control" id="treatmentCost" name="cost" min="0" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recoveredCount" class="form-label">康复数量</label>
                                <input type="number" class="form-control" id="recoveredCount" name="recovered_count" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="diseaseSymptoms" class="form-label">症状描述 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="diseaseSymptoms" name="symptoms" rows="2" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="medicationUsed" class="form-label">使用药物</label>
                        <textarea class="form-control" id="medicationUsed" name="medication" rows="2" placeholder="药物名称、剂量、用法等"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="treatmentNotes" class="form-label">治疗备注</label>
                        <textarea class="form-control" id="treatmentNotes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">保存记录</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentPage = 1;
    const pageSize = 10;
    let healthChart, diseaseChart;

    // 页面加载时初始化数据
    loadStatistics();
    loadFlocks();
    loadHealthRecords();
    loadHealthAlerts();
    initCharts();

    // 设置默认日期
    const today = new Date().toISOString().split('T')[0];
    $('#inspectionDate, #vaccinationDate, #treatmentDate').val(today);
    
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    $('#startDate').val(oneWeekAgo.toISOString().split('T')[0]);

    // 加载统计数据
    function loadStatistics() {
        $.ajax({
            url: '/api/health/statistics',
            method: 'GET',
            success: function(data) {
                $('#healthyFlocks').text(data.healthy_flocks || 0);
                $('#treatmentFlocks').text(data.treatment_flocks || 0);
                $('#vaccinationRate').text((data.vaccination_rate || 0).toFixed(1) + '%');
                $('#mortalityRate').text((data.mortality_rate || 0).toFixed(1) + '%');
            },
            error: function() {
                console.error('加载统计数据失败');
            }
        });
    }

    // 加载鹅群列表
    function loadFlocks() {
        $.ajax({
            url: '/api/flocks/active',
            method: 'GET',
            success: function(data) {
                const flockSelects = $('#inspectionFlock, #vaccinationFlock, #treatmentFlock, #flockFilter');
                flockSelects.find('option:not(:first)').remove();
                data.forEach(flock => {
                    flockSelects.append(`<option value="${flock.id}">${flock.name} (${flock.code})</option>`);
                });
            },
            error: function() {
                console.error('加载鹅群列表失败');
            }
        });
    }

    // 加载健康预警
    function loadHealthAlerts() {
        $.ajax({
            url: '/api/health/alerts',
            method: 'GET',
            success: function(data) {
                renderHealthAlerts(data);
            },
            error: function() {
                console.error('加载健康预警失败');
            }
        });
    }

    // 渲染健康预警
    function renderHealthAlerts(alerts) {
        const alertsContainer = $('#healthAlerts');
        alertsContainer.empty();

        if (alerts.length === 0) {
            alertsContainer.append('<div class="col-12 text-center text-muted">暂无健康预警</div>');
            return;
        }

        alerts.forEach(alert => {
            const alertClass = getAlertClass(alert.severity);
            const alertHtml = `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="alert ${alertClass} mb-0">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <div>
                                <strong>${alert.title}</strong><br>
                                <small>${alert.message}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            alertsContainer.append(alertHtml);
        });
    }

    // 获取预警样式类
    function getAlertClass(severity) {
        switch (severity) {
            case 'high': return 'alert-danger';
            case 'medium': return 'alert-warning';
            case 'low': return 'alert-info';
            default: return 'alert-secondary';
        }
    }

    // 初始化图表
    function initCharts() {
        // 健康趋势图
        const healthCtx = document.getElementById('healthTrendChart').getContext('2d');
        healthChart = new Chart(healthCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '健康率',
                        data: [],
                        borderColor: 'rgba(28, 200, 138, 1)',
                        backgroundColor: 'rgba(28, 200, 138, 0.1)',
                        fill: true
                    },
                    {
                        label: '患病率',
                        data: [],
                        borderColor: 'rgba(231, 74, 59, 1)',
                        backgroundColor: 'rgba(231, 74, 59, 0.1)',
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '百分比 (%)'
                        }
                    }
                }
            }
        });

        // 疾病分布图
        const diseaseCtx = document.getElementById('diseaseDistributionChart').getContext('2d');
        diseaseChart = new Chart(diseaseCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        loadChartData(30); // 默认加载30天数据
    }

    // 加载图表数据
    function loadChartData(period) {
        $.ajax({
            url: '/api/health/chart-data',
            method: 'GET',
            data: { period: period },
            success: function(data) {
                // 更新健康趋势图
                healthChart.data.labels = data.trend.labels;
                healthChart.data.datasets[0].data = data.trend.healthy_rate;
                healthChart.data.datasets[1].data = data.trend.sick_rate;
                healthChart.update();

                // 更新疾病分布图
                diseaseChart.data.labels = data.diseases.labels;
                diseaseChart.data.datasets[0].data = data.diseases.values;
                diseaseChart.update();
            },
            error: function() {
                console.error('加载图表数据失败');
            }
        });
    }

    // 加载健康记录
    function loadHealthRecords(page = 1) {
        const params = {
            page: page,
            pageSize: pageSize,
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            status: $('#statusFilter').val(),
            start_date: $('#startDate').val()
        };

        $.ajax({
            url: '/api/health/records',
            method: 'GET',
            data: params,
            success: function(data) {
                renderHealthTable(data.records);
                renderPagination(data.pagination);
            },
            error: function() {
                showAlert('加载健康记录失败', 'error');
            }
        });
    }

    // 渲染健康记录表格
    function renderHealthTable(records) {
        const tbody = $('#healthTableBody');
        tbody.empty();

        if (records.length === 0) {
            tbody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
            return;
        }

        records.forEach(record => {
            const typeClass = getRecordTypeClass(record.type);
            const typeText = getRecordTypeText(record.type);
            
            const row = `
                <tr>
                    <td>${new Date(record.record_date).toLocaleDateString()}</td>
                    <td>${record.flock_name}</td>
                    <td><span class="badge ${typeClass}">${typeText}</span></td>
                    <td>${record.status || record.disease_type || '-'}</td>
                    <td>${record.treatment || record.medication || '-'}</td>
                    <td>${record.veterinarian || '-'}</td>
                    <td>¥${(record.cost || 0).toFixed(2)}</td>
                    <td>${record.next_check_date ? new Date(record.next_check_date).toLocaleDateString() : '-'}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editHealthRecord(${record.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteHealthRecord(${record.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // 获取记录类型样式类
    function getRecordTypeClass(type) {
        switch (type) {
            case 'inspection': return 'bg-primary';
            case 'vaccination': return 'bg-success';
            case 'treatment': return 'bg-warning';
            case 'mortality': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // 获取记录类型文本
    function getRecordTypeText(type) {
        switch (type) {
            case 'inspection': return '健康检查';
            case 'vaccination': return '疫苗接种';
            case 'treatment': return '疾病治疗';
            case 'mortality': return '死亡记录';
            default: return '未知';
        }
    }

    // 渲染分页
    function renderPagination(pagination) {
        const paginationEl = $('#pagination');
        paginationEl.empty();

        if (pagination.totalPages <= 1) return;

        // 上一页
        const prevDisabled = pagination.currentPage === 1 ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage - 1}">上一页</a>
            </li>
        `);

        // 页码
        for (let i = 1; i <= pagination.totalPages; i++) {
            const activeClass = i === pagination.currentPage ? 'active' : '';
            paginationEl.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        // 下一页
        const nextDisabled = pagination.currentPage === pagination.totalPages ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage + 1}">下一页</a>
            </li>
        `);

        currentPage = pagination.currentPage;
    }

    // 事件处理
    $(document).on('click', '.page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && page !== currentPage) {
            loadHealthRecords(page);
        }
    });

    // 时间范围选择
    $(document).on('click', '[data-period]', function(e) {
        e.preventDefault();
        const period = $(this).data('period');
        loadChartData(period);
    });

    // 搜索和重置
    $('#searchBtn').click(function() {
        loadHealthRecords(1);
    });

    $('#resetBtn').click(function() {
        $('#recordTypeFilter').val('');
        $('#flockFilter').val('');
        $('#statusFilter').val('');
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        $('#startDate').val(oneWeekAgo.toISOString().split('T')[0]);
        loadHealthRecords(1);
    });

    // 表单提交处理
    $('#inspectionForm').submit(function(e) {
        e.preventDefault();
        submitHealthRecord('/api/health/inspections', $(this), '#inspectionModal', '健康检查记录保存成功');
    });

    $('#vaccinationForm').submit(function(e) {
        e.preventDefault();
        submitHealthRecord('/api/health/vaccinations', $(this), '#vaccinationModal', '疫苗接种记录保存成功');
    });

    $('#treatmentForm').submit(function(e) {
        e.preventDefault();
        submitHealthRecord('/api/health/treatments', $(this), '#treatmentModal', '疾病治疗记录保存成功');
    });

    // 统一的健康记录提交函数
    function submitHealthRecord(url, form, modalId, successMessage) {
        $.ajax({
            url: url,
            method: 'POST',
            data: form.serialize(),
            success: function(data) {
                $(modalId).modal('hide');
                form[0].reset();
                showAlert(successMessage, 'success');
                loadHealthRecords(currentPage);
                loadStatistics();
                loadHealthAlerts();
                loadChartData(30);
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '保存失败';
                showAlert(error, 'error');
            }
        });
    }

    // 导出功能
    $('#exportExcel').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            status: $('#statusFilter').val(),
            start_date: $('#startDate').val(),
            format: 'excel'
        });
        window.open(`/api/health/export?${params}`);
    });

    $('#exportPdf').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            status: $('#statusFilter').val(),
            start_date: $('#startDate').val(),
            format: 'pdf'
        });
        window.open(`/api/health/export?${params}`);
    });
});

// 编辑健康记录
function editHealthRecord(recordId) {
    $.ajax({
        url: `/api/health/records/${recordId}`,
        method: 'GET',
        success: function(data) {
            // 根据记录类型打开相应的编辑模态框
            if (data.type === 'inspection') {
                fillInspectionForm(data);
                $('#inspectionModal').modal('show');
            } else if (data.type === 'vaccination') {
                fillVaccinationForm(data);
                $('#vaccinationModal').modal('show');
            } else if (data.type === 'treatment') {
                fillTreatmentForm(data);
                $('#treatmentModal').modal('show');
            }
        },
        error: function() {
            showAlert('加载记录信息失败', 'error');
        }
    });
}

// 填充检查表单
function fillInspectionForm(data) {
    $('#inspectionFlock').val(data.flock_id);
    $('#inspectionDate').val(data.record_date);
    $('#inspectionVet').val(data.veterinarian);
    $('#inspectionStatus').val(data.health_status);
    $('#inspectionCount').val(data.checked_count);
    $('#abnormalCount').val(data.abnormal_count);
    $('#inspectionCost').val(data.cost);
    $('#nextInspection').val(data.next_check_date);
    $('#inspectionSymptoms').val(data.symptoms);
    $('#inspectionTreatment').val(data.treatment);
    $('#inspectionNotes').val(data.notes);
}

// 填充疫苗表单
function fillVaccinationForm(data) {
    $('#vaccinationFlock').val(data.flock_id);
    $('#vaccinationDate').val(data.record_date);
    $('#vaccineType').val(data.vaccine_type);
    $('#vaccineBatch').val(data.batch_number);
    $('#vaccinationMethod').val(data.method);
    $('#vaccinatedCount').val(data.vaccinated_count);
    $('#vaccinationVet').val(data.veterinarian);
    $('#vaccinationCost').val(data.cost);
    $('#nextVaccination').val(data.next_vaccination_date);
    $('#vaccineExpiry').val(data.expiry_date);
    $('#vaccinationNotes').val(data.notes);
}

// 填充治疗表单
function fillTreatmentForm(data) {
    $('#treatmentFlock').val(data.flock_id);
    $('#treatmentDate').val(data.record_date);
    $('#diseaseType').val(data.disease_type);
    $('#affectedCount').val(data.affected_count);
    $('#treatmentVet').val(data.veterinarian);
    $('#treatmentStatus').val(data.treatment_status);
    $('#treatmentCost').val(data.cost);
    $('#recoveredCount').val(data.recovered_count);
    $('#diseaseSymptoms').val(data.symptoms);
    $('#medicationUsed').val(data.medication);
    $('#treatmentNotes').val(data.notes);
}

// 删除健康记录
function deleteHealthRecord(recordId) {
    if (confirm('确定要删除这条健康记录吗？此操作不可恢复！')) {
        $.ajax({
            url: `/api/health/records/${recordId}`,
            method: 'DELETE',
            success: function() {
                showAlert('健康记录删除成功', 'success');
                loadHealthRecords(currentPage);
                loadStatistics();
                loadHealthAlerts();
                loadChartData(30);
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '删除失败';
                showAlert(error, 'error');
            }
        });
    }
}

// 显示提示消息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('body').append(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
