<!-- API管理统计卡片 -->
<div class="row">
  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-primary">
      <div class="inner">
        <h3><%= stats.totalApis %></h3>
        <p>API总数</p>
      </div>
      <div class="icon">
        <i class="fas fa-code"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3><%= stats.activeApis %></h3>
        <p>活跃API</p>
      </div>
      <div class="icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3><%= stats.todayCalls.toLocaleString() %></h3>
        <p>今日调用</p>
      </div>
      <div class="icon">
        <i class="fas fa-chart-line"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3><%= stats.avgResponseTime %>ms</h3>
        <p>平均响应时间</p>
      </div>
      <div class="icon">
        <i class="fas fa-clock"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>
</div>

<!-- API列表 -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-list mr-2"></i>API接口列表
        </h3>
        <div class="card-tools">
          <a href="/api-management/docs" class="btn btn-primary btn-sm">
            <i class="fas fa-book mr-1"></i>API文档
          </a>
          <a href="/api-management/versions" class="btn btn-info btn-sm">
            <i class="fas fa-tags mr-1"></i>版本管理
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped" id="apiTable">
            <thead>
              <tr>
                <th>API名称</th>
                <th>端点</th>
                <th>方法</th>
                <th>版本</th>
                <th>状态</th>
                <th>今日调用</th>
                <th>响应时间</th>
                <th>错误率</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% apis.forEach(function(api) { %>
              <tr>
                <td><%= api.name %></td>
                <td><code><%= api.endpoint %></code></td>
                <td>
                  <span class="badge badge-<%= api.method === 'GET' ? 'success' : api.method === 'POST' ? 'primary' : api.method === 'PUT' ? 'warning' : 'danger' %>">
                    <%= api.method %>
                  </span>
                </td>
                <td><%= api.version %></td>
                <td>
                  <span class="badge badge-<%= api.status === 'active' ? 'success' : api.status === 'deprecated' ? 'warning' : 'secondary' %>">
                    <%= api.status === 'active' ? '活跃' : api.status === 'deprecated' ? '已废弃' : '停用' %>
                  </span>
                </td>
                <td><%= api.calls_today %></td>
                <td><%= api.avg_response_time %>ms</td>
                <td>
                  <span class="text-<%= api.error_rate > 0.05 ? 'danger' : api.error_rate > 0.02 ? 'warning' : 'success' %>">
                    <%= (api.error_rate * 100).toFixed(2) %>%
                  </span>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <a href="/api-management/api/<%= api.id %>" class="btn btn-info" title="查看详情">
                      <i class="fas fa-eye"></i>
                    </a>
                    <a href="/api-management/api/<%= api.id %>/edit" class="btn btn-primary" title="编辑">
                      <i class="fas fa-edit"></i>
                    </a>
                    <a href="/api-management/api/<%= api.id %>/test" class="btn btn-success" title="测试">
                      <i class="fas fa-play"></i>
                    </a>
                  </div>
                </td>
              </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    // 初始化数据表
    $('#apiTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese.json"
        },
        "order": [[ 5, "desc" ]], // 按今日调用量排序
        "pageLength": 25
    });
});
</script>
