<!-- 性能监控页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    性能监控
                </h1>
                <p class="page-subtitle">实时监控系统性能和资源使用情况</p>
            </div>
        </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                系统状态
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">正常运行</div>
                            <div class="text-xs text-success">
                                <i class="fas fa-check-circle"></i> 99.9% 可用性
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                CPU使用率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">45.2%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: 45%"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-microchip fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                内存使用率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">68.5%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 68%"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-memory fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                磁盘使用率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">32.1%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 32%"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能图表 -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">系统性能趋势</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <div class="dropdown-header">时间范围:</div>
                            <a class="dropdown-item" href="#">最近1小时</a>
                            <a class="dropdown-item" href="#">最近24小时</a>
                            <a class="dropdown-item" href="#">最近7天</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">实时指标</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="small text-gray-500">响应时间</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">125ms</div>
                        <div class="text-xs text-success">
                            <i class="fas fa-arrow-down"></i> -15ms
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="small text-gray-500">并发用户</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">342</div>
                        <div class="text-xs text-success">
                            <i class="fas fa-arrow-up"></i> +23
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="small text-gray-500">API调用/分钟</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">1,247</div>
                        <div class="text-xs text-info">
                            <i class="fas fa-minus"></i> 稳定
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="small text-gray-500">错误率</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">0.02%</div>
                        <div class="text-xs text-success">
                            <i class="fas fa-arrow-down"></i> -0.01%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务状态表格 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">服务状态监控</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>服务名称</th>
                            <th>状态</th>
                            <th>响应时间</th>
                            <th>CPU使用率</th>
                            <th>内存使用</th>
                            <th>最后检查</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-server text-primary me-2"></i>
                                    Web服务器
                                </div>
                            </td>
                            <td><span class="badge badge-success">运行中</span></td>
                            <td>125ms</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 45%"></div>
                                </div>
                                45%
                            </td>
                            <td>2.1GB / 4GB</td>
                            <td>刚刚</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewServiceDetails('web')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="restartService('web')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-database text-success me-2"></i>
                                    数据库服务
                                </div>
                            </td>
                            <td><span class="badge badge-success">运行中</span></td>
                            <td>45ms</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 32%"></div>
                                </div>
                                32%
                            </td>
                            <td>1.8GB / 8GB</td>
                            <td>刚刚</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewServiceDetails('db')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="restartService('db')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-robot text-info me-2"></i>
                                    AI服务
                                </div>
                            </td>
                            <td><span class="badge badge-success">运行中</span></td>
                            <td>1.2s</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 68%"></div>
                                </div>
                                68%
                            </td>
                            <td>3.2GB / 4GB</td>
                            <td>刚刚</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewServiceDetails('ai')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="restartService('ai')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-memory text-warning me-2"></i>
                                    缓存服务
                                </div>
                            </td>
                            <td><span class="badge badge-warning">高负载</span></td>
                            <td>89ms</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 85%"></div>
                                </div>
                                85%
                            </td>
                            <td>1.5GB / 2GB</td>
                            <td>刚刚</td>
                            <td>
                                <button class="btn btn-sm btn-outline-info" onclick="viewServiceDetails('cache')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="restartService('cache')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 告警信息 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">系统告警</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>警告:</strong> 缓存服务CPU使用率过高 (85%)，建议检查缓存配置。
                <button type="button" class="btn btn-sm btn-outline-warning ml-2" onclick="handleAlert('cache-high-cpu')">
                    处理
                </button>
            </div>
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>信息:</strong> 系统将在今晚2:00进行例行维护，预计持续30分钟。
            </div>
        </div>
    </div>
</div>

<script>
// 性能趋势图表
const ctx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
            label: 'CPU使用率',
            data: [25, 30, 45, 52, 48, 42, 38],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.1
        }, {
            label: '内存使用率',
            data: [60, 65, 68, 72, 70, 68, 65],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// 查看服务详情
function viewServiceDetails(service) {
    alert(`查看${service}服务详情功能开发中...`);
}

// 重启服务
function restartService(service) {
    if (confirm(`确定要重启${service}服务吗？`)) {
        alert(`${service}服务重启中...`);
    }
}

// 处理告警
function handleAlert(alertId) {
    alert(`处理告警 ${alertId} 功能开发中...`);
}

// 自动刷新数据
setInterval(() => {
    // 这里可以添加实时数据更新逻辑
    console.log('刷新监控数据...');
}, 30000); // 每30秒刷新一次
</script>
