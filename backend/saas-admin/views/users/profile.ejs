<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-cog me-2"></i>用户管理</h2>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="fas fa-plus me-1"></i>添加用户
                    </button>
                    <button class="btn btn-info" onclick="exportUsers()">
                        <i class="fas fa-download me-1"></i>导出用户
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总用户数</h5>
                            <h3><%= stats.totalUsers %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>活跃用户</h5>
                            <h3><%= stats.activeUsers %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>在线用户</h5>
                            <h3><%= stats.onlineUsers %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>新增用户</h5>
                            <h3><%= stats.newUsers %></h3>
                            <small>本月</small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i>用户列表</h5>
                    <div class="d-flex">
                        <select class="form-select me-2" id="roleFilter" style="width: 150px;">
                            <option value="">全部角色</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="viewer">查看者</option>
                        </select>
                        <select class="form-select me-2" id="statusFilter" style="width: 150px;">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="suspended">已暂停</option>
                        </select>
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索用户..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usersTable">
                            <thead>
                                <tr>
                                    <th>用户信息</th>
                                    <th>租户</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>最后登录</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% users.forEach(user => { %>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <% if (user.avatar) { %>
                                                        <img src="<%= user.avatar %>" class="rounded-circle" width="40" height="40">
                                                    <% } else { %>
                                                        <img src="/img/default-avatar.png" class="rounded-circle" width="40" height="40">
                                                    <% } %>
                                                </div>
                                                <div>
                                                    <strong><%= user.name %></strong><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-envelope me-1"></i><%= user.email %>
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (user.tenant_name) { %>
                                                <span class="badge bg-primary"><%= user.tenant_name %></span>
                                            <% } else { %>
                                                <span class="text-muted">-</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (user.role === 'admin') { %>
                                                <span class="badge bg-danger">管理员</span>
                                            <% } else if (user.role === 'user') { %>
                                                <span class="badge bg-primary">普通用户</span>
                                            <% } else if (user.role === 'viewer') { %>
                                                <span class="badge bg-secondary">查看者</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (user.status === 'active') { %>
                                                <span class="badge bg-success">活跃</span>
                                            <% } else if (user.status === 'inactive') { %>
                                                <span class="badge bg-warning">非活跃</span>
                                            <% } else if (user.status === 'suspended') { %>
                                                <span class="badge bg-danger">已暂停</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <% if (user.last_login_time) { %>
                                                <%= Utils.formatDate(user.last_login_time, 'MM-DD HH:mm') %>
                                            <% } else { %>
                                                <span class="text-muted">从未登录</span>
                                            <% } %>
                                        </td>
                                        <td><%= Utils.formatDate(user.created_at, 'YYYY-MM-DD') %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editUser(<%= user.id %>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewUserProfile(<%= user.id %>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <% if (user.status === 'active') { %>
                                                    <button class="btn btn-outline-warning" onclick="suspendUser(<%= user.id %>)">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                <% } else if (user.status === 'suspended') { %>
                                                    <button class="btn btn-outline-success" onclick="activateUser(<%= user.id %>)">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                <% } %>
                                                <button class="btn btn-outline-danger" onclick="deleteUser(<%= user.id %>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="userName" class="form-label">用户姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="userName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="userEmail" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="userEmail" name="email" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="userPhone" class="form-label">手机号码</label>
                            <input type="tel" class="form-control" id="userPhone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="userTenant" class="form-label">所属租户 <span class="text-danger">*</span></label>
                            <select class="form-select" id="userTenant" name="tenant_id" required>
                                <option value="">请选择租户</option>
                                <% tenants.forEach(tenant => { %>
                                    <option value="<%= tenant.id %>"><%= tenant.company_name %></option>
                                <% }) %>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="userRole" class="form-label">用户角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="userRole" name="role" required>
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                                <option value="viewer">查看者</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="userPassword" class="form-label">初始密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="userPassword" name="password" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="userRemarks" class="form-label">备注</label>
                        <textarea class="form-control" id="userRemarks" name="remarks" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sendWelcome" name="send_welcome" checked>
                        <label class="form-check-label" for="sendWelcome">
                            发送欢迎邮件
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">创建用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editUserName" class="form-label">用户姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editUserName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editUserEmail" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="editUserEmail" name="email" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editUserPhone" class="form-label">手机号码</label>
                            <input type="tel" class="form-control" id="editUserPhone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editUserRole" class="form-label">用户角色 <span class="text-danger">*</span></label>
                            <select class="form-select" id="editUserRole" name="role" required>
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                                <option value="viewer">查看者</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editUserStatus" class="form-label">用户状态</label>
                            <select class="form-select" id="editUserStatus" name="status">
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="suspended">已暂停</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newPassword" class="form-label">新密码</label>
                            <input type="password" class="form-control" id="newPassword" name="new_password" placeholder="留空表示不修改">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editUserRemarks" class="form-label">备注</label>
                        <textarea class="form-control" id="editUserRemarks" name="remarks" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">保存更改</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 筛选和搜索功能
    document.getElementById('roleFilter').addEventListener('change', filterUsers);
    document.getElementById('statusFilter').addEventListener('change', filterUsers);
    document.getElementById('searchInput').addEventListener('input', debounce(filterUsers, 500));
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function filterUsers() {
    const role = document.getElementById('roleFilter').value;
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const url = new URL(window.location);
    url.searchParams.set('role', role);
    url.searchParams.set('status', status);
    url.searchParams.set('search', search);
    window.location.href = url.toString();
}

function createUser() {
    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);
    
    fetch('/users/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户创建成功');
            location.reload();
        } else {
            alert('创建失败：' + data.message);
        }
    });
}

function editUser(userId) {
    fetch(`/users/${userId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const user = data.data;
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUserName').value = user.name;
            document.getElementById('editUserEmail').value = user.email;
            document.getElementById('editUserPhone').value = user.phone || '';
            document.getElementById('editUserRole').value = user.role;
            document.getElementById('editUserStatus').value = user.status;
            document.getElementById('editUserRemarks').value = user.remarks || '';
            
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        }
    });
}

function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = formData.get('user_id');
    
    fetch(`/users/${userId}/update`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户更新成功');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    });
}

function viewUserProfile(userId) {
    window.open(`/users/${userId}/profile`, '_blank');
}

function suspendUser(userId) {
    if (confirm('确定要暂停该用户吗？')) {
        fetch(`/users/${userId}/suspend`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        });
    }
}

function activateUser(userId) {
    if (confirm('确定要激活该用户吗？')) {
        fetch(`/users/${userId}/activate`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        });
    }
}

function deleteUser(userId) {
    if (confirm('确定要删除该用户吗？此操作不可恢复！')) {
        fetch(`/users/${userId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        });
    }
}

function exportUsers() {
    const role = document.getElementById('roleFilter').value;
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const params = new URLSearchParams({
        role: role,
        status: status,
        search: search
    });
    
    window.open('/users/export?' + params.toString());
}
</script>