<%- contentFor('title') %>
生产管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">生产管理</h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#feedRecordModal">
                <i class="bi bi-plus-lg"></i> 饲料投喂记录
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#eggProductionModal">
                <i class="bi bi-plus-lg"></i> 产蛋记录
            </button>
        </div>
    </div>

    <!-- 生产统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">今日产蛋量</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayEggs">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-egg text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">本月产蛋量</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="monthEggs">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-month text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">饲料消耗 (kg)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="feedConsumption">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-basket2 text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">平均产蛋率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="eggRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-percent text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">产蛋趋势分析</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-calendar"></i> 时间范围
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-period="7">最近7天</a></li>
                            <li><a class="dropdown-item" href="#" data-period="30">最近30天</a></li>
                            <li><a class="dropdown-item" href="#" data-period="90">最近90天</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="eggProductionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">鹅群产蛋分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="flockProductionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">生产记录查询</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label for="recordTypeFilter" class="form-label">记录类型</label>
                    <select class="form-select" id="recordTypeFilter">
                        <option value="">全部类型</option>
                        <option value="egg">产蛋记录</option>
                        <option value="feed">饲料记录</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="flockFilter" class="form-label">选择鹅群</label>
                    <select class="form-select" id="flockFilter">
                        <option value="">全部鹅群</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="startDate" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-2">
                    <label for="endDate" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="searchBtn">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生产记录列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">生产记录列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i> 导出数据
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" id="exportExcel"><i class="bi bi-file-earmark-excel"></i> Excel</a></li>
                    <li><a class="dropdown-item" href="#" id="exportPdf"><i class="bi bi-file-earmark-pdf"></i> PDF</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="productionTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>鹅群</th>
                            <th>记录类型</th>
                            <th>数量/重量</th>
                            <th>单位</th>
                            <th>品质等级</th>
                            <th>记录员</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody">
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="生产记录分页" class="mt-3">
                <ul class="pagination justify-content-center" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 产蛋记录模态框 -->
<div class="modal fade" id="eggProductionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">产蛋记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="eggProductionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eggFlock" class="form-label">选择鹅群 <span class="text-danger">*</span></label>
                                <select class="form-select" id="eggFlock" name="flock_id" required>
                                    <option value="">请选择鹅群</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eggDate" class="form-label">记录日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="eggDate" name="record_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eggCount" class="form-label">产蛋数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="eggCount" name="egg_count" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eggWeight" class="form-label">总重量 (kg)</label>
                                <input type="number" class="form-control" id="eggWeight" name="total_weight" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="eggGrade" class="form-label">品质等级</label>
                                <select class="form-select" id="eggGrade" name="quality_grade">
                                    <option value="">请选择等级</option>
                                    <option value="A+">特级 (A+)</option>
                                    <option value="A">优级 (A)</option>
                                    <option value="B">良级 (B)</option>
                                    <option value="C">普级 (C)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brokenEggs" class="form-label">破损数量</label>
                                <input type="number" class="form-control" id="brokenEggs" name="broken_count" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="eggNotes" class="form-label">备注说明</label>
                        <textarea class="form-control" id="eggNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存记录</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 饲料投喂记录模态框 -->
<div class="modal fade" id="feedRecordModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">饲料投喂记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="feedRecordForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedFlock" class="form-label">选择鹅群 <span class="text-danger">*</span></label>
                                <select class="form-select" id="feedFlock" name="flock_id" required>
                                    <option value="">请选择鹅群</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedDate" class="form-label">投喂日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="feedDate" name="record_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedType" class="form-label">饲料类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="feedType" name="feed_type" required>
                                    <option value="">请选择类型</option>
                                    <option value="starter">雏鹅料</option>
                                    <option value="grower">生长料</option>
                                    <option value="layer">产蛋料</option>
                                    <option value="maintenance">维持料</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedAmount" class="form-label">投喂量 (kg) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="feedAmount" name="feed_amount" min="0" step="0.1" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedTime" class="form-label">投喂时间</label>
                                <select class="form-select" id="feedTime" name="feed_time">
                                    <option value="morning">上午</option>
                                    <option value="noon">中午</option>
                                    <option value="evening">下午</option>
                                    <option value="night">晚上</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="feedCost" class="form-label">饲料成本 (元)</label>
                                <input type="number" class="form-control" id="feedCost" name="cost" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="feedNotes" class="form-label">备注说明</label>
                        <textarea class="form-control" id="feedNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-info">保存记录</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentPage = 1;
    const pageSize = 10;
    let eggChart, flockChart;

    // 页面加载时初始化数据
    loadStatistics();
    loadFlocks();
    loadProductionRecords();
    initCharts();

    // 设置默认日期
    const today = new Date().toISOString().split('T')[0];
    $('#eggDate, #feedDate').val(today);
    
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    $('#startDate').val(oneWeekAgo.toISOString().split('T')[0]);
    $('#endDate').val(today);

    // 加载统计数据
    function loadStatistics() {
        $.ajax({
            url: '/api/production/statistics',
            method: 'GET',
            success: function(data) {
                $('#todayEggs').text(data.today_eggs || 0);
                $('#monthEggs').text(data.month_eggs || 0);
                $('#feedConsumption').text((data.feed_consumption || 0).toFixed(1));
                $('#eggRate').text((data.egg_rate || 0).toFixed(1) + '%');
            },
            error: function() {
                console.error('加载统计数据失败');
            }
        });
    }

    // 加载鹅群列表
    function loadFlocks() {
        $.ajax({
            url: '/api/flocks/active',
            method: 'GET',
            success: function(data) {
                const flockSelects = $('#eggFlock, #feedFlock, #flockFilter');
                flockSelects.find('option:not(:first)').remove();
                data.forEach(flock => {
                    flockSelects.append(`<option value="${flock.id}">${flock.name} (${flock.code})</option>`);
                });
            },
            error: function() {
                console.error('加载鹅群列表失败');
            }
        });
    }

    // 初始化图表
    function initCharts() {
        // 产蛋趋势图
        const eggCtx = document.getElementById('eggProductionChart').getContext('2d');
        eggChart = new Chart(eggCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '日产蛋量',
                    data: [],
                    borderColor: 'rgba(78, 115, 223, 1)',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '产蛋数量'
                        }
                    }
                }
            }
        });

        // 鹅群产蛋分布图
        const flockCtx = document.getElementById('flockProductionChart').getContext('2d');
        flockChart = new Chart(flockCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        loadChartData(7); // 默认加载7天数据
    }

    // 加载图表数据
    function loadChartData(period) {
        $.ajax({
            url: '/api/production/chart-data',
            method: 'GET',
            data: { period: period },
            success: function(data) {
                // 更新产蛋趋势图
                eggChart.data.labels = data.trend.labels;
                eggChart.data.datasets[0].data = data.trend.values;
                eggChart.update();

                // 更新鹅群分布图
                flockChart.data.labels = data.distribution.labels;
                flockChart.data.datasets[0].data = data.distribution.values;
                flockChart.update();
            },
            error: function() {
                console.error('加载图表数据失败');
            }
        });
    }

    // 加载生产记录
    function loadProductionRecords(page = 1) {
        const params = {
            page: page,
            pageSize: pageSize,
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val()
        };

        $.ajax({
            url: '/api/production/records',
            method: 'GET',
            data: params,
            success: function(data) {
                renderProductionTable(data.records);
                renderPagination(data.pagination);
            },
            error: function() {
                showAlert('加载生产记录失败', 'error');
            }
        });
    }

    // 渲染生产记录表格
    function renderProductionTable(records) {
        const tbody = $('#productionTableBody');
        tbody.empty();

        if (records.length === 0) {
            tbody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
            return;
        }

        records.forEach(record => {
            const typeClass = record.type === 'egg' ? 'badge bg-primary' : 'badge bg-info';
            const typeText = record.type === 'egg' ? '产蛋记录' : '饲料记录';
            
            const row = `
                <tr>
                    <td>${new Date(record.record_date).toLocaleDateString()}</td>
                    <td>${record.flock_name}</td>
                    <td><span class="${typeClass}">${typeText}</span></td>
                    <td>${record.quantity || record.amount || '-'}</td>
                    <td>${record.type === 'egg' ? '个' : 'kg'}</td>
                    <td>${record.quality_grade || '-'}</td>
                    <td>${record.recorder_name || '-'}</td>
                    <td>${record.notes || '-'}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editRecord(${record.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${record.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // 渲染分页
    function renderPagination(pagination) {
        const paginationEl = $('#pagination');
        paginationEl.empty();

        if (pagination.totalPages <= 1) return;

        // 上一页
        const prevDisabled = pagination.currentPage === 1 ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage - 1}">上一页</a>
            </li>
        `);

        // 页码
        for (let i = 1; i <= pagination.totalPages; i++) {
            const activeClass = i === pagination.currentPage ? 'active' : '';
            paginationEl.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        // 下一页
        const nextDisabled = pagination.currentPage === pagination.totalPages ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage + 1}">下一页</a>
            </li>
        `);

        currentPage = pagination.currentPage;
    }

    // 事件处理
    $(document).on('click', '.page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && page !== currentPage) {
            loadProductionRecords(page);
        }
    });

    // 时间范围选择
    $(document).on('click', '[data-period]', function(e) {
        e.preventDefault();
        const period = $(this).data('period');
        loadChartData(period);
    });

    // 搜索和重置
    $('#searchBtn').click(function() {
        loadProductionRecords(1);
    });

    $('#resetBtn').click(function() {
        $('#recordTypeFilter').val('');
        $('#flockFilter').val('');
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        $('#startDate').val(oneWeekAgo.toISOString().split('T')[0]);
        $('#endDate').val(new Date().toISOString().split('T')[0]);
        loadProductionRecords(1);
    });

    // 产蛋记录表单提交
    $('#eggProductionForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '/api/production/egg-records',
            method: 'POST',
            data: $(this).serialize(),
            success: function(data) {
                $('#eggProductionModal').modal('hide');
                $('#eggProductionForm')[0].reset();
                showAlert('产蛋记录保存成功', 'success');
                loadProductionRecords(currentPage);
                loadStatistics();
                loadChartData(7);
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '保存失败';
                showAlert(error, 'error');
            }
        });
    });

    // 饲料记录表单提交
    $('#feedRecordForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '/api/production/feed-records',
            method: 'POST',
            data: $(this).serialize(),
            success: function(data) {
                $('#feedRecordModal').modal('hide');
                $('#feedRecordForm')[0].reset();
                showAlert('饲料记录保存成功', 'success');
                loadProductionRecords(currentPage);
                loadStatistics();
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '保存失败';
                showAlert(error, 'error');
            }
        });
    });

    // 导出功能
    $('#exportExcel').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val(),
            format: 'excel'
        });
        window.open(`/api/production/export?${params}`);
    });

    $('#exportPdf').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            type: $('#recordTypeFilter').val(),
            flock_id: $('#flockFilter').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val(),
            format: 'pdf'
        });
        window.open(`/api/production/export?${params}`);
    });
});

// 编辑记录
function editRecord(recordId) {
    // 根据记录类型打开相应的编辑模态框
    $.ajax({
        url: `/api/production/records/${recordId}`,
        method: 'GET',
        success: function(data) {
            if (data.type === 'egg') {
                // 填充产蛋记录编辑表单
                $('#eggFlock').val(data.flock_id);
                $('#eggDate').val(data.record_date);
                $('#eggCount').val(data.egg_count);
                $('#eggWeight').val(data.total_weight);
                $('#eggGrade').val(data.quality_grade);
                $('#brokenEggs').val(data.broken_count);
                $('#eggNotes').val(data.notes);
                $('#eggProductionModal').modal('show');
            } else {
                // 填充饲料记录编辑表单
                $('#feedFlock').val(data.flock_id);
                $('#feedDate').val(data.record_date);
                $('#feedType').val(data.feed_type);
                $('#feedAmount').val(data.feed_amount);
                $('#feedTime').val(data.feed_time);
                $('#feedCost').val(data.cost);
                $('#feedNotes').val(data.notes);
                $('#feedRecordModal').modal('show');
            }
        },
        error: function() {
            showAlert('加载记录信息失败', 'error');
        }
    });
}

// 删除记录
function deleteRecord(recordId) {
    if (confirm('确定要删除这条记录吗？此操作不可恢复！')) {
        $.ajax({
            url: `/api/production/records/${recordId}`,
            method: 'DELETE',
            success: function() {
                showAlert('记录删除成功', 'success');
                loadProductionRecords(currentPage);
                loadStatistics();
                loadChartData(7);
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '删除失败';
                showAlert(error, 'error');
            }
        });
    }
}

// 显示提示消息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('body').append(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
