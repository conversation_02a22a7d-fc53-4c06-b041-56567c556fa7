<!-- 平台用户管理统计卡片 -->
<div class="row">
  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-primary">
      <div class="inner">
        <h3><%= stats.total %></h3>
        <p>总用户数</p>
      </div>
      <div class="icon">
        <i class="fas fa-users"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3><%= stats.active %></h3>
        <p>活跃用户</p>
      </div>
      <div class="icon">
        <i class="fas fa-user-check"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3><%= stats.admin %></h3>
        <p>管理员</p>
      </div>
      <div class="icon">
        <i class="fas fa-user-shield"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3><%= stats.newToday %></h3>
        <p>今日新增</p>
      </div>
      <div class="icon">
        <i class="fas fa-user-plus"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>
</div>

<!-- 用户列表 -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-users-cog mr-2"></i>平台用户列表
        </h3>
        <div class="card-tools">
          <a href="/platform-users/create" class="btn btn-primary btn-sm">
            <i class="fas fa-plus mr-1"></i>添加用户
          </a>
          <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#importModal">
            <i class="fas fa-upload mr-1"></i>批量导入
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped" id="usersTable">
            <thead>
              <tr>
                <th>用户名</th>
                <th>姓名</th>
                <th>邮箱</th>
                <th>角色</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>最后登录</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% if (users && users.length > 0) { %>
                <% users.forEach(function(user) { %>
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar-sm mr-2">
                        <img class="img-profile rounded-circle" 
                             src="<%= user.avatar || '/img/undraw_profile.svg' %>" 
                             style="width: 30px; height: 30px;">
                      </div>
                      <%= user.username %>
                    </div>
                  </td>
                  <td><%= user.name || '-' %></td>
                  <td><%= user.email || '-' %></td>
                  <td>
                    <span class="badge badge-<%= user.role === 'admin' ? 'danger' : user.role === 'manager' ? 'warning' : 'info' %>">
                      <%= user.role === 'admin' ? '超级管理员' : user.role === 'manager' ? '管理员' : user.role === 'support' ? '客服' : '普通用户' %>
                    </span>
                  </td>
                  <td>
                    <span class="badge badge-<%= user.status === 'active' ? 'success' : user.status === 'inactive' ? 'secondary' : 'danger' %>">
                      <%= user.status === 'active' ? '活跃' : user.status === 'inactive' ? '停用' : '锁定' %>
                    </span>
                  </td>
                  <td>
                    <% if (user.created_at) { %>
                      <%= new Date(user.created_at).toLocaleDateString('zh-CN') %>
                    <% } else { %>
                      -
                    <% } %>
                  </td>
                  <td>
                    <% if (user.last_login) { %>
                      <%= new Date(user.last_login).toLocaleDateString('zh-CN') %>
                    <% } else { %>
                      <span class="text-muted">从未登录</span>
                    <% } %>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="/platform-users/<%= user.id %>" class="btn btn-info" title="查看详情">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="/platform-users/<%= user.id %>/edit" class="btn btn-primary" title="编辑">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="/platform-users/<%= user.id %>/permissions" class="btn btn-warning" title="权限管理">
                        <i class="fas fa-key"></i>
                      </a>
                      <% if (user.status === 'active') { %>
                      <button class="btn btn-secondary" onclick="toggleUserStatus('<%= user.id %>', 'inactive')" title="停用">
                        <i class="fas fa-ban"></i>
                      </button>
                      <% } else { %>
                      <button class="btn btn-success" onclick="toggleUserStatus('<%= user.id %>', 'active')" title="启用">
                        <i class="fas fa-check"></i>
                      </button>
                      <% } %>
                    </div>
                  </td>
                </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center text-muted">暂无用户数据</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#usersTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese.json"
        },
        "order": [[ 5, "desc" ]], // 按创建时间排序
        "pageLength": 25
    });
});

function toggleUserStatus(userId, status) {
    if (confirm('确定要' + (status === 'active' ? '启用' : '停用') + '该用户吗？')) {
        $.ajax({
            url: '/platform-users/' + userId + '/status',
            method: 'POST',
            data: { status: status },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('操作失败：' + response.message);
                }
            },
            error: function() {
                alert('操作失败，请稍后重试');
            }
        });
    }
}
</script>
