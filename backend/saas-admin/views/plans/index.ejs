<!-- 套餐管理页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-layer-group me-2"></i>
                    套餐管理
                </h1>
                <p class="page-subtitle">管理订阅套餐和定价策略</p>
            </div>
        </div>
    </div>

    <!-- 套餐统计 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                基础版用户
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">45</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                标准版用户
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">32</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star-half-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                企业版用户
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">18</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                月收入
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">¥45,680</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 套餐卡片 -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-warning text-white text-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        基础版
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="pricing-price">
                        <span class="pricing-currency">¥</span>
                        <span class="pricing-amount">299</span>
                        <span class="pricing-period">/月</span>
                    </div>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li><i class="fas fa-check text-success me-2"></i>最多5个用户</li>
                        <li><i class="fas fa-check text-success me-2"></i>最多20个鹅群</li>
                        <li><i class="fas fa-check text-success me-2"></i>基础数据分析</li>
                        <li><i class="fas fa-check text-success me-2"></i>5GB存储空间</li>
                        <li><i class="fas fa-times text-danger me-2"></i>AI诊断功能</li>
                        <li><i class="fas fa-times text-danger me-2"></i>高级报表</li>
                    </ul>
                    <button class="btn btn-warning btn-block" onclick="editPlan('basic')">
                        <i class="fas fa-edit me-1"></i>
                        编辑套餐
                    </button>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">45个租户使用</small>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card shadow h-100 border-primary">
                <div class="card-header bg-primary text-white text-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star-half-alt me-2"></i>
                        标准版
                        <span class="badge badge-light ml-2">推荐</span>
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="pricing-price">
                        <span class="pricing-currency">¥</span>
                        <span class="pricing-amount">599</span>
                        <span class="pricing-period">/月</span>
                    </div>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li><i class="fas fa-check text-success me-2"></i>最多20个用户</li>
                        <li><i class="fas fa-check text-success me-2"></i>最多100个鹅群</li>
                        <li><i class="fas fa-check text-success me-2"></i>高级数据分析</li>
                        <li><i class="fas fa-check text-success me-2"></i>20GB存储空间</li>
                        <li><i class="fas fa-check text-success me-2"></i>AI诊断功能</li>
                        <li><i class="fas fa-times text-danger me-2"></i>定制报表</li>
                    </ul>
                    <button class="btn btn-primary btn-block" onclick="editPlan('standard')">
                        <i class="fas fa-edit me-1"></i>
                        编辑套餐
                    </button>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">32个租户使用</small>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-success text-white text-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-crown me-2"></i>
                        企业版
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="pricing-price">
                        <span class="pricing-currency">¥</span>
                        <span class="pricing-amount">1299</span>
                        <span class="pricing-period">/月</span>
                    </div>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li><i class="fas fa-check text-success me-2"></i>无限用户</li>
                        <li><i class="fas fa-check text-success me-2"></i>无限鹅群</li>
                        <li><i class="fas fa-check text-success me-2"></i>全功能数据分析</li>
                        <li><i class="fas fa-check text-success me-2"></i>100GB存储空间</li>
                        <li><i class="fas fa-check text-success me-2"></i>AI诊断功能</li>
                        <li><i class="fas fa-check text-success me-2"></i>定制报表</li>
                    </ul>
                    <button class="btn btn-success btn-block" onclick="editPlan('enterprise')">
                        <i class="fas fa-edit me-1"></i>
                        编辑套餐
                    </button>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">18个租户使用</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 套餐管理表格 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">套餐详细配置</h6>
            <button class="btn btn-primary btn-sm" onclick="createNewPlan()">
                <i class="fas fa-plus"></i> 创建新套餐
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>套餐名称</th>
                            <th>价格</th>
                            <th>用户限制</th>
                            <th>鹅群限制</th>
                            <th>存储空间</th>
                            <th>功能特性</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    基础版
                                </div>
                            </td>
                            <td><strong>¥299/月</strong></td>
                            <td>5个用户</td>
                            <td>20个鹅群</td>
                            <td>5GB</td>
                            <td>
                                <span class="badge badge-secondary">基础分析</span>
                            </td>
                            <td><span class="badge badge-success">启用</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editPlan('basic')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="togglePlan('basic')">
                                    <i class="fas fa-toggle-on"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-star-half-alt text-primary me-2"></i>
                                    标准版
                                </div>
                            </td>
                            <td><strong>¥599/月</strong></td>
                            <td>20个用户</td>
                            <td>100个鹅群</td>
                            <td>20GB</td>
                            <td>
                                <span class="badge badge-info">高级分析</span>
                                <span class="badge badge-success">AI诊断</span>
                            </td>
                            <td><span class="badge badge-success">启用</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editPlan('standard')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="togglePlan('standard')">
                                    <i class="fas fa-toggle-on"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-crown text-success me-2"></i>
                                    企业版
                                </div>
                            </td>
                            <td><strong>¥1299/月</strong></td>
                            <td>无限</td>
                            <td>无限</td>
                            <td>100GB</td>
                            <td>
                                <span class="badge badge-primary">全功能</span>
                                <span class="badge badge-success">AI诊断</span>
                                <span class="badge badge-warning">定制报表</span>
                            </td>
                            <td><span class="badge badge-success">启用</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editPlan('enterprise')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="togglePlan('enterprise')">
                                    <i class="fas fa-toggle-on"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.pricing-price {
    font-size: 2rem;
    margin: 1rem 0;
}

.pricing-currency {
    font-size: 1.2rem;
    vertical-align: top;
}

.pricing-amount {
    font-weight: bold;
}

.pricing-period {
    font-size: 1rem;
    color: #6c757d;
}

.card.border-primary {
    border-width: 2px;
}
</style>

<script>
// 编辑套餐
function editPlan(planType) {
    alert(`编辑${planType}套餐功能开发中...`);
}

// 切换套餐状态
function togglePlan(planType) {
    if (confirm(`确定要切换${planType}套餐的状态吗？`)) {
        alert(`${planType}套餐状态已切换`);
    }
}

// 创建新套餐
function createNewPlan() {
    alert('创建新套餐功能开发中...');
}
</script>
