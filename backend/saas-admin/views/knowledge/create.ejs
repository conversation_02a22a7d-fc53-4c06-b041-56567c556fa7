<!-- 创建知识文章页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-plus me-2"></i>
                    创建知识文章
                </h2>
                <a href="/knowledge" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文章内容</h3>
                </div>
                <div class="card-body">
                    <form id="knowledgeForm">
                        <div class="form-group">
                            <label for="title">文章标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="请输入文章标题" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">文章分类 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">请选择分类</option>
                                        <option value="disease">疾病防治</option>
                                        <option value="breeding">繁殖技术</option>
                                        <option value="feeding">饲养管理</option>
                                        <option value="environment">环境控制</option>
                                        <option value="management">生产管理</option>
                                        <option value="nutrition">营养配方</option>
                                        <option value="equipment">设备使用</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="difficulty">难度等级</label>
                                    <select class="form-control" id="difficulty" name="difficulty">
                                        <option value="beginner">初级</option>
                                        <option value="intermediate">中级</option>
                                        <option value="advanced">高级</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="readTime">预计阅读时间（分钟）</label>
                                    <input type="number" class="form-control" id="readTime" name="readTime" 
                                           min="1" max="120" placeholder="5">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="keywords">关键词</label>
                                    <input type="text" class="form-control" id="keywords" name="keywords" 
                                           placeholder="用逗号分隔多个关键词">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="tags">标签</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   placeholder="用逗号分隔多个标签">
                        </div>

                        <div class="form-group">
                            <label for="content">文章内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="15" 
                                      placeholder="请输入文章内容，支持Markdown格式" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="summary">文章摘要</label>
                            <textarea class="form-control" id="summary" name="summary" rows="3" 
                                      placeholder="请输入文章摘要，用于搜索和预览"></textarea>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 发布设置 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布设置</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="status">发布状态</label>
                        <select class="form-control" id="status" name="status">
                            <option value="draft">草稿</option>
                            <option value="published">立即发布</option>
                            <option value="scheduled">定时发布</option>
                        </select>
                    </div>

                    <div class="form-group" id="publishTimeGroup" style="display: none;">
                        <label for="publishTime">发布时间</label>
                        <input type="datetime-local" class="form-control" id="publishTime" name="publishTime">
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="isRecommended" name="isRecommended">
                            <label class="custom-control-label" for="isRecommended">推荐文章</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="allowComments" name="allowComments" checked>
                            <label class="custom-control-label" for="allowComments">允许评论</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 媒体文件 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">媒体文件</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="featuredImage">特色图片</label>
                        <input type="file" class="form-control-file" id="featuredImage" name="featuredImage" 
                               accept="image/*">
                        <small class="form-text text-muted">建议尺寸：800x400px</small>
                    </div>

                    <div class="form-group">
                        <label for="images">文章图片</label>
                        <input type="file" class="form-control-file" id="images" name="images" 
                               accept="image/*" multiple>
                        <small class="form-text text-muted">可选择多张图片</small>
                    </div>

                    <div class="form-group">
                        <label for="attachments">附件文档</label>
                        <input type="file" class="form-control-file" id="attachments" name="attachments" 
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx" multiple>
                        <small class="form-text text-muted">支持PDF、Office文档</small>
                    </div>
                </div>
            </div>

            <!-- 相关文章 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">相关文章</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="relatedArticles">选择相关文章</label>
                        <select class="form-control" id="relatedArticles" name="relatedArticles" multiple>
                            <!-- 这里会通过JavaScript动态加载文章列表 -->
                        </select>
                        <small class="form-text text-muted">按住Ctrl键可多选</small>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <button type="button" class="btn btn-primary btn-block" onclick="saveArticle()">
                        <i class="fas fa-save me-1"></i>
                        保存文章
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-block mt-2" onclick="previewArticle()">
                        <i class="fas fa-eye me-1"></i>
                        预览文章
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-block mt-2" onclick="resetForm()">
                        <i class="fas fa-undo me-1"></i>
                        重置表单
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 状态变化处理
document.getElementById('status').addEventListener('change', function() {
    const publishTimeGroup = document.getElementById('publishTimeGroup');
    if (this.value === 'scheduled') {
        publishTimeGroup.style.display = 'block';
        document.getElementById('publishTime').required = true;
    } else {
        publishTimeGroup.style.display = 'none';
        document.getElementById('publishTime').required = false;
    }
});

// 保存文章
function saveArticle() {
    const form = document.getElementById('knowledgeForm');
    const formData = new FormData(form);
    
    // 表单验证
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 处理标签和关键词
    const tags = formData.get('tags');
    if (tags) {
        formData.set('tags', JSON.stringify(tags.split(',').map(tag => tag.trim())));
    }
    
    // 显示加载状态
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    saveBtn.disabled = true;
    
    // 发送请求
    fetch('/knowledge', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('文章保存成功！');
            window.location.href = '/knowledge';
        } else {
            alert('保存失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        alert('保存失败，请稍后重试');
    })
    .finally(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// 预览文章
function previewArticle() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    
    if (!title || !content) {
        alert('请先填写标题和内容');
        return;
    }
    
    // 打开预览窗口
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
        <head>
            <title>预览：${title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                .content { margin-top: 20px; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <div class="content">${content.replace(/\n/g, '<br>')}</div>
        </body>
        </html>
    `);
}

// 重置表单
function resetForm() {
    if (confirm('确定要重置表单吗？所有输入的内容将丢失。')) {
        document.getElementById('knowledgeForm').reset();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载相关文章列表
    loadRelatedArticles();
});

// 加载相关文章列表
function loadRelatedArticles() {
    fetch('/api/knowledge/articles')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('relatedArticles');
                data.data.articles.forEach(article => {
                    const option = document.createElement('option');
                    option.value = article.id;
                    option.textContent = article.title;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载相关文章失败:', error);
        });
}
</script>
