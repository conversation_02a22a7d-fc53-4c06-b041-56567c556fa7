<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book me-2"></i>知识库管理</h2>
                <div>
                    <a href="/knowledge/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>创建文章
                    </a>
                    <a href="/knowledge/categories" class="btn btn-outline-secondary">
                        <i class="fas fa-tags me-1"></i>分类管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><%= stats.total %></h3>
                    <p>总文章数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><%= stats.published %></h3>
                    <p>已发布</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3><%= stats.draft %></h3>
                    <p>草稿</p>
                </div>
                <div class="icon">
                    <i class="fas fa-edit"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3><%= stats.views %></h3>
                    <p>总浏览量</p>
                </div>
                <div class="icon">
                    <i class="fas fa-eye"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 文章列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">知识文章列表</h3>
                    <div class="card-tools">
                        <div class="input-group input-group-sm" style="width: 250px;">
                            <input type="text" name="search" class="form-control float-right" placeholder="搜索文章...">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>分类</th>
                                <th>状态</th>
                                <th>浏览量</th>
                                <th>作者</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% articles.forEach(article => { %>
                            <tr>
                                <td><%= article.id %></td>
                                <td>
                                    <a href="/knowledge/<%= article.id %>/edit" class="text-decoration-none">
                                        <%= article.title %>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge badge-info"><%= article.category %></span>
                                </td>
                                <td>
                                    <% if (article.status === 'published') { %>
                                        <span class="badge badge-success">已发布</span>
                                    <% } else if (article.status === 'draft') { %>
                                        <span class="badge badge-warning">草稿</span>
                                    <% } else { %>
                                        <span class="badge badge-secondary"><%= article.status %></span>
                                    <% } %>
                                </td>
                                <td><%= article.views || 0 %></td>
                                <td><%= article.author || '系统管理员' %></td>
                                <td><%= new Date(article.created_at).toLocaleDateString() %></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/knowledge/<%= article.id %>/edit" class="btn btn-info btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteArticle(<%= article.id %>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <% if (typeof isDemo !== 'undefined' && isDemo) { %>
    <div class="alert alert-info mt-3">
        <i class="fas fa-info-circle me-2"></i>
        当前显示的是演示数据，实际使用时将从数据库加载真实数据。
    </div>
    <% } %>
</div>

<script>
function deleteArticle(id) {
    if (confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
        fetch(`/knowledge/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请稍后重试');
        });
    }
}

// 搜索功能
document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const searchTerm = this.value;
        window.location.href = `/knowledge?search=${encodeURIComponent(searchTerm)}`;
    }
});
</script>
