<!-- 编辑知识文章页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-edit me-2"></i>
                    编辑知识文章
                </h2>
                <div>
                    <a href="/knowledge/<%= article.id %>" class="btn btn-outline-info me-2" target="_blank">
                        <i class="fas fa-eye me-1"></i>
                        查看文章
                    </a>
                    <a href="/knowledge" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文章内容</h3>
                </div>
                <div class="card-body">
                    <form id="knowledgeForm">
                        <input type="hidden" id="articleId" value="<%= article.id %>">
                        
                        <div class="form-group">
                            <label for="title">文章标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<%= article.title %>" placeholder="请输入文章标题" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">文章分类 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">请选择分类</option>
                                        <option value="disease" <%= article.category === 'disease' ? 'selected' : '' %>>疾病防治</option>
                                        <option value="breeding" <%= article.category === 'breeding' ? 'selected' : '' %>>繁殖技术</option>
                                        <option value="feeding" <%= article.category === 'feeding' ? 'selected' : '' %>>饲养管理</option>
                                        <option value="environment" <%= article.category === 'environment' ? 'selected' : '' %>>环境控制</option>
                                        <option value="management" <%= article.category === 'management' ? 'selected' : '' %>>生产管理</option>
                                        <option value="nutrition" <%= article.category === 'nutrition' ? 'selected' : '' %>>营养配方</option>
                                        <option value="equipment" <%= article.category === 'equipment' ? 'selected' : '' %>>设备使用</option>
                                        <option value="other" <%= article.category === 'other' ? 'selected' : '' %>>其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="difficulty">难度等级</label>
                                    <select class="form-control" id="difficulty" name="difficulty">
                                        <option value="beginner" <%= article.difficulty === 'beginner' ? 'selected' : '' %>>初级</option>
                                        <option value="intermediate" <%= article.difficulty === 'intermediate' ? 'selected' : '' %>>中级</option>
                                        <option value="advanced" <%= article.difficulty === 'advanced' ? 'selected' : '' %>>高级</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="readTime">预计阅读时间（分钟）</label>
                                    <input type="number" class="form-control" id="readTime" name="readTime" 
                                           value="<%= article.readTime || '' %>" min="1" max="120" placeholder="5">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="keywords">关键词</label>
                                    <input type="text" class="form-control" id="keywords" name="keywords" 
                                           value="<%= article.keywords || '' %>" placeholder="用逗号分隔多个关键词">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="tags">标签</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   value="<%= Array.isArray(article.tags) ? article.tags.join(', ') : (article.tags || '') %>" 
                                   placeholder="用逗号分隔多个标签">
                        </div>

                        <div class="form-group">
                            <label for="content">文章内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="15" 
                                      placeholder="请输入文章内容，支持Markdown格式" required><%= article.content %></textarea>
                        </div>

                        <div class="form-group">
                            <label for="summary">文章摘要</label>
                            <textarea class="form-control" id="summary" name="summary" rows="3" 
                                      placeholder="请输入文章摘要，用于搜索和预览"><%= article.summary || '' %></textarea>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 文章信息 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文章信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>文章ID:</strong> <%= article.id %>
                    </div>
                    <div class="info-item">
                        <strong>创建时间:</strong> <%= new Date(article.created_at || article.createdAt).toLocaleString() %>
                    </div>
                    <div class="info-item">
                        <strong>更新时间:</strong> <%= new Date(article.updated_at || article.updatedAt).toLocaleString() %>
                    </div>
                    <div class="info-item">
                        <strong>浏览次数:</strong> <%= article.views || 0 %>
                    </div>
                    <div class="info-item">
                        <strong>作者:</strong> <%= article.author || '系统管理员' %>
                    </div>
                </div>
            </div>

            <!-- 发布设置 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布设置</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="status">发布状态</label>
                        <select class="form-control" id="status" name="status">
                            <option value="draft" <%= article.status === 'draft' ? 'selected' : '' %>>草稿</option>
                            <option value="published" <%= article.status === 'published' ? 'selected' : '' %>>已发布</option>
                            <option value="scheduled" <%= article.status === 'scheduled' ? 'selected' : '' %>>定时发布</option>
                        </select>
                    </div>

                    <div class="form-group" id="publishTimeGroup" style="display: <%= article.status === 'scheduled' ? 'block' : 'none' %>;">
                        <label for="publishTime">发布时间</label>
                        <input type="datetime-local" class="form-control" id="publishTime" name="publishTime" 
                               value="<%= article.publishTime ? new Date(article.publishTime).toISOString().slice(0, 16) : '' %>">
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="isRecommended" name="isRecommended" 
                                   <%= article.isRecommended ? 'checked' : '' %>>
                            <label class="custom-control-label" for="isRecommended">推荐文章</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="allowComments" name="allowComments" 
                                   <%= article.allowComments !== false ? 'checked' : '' %>>
                            <label class="custom-control-label" for="allowComments">允许评论</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 媒体文件 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">媒体文件</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="featuredImage">特色图片</label>
                        <% if (article.featuredImage) { %>
                        <div class="current-image mb-2">
                            <img src="<%= article.featuredImage %>" alt="当前特色图片" class="img-thumbnail" style="max-width: 200px;">
                            <button type="button" class="btn btn-sm btn-danger ml-2" onclick="removeImage('featured')">删除</button>
                        </div>
                        <% } %>
                        <input type="file" class="form-control-file" id="featuredImage" name="featuredImage" 
                               accept="image/*">
                        <small class="form-text text-muted">建议尺寸：800x400px</small>
                    </div>

                    <div class="form-group">
                        <label for="images">文章图片</label>
                        <% if (article.images && article.images.length > 0) { %>
                        <div class="current-images mb-2">
                            <% article.images.forEach((image, index) => { %>
                            <div class="image-item d-inline-block mr-2 mb-2">
                                <img src="<%= image %>" alt="文章图片" class="img-thumbnail" style="max-width: 100px;">
                                <button type="button" class="btn btn-sm btn-danger" onclick="removeImage('images', <%= index %>)">×</button>
                            </div>
                            <% }) %>
                        </div>
                        <% } %>
                        <input type="file" class="form-control-file" id="images" name="images" 
                               accept="image/*" multiple>
                        <small class="form-text text-muted">可选择多张图片</small>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <button type="button" class="btn btn-primary btn-block" onclick="updateArticle()">
                        <i class="fas fa-save me-1"></i>
                        更新文章
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-block mt-2" onclick="previewArticle()">
                        <i class="fas fa-eye me-1"></i>
                        预览文章
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-block mt-2" onclick="deleteArticle()">
                        <i class="fas fa-trash me-1"></i>
                        删除文章
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.image-item {
    position: relative;
}

.image-item .btn {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 50%;
    font-size: 12px;
    line-height: 1;
}
</style>

<script>
// 状态变化处理
document.getElementById('status').addEventListener('change', function() {
    const publishTimeGroup = document.getElementById('publishTimeGroup');
    if (this.value === 'scheduled') {
        publishTimeGroup.style.display = 'block';
        document.getElementById('publishTime').required = true;
    } else {
        publishTimeGroup.style.display = 'none';
        document.getElementById('publishTime').required = false;
    }
});

// 更新文章
function updateArticle() {
    const form = document.getElementById('knowledgeForm');
    const formData = new FormData(form);
    const articleId = document.getElementById('articleId').value;
    
    // 表单验证
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 处理标签和关键词
    const tags = formData.get('tags');
    if (tags) {
        formData.set('tags', JSON.stringify(tags.split(',').map(tag => tag.trim())));
    }
    
    // 显示加载状态
    const updateBtn = event.target;
    const originalText = updateBtn.innerHTML;
    updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>更新中...';
    updateBtn.disabled = true;
    
    // 发送请求
    fetch(`/knowledge/${articleId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('文章更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请稍后重试');
    })
    .finally(() => {
        updateBtn.innerHTML = originalText;
        updateBtn.disabled = false;
    });
}

// 预览文章
function previewArticle() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    
    if (!title || !content) {
        alert('请先填写标题和内容');
        return;
    }
    
    // 打开预览窗口
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
        <head>
            <title>预览：${title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                .content { margin-top: 20px; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <div class="content">${content.replace(/\n/g, '<br>')}</div>
        </body>
        </html>
    `);
}

// 删除文章
function deleteArticle() {
    if (confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
        const articleId = document.getElementById('articleId').value;
        
        fetch(`/knowledge/${articleId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('文章删除成功！');
                window.location.href = '/knowledge';
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            alert('删除失败，请稍后重试');
        });
    }
}

// 删除图片
function removeImage(type, index) {
    if (confirm('确定要删除这张图片吗？')) {
        // 这里应该发送请求到后端删除图片
        alert('删除图片功能开发中...');
    }
}
</script>
