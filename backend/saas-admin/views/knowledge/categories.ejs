<!-- 知识库分类管理页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-tags me-2"></i>
                    知识库分类管理
                </h2>
                <div>
                    <button class="btn btn-primary" onclick="showAddCategoryModal()">
                        <i class="fas fa-plus me-1"></i>
                        添加分类
                    </button>
                    <a href="/knowledge" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        返回知识库
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类统计 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                总分类数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><%= categories.length %></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                总文章数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <%= categories.reduce((sum, cat) => sum + (cat.article_count || 0), 0) %>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                最热门分类
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <%= categories.length > 0 ? categories.reduce((max, cat) => (cat.article_count || 0) > (max.article_count || 0) ? cat : max).name : '暂无' %>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-fire fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                平均文章数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <%= categories.length > 0 ? Math.round(categories.reduce((sum, cat) => sum + (cat.article_count || 0), 0) / categories.length) : 0 %>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">分类列表</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>排序</th>
                            <th>分类名称</th>
                            <th>描述</th>
                            <th>文章数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="categoriesTableBody">
                        <% categories.forEach((category, index) => { %>
                        <tr data-id="<%= category.id %>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="sort-order"><%= category.sort_order || index + 1 %></span>
                                    <div class="ml-2">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="moveCategory(<%= category.id %>, 'up')" 
                                                <%= index === 0 ? 'disabled' : '' %>>
                                            <i class="fas fa-arrow-up"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary ml-1" onclick="moveCategory(<%= category.id %>, 'down')" 
                                                <%= index === categories.length - 1 ? 'disabled' : '' %>>
                                            <i class="fas fa-arrow-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    <strong><%= category.name %></strong>
                                </div>
                            </td>
                            <td><%= category.description || '暂无描述' %></td>
                            <td>
                                <span class="badge badge-info"><%= category.article_count || 0 %> 篇</span>
                            </td>
                            <td><%= category.created_at ? new Date(category.created_at).toLocaleDateString() : '未知' %></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editCategory(<%= category.id %>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteCategory(<%= category.id %>)" 
                                            <%= (category.article_count || 0) > 0 ? 'disabled title="该分类下还有文章，无法删除"' : '' %>>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <% }) %>
                        
                        <% if (categories.length === 0) { %>
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>暂无分类数据</p>
                                <button class="btn btn-primary" onclick="showAddCategoryModal()">
                                    <i class="fas fa-plus me-1"></i>
                                    添加第一个分类
                                </button>
                            </td>
                        </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <% if (typeof isDemo !== 'undefined' && isDemo) { %>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        当前显示的是演示数据，实际使用时将从数据库加载真实数据。
    </div>
    <% } %>
</div>

<!-- 添加/编辑分类模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" name="id">
                    
                    <div class="form-group">
                        <label for="categoryName">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="name" 
                               placeholder="请输入分类名称" required>
                    </div>

                    <div class="form-group">
                        <label for="categoryDescription">分类描述</label>
                        <textarea class="form-control" id="categoryDescription" name="description" 
                                  rows="3" placeholder="请输入分类描述"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="categoryIcon">分类图标</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">
                                    <i id="iconPreview" class="fas fa-tag"></i>
                                </span>
                            </div>
                            <input type="text" class="form-control" id="categoryIcon" name="icon" 
                                   placeholder="fas fa-tag" value="fas fa-tag">
                        </div>
                        <small class="form-text text-muted">使用FontAwesome图标类名</small>
                    </div>

                    <div class="form-group">
                        <label for="categoryColor">分类颜色</label>
                        <input type="color" class="form-control" id="categoryColor" name="color" value="#007bff">
                    </div>

                    <div class="form-group">
                        <label for="sortOrder">排序顺序</label>
                        <input type="number" class="form-control" id="sortOrder" name="sort_order" 
                               min="1" placeholder="数字越小排序越靠前">
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="isActive" name="is_active" checked>
                            <label class="custom-control-label" for="isActive">启用分类</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">
                    <span class="btn-text">保存</span>
                    <span class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 保存中...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 显示添加分类模态框
function showAddCategoryModal() {
    document.getElementById('categoryModalTitle').textContent = '添加分类';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('categoryIcon').value = 'fas fa-tag';
    document.getElementById('iconPreview').className = 'fas fa-tag';
    document.getElementById('categoryColor').value = '#007bff';
    
    const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
    modal.show();
}

// 编辑分类
function editCategory(id) {
    // 这里应该从后端获取分类详情
    // 暂时使用模拟数据
    const mockCategory = {
        id: id,
        name: '示例分类',
        description: '这是一个示例分类',
        icon: 'fas fa-tag',
        color: '#007bff',
        sort_order: 1,
        is_active: true
    };
    
    document.getElementById('categoryModalTitle').textContent = '编辑分类';
    document.getElementById('categoryId').value = mockCategory.id;
    document.getElementById('categoryName').value = mockCategory.name;
    document.getElementById('categoryDescription').value = mockCategory.description;
    document.getElementById('categoryIcon').value = mockCategory.icon;
    document.getElementById('iconPreview').className = mockCategory.icon;
    document.getElementById('categoryColor').value = mockCategory.color;
    document.getElementById('sortOrder').value = mockCategory.sort_order;
    document.getElementById('isActive').checked = mockCategory.is_active;
    
    const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
    modal.show();
}

// 保存分类
function saveCategory() {
    const form = document.getElementById('categoryForm');
    const formData = new FormData(form);
    const categoryId = document.getElementById('categoryId').value;
    const isEdit = categoryId !== '';
    
    // 表单验证
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 显示加载状态
    const saveBtn = document.querySelector('#categoryModal .btn-primary');
    const btnText = saveBtn.querySelector('.btn-text');
    const btnLoading = saveBtn.querySelector('.btn-loading');
    
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline';
    saveBtn.disabled = true;
    
    // 准备数据
    const categoryData = Object.fromEntries(formData);
    
    // API调用
    const url = isEdit ? `/knowledge/categories/${categoryId}` : '/knowledge/categories';
    const method = isEdit ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(categoryData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(isEdit ? '分类更新成功' : '分类创建成功');
            location.reload();
        } else {
            alert(data.message || '操作失败');
        }
    })
    .catch(error => {
        console.error('保存分类失败:', error);
        alert('网络错误，请稍后重试');
    })
    .finally(() => {
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        saveBtn.disabled = false;
    });
}

// 删除分类
function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？删除后无法恢复。')) {
        fetch(`/knowledge/categories/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('分类删除成功');
                location.reload();
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('删除分类失败:', error);
            alert('网络错误，请稍后重试');
        });
    }
}

// 移动分类排序
function moveCategory(id, direction) {
    fetch(`/knowledge/categories/${id}/move`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ direction })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '移动失败');
        }
    })
    .catch(error => {
        console.error('移动分类失败:', error);
        alert('网络错误，请稍后重试');
    });
}

// 图标预览
document.getElementById('categoryIcon').addEventListener('input', function() {
    const iconPreview = document.getElementById('iconPreview');
    iconPreview.className = this.value || 'fas fa-tag';
});
</script>
