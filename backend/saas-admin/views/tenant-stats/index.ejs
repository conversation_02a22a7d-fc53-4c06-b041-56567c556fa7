<!-- 租户统计页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    租户统计
                </h1>
                <p class="page-subtitle">查看租户使用情况和统计数据</p>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                总租户数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">156</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                活跃租户
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">142</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                本月新增
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">18</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plus-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                总收入
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">¥128,450</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">租户增长趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="tenantGrowthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">订阅计划分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="subscriptionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 租户活跃度表格 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">租户活跃度排行</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>租户名称</th>
                            <th>订阅计划</th>
                            <th>用户数</th>
                            <th>月活跃度</th>
                            <th>最后登录</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>示例养殖场A</td>
                            <td><span class="badge badge-success">企业版</span></td>
                            <td>25</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 85%"></div>
                                </div>
                                85%
                            </td>
                            <td>2024-01-15 14:30</td>
                            <td><span class="badge badge-success">活跃</span></td>
                        </tr>
                        <tr>
                            <td>绿色养殖场</td>
                            <td><span class="badge badge-primary">标准版</span></td>
                            <td>12</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 72%"></div>
                                </div>
                                72%
                            </td>
                            <td>2024-01-14 09:15</td>
                            <td><span class="badge badge-success">活跃</span></td>
                        </tr>
                        <tr>
                            <td>智慧农业科技</td>
                            <td><span class="badge badge-warning">基础版</span></td>
                            <td>8</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 45%"></div>
                                </div>
                                45%
                            </td>
                            <td>2024-01-10 16:20</td>
                            <td><span class="badge badge-warning">低活跃</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// 租户增长趋势图表
const ctx1 = document.getElementById('tenantGrowthChart').getContext('2d');
const tenantGrowthChart = new Chart(ctx1, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [{
            label: '新增租户',
            data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 38, 42, 45],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 订阅计划分布图表
const ctx2 = document.getElementById('subscriptionChart').getContext('2d');
const subscriptionChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['基础版', '标准版', '企业版'],
        datasets: [{
            data: [45, 32, 18],
            backgroundColor: [
                '#f6c23e',
                '#36b9cc',
                '#1cc88a'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
