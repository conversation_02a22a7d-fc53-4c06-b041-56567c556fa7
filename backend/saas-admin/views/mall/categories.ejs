<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tags me-2"></i>商品分类管理</h2>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                        <i class="fas fa-plus me-1"></i>添加分类
                    </button>
                    <a href="/mall/products" class="btn btn-info">
                        <i class="fas fa-cube me-1"></i>商品管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总分类数</h5>
                            <h3><%= stats.totalCategories %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>启用分类</h5>
                            <h3><%= stats.activeCategories %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>有商品分类</h5>
                            <h3><%= stats.categoriesWithProducts %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-cube fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>空分类</h5>
                            <h3><%= stats.emptyCategories %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-inbox fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i>分类列表</h5>
                    <div class="d-flex">
                        <select class="form-select me-2" id="statusFilter" style="width: 150px;">
                            <option value="">全部状态</option>
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索分类..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">排序</th>
                                    <th>分类信息</th>
                                    <th>父级分类</th>
                                    <th>商品数量</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTable">
                                <% categories.forEach(category => { %>
                                    <tr data-category-id="<%= category.id %>">
                                        <td>
                                            <input type="number" class="form-control form-control-sm" 
                                                   value="<%= category.sort_order || 0 %>" 
                                                   onchange="updateSort(<%= category.id %>, this.value)"
                                                   style="width: 70px;">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <% if (category.icon) { %>
                                                        <img src="<%= category.icon %>" class="rounded" width="40" height="40">
                                                    <% } else { %>
                                                        <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-tag text-white"></i>
                                                        </div>
                                                    <% } %>
                                                </div>
                                                <div>
                                                    <strong><%= category.name %></strong><br>
                                                    <% if (category.description) { %>
                                                        <small class="text-muted"><%= category.description %></small>
                                                    <% } %>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (category.parent_name) { %>
                                                <span class="badge bg-secondary"><%= category.parent_name %></span>
                                            <% } else { %>
                                                <span class="text-muted">顶级分类</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><%= category.product_count || 0 %></span>
                                        </td>
                                        <td>
                                            <% if (category.status === 'active') { %>
                                                <span class="badge bg-success">启用</span>
                                            <% } else { %>
                                                <span class="badge bg-danger">禁用</span>
                                            <% } %>
                                        </td>
                                        <td><%= Utils.formatDate(category.created_at, 'YYYY-MM-DD') %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editCategory(<%= category.id %>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="addSubCategory(<%= category.id %>)">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <% if (category.status === 'active') { %>
                                                    <button class="btn btn-outline-warning" onclick="toggleCategoryStatus(<%= category.id %>, 'inactive')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                <% } else { %>
                                                    <button class="btn btn-outline-success" onclick="toggleCategoryStatus(<%= category.id %>, 'active')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                <% } %>
                                                <button class="btn btn-outline-danger" onclick="deleteCategory(<%= category.id %>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建分类模态框 -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCategoryForm">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="categoryName" class="form-label">分类名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="categoryName" name="name" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="categoryStatus" class="form-label">状态</label>
                            <select class="form-select" id="categoryStatus" name="status">
                                <option value="active" selected>启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parentCategory" class="form-label">父级分类</label>
                            <select class="form-select" id="parentCategory" name="parent_id">
                                <option value="">无（顶级分类）</option>
                                <% categories.filter(c => !c.parent_id).forEach(category => { %>
                                    <option value="<%= category.id %>"><%= category.name %></option>
                                <% }) %>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sortOrder" class="form-label">排序</label>
                            <input type="number" class="form-control" id="sortOrder" name="sort_order" value="0" min="0">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">分类描述</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="categoryIcon" class="form-label">分类图标</label>
                        <input type="file" class="form-control" id="categoryIcon" name="icon" accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 100x100 像素</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createCategory()">创建分类</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑分类模态框 -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm">
                    <input type="hidden" id="editCategoryId" name="category_id">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="editCategoryName" class="form-label">分类名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editCategoryName" name="name" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="editCategoryStatus" class="form-label">状态</label>
                            <select class="form-select" id="editCategoryStatus" name="status">
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editParentCategory" class="form-label">父级分类</label>
                            <select class="form-select" id="editParentCategory" name="parent_id">
                                <option value="">无（顶级分类）</option>
                                <% categories.filter(c => !c.parent_id).forEach(category => { %>
                                    <option value="<%= category.id %>"><%= category.name %></option>
                                <% }) %>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editSortOrder" class="form-label">排序</label>
                            <input type="number" class="form-control" id="editSortOrder" name="sort_order" min="0">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editCategoryDescription" class="form-label">分类描述</label>
                        <textarea class="form-control" id="editCategoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="editCategoryIcon" class="form-label">分类图标</label>
                        <input type="file" class="form-control" id="editCategoryIcon" name="icon" accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 100x100 像素</div>
                        <div id="currentIcon" style="margin-top: 10px;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateCategory()">保存更改</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 筛选和搜索功能
    document.getElementById('statusFilter').addEventListener('change', filterCategories);
    document.getElementById('searchInput').addEventListener('input', debounce(filterCategories, 500));
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function filterCategories() {
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    url.searchParams.set('search', search);
    window.location.href = url.toString();
}

function createCategory() {
    const form = document.getElementById('createCategoryForm');
    const formData = new FormData(form);
    
    fetch('/mall/categories/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('分类创建成功');
            location.reload();
        } else {
            alert('创建失败：' + data.message);
        }
    });
}

function editCategory(categoryId) {
    fetch(`/mall/categories/${categoryId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const category = data.data;
            document.getElementById('editCategoryId').value = category.id;
            document.getElementById('editCategoryName').value = category.name;
            document.getElementById('editCategoryStatus').value = category.status;
            document.getElementById('editParentCategory').value = category.parent_id || '';
            document.getElementById('editSortOrder').value = category.sort_order;
            document.getElementById('editCategoryDescription').value = category.description || '';
            
            const currentIcon = document.getElementById('currentIcon');
            if (category.icon) {
                currentIcon.innerHTML = `<img src="${category.icon}" class="rounded" width="60" height="60">`;
            } else {
                currentIcon.innerHTML = '';
            }
            
            const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
            modal.show();
        }
    });
}

function updateCategory() {
    const form = document.getElementById('editCategoryForm');
    const formData = new FormData(form);
    const categoryId = formData.get('category_id');
    
    fetch(`/mall/categories/${categoryId}/update`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('分类更新成功');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    });
}

function addSubCategory(parentId) {
    document.getElementById('parentCategory').value = parentId;
    const modal = new bootstrap.Modal(document.getElementById('createCategoryModal'));
    modal.show();
}

function updateSort(categoryId, sortOrder) {
    fetch(`/mall/categories/${categoryId}/sort`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sort_order: parseInt(sortOrder) })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('排序更新失败：' + data.message);
        }
    });
}

function toggleCategoryStatus(categoryId, status) {
    const action = status === 'active' ? '启用' : '禁用';
    if (confirm(`确定要${action}该分类吗？`)) {
        fetch(`/mall/categories/${categoryId}/status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        });
    }
}

function deleteCategory(categoryId) {
    if (confirm('确定要删除该分类吗？此操作不可恢复，且会影响该分类下的商品！')) {
        fetch(`/mall/categories/${categoryId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        });
    }
}
</script>