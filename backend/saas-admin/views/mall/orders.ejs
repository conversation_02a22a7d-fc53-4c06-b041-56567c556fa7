<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shopping-cart me-2"></i>订单管理</h2>
                <div>
                    <button class="btn btn-info me-2" onclick="exportOrders()">
                        <i class="fas fa-download me-1"></i>导出订单
                    </button>
                    <a href="/mall/products" class="btn btn-primary">
                        <i class="fas fa-cube me-1"></i>商品管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单统计 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总订单数</h5>
                            <h3><%= stats.totalOrders %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>待处理</h5>
                            <h3><%= stats.pendingOrders %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>今日销售额</h5>
                            <h3>¥<%= Utils.formatNumber(stats.todaySales) %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-yen-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>本月销售额</h5>
                            <h3>¥<%= Utils.formatNumber(stats.monthlySales) %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i>订单列表</h5>
                    <div class="d-flex">
                        <select class="form-select me-2" id="statusFilter" style="width: 150px;">
                            <option value="">全部状态</option>
                            <option value="pending">待付款</option>
                            <option value="paid">已付款</option>
                            <option value="shipped">已发货</option>
                            <option value="delivered">已送达</option>
                            <option value="cancelled">已取消</option>
                            <option value="refunded">已退款</option>
                        </select>
                        <select class="form-select me-2" id="timeFilter" style="width: 150px;">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                        <input type="text" class="form-control" id="searchInput" placeholder="订单号/客户名..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>订单信息</th>
                                    <th>客户信息</th>
                                    <th>商品</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>下单时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% orders.forEach(order => { %>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>#<%= order.order_number %></strong><br>
                                                <small class="text-muted">
                                                    <i class="fas fa-truck me-1"></i><%= order.shipping_method || '普通快递' %>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><%= order.customer_name %></strong><br>
                                                <small class="text-muted">
                                                    <i class="fas fa-phone me-1"></i><%= order.customer_phone %>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="max-width: 200px;">
                                                <% if (order.items && order.items.length > 0) { %>
                                                    <% order.items.slice(0, 2).forEach(item => { %>
                                                        <small class="d-block"><%= item.product_name %> x <%= item.quantity %></small>
                                                    <% }) %>
                                                    <% if (order.items.length > 2) { %>
                                                        <small class="text-muted">... +<%= order.items.length - 2 %>个商品</small>
                                                    <% } %>
                                                <% } %>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong class="text-primary">¥<%= Utils.formatNumber(order.total_amount, 2) %></strong><br>
                                                <small class="text-muted">含运费: ¥<%= Utils.formatNumber(order.shipping_fee || 0, 2) %></small>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (order.status === 'pending') { %>
                                                <span class="badge bg-warning">待付款</span>
                                            <% } else if (order.status === 'paid') { %>
                                                <span class="badge bg-info">已付款</span>
                                            <% } else if (order.status === 'shipped') { %>
                                                <span class="badge bg-primary">已发货</span>
                                            <% } else if (order.status === 'delivered') { %>
                                                <span class="badge bg-success">已送达</span>
                                            <% } else if (order.status === 'cancelled') { %>
                                                <span class="badge bg-secondary">已取消</span>
                                            <% } else if (order.status === 'refunded') { %>
                                                <span class="badge bg-danger">已退款</span>
                                            <% } %>
                                        </td>
                                        <td><%= Utils.formatDate(order.created_at, 'MM-DD HH:mm') %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewOrder('<%= order.id %>')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <% if (order.status === 'paid') { %>
                                                    <button class="btn btn-outline-success" onclick="shipOrder('<%= order.id %>')">
                                                        <i class="fas fa-truck"></i>
                                                    </button>
                                                <% } %>
                                                <% if (['pending', 'paid'].includes(order.status)) { %>
                                                    <button class="btn btn-outline-danger" onclick="cancelOrder('<%= order.id %>')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <% } %>
                                                <button class="btn btn-outline-info" onclick="printOrder('<%= order.id %>')">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailContent">
                <!-- 内容动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="printCurrentOrder()">打印订单</button>
            </div>
        </div>
    </div>
</div>

<!-- 发货模态框 -->
<div class="modal fade" id="shipModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">订单发货</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="shipForm">
                    <input type="hidden" id="shipOrderId">
                    <div class="mb-3">
                        <label for="trackingCompany" class="form-label">快递公司 <span class="text-danger">*</span></label>
                        <select class="form-select" id="trackingCompany" name="tracking_company" required>
                            <option value="">请选择快递公司</option>
                            <option value="顺丰速运">顺丰速运</option>
                            <option value="圆通速递">圆通速递</option>
                            <option value="中通快递">中通快递</option>
                            <option value="申通快递">申通快递</option>
                            <option value="韵达速递">韵达速递</option>
                            <option value="百世汇通">百世汇通</option>
                            <option value="德邦物流">德邦物流</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="trackingNumber" class="form-label">快递单号 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="trackingNumber" name="tracking_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="shipNote" class="form-label">发货备注</label>
                        <textarea class="form-control" id="shipNote" name="ship_note" rows="3" placeholder="发货备注信息..."></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notifyCustomer" name="notify_customer" checked>
                        <label class="form-check-label" for="notifyCustomer">
                            发送短信通知客户
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmShip()">确认发货</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentOrderId = null;

document.addEventListener('DOMContentLoaded', function() {
    // 筛选和搜索功能
    document.getElementById('statusFilter').addEventListener('change', filterOrders);
    document.getElementById('timeFilter').addEventListener('change', filterOrders);
    document.getElementById('searchInput').addEventListener('input', debounce(filterOrders, 500));
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function filterOrders() {
    const status = document.getElementById('statusFilter').value;
    const time = document.getElementById('timeFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    url.searchParams.set('time', time);
    url.searchParams.set('search', search);
    window.location.href = url.toString();
}

function viewOrder(orderId) {
    currentOrderId = orderId;
    
    fetch(`/mall/orders/${orderId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const order = data.data;
            const content = generateOrderDetailHTML(order);
            document.getElementById('orderDetailContent').innerHTML = content;
            
            const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
            modal.show();
        } else {
            alert('获取订单详情失败：' + data.message);
        }
    });
}

function generateOrderDetailHTML(order) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>订单信息</h6>
                <table class="table table-sm">
                    <tr><td>订单号:</td><td><strong>${order.order_number}</strong></td></tr>
                    <tr><td>订单状态:</td><td><span class="badge bg-info">${getStatusText(order.status)}</span></td></tr>
                    <tr><td>下单时间:</td><td>${order.created_at}</td></tr>
                    <tr><td>支付方式:</td><td>${order.payment_method || '未设置'}</td></tr>
                    <tr><td>配送方式:</td><td>${order.shipping_method || '普通快递'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>客户信息</h6>
                <table class="table table-sm">
                    <tr><td>客户姓名:</td><td>${order.customer_name}</td></tr>
                    <tr><td>联系电话:</td><td>${order.customer_phone}</td></tr>
                    <tr><td>收货地址:</td><td>${order.shipping_address}</td></tr>
                    <tr><td>客户备注:</td><td>${order.customer_note || '无'}</td></tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>商品明细</h6>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>商品</th>
                            <th>单价</th>
                            <th>数量</th>
                            <th>小计</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.items ? order.items.map(item => `
                            <tr>
                                <td>${item.product_name}</td>
                                <td>¥${item.price}</td>
                                <td>${item.quantity}</td>
                                <td>¥${(item.price * item.quantity).toFixed(2)}</td>
                            </tr>
                        `).join('') : ''}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end"><strong>商品总计:</strong></td>
                            <td><strong>¥${order.subtotal}</strong></td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end">配送费:</td>
                            <td>¥${order.shipping_fee || 0}</td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end"><strong>订单总计:</strong></td>
                            <td><strong class="text-primary">¥${order.total_amount}</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    `;
}

function getStatusText(status) {
    const statusMap = {
        'pending': '待付款',
        'paid': '已付款',
        'shipped': '已发货',
        'delivered': '已送达',
        'cancelled': '已取消',
        'refunded': '已退款'
    };
    return statusMap[status] || status;
}

function shipOrder(orderId) {
    document.getElementById('shipOrderId').value = orderId;
    const modal = new bootstrap.Modal(document.getElementById('shipModal'));
    modal.show();
}

function confirmShip() {
    const form = document.getElementById('shipForm');
    const formData = new FormData(form);
    const orderId = formData.get('order_id');
    
    fetch(`/mall/orders/${orderId}/ship`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('发货成功');
            location.reload();
        } else {
            alert('发货失败：' + data.message);
        }
    });
}

function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？')) {
        fetch(`/mall/orders/${orderId}/cancel`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('取消失败：' + data.message);
            }
        });
    }
}

function printOrder(orderId) {
    window.open(`/mall/orders/${orderId}/print`, '_blank');
}

function printCurrentOrder() {
    if (currentOrderId) {
        printOrder(currentOrderId);
    }
}

function exportOrders() {
    const status = document.getElementById('statusFilter').value;
    const time = document.getElementById('timeFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const params = new URLSearchParams({
        status: status,
        time: time,
        search: search
    });
    
    window.open('/mall/orders/export?' + params.toString());
}
</script>