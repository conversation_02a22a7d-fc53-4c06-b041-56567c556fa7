<!-- 商品管理页面 -->
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
          <i class="fas fa-box me-2"></i>
          商品管理
        </h2>
        <div>
          <button class="btn btn-success me-2" onclick="exportProducts()">
            <i class="fas fa-download me-1"></i>
            导出商品
          </button>
          <a href="/mall/products/create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            添加商品
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 商品统计卡片 -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                总商品数
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= products ? products.length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-box fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                在售商品
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= products ? products.filter(p=> p.status === 'active').length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-check-circle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                库存预警
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= products ? products.filter(p=> p.stock_qty <= 10).length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                总销售额
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                ¥<%= products ? products.reduce((sum, p)=> sum + (p.sales_count || 0) * p.price, 0).toFixed(2) : '0.00'
                  %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 搜索和筛选 -->
  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">商品筛选</h6>
    </div>
    <div class="card-body">
      <form id="filterForm" class="row">
        <div class="col-md-3">
          <label for="categoryFilter">商品分类</label>
          <select class="form-control" id="categoryFilter" name="category">
            <option value="">全部分类</option>
            <option value="feed">饲料</option>
            <option value="medicine">药品</option>
            <option value="equipment">设备</option>
            <option value="supplement">营养品</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="statusFilter">商品状态</label>
          <select class="form-control" id="statusFilter" name="status">
            <option value="">全部状态</option>
            <option value="active">在售</option>
            <option value="inactive">下架</option>
            <option value="out_of_stock">缺货</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="stockFilter">库存状态</label>
          <select class="form-control" id="stockFilter" name="stock">
            <option value="">全部库存</option>
            <option value="low">库存不足</option>
            <option value="normal">库存正常</option>
            <option value="high">库存充足</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="searchInput">搜索商品</label>
          <div class="input-group">
            <input type="text" class="form-control" id="searchInput" name="search" placeholder="商品名称、代码...">
            <div class="input-group-append">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- 商品列表 -->
  <div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
      <h6 class="m-0 font-weight-bold text-primary">商品列表</h6>
      <div>
        <button class="btn btn-sm btn-outline-primary" onclick="selectAll()">
          <i class="fas fa-check-square"></i> 全选
        </button>
        <button class="btn btn-sm btn-outline-warning" onclick="batchUpdateStatus('active')">
          <i class="fas fa-arrow-up"></i> 批量上架
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="batchUpdateStatus('inactive')">
          <i class="fas fa-arrow-down"></i> 批量下架
        </button>
        <button class="btn btn-sm btn-outline-danger" onclick="batchDelete()">
          <i class="fas fa-trash"></i> 批量删除
        </button>
      </div>
    </div>
    <div class="card-body">
      <% if (products && products.length> 0) { %>
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>商品信息</th>
                <th>分类</th>
                <th>价格</th>
                <th>库存</th>
                <th>状态</th>
                <th>销量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% products.forEach(function(product) { %>
                <tr>
                  <td>
                    <div>
                      <strong>
                        <%= product.name %>
                      </strong>
                      <br>
                      <small class="text-muted">
                        代码: <%= product.product_code %>
                          <br>
                          品牌: <%= product.brand || '无' %>
                      </small>
                    </div>
                  </td>
                  <td>
                    <%= product.category_name || '未分类' %>
                  </td>
                  <td>
                    <strong>¥<%= parseFloat(product.price).toFixed(2) %></strong>
                    <% if (product.market_price> product.price) { %>
                      <br>
                      <small class="text-muted">市场价: ¥<%= parseFloat(product.market_price).toFixed(2) %></small>
                      <% } %>
                  </td>
                  <td>
                    <% if (product.stock_qty <=10) { %>
                      <span class="badge bg-danger">
                        <%= product.stock_qty %>
                      </span>
                      <% } else if (product.stock_qty <=50) { %>
                        <span class="badge bg-warning">
                          <%= product.stock_qty %>
                        </span>
                        <% } else { %>
                          <span class="badge bg-success">
                            <%= product.stock_qty %>
                          </span>
                          <% } %>
                  </td>
                  <td>
                    <% if (product.status==='active' ) { %>
                      <span class="badge bg-success">上架</span>
                      <% } else if (product.status==='inactive' ) { %>
                        <span class="badge bg-secondary">下架</span>
                        <% } else { %>
                          <span class="badge bg-danger">缺货</span>
                          <% } %>
                  </td>
                  <td>
                    <%= product.sales_count || 0 %>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="/mall/products/<%= product.id %>/edit" class="btn btn-sm btn-warning" title="编辑">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="/mall/products/<%= product.id %>/view" class="btn btn-sm btn-info" title="查看">
                        <i class="fas fa-eye"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                <% }); %>
            </tbody>
          </table>
        </div>
        <% } else { %>
          <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
            <h4>暂无商品</h4>
            <p class="text-muted">还没有添加任何商品</p>
            <a href="/mall/products/create" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>
              添加第一个商品
            </a>
          </div>
          <% } %>
    </div>
  </div>
</div>

<script>
  // 导出商品
  function exportProducts() {
    if (confirm('确定要导出商品数据吗？')) {
      window.open('/mall/products/export', '_blank');
    }
  }

  // 全选/取消全选
  function selectAll() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
      checkbox.checked = !allChecked;
    });
  }

  // 批量更新状态
  function batchUpdateStatus(status) {
    const selectedIds = getSelectedProductIds();

    if (selectedIds.length === 0) {
      alert('请先选择要操作的商品');
      return;
    }

    const statusText = status === 'active' ? '上架' : '下架';
    if (confirm(`确定要批量${statusText}选中的 ${selectedIds.length} 个商品吗？`)) {
      // 这里应该发送AJAX请求到后端
      alert(`批量${statusText}功能开发中...`);
    }
  }

  // 批量删除
  function batchDelete() {
    const selectedIds = getSelectedProductIds();

    if (selectedIds.length === 0) {
      alert('请先选择要删除的商品');
      return;
    }

    if (confirm(`确定要删除选中的 ${selectedIds.length} 个商品吗？此操作不可恢复！`)) {
      // 这里应该发送AJAX请求到后端
      alert('批量删除功能开发中...');
    }
  }

  // 获取选中的商品ID
  function getSelectedProductIds() {
    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
    return Array.from(checkedBoxes).map(checkbox => checkbox.value);
  }
</script>