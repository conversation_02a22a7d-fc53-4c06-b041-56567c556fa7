<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-boxes me-2"></i>商城库存管理</h2>
                <div>
                    <button class="btn btn-warning me-2" onclick="checkLowStock()">
                        <i class="fas fa-exclamation-triangle me-1"></i>库存预警
                    </button>
                    <button class="btn btn-info me-2" onclick="exportInventory()">
                        <i class="fas fa-download me-1"></i>导出库存
                    </button>
                    <a href="/mall/products" class="btn btn-primary">
                        <i class="fas fa-cube me-1"></i>商品管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 库存概览 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总商品数</h5>
                            <h3><%= stats.totalProducts %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-cube fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总库存量</h5>
                            <h3><%= stats.totalStock %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>库存预警</h5>
                            <h3><%= stats.lowStockCount %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>缺货商品</h5>
                            <h3><%= stats.outOfStockCount %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 库存列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i>商品库存</h5>
                    <div class="d-flex">
                        <select class="form-select me-2" id="categoryFilter" style="width: 150px;">
                            <option value="">全部分类</option>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.id %>"><%= category.name %></option>
                            <% }) %>
                        </select>
                        <select class="form-select me-2" id="stockFilter" style="width: 150px;">
                            <option value="">全部库存</option>
                            <option value="normal">正常</option>
                            <option value="low">库存预警</option>
                            <option value="out">缺货</option>
                        </select>
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索商品..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>商品信息</th>
                                    <th>分类</th>
                                    <th>当前库存</th>
                                    <th>预警值</th>
                                    <th>销售价格</th>
                                    <th>库存状态</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% inventory.forEach(item => { %>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <% if (item.image) { %>
                                                        <img src="<%= item.image %>" class="rounded" width="50" height="50">
                                                    <% } else { %>
                                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <% } %>
                                                </div>
                                                <div>
                                                    <strong><%= item.name %></strong><br>
                                                    <small class="text-muted">SKU: <%= item.sku || '无' %></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (item.category_name) { %>
                                                <span class="badge bg-secondary"><%= item.category_name %></span>
                                            <% } else { %>
                                                <span class="text-muted">未分类</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <div>
                                                <strong class="<%= item.stock <= 0 ? 'text-danger' : item.stock <= item.warning_stock ? 'text-warning' : 'text-success' %>">
                                                    <%= item.stock %>
                                                </strong>
                                                <small class="text-muted d-block"><%= item.unit || '件' %></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-warning"><%= item.warning_stock || 0 %></span>
                                        </td>
                                        <td>
                                            <strong class="text-primary">¥<%= Utils.formatNumber(item.price, 2) %></strong>
                                        </td>
                                        <td>
                                            <% if (item.stock <= 0) { %>
                                                <span class="badge bg-danger">缺货</span>
                                            <% } else if (item.stock <= item.warning_stock) { %>
                                                <span class="badge bg-warning">预警</span>
                                            <% } else { %>
                                                <span class="badge bg-success">正常</span>
                                            <% } %>
                                        </td>
                                        <td><%= Utils.formatDate(item.updated_at, 'MM-DD HH:mm') %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="adjustStock(<%= item.id %>, '<%= item.name %>', <%= item.stock %>)">
                                                    <i class="fas fa-edit"></i> 调整
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewStockHistory(<%= item.id %>)">
                                                    <i class="fas fa-history"></i> 记录
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="setWarning(<%= item.id %>, <%= item.warning_stock || 0 %>)">
                                                    <i class="fas fa-bell"></i> 预警
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 库存调整模态框 -->
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">库存调整</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="adjustStockForm">
                    <input type="hidden" id="adjustProductId">
                    <div class="mb-3">
                        <label class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="adjustProductName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">当前库存</label>
                        <input type="number" class="form-control" id="currentStock" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="adjustType" class="form-label">调整类型 <span class="text-danger">*</span></label>
                        <select class="form-select" id="adjustType" name="adjust_type" required>
                            <option value="">请选择调整类型</option>
                            <option value="in">入库</option>
                            <option value="out">出库</option>
                            <option value="set">设置</option>
                            <option value="loss">损耗</option>
                            <option value="return">退货</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustQuantity" class="form-label">调整数量 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="adjustQuantity" name="quantity" min="0" required>
                        <div class="form-text" id="resultStock">调整后库存: --</div>
                    </div>
                    <div class="mb-3">
                        <label for="adjustReason" class="form-label">调整原因</label>
                        <textarea class="form-control" id="adjustReason" name="reason" rows="3" placeholder="请输入调整原因..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmAdjust()">确认调整</button>
            </div>
        </div>
    </div>
</div>

<!-- 库存预警设置模态框 -->
<div class="modal fade" id="warningModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">库存预警设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="warningForm">
                    <input type="hidden" id="warningProductId">
                    <div class="mb-3">
                        <label for="warningStock" class="form-label">预警库存 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="warningStock" name="warning_stock" min="0" required>
                        <div class="form-text">当库存低于此值时将显示预警</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableEmailAlert" name="email_alert">
                        <label class="form-check-label" for="enableEmailAlert">
                            启用邮件提醒
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmWarning()">保存设置</button>
            </div>
        </div>
    </div>
</div>

<!-- 库存记录模态框 -->
<div class="modal fade" id="stockHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">库存变动记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="stockHistoryContent">
                <!-- 内容动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 筛选和搜索功能
    document.getElementById('categoryFilter').addEventListener('change', filterInventory);
    document.getElementById('stockFilter').addEventListener('change', filterInventory);
    document.getElementById('searchInput').addEventListener('input', debounce(filterInventory, 500));
    
    // 库存调整计算
    document.getElementById('adjustType').addEventListener('change', calculateResultStock);
    document.getElementById('adjustQuantity').addEventListener('input', calculateResultStock);
});

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function filterInventory() {
    const category = document.getElementById('categoryFilter').value;
    const stock = document.getElementById('stockFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const url = new URL(window.location);
    url.searchParams.set('category', category);
    url.searchParams.set('stock', stock);
    url.searchParams.set('search', search);
    window.location.href = url.toString();
}

function adjustStock(productId, productName, currentStock) {
    document.getElementById('adjustProductId').value = productId;
    document.getElementById('adjustProductName').value = productName;
    document.getElementById('currentStock').value = currentStock;
    document.getElementById('adjustType').value = '';
    document.getElementById('adjustQuantity').value = '';
    document.getElementById('adjustReason').value = '';
    document.getElementById('resultStock').textContent = '调整后库存: --';
    
    const modal = new bootstrap.Modal(document.getElementById('adjustStockModal'));
    modal.show();
}

function calculateResultStock() {
    const type = document.getElementById('adjustType').value;
    const quantity = parseInt(document.getElementById('adjustQuantity').value) || 0;
    const current = parseInt(document.getElementById('currentStock').value);
    
    let result = current;
    
    if (type === 'in' || type === 'return') {
        result = current + quantity;
    } else if (type === 'out' || type === 'loss') {
        result = current - quantity;
    } else if (type === 'set') {
        result = quantity;
    }
    
    document.getElementById('resultStock').textContent = `调整后库存: ${result}`;
    document.getElementById('resultStock').className = 'form-text ' + 
        (result < 0 ? 'text-danger' : result === 0 ? 'text-warning' : 'text-success');
}

function confirmAdjust() {
    const form = document.getElementById('adjustStockForm');
    const formData = new FormData(form);
    const productId = document.getElementById('adjustProductId').value;
    
    fetch(`/mall/inventory/${productId}/adjust`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('库存调整成功');
            location.reload();
        } else {
            alert('调整失败：' + data.message);
        }
    });
}

function setWarning(productId, currentWarning) {
    document.getElementById('warningProductId').value = productId;
    document.getElementById('warningStock').value = currentWarning;
    
    const modal = new bootstrap.Modal(document.getElementById('warningModal'));
    modal.show();
}

function confirmWarning() {
    const form = document.getElementById('warningForm');
    const formData = new FormData(form);
    const productId = document.getElementById('warningProductId').value;
    
    fetch(`/mall/inventory/${productId}/warning`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('预警设置成功');
            location.reload();
        } else {
            alert('设置失败：' + data.message);
        }
    });
}

function viewStockHistory(productId) {
    fetch(`/mall/inventory/${productId}/history`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const history = data.data;
            const content = generateHistoryHTML(history);
            document.getElementById('stockHistoryContent').innerHTML = content;
            
            const modal = new bootstrap.Modal(document.getElementById('stockHistoryModal'));
            modal.show();
        } else {
            alert('获取历史记录失败：' + data.message);
        }
    });
}

function generateHistoryHTML(history) {
    if (!history || history.length === 0) {
        return '<p class="text-muted text-center">暂无库存变动记录</p>';
    }
    
    return `
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>操作类型</th>
                        <th>变动数量</th>
                        <th>变动后库存</th>
                        <th>操作原因</th>
                        <th>操作人</th>
                    </tr>
                </thead>
                <tbody>
                    ${history.map(record => `
                        <tr>
                            <td>${record.created_at}</td>
                            <td><span class="badge bg-primary">${getTypeText(record.adjust_type)}</span></td>
                            <td class="${record.quantity > 0 ? 'text-success' : 'text-danger'}">${record.quantity > 0 ? '+' : ''}${record.quantity}</td>
                            <td>${record.stock_after}</td>
                            <td>${record.reason || '-'}</td>
                            <td>${record.operator || '系统'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function getTypeText(type) {
    const typeMap = {
        'in': '入库',
        'out': '出库',
        'set': '设置',
        'loss': '损耗',
        'return': '退货',
        'sale': '销售'
    };
    return typeMap[type] || type;
}

function checkLowStock() {
    window.location.href = '/mall/inventory?stock=low';
}

function exportInventory() {
    const category = document.getElementById('categoryFilter').value;
    const stock = document.getElementById('stockFilter').value;
    const search = document.getElementById('searchInput').value;
    
    const params = new URLSearchParams({
        category: category,
        stock: stock,
        search: search
    });
    
    window.open('/mall/inventory/export?' + params.toString());
}
</script>