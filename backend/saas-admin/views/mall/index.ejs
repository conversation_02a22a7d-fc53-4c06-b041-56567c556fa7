<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-store me-2"></i>商城管理</h2>
      </div>
    </div>
  </div>

  <!-- 统计卡片 -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <h5>商品总数</h5>
              <h3><%= stats.totalProducts %></h3>
            </div>
            <div class="ms-3">
              <i class="fas fa-box fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <h5>订单总数</h5>
              <h3><%= stats.totalOrders %></h3>
            </div>
            <div class="ms-3">
              <i class="fas fa-shopping-cart fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <h5>分类数量</h5>
              <h3><%= stats.totalCategories %></h3>
            </div>
            <div class="ms-3">
              <i class="fas fa-tags fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <h5>总收入</h5>
              <h3>¥<%= Utils.formatNumber(stats.totalRevenue || 0, 2) %></h3>
            </div>
            <div class="ms-3">
              <i class="fas fa-yen-sign fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 功能模块快捷入口 -->
  <div class="row">
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-box fa-3x text-primary mb-3"></i>
          <h5 class="card-title">商品管理</h5>
          <p class="card-text text-muted">管理商城商品信息、价格、库存等</p>
          <a href="/mall/products" class="btn btn-primary">进入管理</a>
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-shopping-cart fa-3x text-success mb-3"></i>
          <h5 class="card-title">订单管理</h5>
          <p class="card-text text-muted">处理客户订单、发货、退款等业务</p>
          <a href="/mall/orders" class="btn btn-success">进入管理</a>
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-tags fa-3x text-info mb-3"></i>
          <h5 class="card-title">分类管理</h5>
          <p class="card-text text-muted">管理商品分类、层级关系</p>
          <a href="/mall/categories" class="btn btn-info">进入管理</a>
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-warehouse fa-3x text-warning mb-3"></i>
          <h5 class="card-title">库存管理</h5>
          <p class="card-text text-muted">监控库存水平、预警提醒</p>
          <a href="/mall/inventory" class="btn btn-warning">进入管理</a>
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-chart-line fa-3x text-secondary mb-3"></i>
          <h5 class="card-title">销售报表</h5>
          <p class="card-text text-muted">查看销售数据和趋势分析</p>
          <a href="/reports?type=sales" class="btn btn-secondary">查看报表</a>
        </div>
      </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <i class="fas fa-cog fa-3x text-dark mb-3"></i>
          <h5 class="card-title">商城设置</h5>
          <p class="card-text text-muted">配置商城基本信息和参数</p>
          <a href="/system?tab=mall" class="btn btn-dark">进入设置</a>
        </div>
      </div>
    </div>
  </div>

  <% if (typeof error !== 'undefined' && error) { %>
  <!-- 错误提示 -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>数据加载提示</strong>
        <%= error %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    </div>
  </div>
  <% } %>
</div>