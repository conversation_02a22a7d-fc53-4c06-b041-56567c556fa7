<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus me-2"></i>添加价格记录</h2>
                <a href="/goose-prices" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit me-2"></i>价格信息</h5>
                </div>
                <div class="card-body">
                    <form id="priceForm" method="POST" action="/goose-prices/create">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="record_date" class="form-label">记录日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="record_date" name="record_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="region" class="form-label">地区 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="region" name="region" placeholder="如：广东广州" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="market_name" class="form-label">市场名称</label>
                                <input type="text" class="form-control" id="market_name" name="market_name" placeholder="如：江南农副产品批发市场">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="goose_type" class="form-label">鹅种类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="goose_type" name="goose_type" required>
                                    <option value="">请选择鹅种类型</option>
                                    <option value="白鹅">白鹅</option>
                                    <option value="灰鹅">灰鹅</option>
                                    <option value="黑鹅">黑鹅</option>
                                    <option value="狮头鹅">狮头鹅</option>
                                    <option value="太湖鹅">太湖鹅</option>
                                    <option value="四川白鹅">四川白鹅</option>
                                    <option value="莱茵鹅">莱茵鹅</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="price_per_jin" class="form-label">价格(元/斤) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" step="0.01" min="0" max="999.99" class="form-control" id="price_per_jin" name="price_per_jin" placeholder="0.00" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="data_source" class="form-label">数据来源 <span class="text-danger">*</span></label>
                                <select class="form-select" id="data_source" name="data_source" required>
                                    <option value="">请选择数据来源</option>
                                    <option value="市场调研">市场调研</option>
                                    <option value="批发市场">批发市场</option>
                                    <option value="养殖户">养殖户</option>
                                    <option value="经销商">经销商</option>
                                    <option value="网络信息">网络信息</option>
                                    <option value="政府发布">政府发布</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="weight_range" class="form-label">重量范围</label>
                                <select class="form-select" id="weight_range" name="weight_range">
                                    <option value="">请选择重量范围</option>
                                    <option value="2-3斤">2-3斤</option>
                                    <option value="3-4斤">3-4斤</option>
                                    <option value="4-5斤">4-5斤</option>
                                    <option value="5-6斤">5-6斤</option>
                                    <option value="6斤以上">6斤以上</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="quality_grade" class="form-label">品质等级</label>
                                <select class="form-select" id="quality_grade" name="quality_grade">
                                    <option value="">请选择品质等级</option>
                                    <option value="特级">特级</option>
                                    <option value="一级">一级</option>
                                    <option value="二级">二级</option>
                                    <option value="三级">三级</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="remarks" class="form-label">备注</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="请输入相关备注信息"></textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存记录
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 最近价格趋势 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6><i class="fas fa-chart-line me-2"></i>最近价格趋势</h6>
                </div>
                <div class="card-body">
                    <canvas id="recentTrendChart" height="200"></canvas>
                </div>
            </div>

            <!-- 价格录入提示 -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6><i class="fas fa-lightbulb me-2"></i>录入提示</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <small>
                            <strong>注意事项：</strong><br>
                            • 请确保价格信息准确无误<br>
                            • 建议选择具体的市场名称<br>
                            • 数据来源要可靠<br>
                            • 价格单位为元/斤<br>
                        </small>
                    </div>
                    
                    <div class="mt-3">
                        <h6>当前市场参考价格：</h6>
                        <ul class="list-unstyled small">
                            <li><strong>白鹅：</strong> ¥15-18/斤</li>
                            <li><strong>灰鹅：</strong> ¥12-16/斤</li>
                            <li><strong>狮头鹅：</strong> ¥20-25/斤</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期为今天
    document.getElementById('record_date').valueAsDate = new Date();
    
    // 初始化最近趋势图
    const ctx = document.getElementById('recentTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '1天前'],
            datasets: [{
                label: '平均价格',
                data: [16.5, 16.8, 17.2, 16.9, 17.5, 17.8, 18.2],
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value;
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // 表单验证
    const form = document.getElementById('priceForm');
    form.addEventListener('submit', function(e) {
        const price = parseFloat(document.getElementById('price_per_jin').value);
        
        if (price < 1 || price > 100) {
            e.preventDefault();
            alert('价格似乎不在合理范围内(1-100元/斤)，请检查后重新输入');
            return false;
        }
        
        // 显示提交状态
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
        submitBtn.disabled = true;
    });
});
</script>