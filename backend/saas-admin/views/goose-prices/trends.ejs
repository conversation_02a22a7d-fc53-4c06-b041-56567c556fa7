<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-area me-2"></i>鹅价格趋势分析</h2>
                <a href="/goose-prices" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="dateRange" class="form-label">时间范围</label>
                            <select class="form-select" id="dateRange" name="dateRange">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近3个月</option>
                                <option value="180">最近6个月</option>
                                <option value="365">最近1年</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="regionFilter" class="form-label">地区</label>
                            <select class="form-select" id="regionFilter" name="region">
                                <option value="">全部地区</option>
                                <% regions.forEach(region => { %>
                                    <option value="<%= region %>"><%= region %></option>
                                <% }) %>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="gooseTypeFilter" class="form-label">鹅种类型</label>
                            <select class="form-select" id="gooseTypeFilter" name="gooseType">
                                <option value="">全部类型</option>
                                <option value="白鹅">白鹅</option>
                                <option value="灰鹅">灰鹅</option>
                                <option value="黑鹅">黑鹅</option>
                                <option value="狮头鹅">狮头鹅</option>
                                <option value="太湖鹅">太湖鹅</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-primary" onclick="updateCharts()" style="margin-top: 32px;">
                                <i class="fas fa-search me-1"></i>筛选
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 价格趋势图表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-chart-line me-2"></i>价格趋势图</h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary active" onclick="showChart('line')">折线图</button>
                        <button type="button" class="btn btn-outline-primary" onclick="showChart('bar')">柱状图</button>
                        <button type="button" class="btn btn-outline-primary" onclick="showChart('area')">面积图</button>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="trendChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计分析 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie me-2"></i>地区价格分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="regionChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>鹅种价格对比</h5>
                </div>
                <div class="card-body">
                    <canvas id="typeChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 价格统计表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>价格统计分析</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>统计项目</th>
                                    <th>当前值</th>
                                    <th>上期值</th>
                                    <th>变化</th>
                                    <th>变化率</th>
                                </tr>
                            </thead>
                            <tbody id="statsTableBody">
                                <tr>
                                    <td><strong>平均价格</strong></td>
                                    <td>¥<span id="avgPrice">16.80</span></td>
                                    <td>¥<span id="lastAvgPrice">16.20</span></td>
                                    <td class="text-success">+¥0.60</td>
                                    <td class="text-success">+3.7%</td>
                                </tr>
                                <tr>
                                    <td><strong>最高价格</strong></td>
                                    <td>¥<span id="maxPrice">22.50</span></td>
                                    <td>¥<span id="lastMaxPrice">21.80</span></td>
                                    <td class="text-success">+¥0.70</td>
                                    <td class="text-success">+3.2%</td>
                                </tr>
                                <tr>
                                    <td><strong>最低价格</strong></td>
                                    <td>¥<span id="minPrice">12.30</span></td>
                                    <td>¥<span id="lastMinPrice">11.90</span></td>
                                    <td class="text-success">+¥0.40</td>
                                    <td class="text-success">+3.4%</td>
                                </tr>
                                <tr>
                                    <td><strong>价格波动</strong></td>
                                    <td><span id="volatility">±2.8%</span></td>
                                    <td><span id="lastVolatility">±3.2%</span></td>
                                    <td class="text-success">-0.4%</td>
                                    <td class="text-success">更稳定</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let mainChart = null;
let regionChart = null;
let typeChart = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // 主趋势图
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    const trendData = <%- JSON.stringify(trendData || {}) %>;
    
    mainChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: trendData.labels || [],
            datasets: [{
                label: '平均价格(元/斤)',
                data: trendData.prices || [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.4,
                fill: true,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '价格: ¥' + context.parsed.y.toFixed(2) + '/斤';
                        }
                    }
                }
            }
        }
    });

    // 地区分布图
    const regionCtx = document.getElementById('regionChart').getContext('2d');
    const regionData = <%- JSON.stringify(regionData || {}) %>;
    
    regionChart = new Chart(regionCtx, {
        type: 'doughnut',
        data: {
            labels: regionData.labels || [],
            datasets: [{
                data: regionData.prices || [],
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ¥' + context.parsed.toFixed(2) + '/斤';
                        }
                    }
                }
            }
        }
    });

    // 鹅种对比图
    const typeCtx = document.getElementById('typeChart').getContext('2d');
    const typeData = <%- JSON.stringify(typeData || {}) %>;
    
    typeChart = new Chart(typeCtx, {
        type: 'bar',
        data: {
            labels: typeData.labels || [],
            datasets: [{
                label: '平均价格',
                data: typeData.prices || [],
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '价格: ¥' + context.parsed.y.toFixed(2) + '/斤';
                        }
                    }
                }
            }
        }
    });
}

function showChart(type) {
    // 更新按钮状态
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // 更新图表类型
    if (mainChart) {
        mainChart.destroy();
    }
    
    const ctx = document.getElementById('trendChart').getContext('2d');
    const trendData = <%- JSON.stringify(trendData || {}) %>;
    
    const config = {
        type: type === 'area' ? 'line' : type,
        data: {
            labels: trendData.labels || [],
            datasets: [{
                label: '平均价格(元/斤)',
                data: trendData.prices || [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: type === 'area' ? 'rgba(75, 192, 192, 0.3)' : 'rgba(75, 192, 192, 0.8)',
                tension: type === 'line' || type === 'area' ? 0.4 : 0,
                fill: type === 'area',
                pointRadius: type === 'bar' ? 0 : 4,
                pointHoverRadius: type === 'bar' ? 0 : 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '价格: ¥' + context.parsed.y.toFixed(2) + '/斤';
                        }
                    }
                }
            }
        }
    };
    
    mainChart = new Chart(ctx, config);
}

function updateCharts() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // 显示加载状态
    document.querySelector('#filterForm button').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
    
    // 重新加载页面带参数
    window.location.search = params.toString();
}
</script>