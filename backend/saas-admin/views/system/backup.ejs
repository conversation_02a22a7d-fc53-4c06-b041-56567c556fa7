<!-- 数据备份页面 -->
<div class="row">
  <!-- 备份配置卡片 -->
  <div class="col-md-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-cog mr-2"></i>备份配置
        </h3>
      </div>
      <div class="card-body">
        <form id="backupConfigForm">
          <div class="form-group">
            <label for="backupFrequency">备份频率</label>
            <select class="form-control" id="backupFrequency" name="frequency">
              <option value="daily" <%= backupConfig.frequency === 'daily' ? 'selected' : '' %>>每日</option>
              <option value="weekly" <%= backupConfig.frequency === 'weekly' ? 'selected' : '' %>>每周</option>
              <option value="monthly" <%= backupConfig.frequency === 'monthly' ? 'selected' : '' %>>每月</option>
            </select>
          </div>
          <div class="form-group">
            <label for="backupTime">备份时间</label>
            <input type="time" class="form-control" id="backupTime" name="time" value="<%= backupConfig.time %>">
          </div>
          <div class="form-group">
            <label for="retention">保留天数</label>
            <input type="number" class="form-control" id="retention" name="retention" value="<%= backupConfig.retention %>" min="1" max="365">
          </div>
          <div class="form-group">
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="compression" name="compression" <%= backupConfig.compression ? 'checked' : '' %>>
              <label class="form-check-label" for="compression">启用压缩</label>
            </div>
          </div>
          <button type="submit" class="btn btn-primary btn-block">
            <i class="fas fa-save mr-1"></i>保存配置
          </button>
        </form>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-bolt mr-2"></i>快速操作
        </h3>
      </div>
      <div class="card-body">
        <button class="btn btn-success btn-block mb-2" onclick="createBackup()">
          <i class="fas fa-plus mr-1"></i>立即备份
        </button>
        <button class="btn btn-info btn-block mb-2" onclick="testBackup()">
          <i class="fas fa-vial mr-1"></i>测试备份
        </button>
        <button class="btn btn-warning btn-block" onclick="cleanupOldBackups()">
          <i class="fas fa-broom mr-1"></i>清理旧备份
        </button>
      </div>
    </div>
  </div>

  <!-- 备份列表 -->
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-archive mr-2"></i>备份文件列表
        </h3>
        <div class="card-tools">
          <button class="btn btn-primary btn-sm" onclick="refreshBackups()">
            <i class="fas fa-sync mr-1"></i>刷新
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped" id="backupsTable">
            <thead>
              <tr>
                <th>文件名</th>
                <th>创建时间</th>
                <th>大小</th>
                <th>类型</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% backups.forEach(function(backup) { %>
              <tr>
                <td>
                  <i class="fas fa-file-archive mr-1"></i>
                  <%= backup.filename %>
                </td>
                <td><%= new Date(backup.created_at).toLocaleString('zh-CN') %></td>
                <td><%= backup.size %></td>
                <td>
                  <span class="badge badge-<%= backup.type === 'full' ? 'primary' : backup.type === 'incremental' ? 'info' : 'secondary' %>">
                    <%= backup.type === 'full' ? '完整备份' : backup.type === 'incremental' ? '增量备份' : '其他' %>
                  </span>
                </td>
                <td>
                  <span class="badge badge-<%= backup.status === 'completed' ? 'success' : backup.status === 'failed' ? 'danger' : 'warning' %>">
                    <%= backup.status === 'completed' ? '完成' : backup.status === 'failed' ? '失败' : '进行中' %>
                  </span>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <% if (backup.status === 'completed') { %>
                    <button class="btn btn-success" onclick="downloadBackup('<%= backup.id %>')" title="下载">
                      <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-info" onclick="restoreBackup('<%= backup.id %>')" title="恢复">
                      <i class="fas fa-undo"></i>
                    </button>
                    <button class="btn btn-warning" onclick="verifyBackup('<%= backup.id %>')" title="验证">
                      <i class="fas fa-check-circle"></i>
                    </button>
                    <% } %>
                    <button class="btn btn-danger" onclick="deleteBackup('<%= backup.id %>')" title="删除">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 备份进度模态框 -->
<div class="modal fade" id="backupProgressModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">备份进度</h4>
      </div>
      <div class="modal-body">
        <div class="progress mb-3">
          <div class="progress-bar" id="backupProgressBar" role="progressbar" style="width: 0%">
            0%
          </div>
        </div>
        <div id="backupStatus">准备开始备份...</div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#backupsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese.json"
        },
        "order": [[ 1, "desc" ]], // 按创建时间倒序
        "pageLength": 10
    });

    // 备份配置表单提交
    $('#backupConfigForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            frequency: $('#backupFrequency').val(),
            time: $('#backupTime').val(),
            retention: $('#retention').val(),
            compression: $('#compression').is(':checked')
        };

        $.ajax({
            url: '/system/backup/config',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    alert('备份配置已保存');
                } else {
                    alert('保存失败：' + response.message);
                }
            },
            error: function() {
                alert('保存失败，请稍后重试');
            }
        });
    });
});

function createBackup() {
    if (confirm('确定要立即创建备份吗？这可能需要几分钟时间。')) {
        $('#backupProgressModal').modal('show');
        
        // 模拟备份进度
        let progress = 0;
        const progressInterval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            $('#backupProgressBar').css('width', progress + '%').text(Math.floor(progress) + '%');
            
            if (progress < 30) {
                $('#backupStatus').text('正在准备备份数据...');
            } else if (progress < 60) {
                $('#backupStatus').text('正在备份数据库...');
            } else if (progress < 90) {
                $('#backupStatus').text('正在压缩备份文件...');
            } else if (progress >= 100) {
                $('#backupStatus').text('备份完成！');
                clearInterval(progressInterval);
                setTimeout(function() {
                    $('#backupProgressModal').modal('hide');
                    refreshBackups();
                }, 2000);
            }
        }, 500);
    }
}

function testBackup() {
    alert('备份测试功能：检查备份配置和存储空间...');
}

function cleanupOldBackups() {
    if (confirm('确定要清理过期的备份文件吗？此操作不可撤销。')) {
        alert('正在清理旧备份文件...');
    }
}

function refreshBackups() {
    window.location.reload();
}

function downloadBackup(backupId) {
    window.open('/system/backup/download/' + backupId);
}

function restoreBackup(backupId) {
    if (confirm('确定要从此备份恢复数据吗？当前数据将被覆盖！')) {
        alert('开始恢复备份：' + backupId);
    }
}

function verifyBackup(backupId) {
    alert('正在验证备份文件完整性：' + backupId);
}

function deleteBackup(backupId) {
    if (confirm('确定要删除此备份文件吗？此操作不可撤销。')) {
        $.ajax({
            url: '/system/backup/' + backupId,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    alert('备份文件已删除');
                    refreshBackups();
                } else {
                    alert('删除失败：' + response.message);
                }
            },
            error: function() {
                alert('删除失败，请稍后重试');
            }
        });
    }
}
</script>
