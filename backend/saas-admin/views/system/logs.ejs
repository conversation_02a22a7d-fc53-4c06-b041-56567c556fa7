<!-- 系统日志页面 -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-file-alt mr-2"></i>系统日志
        </h3>
        <div class="card-tools">
          <button class="btn btn-primary btn-sm" onclick="refreshLogs()">
            <i class="fas fa-sync mr-1"></i>刷新
          </button>
          <button class="btn btn-success btn-sm" onclick="exportLogs()">
            <i class="fas fa-download mr-1"></i>导出
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- 过滤器 -->
        <div class="row mb-3">
          <div class="col-md-3">
            <label for="levelFilter">日志级别</label>
            <select class="form-control" id="levelFilter">
              <option value="">全部</option>
              <option value="error" <%= currentFilters.level === 'error' ? 'selected' : '' %>>错误</option>
              <option value="warn" <%= currentFilters.level === 'warn' ? 'selected' : '' %>>警告</option>
              <option value="info" <%= currentFilters.level === 'info' ? 'selected' : '' %>>信息</option>
              <option value="debug" <%= currentFilters.level === 'debug' ? 'selected' : '' %>>调试</option>
            </select>
          </div>
          <div class="col-md-3">
            <label for="dateFilter">日期</label>
            <input type="date" class="form-control" id="dateFilter" value="<%= currentFilters.date || '' %>">
          </div>
          <div class="col-md-3">
            <label for="limitFilter">显示条数</label>
            <select class="form-control" id="limitFilter">
              <option value="50" <%= currentFilters.limit === 50 ? 'selected' : '' %>>50</option>
              <option value="100" <%= currentFilters.limit === 100 ? 'selected' : '' %>>100</option>
              <option value="200" <%= currentFilters.limit === 200 ? 'selected' : '' %>>200</option>
              <option value="500" <%= currentFilters.limit === 500 ? 'selected' : '' %>>500</option>
            </select>
          </div>
          <div class="col-md-3">
            <label>&nbsp;</label>
            <div>
              <button class="btn btn-primary btn-block" onclick="applyFilters()">
                <i class="fas fa-filter mr-1"></i>应用过滤
              </button>
            </div>
          </div>
        </div>

        <!-- 日志列表 -->
        <div class="table-responsive">
          <table class="table table-bordered table-striped" id="logsTable">
            <thead>
              <tr>
                <th width="150">时间</th>
                <th width="80">级别</th>
                <th width="120">来源</th>
                <th>消息</th>
                <th width="100">操作</th>
              </tr>
            </thead>
            <tbody>
              <% logs.forEach(function(log) { %>
              <tr>
                <td><%= new Date(log.timestamp).toLocaleString('zh-CN') %></td>
                <td>
                  <span class="badge badge-<%= log.level === 'error' ? 'danger' : log.level === 'warn' ? 'warning' : log.level === 'info' ? 'info' : 'secondary' %>">
                    <%= log.level.toUpperCase() %>
                  </span>
                </td>
                <td><%= log.source %></td>
                <td>
                  <div class="log-message" style="max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    <%= log.message %>
                  </div>
                </td>
                <td>
                  <button class="btn btn-sm btn-info" onclick="showLogDetails('<%= log.id %>')">
                    <i class="fas fa-eye"></i>
                  </button>
                </td>
              </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">日志详情</h4>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div id="logDetailContent">
          <!-- 日志详情内容 -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#logsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese.json"
        },
        "order": [[ 0, "desc" ]], // 按时间倒序
        "pageLength": 25,
        "searching": false,
        "paging": true
    });
});

function applyFilters() {
    const level = $('#levelFilter').val();
    const date = $('#dateFilter').val();
    const limit = $('#limitFilter').val();
    
    const params = new URLSearchParams();
    if (level) params.append('level', level);
    if (date) params.append('date', date);
    if (limit) params.append('limit', limit);
    
    window.location.href = '/system/logs?' + params.toString();
}

function refreshLogs() {
    window.location.reload();
}

function exportLogs() {
    const level = $('#levelFilter').val();
    const date = $('#dateFilter').val();
    const limit = $('#limitFilter').val();
    
    const params = new URLSearchParams();
    if (level) params.append('level', level);
    if (date) params.append('date', date);
    if (limit) params.append('limit', limit);
    params.append('export', 'true');
    
    window.open('/system/logs?' + params.toString());
}

function showLogDetails(logId) {
    // 这里可以通过AJAX获取日志详情
    $('#logDetailContent').html('<p>日志ID: ' + logId + '</p><p>详细信息加载中...</p>');
    $('#logDetailModal').modal('show');
}
</script>
