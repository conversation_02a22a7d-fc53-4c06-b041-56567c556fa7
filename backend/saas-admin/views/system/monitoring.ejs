<!-- 系统监控页面 -->
<div class="row">
  <!-- 系统状态卡片 -->
  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-<%= monitoring.system.status === 'healthy' ? 'success' : 'danger' %>">
      <div class="inner">
        <h3><%= monitoring.system.uptime %></h3>
        <p>系统运行时间</p>
      </div>
      <div class="icon">
        <i class="fas fa-server"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3><%= monitoring.system.cpu %>%</h3>
        <p>CPU使用率</p>
      </div>
      <div class="icon">
        <i class="fas fa-microchip"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3><%= monitoring.system.memory %>%</h3>
        <p>内存使用率</p>
      </div>
      <div class="icon">
        <i class="fas fa-memory"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-secondary">
      <div class="inner">
        <h3><%= monitoring.system.disk %>%</h3>
        <p>磁盘使用率</p>
      </div>
      <div class="icon">
        <i class="fas fa-hdd"></i>
      </div>
      <a href="#" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>
</div>

<!-- 服务状态 -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-cogs mr-2"></i>服务状态
        </h3>
        <div class="card-tools">
          <button class="btn btn-primary btn-sm" onclick="refreshMonitoring()">
            <i class="fas fa-sync mr-1"></i>刷新
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>服务名称</th>
                <th>状态</th>
                <th>端口</th>
                <th>最后检查</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% monitoring.services.forEach(function(service) { %>
              <tr>
                <td><%= service.name %></td>
                <td>
                  <span class="badge badge-<%= service.status === 'running' ? 'success' : service.status === 'stopped' ? 'danger' : 'warning' %>">
                    <%= service.status === 'running' ? '运行中' : service.status === 'stopped' ? '已停止' : '异常' %>
                  </span>
                </td>
                <td><%= service.port %></td>
                <td><%= new Date().toLocaleString('zh-CN') %></td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <% if (service.status === 'running') { %>
                    <button class="btn btn-warning" onclick="restartService('<%= service.name %>')">
                      <i class="fas fa-redo"></i> 重启
                    </button>
                    <button class="btn btn-danger" onclick="stopService('<%= service.name %>')">
                      <i class="fas fa-stop"></i> 停止
                    </button>
                    <% } else { %>
                    <button class="btn btn-success" onclick="startService('<%= service.name %>')">
                      <i class="fas fa-play"></i> 启动
                    </button>
                    <% } %>
                  </div>
                </td>
              </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 性能图表 -->
<div class="row">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-chart-line mr-2"></i>CPU使用率趋势
        </h3>
      </div>
      <div class="card-body">
        <canvas id="cpuChart" width="400" height="200"></canvas>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-chart-area mr-2"></i>内存使用率趋势
        </h3>
      </div>
      <div class="card-body">
        <canvas id="memoryChart" width="400" height="200"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- 实时日志 -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-terminal mr-2"></i>实时日志
        </h3>
        <div class="card-tools">
          <button class="btn btn-sm btn-primary" onclick="toggleLogStream()">
            <i class="fas fa-play" id="logStreamIcon"></i>
            <span id="logStreamText">开始</span>
          </button>
          <button class="btn btn-sm btn-secondary" onclick="clearLogs()">
            <i class="fas fa-trash"></i> 清空
          </button>
        </div>
      </div>
      <div class="card-body">
        <div id="realTimeLogs" style="height: 300px; overflow-y: auto; background: #000; color: #00ff00; font-family: monospace; padding: 10px;">
          <div>系统监控日志流...</div>
          <div>等待日志数据...</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let logStreamActive = false;
let logStreamInterval;

$(document).ready(function() {
    // 初始化CPU图表
    const cpuCtx = document.getElementById('cpuChart').getContext('2d');
    new Chart(cpuCtx, {
        type: 'line',
        data: {
            labels: ['5分钟前', '4分钟前', '3分钟前', '2分钟前', '1分钟前', '现在'],
            datasets: [{
                label: 'CPU使用率',
                data: [45, 52, 48, 61, 55, <%= monitoring.system.cpu %>],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // 初始化内存图表
    const memoryCtx = document.getElementById('memoryChart').getContext('2d');
    new Chart(memoryCtx, {
        type: 'line',
        data: {
            labels: ['5分钟前', '4分钟前', '3分钟前', '2分钟前', '1分钟前', '现在'],
            datasets: [{
                label: '内存使用率',
                data: [62, 58, 65, 70, 68, <%= monitoring.system.memory %>],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
});

function refreshMonitoring() {
    window.location.reload();
}

function startService(serviceName) {
    if (confirm('确定要启动 ' + serviceName + ' 服务吗？')) {
        // 这里可以发送AJAX请求启动服务
        alert('启动服务: ' + serviceName);
    }
}

function stopService(serviceName) {
    if (confirm('确定要停止 ' + serviceName + ' 服务吗？')) {
        // 这里可以发送AJAX请求停止服务
        alert('停止服务: ' + serviceName);
    }
}

function restartService(serviceName) {
    if (confirm('确定要重启 ' + serviceName + ' 服务吗？')) {
        // 这里可以发送AJAX请求重启服务
        alert('重启服务: ' + serviceName);
    }
}

function toggleLogStream() {
    if (logStreamActive) {
        clearInterval(logStreamInterval);
        logStreamActive = false;
        $('#logStreamIcon').removeClass('fa-stop').addClass('fa-play');
        $('#logStreamText').text('开始');
    } else {
        logStreamActive = true;
        $('#logStreamIcon').removeClass('fa-play').addClass('fa-stop');
        $('#logStreamText').text('停止');
        
        logStreamInterval = setInterval(function() {
            const now = new Date().toLocaleTimeString();
            const logEntry = '<div>[' + now + '] 系统运行正常 - CPU: ' + Math.floor(Math.random() * 20 + 40) + '% 内存: ' + Math.floor(Math.random() * 20 + 60) + '%</div>';
            $('#realTimeLogs').append(logEntry);
            $('#realTimeLogs').scrollTop($('#realTimeLogs')[0].scrollHeight);
        }, 2000);
    }
}

function clearLogs() {
    $('#realTimeLogs').html('<div>日志已清空...</div>');
}
</script>
