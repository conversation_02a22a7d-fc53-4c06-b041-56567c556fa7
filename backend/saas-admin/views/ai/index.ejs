<!-- AI助手页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-robot me-2"></i>
                    AI智能助手
                </h1>
                <p class="page-subtitle">智能问答和系统管理助手</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- AI对话区域 -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comments me-2"></i>
                        AI对话
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearChat()">
                            <i class="fas fa-trash"></i> 清空对话
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="exportChat()">
                            <i class="fas fa-download"></i> 导出对话
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 对话历史 -->
                    <div id="chatHistory" class="chat-history mb-3">
                        <div class="message ai-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <strong>AI助手</strong>
                                    <span class="message-time">刚刚</span>
                                </div>
                                <div class="message-text">
                                    您好！我是智慧养鹅平台的AI助手。我可以帮您：<br>
                                    • 解答养鹅相关问题<br>
                                    • 分析系统数据<br>
                                    • 提供管理建议<br>
                                    • 协助故障排查<br><br>
                                    请问有什么可以帮助您的吗？
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div class="chat-input">
                        <div class="input-group">
                            <textarea class="form-control" id="messageInput" rows="3" 
                                      placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)"></textarea>
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" onclick="sendMessage()">
                                    <i class="fas fa-paper-plane"></i>
                                    发送
                                </button>
                            </div>
                        </div>
                        <div class="input-tools mt-2">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb"></i>
                                提示：您可以询问关于鹅群管理、疾病诊断、数据分析等问题
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 快捷问题 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-question-circle me-2"></i>
                        常见问题
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="askQuestion('如何提高鹅群的产蛋率？')">
                            <i class="fas fa-egg text-warning me-2"></i>
                            如何提高鹅群的产蛋率？
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="askQuestion('鹅群出现食欲不振怎么办？')">
                            <i class="fas fa-heartbeat text-danger me-2"></i>
                            鹅群出现食欲不振怎么办？
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="askQuestion('如何分析系统性能数据？')">
                            <i class="fas fa-chart-line text-info me-2"></i>
                            如何分析系统性能数据？
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="askQuestion('租户管理最佳实践？')">
                            <i class="fas fa-users text-success me-2"></i>
                            租户管理最佳实践？
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="askQuestion('如何优化饲料成本？')">
                            <i class="fas fa-seedling text-primary me-2"></i>
                            如何优化饲料成本？
                        </a>
                    </div>
                </div>
            </div>

            <!-- AI状态 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>
                        AI状态
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>服务状态</span>
                            <span class="badge badge-success">在线</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>响应时间</span>
                            <span class="text-success">1.2秒</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>今日对话</span>
                            <span class="text-info">247次</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>满意度</span>
                            <span class="text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                4.2
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用统计 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        使用统计
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="aiUsageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-history {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    background-color: #f8f9fc;
}

.message {
    display: flex;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background-color: #4e73df;
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background-color: #1cc88a;
    color: white;
    margin-right: 0;
    margin-left: 0.75rem;
}

.message-content {
    flex: 1;
    background-color: white;
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.user-message .message-content {
    background-color: #4e73df;
    color: white;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.message-text {
    line-height: 1.5;
}

.chat-input {
    border-top: 1px solid #e3e6f0;
    padding-top: 1rem;
}

.input-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>

<script>
let chatHistory = [];

// 发送消息
function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 添加用户消息
    addMessage('user', message);
    input.value = '';
    
    // 显示AI正在思考
    showTyping();
    
    // 模拟AI响应
    setTimeout(() => {
        hideTyping();
        const aiResponse = generateAIResponse(message);
        addMessage('ai', aiResponse);
    }, 2000);
}

// 添加消息到对话历史
function addMessage(sender, text) {
    const chatHistoryEl = document.getElementById('chatHistory');
    const messageEl = document.createElement('div');
    
    const isUser = sender === 'user';
    const avatarIcon = isUser ? 'fas fa-user' : 'fas fa-robot';
    const senderName = isUser ? '您' : 'AI助手';
    
    messageEl.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
    messageEl.innerHTML = `
        <div class="message-avatar">
            <i class="${avatarIcon}"></i>
        </div>
        <div class="message-content">
            <div class="message-header">
                <strong>${senderName}</strong>
                <span class="message-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-text">${text}</div>
        </div>
    `;
    
    chatHistoryEl.appendChild(messageEl);
    chatHistoryEl.scrollTop = chatHistoryEl.scrollHeight;
    
    // 保存到历史记录
    chatHistory.push({ sender, text, timestamp: new Date() });
}

// 显示AI正在输入
function showTyping() {
    const chatHistoryEl = document.getElementById('chatHistory');
    const typingEl = document.createElement('div');
    typingEl.id = 'typingIndicator';
    typingEl.className = 'message ai-message';
    typingEl.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-text">
                <i class="fas fa-spinner fa-spin"></i> AI正在思考...
            </div>
        </div>
    `;
    
    chatHistoryEl.appendChild(typingEl);
    chatHistoryEl.scrollTop = chatHistoryEl.scrollHeight;
}

// 隐藏AI正在输入
function hideTyping() {
    const typingEl = document.getElementById('typingIndicator');
    if (typingEl) {
        typingEl.remove();
    }
}

// 生成AI响应
function generateAIResponse(message) {
    const responses = {
        '如何提高鹅群的产蛋率': '提高鹅群产蛋率的关键因素包括：\n1. 优化饲料配比，确保蛋白质含量在16-18%\n2. 保持适宜的光照时间（14-16小时/天）\n3. 维持舍内温度在15-25℃\n4. 提供充足清洁的饮水\n5. 定期健康检查，及时防疫\n6. 减少应激因素，保持环境安静',
        '鹅群出现食欲不振怎么办': '鹅群食欲不振可能的原因和处理方法：\n1. 检查饲料质量，确保新鲜无霉变\n2. 观察是否有发热、腹泻等症状\n3. 检查饮水系统是否正常\n4. 评估环境温度和通风情况\n5. 考虑是否有应激因素\n6. 必要时请兽医进行专业诊断\n建议立即隔离观察，记录症状变化。',
        '如何分析系统性能数据': '系统性能数据分析建议：\n1. 关注CPU、内存、磁盘使用率趋势\n2. 监控API响应时间和错误率\n3. 分析用户访问模式和峰值时段\n4. 检查数据库查询性能\n5. 观察缓存命中率\n6. 设置合理的告警阈值\n建议定期生成性能报告，识别瓶颈并优化。'
    };
    
    // 检查是否有预设回答
    for (const [key, value] of Object.entries(responses)) {
        if (message.includes(key)) {
            return value;
        }
    }
    
    // 默认回答
    return `感谢您的问题："${message}"。我正在分析相关信息，为您提供专业建议。基于我的知识库，我建议您：\n\n1. 首先确认具体情况和症状\n2. 查看相关的历史数据和趋势\n3. 考虑环境因素的影响\n4. 如需要，可以联系专业技术支持\n\n如果您需要更详细的分析，请提供更多具体信息。`;
}

// 快捷提问
function askQuestion(question) {
    document.getElementById('messageInput').value = question;
    sendMessage();
}

// 处理回车键
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// 清空对话
function clearChat() {
    if (confirm('确定要清空所有对话记录吗？')) {
        document.getElementById('chatHistory').innerHTML = '';
        chatHistory = [];
        // 重新添加欢迎消息
        addMessage('ai', '您好！我是智慧养鹅平台的AI助手。我可以帮您解答养鹅相关问题、分析系统数据、提供管理建议等。请问有什么可以帮助您的吗？');
    }
}

// 导出对话
function exportChat() {
    if (chatHistory.length === 0) {
        alert('暂无对话记录可导出');
        return;
    }
    
    let exportText = '智慧养鹅AI助手对话记录\n';
    exportText += '导出时间：' + new Date().toLocaleString() + '\n\n';
    
    chatHistory.forEach((msg, index) => {
        exportText += `${index + 1}. [${msg.sender === 'user' ? '用户' : 'AI助手'}] ${msg.timestamp.toLocaleString()}\n`;
        exportText += `${msg.text}\n\n`;
    });
    
    const blob = new Blob([exportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI对话记录_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// AI使用统计图表
const ctx = document.getElementById('aiUsageChart').getContext('2d');
const aiUsageChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['养殖咨询', '系统问题', '数据分析', '其他'],
        datasets: [{
            data: [45, 25, 20, 10],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
