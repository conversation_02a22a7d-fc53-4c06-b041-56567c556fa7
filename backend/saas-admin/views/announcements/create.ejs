<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-plus-circle me-2"></i>新建公告</h2>
        <a href="/announcements" class="btn btn-secondary">
          <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h5><i class="fas fa-edit me-2"></i>公告信息</h5>
        </div>
        <div class="card-body">
          <form id="announcementForm" action="/announcements" method="POST">
            <div class="mb-3">
              <label for="title" class="form-label">标题 <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="title" name="title" required maxlength="200">
              <div class="form-text">最多200个字符</div>
            </div>

            <div class="mb-3">
              <label for="type" class="form-label">类型</label>
              <select class="form-select" id="type" name="type">
                <option value="info">通知</option>
                <option value="warning">提醒</option>
                <option value="error">紧急</option>
                <option value="feature">功能</option>
                <option value="maintenance">维护</option>
              </select>
            </div>

            <div class="mb-3">
              <label for="priority" class="form-label">优先级</label>
              <select class="form-select" id="priority" name="priority">
                <option value="0">普通</option>
                <option value="1">重要</option>
                <option value="2">紧急</option>
                <option value="3">非常紧急</option>
              </select>
            </div>

            <div class="mb-3">
              <label for="target_audience" class="form-label">目标受众</label>
              <select class="form-select" id="target_audience" name="target_audience">
                <option value="all">全部用户</option>
                <option value="admin">管理员</option>
                <option value="tenant">租户</option>
                <option value="user">普通用户</option>
              </select>
            </div>

            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="is_top" name="is_top" value="1">
              <label class="form-check-label" for="is_top">
                置顶显示
              </label>
            </div>

            <div class="mb-3">
              <label for="content" class="form-label">内容 <span class="text-danger">*</span></label>
              <textarea class="form-control" id="content" name="content" rows="8" required placeholder="请输入公告内容..."></textarea>
            </div>

            <div class="mb-3">
              <label for="status" class="form-label">状态</label>
              <select class="form-select" id="status" name="status">
                <option value="draft">草稿</option>
                <option value="published" selected>发布</option>
                <option value="archived">存档</option>
              </select>
            </div>
          </form>
        </div>
        <div class="card-footer">
          <button type="submit" form="announcementForm" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>保存公告
          </button>
          <button type="button" class="btn btn-info ms-2" onclick="previewAnnouncement()">
            <i class="fas fa-eye me-1"></i>预览
          </button>
          <a href="/announcements" class="btn btn-secondary ms-2">
            <i class="fas fa-times me-1"></i>取消
          </a>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="card">
        <div class="card-header">
          <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
        </div>
        <div class="card-body">
          <h6>公告类型说明：</h6>
          <ul class="small">
            <li><strong>通知</strong>：一般性信息通知</li>
            <li><strong>提醒</strong>：重要提醒信息</li>
            <li><strong>紧急</strong>：紧急通知</li>
            <li><strong>功能</strong>：新功能发布</li>
            <li><strong>维护</strong>：系统维护通知</li>
          </ul>

          <h6>优先级说明：</h6>
          <ul class="small">
            <li><strong>普通</strong>：日常信息</li>
            <li><strong>重要</strong>：重要信息</li>
            <li><strong>紧急</strong>：紧急处理</li>
            <li><strong>非常紧急</strong>：立即处理</li>
          </ul>

          <h6>目标受众：</h6>
          <ul class="small">
            <li><strong>全部用户</strong>：所有用户可见</li>
            <li><strong>管理员</strong>：仅管理员可见</li>
            <li><strong>租户</strong>：仅租户管理员可见</li>
            <li><strong>普通用户</strong>：仅普通用户可见</li>
          </ul>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6><i class="fas fa-eye me-2"></i>预览</h6>
        </div>
        <div class="card-body">
          <div id="preview-content" class="text-muted">
            输入内容后可以预览效果...
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 表单提交处理
    document.getElementById('announcementForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const formData = new FormData(this);
      const data = Object.fromEntries(formData);
      
      // 处理复选框值
      data.is_top = document.getElementById('is_top').checked ? 1 : 0;
      
      fetch('/announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      .then(response => response.json())
      .then(result => {
        if (result.success) {
          alert('公告创建成功！');
          window.location.href = '/announcements';
        } else {
          alert('创建失败：' + result.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('创建失败，请重试！');
      });
    });

    // 实时预览
    document.getElementById('content').addEventListener('input', updatePreview);
    document.getElementById('title').addEventListener('input', updatePreview);
    document.getElementById('type').addEventListener('change', updatePreview);

    updatePreview();
  });

  function updatePreview() {
    const title = document.getElementById('title').value || '标题预览';
    const content = document.getElementById('content').value || '内容预览...';
    const type = document.getElementById('type').value;
    
    let typeClass = 'alert-info';
    let typeIcon = 'fa-info-circle';
    
    switch(type) {
      case 'warning':
        typeClass = 'alert-warning';
        typeIcon = 'fa-exclamation-triangle';
        break;
      case 'error':
        typeClass = 'alert-danger';
        typeIcon = 'fa-times-circle';
        break;
      case 'feature':
        typeClass = 'alert-success';
        typeIcon = 'fa-star';
        break;
      case 'maintenance':
        typeClass = 'alert-secondary';
        typeIcon = 'fa-wrench';
        break;
    }

    const previewHtml = `
      <div class="alert ${typeClass} mb-0">
        <h6><i class="fas ${typeIcon} me-2"></i>${title}</h6>
        <div style="white-space: pre-wrap;">${content}</div>
        <small class="text-muted">
          <i class="fas fa-clock me-1"></i>刚刚发布
        </small>
      </div>
    `;

    document.getElementById('preview-content').innerHTML = previewHtml;
  }

  function previewAnnouncement() {
    const modal = new bootstrap.Modal(document.createElement('div'));
    // 这里可以实现更详细的预览模态框
    alert('预览功能：' + document.getElementById('title').value);
  }
</script>