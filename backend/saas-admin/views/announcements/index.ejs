<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bullhorn me-2"></i>公告管理</h2>
                <div>
                    <a href="/announcements/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>创建公告
                    </a>
                    <a href="/announcements/stats" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-1"></i>统计分析
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><%= stats.total %></h3>
                    <p>总公告数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><%= stats.active %></h3>
                    <p>活跃公告</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3><%= stats.scheduled %></h3>
                    <p>定时发布</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3><%= stats.totalViews %></h3>
                    <p>总浏览量</p>
                </div>
                <div class="icon">
                    <i class="fas fa-eye"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 公告列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">公告列表</h3>
                    <div class="card-tools">
                        <div class="input-group input-group-sm" style="width: 250px;">
                            <input type="text" name="search" class="form-control float-right" placeholder="搜索公告...">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>类型</th>
                                <th>优先级</th>
                                <th>状态</th>
                                <th>目标用户</th>
                                <th>浏览量</th>
                                <th>发布时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% announcements.forEach(announcement => { %>
                            <tr>
                                <td><%= announcement.id %></td>
                                <td>
                                    <a href="/announcements/<%= announcement.id %>/edit" class="text-decoration-none">
                                        <%= announcement.title %>
                                    </a>
                                </td>
                                <td>
                                    <% if (announcement.type === 'system') { %>
                                        <span class="badge badge-primary">系统</span>
                                    <% } else if (announcement.type === 'feature') { %>
                                        <span class="badge badge-info">功能</span>
                                    <% } else if (announcement.type === 'maintenance') { %>
                                        <span class="badge badge-warning">维护</span>
                                    <% } else { %>
                                        <span class="badge badge-secondary">一般</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (announcement.priority === 'high') { %>
                                        <span class="badge badge-danger">高</span>
                                    <% } else if (announcement.priority === 'medium') { %>
                                        <span class="badge badge-warning">中</span>
                                    <% } else { %>
                                        <span class="badge badge-success">低</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (announcement.status === 'active') { %>
                                        <span class="badge badge-success">活跃</span>
                                    <% } else if (announcement.status === 'scheduled') { %>
                                        <span class="badge badge-info">定时</span>
                                    <% } else if (announcement.status === 'draft') { %>
                                        <span class="badge badge-secondary">草稿</span>
                                    <% } else { %>
                                        <span class="badge badge-dark">已过期</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (announcement.target_audience === 'all') { %>
                                        <span class="badge badge-primary">所有用户</span>
                                    <% } else if (announcement.target_audience === 'premium') { %>
                                        <span class="badge badge-warning">付费用户</span>
                                    <% } else { %>
                                        <span class="badge badge-info"><%= announcement.target_audience %></span>
                                    <% } %>
                                </td>
                                <td><%= announcement.views || 0 %></td>
                                <td><%= new Date(announcement.publish_time).toLocaleDateString() %></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/announcements/<%= announcement.id %>/edit" class="btn btn-info btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <% if (announcement.status === 'draft') { %>
                                        <button type="button" class="btn btn-success btn-sm" onclick="publishAnnouncement(<%= announcement.id %>)">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                        <% } else if (announcement.status === 'active') { %>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="withdrawAnnouncement(<%= announcement.id %>)">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <% } %>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteAnnouncement(<%= announcement.id %>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <% if (typeof isDemo !== 'undefined' && isDemo) { %>
    <div class="alert alert-info mt-3">
        <i class="fas fa-info-circle me-2"></i>
        当前显示的是演示数据，实际使用时将从数据库加载真实数据。
    </div>
    <% } %>
</div>

<script>
function publishAnnouncement(id) {
    if (confirm('确定要发布这个公告吗？')) {
        fetch(`/announcements/${id}/publish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发布失败，请稍后重试');
        });
    }
}

function withdrawAnnouncement(id) {
    if (confirm('确定要撤回这个公告吗？')) {
        fetch(`/announcements/${id}/withdraw`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('撤回失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('撤回失败，请稍后重试');
        });
    }
}

function deleteAnnouncement(id) {
    if (confirm('确定要删除这个公告吗？此操作不可恢复。')) {
        fetch(`/announcements/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请稍后重试');
        });
    }
}
</script>

<%- include('../layouts/footer') %>
