<!-- 编辑公告页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-edit me-2"></i>
                    编辑公告
                </h2>
                <div>
                    <a href="/announcements/<%= announcement.id %>" class="btn btn-outline-info me-2" target="_blank">
                        <i class="fas fa-eye me-1"></i>
                        预览公告
                    </a>
                    <a href="/announcements" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">公告内容</h3>
                </div>
                <div class="card-body">
                    <form id="announcementForm">
                        <input type="hidden" id="announcementId" value="<%= announcement.id %>">
                        
                        <div class="form-group">
                            <label for="title">公告标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<%= announcement.title %>" placeholder="请输入公告标题" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type">公告类型</label>
                                    <select class="form-control" id="type" name="type">
                                        <option value="general" <%= announcement.type === 'general' ? 'selected' : '' %>>通用公告</option>
                                        <option value="system" <%= announcement.type === 'system' ? 'selected' : '' %>>系统公告</option>
                                        <option value="maintenance" <%= announcement.type === 'maintenance' ? 'selected' : '' %>>维护公告</option>
                                        <option value="feature" <%= announcement.type === 'feature' ? 'selected' : '' %>>功能更新</option>
                                        <option value="security" <%= announcement.type === 'security' ? 'selected' : '' %>>安全公告</option>
                                        <option value="emergency" <%= announcement.type === 'emergency' ? 'selected' : '' %>>紧急公告</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">优先级</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low" <%= announcement.priority === 'low' ? 'selected' : '' %>>低</option>
                                        <option value="medium" <%= announcement.priority === 'medium' ? 'selected' : '' %>>中</option>
                                        <option value="high" <%= announcement.priority === 'high' ? 'selected' : '' %>>高</option>
                                        <option value="urgent" <%= announcement.priority === 'urgent' ? 'selected' : '' %>>紧急</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="target_audience">目标受众</label>
                            <select class="form-control" id="target_audience" name="target_audience">
                                <option value="all" <%= announcement.target_audience === 'all' ? 'selected' : '' %>>所有用户</option>
                                <option value="admin" <%= announcement.target_audience === 'admin' ? 'selected' : '' %>>管理员</option>
                                <option value="tenant" <%= announcement.target_audience === 'tenant' ? 'selected' : '' %>>租户用户</option>
                                <option value="basic" <%= announcement.target_audience === 'basic' ? 'selected' : '' %>>基础版用户</option>
                                <option value="premium" <%= announcement.target_audience === 'premium' ? 'selected' : '' %>>高级版用户</option>
                                <option value="enterprise" <%= announcement.target_audience === 'enterprise' ? 'selected' : '' %>>企业版用户</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="content">公告内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" 
                                      placeholder="请输入公告内容，支持HTML格式" required><%= announcement.content %></textarea>
                        </div>

                        <div class="form-group">
                            <label for="summary">公告摘要</label>
                            <textarea class="form-control" id="summary" name="summary" rows="3" 
                                      placeholder="请输入公告摘要，用于列表显示"><%= announcement.summary || '' %></textarea>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- 公告信息 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">公告信息</h3>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <strong>公告ID:</strong> <%= announcement.id %>
                    </div>
                    <div class="info-item">
                        <strong>创建时间:</strong> <%= new Date(announcement.created_at || announcement.createdAt).toLocaleString() %>
                    </div>
                    <div class="info-item">
                        <strong>更新时间:</strong> <%= new Date(announcement.updated_at || announcement.updatedAt).toLocaleString() %>
                    </div>
                    <div class="info-item">
                        <strong>浏览次数:</strong> <%= announcement.views || 0 %>
                    </div>
                    <div class="info-item">
                        <strong>作者:</strong> <%= announcement.author || '系统管理员' %>
                    </div>
                </div>
            </div>

            <!-- 发布设置 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布设置</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="status">发布状态</label>
                        <select class="form-control" id="status" name="status">
                            <option value="draft" <%= announcement.status === 'draft' ? 'selected' : '' %>>草稿</option>
                            <option value="active" <%= announcement.status === 'active' ? 'selected' : '' %>>已发布</option>
                            <option value="scheduled" <%= announcement.status === 'scheduled' ? 'selected' : '' %>>定时发布</option>
                            <option value="expired" <%= announcement.status === 'expired' ? 'selected' : '' %>>已过期</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="publish_time">发布时间</label>
                                <input type="datetime-local" class="form-control" id="publish_time" name="publish_time" 
                                       value="<%= announcement.publish_time ? new Date(announcement.publish_time).toISOString().slice(0, 16) : '' %>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="expire_time">过期时间</label>
                                <input type="datetime-local" class="form-control" id="expire_time" name="expire_time" 
                                       value="<%= announcement.expire_time ? new Date(announcement.expire_time).toISOString().slice(0, 16) : '' %>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="is_top" name="is_top" 
                                   <%= announcement.is_top ? 'checked' : '' %>>
                            <label class="custom-control-label" for="is_top">置顶公告</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="is_important" name="is_important" 
                                   <%= announcement.is_important ? 'checked' : '' %>>
                            <label class="custom-control-label" for="is_important">重要公告</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="send_notification" name="send_notification">
                            <label class="custom-control-label" for="send_notification">发送通知</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 附件管理 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">附件管理</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="attachments">上传附件</label>
                        <input type="file" class="form-control-file" id="attachments" name="attachments" 
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar" multiple>
                        <small class="form-text text-muted">支持PDF、Office文档、压缩包等格式</small>
                    </div>

                    <% if (announcement.attachments && announcement.attachments.length > 0) { %>
                    <div class="current-attachments">
                        <label>当前附件:</label>
                        <% announcement.attachments.forEach((attachment, index) => { %>
                        <div class="attachment-item d-flex justify-content-between align-items-center mb-2">
                            <span>
                                <i class="fas fa-file me-2"></i>
                                <%= attachment.name %>
                            </span>
                            <button type="button" class="btn btn-sm btn-danger" onclick="removeAttachment(<%= index %>)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <% }) %>
                    </div>
                    <% } %>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-body">
                    <button type="button" class="btn btn-primary btn-block" onclick="updateAnnouncement()">
                        <i class="fas fa-save me-1"></i>
                        更新公告
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-block mt-2" onclick="previewAnnouncement()">
                        <i class="fas fa-eye me-1"></i>
                        预览公告
                    </button>
                    <% if (announcement.status === 'draft') { %>
                    <button type="button" class="btn btn-success btn-block mt-2" onclick="publishAnnouncement()">
                        <i class="fas fa-paper-plane me-1"></i>
                        立即发布
                    </button>
                    <% } else if (announcement.status === 'active') { %>
                    <button type="button" class="btn btn-warning btn-block mt-2" onclick="withdrawAnnouncement()">
                        <i class="fas fa-undo me-1"></i>
                        撤回公告
                    </button>
                    <% } %>
                    <button type="button" class="btn btn-outline-danger btn-block mt-2" onclick="deleteAnnouncement()">
                        <i class="fas fa-trash me-1"></i>
                        删除公告
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.attachment-item {
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
}
</style>

<script>
// 更新公告
function updateAnnouncement() {
    const form = document.getElementById('announcementForm');
    const formData = new FormData(form);
    const announcementId = document.getElementById('announcementId').value;
    
    // 表单验证
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 显示加载状态
    const updateBtn = event.target;
    const originalText = updateBtn.innerHTML;
    updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>更新中...';
    updateBtn.disabled = true;
    
    // 发送请求
    fetch(`/announcements/${announcementId}/update`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('公告更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请稍后重试');
    })
    .finally(() => {
        updateBtn.innerHTML = originalText;
        updateBtn.disabled = false;
    });
}

// 预览公告
function previewAnnouncement() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    
    if (!title || !content) {
        alert('请先填写标题和内容');
        return;
    }
    
    // 打开预览窗口
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
        <head>
            <title>预览：${title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                .content { margin-top: 20px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <div class="meta">
                类型: ${document.getElementById('type').options[document.getElementById('type').selectedIndex].text} | 
                优先级: ${document.getElementById('priority').options[document.getElementById('priority').selectedIndex].text}
            </div>
            <div class="content">${content}</div>
        </body>
        </html>
    `);
}

// 发布公告
function publishAnnouncement() {
    if (confirm('确定要立即发布这个公告吗？')) {
        const announcementId = document.getElementById('announcementId').value;
        
        fetch(`/announcements/${announcementId}/publish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('公告发布成功！');
                location.reload();
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('发布失败:', error);
            alert('发布失败，请稍后重试');
        });
    }
}

// 撤回公告
function withdrawAnnouncement() {
    if (confirm('确定要撤回这个公告吗？撤回后用户将无法看到此公告。')) {
        const announcementId = document.getElementById('announcementId').value;
        
        fetch(`/announcements/${announcementId}/withdraw`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('公告撤回成功！');
                location.reload();
            } else {
                alert('撤回失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('撤回失败:', error);
            alert('撤回失败，请稍后重试');
        });
    }
}

// 删除公告
function deleteAnnouncement() {
    if (confirm('确定要删除这个公告吗？此操作不可恢复。')) {
        const announcementId = document.getElementById('announcementId').value;
        
        fetch(`/announcements/${announcementId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('公告删除成功！');
                window.location.href = '/announcements';
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            alert('删除失败，请稍后重试');
        });
    }
}

// 删除附件
function removeAttachment(index) {
    if (confirm('确定要删除这个附件吗？')) {
        // 这里应该发送请求到后端删除附件
        alert('删除附件功能开发中...');
    }
}
</script>
