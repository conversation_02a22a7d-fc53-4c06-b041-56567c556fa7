<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus me-2"></i>新增租户</h2>
                <a href="/tenants" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <form id="tenantForm" method="POST" action="/tenants/create">
        <div class="row">
            <div class="col-lg-8">
                <!-- 基本信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">公司名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="company_name" name="company_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_person" class="form-label">联系人 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="province" class="form-label">省份 <span class="text-danger">*</span></label>
                                <select class="form-select" id="province" name="province" required>
                                    <option value="">请选择省份</option>
                                    <option value="广东省">广东省</option>
                                    <option value="江苏省">江苏省</option>
                                    <option value="浙江省">浙江省</option>
                                    <option value="山东省">山东省</option>
                                    <option value="河南省">河南省</option>
                                    <option value="四川省">四川省</option>
                                    <option value="湖北省">湖北省</option>
                                    <option value="湖南省">湖南省</option>
                                    <option value="安徽省">安徽省</option>
                                    <option value="河北省">河北省</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">城市 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="district" class="form-label">区县</label>
                                <input type="text" class="form-control" id="district" name="district">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">详细地址</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 业务信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-briefcase me-2"></i>业务信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="business_type" class="form-label">业务类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="business_type" name="business_type" required>
                                    <option value="">请选择业务类型</option>
                                    <option value="养殖场">养殖场</option>
                                    <option value="合作社">合作社</option>
                                    <option value="企业">企业</option>
                                    <option value="个人">个人</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="scale" class="form-label">养殖规模</label>
                                <select class="form-select" id="scale" name="scale">
                                    <option value="">请选择规模</option>
                                    <option value="小规模">小规模（500只以下）</option>
                                    <option value="中规模">中规模（500-2000只）</option>
                                    <option value="大规模">大规模（2000只以上）</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="registration_source" class="form-label">注册来源</label>
                                <select class="form-select" id="registration_source" name="registration_source">
                                    <option value="官网">官网注册</option>
                                    <option value="推广">推广渠道</option>
                                    <option value="介绍">朋友介绍</option>
                                    <option value="展会">展会</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="expected_start_date" class="form-label">预计开始时间</label>
                                <input type="date" class="form-control" id="expected_start_date" name="expected_start_date">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户设置 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-cog me-2"></i>账户设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="admin_username" class="form-label">管理员用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" required>
                                <div class="form-text">用于登录系统的用户名</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="admin_password" class="form-label">初始密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                <div class="form-text">至少6位字符</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="max_users" class="form-label">最大用户数</label>
                                <input type="number" class="form-control" id="max_users" name="max_users" min="1" max="1000" value="10">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="storage_limit" class="form-label">存储限制(GB)</label>
                                <input type="number" class="form-control" id="storage_limit" name="storage_limit" min="1" max="1000" value="5">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- 套餐选择 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-package me-2"></i>套餐选择</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="subscription_plan" id="plan_basic" value="basic" checked>
                                <label class="form-check-label" for="plan_basic">
                                    <strong>基础版</strong><br>
                                    <small class="text-muted">适合小规模养殖户</small><br>
                                    <span class="text-primary">¥99/月</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="subscription_plan" id="plan_standard" value="standard">
                                <label class="form-check-label" for="plan_standard">
                                    <strong>标准版</strong><br>
                                    <small class="text-muted">适合中等规模养殖场</small><br>
                                    <span class="text-primary">¥199/月</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="subscription_plan" id="plan_premium" value="premium">
                                <label class="form-check-label" for="plan_premium">
                                    <strong>高级版</strong><br>
                                    <small class="text-muted">适合大规模养殖企业</small><br>
                                    <span class="text-primary">¥399/月</span>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="trial_days" class="form-label">试用天数</label>
                            <select class="form-select" id="trial_days" name="trial_days">
                                <option value="0">不试用</option>
                                <option value="7">7天试用</option>
                                <option value="15" selected>15天试用</option>
                                <option value="30">30天试用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 功能权限 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-key me-2"></i>功能权限</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_flock" name="features" value="flock_management" checked>
                            <label class="form-check-label" for="feature_flock">
                                鹅群管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_production" name="features" value="production_record" checked>
                            <label class="form-check-label" for="feature_production">
                                生产记录
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_health" name="features" value="health_monitor" checked>
                            <label class="form-check-label" for="feature_health">
                                健康监控
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_finance" name="features" value="finance_manage">
                            <label class="form-check-label" for="feature_finance">
                                财务管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_inventory" name="features" value="inventory_manage">
                            <label class="form-check-label" for="feature_inventory">
                                库存管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_reports" name="features" value="reports">
                            <label class="form-check-label" for="feature_reports">
                                报表分析
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 备注信息 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-sticky-note me-2"></i>备注信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">备注</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4" placeholder="请输入备注信息..."></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="send_welcome_email" name="send_welcome_email" value="1" checked>
                            <label class="form-check-label" for="send_welcome_email">
                                发送欢迎邮件
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>创建租户
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表单验证
    const form = document.getElementById('tenantForm');
    
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('admin_username').value;
        const password = document.getElementById('admin_password').value;
        
        // 验证用户名格式
        if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
            e.preventDefault();
            alert('用户名只能包含字母、数字和下划线，长度3-20位');
            return false;
        }
        
        // 验证密码强度
        if (password.length < 6) {
            e.preventDefault();
            alert('密码至少需要6位字符');
            return false;
        }
        
        // 显示提交状态
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>创建中...';
        submitBtn.disabled = true;
    });

    // 套餐选择联动功能权限
    const planRadios = document.querySelectorAll('input[name="subscription_plan"]');
    planRadios.forEach(radio => {
        radio.addEventListener('change', updateFeaturesByPlan);
    });

    function updateFeaturesByPlan() {
        const selectedPlan = document.querySelector('input[name="subscription_plan"]:checked').value;
        const features = document.querySelectorAll('input[name="features"]');
        
        // 根据套餐类型设置默认功能权限
        features.forEach(feature => {
            switch(selectedPlan) {
                case 'basic':
                    if (['flock_management', 'production_record', 'health_monitor'].includes(feature.value)) {
                        feature.checked = true;
                    } else {
                        feature.checked = false;
                    }
                    break;
                case 'standard':
                    if (['flock_management', 'production_record', 'health_monitor', 'finance_manage', 'inventory_manage'].includes(feature.value)) {
                        feature.checked = true;
                    } else {
                        feature.checked = false;
                    }
                    break;
                case 'premium':
                    feature.checked = true;
                    break;
            }
        });
    }

    // 初始化默认功能权限
    updateFeaturesByPlan();
});
</script>