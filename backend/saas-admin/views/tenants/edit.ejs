<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>编辑租户 - <%= tenant.company_name %></h2>
                <div>
                    <a href="/tenants/<%= tenant.id %>" class="btn btn-info me-2">
                        <i class="fas fa-eye me-1"></i>查看详情
                    </a>
                    <a href="/tenants" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form id="tenantEditForm" method="POST" action="/tenants/<%= tenant.id %>/update">
        <div class="row">
            <div class="col-lg-8">
                <!-- 基本信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">公司名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="company_name" name="company_name" value="<%= tenant.company_name %>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_person" class="form-label">联系人 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" value="<%= tenant.contact_person %>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" value="<%= tenant.contact_phone %>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" value="<%= tenant.contact_email %>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="province" class="form-label">省份 <span class="text-danger">*</span></label>
                                <select class="form-select" id="province" name="province" required>
                                    <option value="">请选择省份</option>
                                    <option value="广东省" <%= tenant.province === '广东省' ? 'selected' : '' %>>广东省</option>
                                    <option value="江苏省" <%= tenant.province === '江苏省' ? 'selected' : '' %>>江苏省</option>
                                    <option value="浙江省" <%= tenant.province === '浙江省' ? 'selected' : '' %>>浙江省</option>
                                    <option value="山东省" <%= tenant.province === '山东省' ? 'selected' : '' %>>山东省</option>
                                    <option value="河南省" <%= tenant.province === '河南省' ? 'selected' : '' %>>河南省</option>
                                    <option value="四川省" <%= tenant.province === '四川省' ? 'selected' : '' %>>四川省</option>
                                    <option value="湖北省" <%= tenant.province === '湖北省' ? 'selected' : '' %>>湖北省</option>
                                    <option value="湖南省" <%= tenant.province === '湖南省' ? 'selected' : '' %>>湖南省</option>
                                    <option value="安徽省" <%= tenant.province === '安徽省' ? 'selected' : '' %>>安徽省</option>
                                    <option value="河北省" <%= tenant.province === '河北省' ? 'selected' : '' %>>河北省</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">城市 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" value="<%= tenant.city %>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="district" class="form-label">区县</label>
                                <input type="text" class="form-control" id="district" name="district" value="<%= tenant.district %>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">详细地址</label>
                            <textarea class="form-control" id="address" name="address" rows="2"><%= tenant.address %></textarea>
                        </div>
                    </div>
                </div>

                <!-- 业务信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-briefcase me-2"></i>业务信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="business_type" class="form-label">业务类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="business_type" name="business_type" required>
                                    <option value="">请选择业务类型</option>
                                    <option value="养殖场" <%= tenant.business_type === '养殖场' ? 'selected' : '' %>>养殖场</option>
                                    <option value="合作社" <%= tenant.business_type === '合作社' ? 'selected' : '' %>>合作社</option>
                                    <option value="企业" <%= tenant.business_type === '企业' ? 'selected' : '' %>>企业</option>
                                    <option value="个人" <%= tenant.business_type === '个人' ? 'selected' : '' %>>个人</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="scale" class="form-label">养殖规模</label>
                                <select class="form-select" id="scale" name="scale">
                                    <option value="">请选择规模</option>
                                    <option value="小规模" <%= tenant.scale === '小规模' ? 'selected' : '' %>>小规模（500只以下）</option>
                                    <option value="中规模" <%= tenant.scale === '中规模' ? 'selected' : '' %>>中规模（500-2000只）</option>
                                    <option value="大规模" <%= tenant.scale === '大规模' ? 'selected' : '' %>>大规模（2000只以上）</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="registration_source" class="form-label">注册来源</label>
                                <select class="form-select" id="registration_source" name="registration_source">
                                    <option value="官网" <%= tenant.registration_source === '官网' ? 'selected' : '' %>>官网注册</option>
                                    <option value="推广" <%= tenant.registration_source === '推广' ? 'selected' : '' %>>推广渠道</option>
                                    <option value="介绍" <%= tenant.registration_source === '介绍' ? 'selected' : '' %>>朋友介绍</option>
                                    <option value="展会" <%= tenant.registration_source === '展会' ? 'selected' : '' %>>展会</option>
                                    <option value="其他" <%= tenant.registration_source === '其他' ? 'selected' : '' %>>其他</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">状态 <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <%= tenant.status === 'active' ? 'selected' : '' %>>正常</option>
                                    <option value="suspended" <%= tenant.status === 'suspended' ? 'selected' : '' %>>暂停</option>
                                    <option value="expired" <%= tenant.status === 'expired' ? 'selected' : '' %>>过期</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户设置 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-cog me-2"></i>账户设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="max_users" class="form-label">最大用户数</label>
                                <input type="number" class="form-control" id="max_users" name="max_users" min="1" max="1000" value="<%= tenant.max_users %>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="storage_limit" class="form-label">存储限制(GB)</label>
                                <input type="number" class="form-control" id="storage_limit" name="storage_limit" min="1" max="1000" value="<%= tenant.storage_limit %>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="current_users" class="form-label">当前用户数</label>
                                <input type="number" class="form-control" id="current_users" value="<%= tenant.current_users || 0 %>" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="storage_used" class="form-label">已用存储(GB)</label>
                                <input type="number" class="form-control" id="storage_used" value="<%= tenant.storage_used || 0 %>" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- 套餐信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-package me-2"></i>套餐信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="subscription_plan" class="form-label">当前套餐</label>
                            <select class="form-select" id="subscription_plan" name="subscription_plan">
                                <option value="basic" <%= tenant.subscription_plan === 'basic' ? 'selected' : '' %>>基础版 - ¥99/月</option>
                                <option value="standard" <%= tenant.subscription_plan === 'standard' ? 'selected' : '' %>>标准版 - ¥199/月</option>
                                <option value="premium" <%= tenant.subscription_plan === 'premium' ? 'selected' : '' %>>高级版 - ¥399/月</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="subscription_start" class="form-label">订阅开始时间</label>
                            <input type="date" class="form-control" id="subscription_start" name="subscription_start" value="<%= tenant.subscription_start ? Utils.formatDate(tenant.subscription_start, 'YYYY-MM-DD') : '' %>">
                        </div>

                        <div class="mb-3">
                            <label for="subscription_end" class="form-label">订阅结束时间</label>
                            <input type="date" class="form-control" id="subscription_end" name="subscription_end" value="<%= tenant.subscription_end ? Utils.formatDate(tenant.subscription_end, 'YYYY-MM-DD') : '' %>">
                        </div>

                        <div class="mb-3">
                            <label for="trial_end" class="form-label">试用结束时间</label>
                            <input type="date" class="form-control" id="trial_end" name="trial_end" value="<%= tenant.trial_end ? Utils.formatDate(tenant.trial_end, 'YYYY-MM-DD') : '' %>">
                        </div>

                        <div class="alert alert-info">
                            <small>
                                <strong>注册时间：</strong><%= Utils.formatDate(tenant.created_at) %><br>
                                <strong>最后更新：</strong><%= Utils.formatDate(tenant.updated_at) %><br>
                                <% if (tenant.last_login_time) { %>
                                    <strong>最后登录：</strong><%= Utils.formatDate(tenant.last_login_time) %>
                                <% } %>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- 功能权限 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-key me-2"></i>功能权限</h5>
                    </div>
                    <div class="card-body">
                        <% const features = tenant.enabled_features ? tenant.enabled_features.split(',') : []; %>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_flock" name="features" value="flock_management" <%= features.includes('flock_management') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_flock">
                                鹅群管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_production" name="features" value="production_record" <%= features.includes('production_record') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_production">
                                生产记录
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_health" name="features" value="health_monitor" <%= features.includes('health_monitor') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_health">
                                健康监控
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_finance" name="features" value="finance_manage" <%= features.includes('finance_manage') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_finance">
                                财务管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_inventory" name="features" value="inventory_manage" <%= features.includes('inventory_manage') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_inventory">
                                库存管理
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="feature_reports" name="features" value="reports" <%= features.includes('reports') ? 'checked' : '' %>>
                            <label class="form-check-label" for="feature_reports">
                                报表分析
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 操作记录 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>最近操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <% if (tenant.operation_logs && tenant.operation_logs.length > 0) { %>
                                <% tenant.operation_logs.slice(0, 5).forEach(log => { %>
                                    <div class="mb-2 pb-2 border-bottom">
                                        <strong><%= log.operation_desc %></strong><br>
                                        <span class="text-muted">
                                            <%= Utils.formatDate(log.created_at) %>
                                        </span>
                                    </div>
                                <% }) %>
                            <% } else { %>
                                <p class="text-muted mb-0">暂无操作记录</p>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备注信息 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-sticky-note me-2"></i>备注信息</h5>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="remarks" name="remarks" rows="4" placeholder="请输入备注信息..."><%= tenant.remarks %></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <div>
                        <% if (tenant.status === 'active') { %>
                            <button type="button" class="btn btn-warning" onclick="suspendTenant(<%= tenant.id %>)">
                                <i class="fas fa-pause me-1"></i>暂停服务
                            </button>
                        <% } else if (tenant.status === 'suspended') { %>
                            <button type="button" class="btn btn-success" onclick="activeTenant(<%= tenant.id %>)">
                                <i class="fas fa-play me-1"></i>恢复服务
                            </button>
                        <% } %>
                        
                        <button type="button" class="btn btn-info ms-2" onclick="resetPassword(<%= tenant.id %>)">
                            <i class="fas fa-key me-1"></i>重置密码
                        </button>
                    </div>
                    
                    <div>
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存更改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('tenantEditForm');
    
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
        submitBtn.disabled = true;
    });
});

function suspendTenant(id) {
    if (confirm('确定要暂停该租户的服务吗？')) {
        fetch(`/tenants/${id}/suspend`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        });
    }
}

function activeTenant(id) {
    if (confirm('确定要恢复该租户的服务吗？')) {
        fetch(`/tenants/${id}/activate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败：' + data.message);
            }
        });
    }
}

function resetPassword(id) {
    if (confirm('确定要重置该租户管理员的密码吗？')) {
        fetch(`/tenants/${id}/reset-password`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('密码重置成功，新密码已发送到租户邮箱');
            } else {
                alert('重置失败：' + data.message);
            }
        });
    }
}
</script>