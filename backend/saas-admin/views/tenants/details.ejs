<div class="row">
    <!-- 租户基本信息 -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        租户详情 - <%= tenant.tenant_name %>
                    </h5>
                    <div>
                        <a href="/tenants/<%= tenant.id %>/edit" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>
                            编辑
                        </a>
                        <a href="/tenants" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            返回
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>租户代码:</strong></td>
                                <td><%= tenant.tenant_code %></td>
                            </tr>
                            <tr>
                                <td><strong>租户名称:</strong></td>
                                <td><%= tenant.tenant_name %></td>
                            </tr>
                            <tr>
                                <td><strong>租户类型:</strong></td>
                                <td>
                                    <% if (tenant.tenant_type === 'individual') { %>
                                        <span class="badge bg-primary">个人</span>
                                    <% } else if (tenant.tenant_type === 'enterprise') { %>
                                        <span class="badge bg-success">企业</span>
                                    <% } else if (tenant.tenant_type === 'cooperative') { %>
                                        <span class="badge bg-info">合作社</span>
                                    <% } %>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>联系人:</strong></td>
                                <td><%= tenant.contact_person %></td>
                            </tr>
                            <tr>
                                <td><strong>联系电话:</strong></td>
                                <td><%= tenant.contact_phone %></td>
                            </tr>
                            <tr>
                                <td><strong>联系邮箱:</strong></td>
                                <td><%= tenant.contact_email %></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>地址:</strong></td>
                                <td><%= tenant.address || '未填写' %></td>
                            </tr>
                            <tr>
                                <td><strong>营业执照:</strong></td>
                                <td><%= tenant.business_license || '未填写' %></td>
                            </tr>
                            <tr>
                                <td><strong>税号:</strong></td>
                                <td><%= tenant.tax_number || '未填写' %></td>
                            </tr>
                            <tr>
                                <td><strong>订阅计划:</strong></td>
                                <td>
                                    <% if (tenant.subscription_plan === 'basic') { %>
                                        <span class="badge bg-secondary">基础版</span>
                                    <% } else if (tenant.subscription_plan === 'standard') { %>
                                        <span class="badge bg-primary">标准版</span>
                                    <% } else if (tenant.subscription_plan === 'premium') { %>
                                        <span class="badge bg-warning">高级版</span>
                                    <% } else if (tenant.subscription_plan === 'enterprise') { %>
                                        <span class="badge bg-success">企业版</span>
                                    <% } %>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>订阅期限:</strong></td>
                                <td>
                                    <% if (tenant.subscription_start && tenant.subscription_end) { %>
                                        <%= new Date(tenant.subscription_start).toLocaleDateString() %> - 
                                        <%= new Date(tenant.subscription_end).toLocaleDateString() %>
                                    <% } else { %>
                                        无期限
                                    <% } %>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td>
                                    <% if (tenant.status === 'active') { %>
                                        <span class="badge bg-success">活跃</span>
                                    <% } else if (tenant.status === 'suspended') { %>
                                        <span class="badge bg-warning">暂停</span>
                                    <% } else { %>
                                        <span class="badge bg-danger">停用</span>
                                    <% } %>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><%= users.length %></h3>
                <p>用户数</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><%= flocks.length %></h3>
                <p>鹅群数</p>
            </div>
            <div class="icon">
                <i class="fas fa-egg"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><%= productions.length %></h3>
                <p>生产记录</p>
            </div>
            <div class="icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><%= healthRecords.length %></h3>
                <p>健康记录</p>
            </div>
            <div class="icon">
                <i class="fas fa-heartbeat"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-primary">
            <div class="inner">
                <h3><%= financialRecords.length %></h3>
                <p>财务记录</p>
            </div>
            <div class="icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-6">
        <div class="small-box bg-secondary">
            <div class="inner">
                <h3><%= orders.length %></h3>
                <p>订单数</p>
            </div>
            <div class="icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
        </div>
    </div>
</div>

<!-- 详细数据标签页 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header p-0 border-bottom-0">
                <ul class="nav nav-tabs" id="tenant-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                            <i class="fas fa-users me-1"></i>
                            用户管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="flocks-tab" data-bs-toggle="tab" data-bs-target="#flocks" type="button" role="tab">
                            <i class="fas fa-egg me-1"></i>
                            鹅群管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="production-tab" data-bs-toggle="tab" data-bs-target="#production" type="button" role="tab">
                            <i class="fas fa-chart-line me-1"></i>
                            生产记录
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="health-tab" data-bs-toggle="tab" data-bs-target="#health" type="button" role="tab">
                            <i class="fas fa-heartbeat me-1"></i>
                            健康记录
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                            <i class="fas fa-dollar-sign me-1"></i>
                            财务记录
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                            <i class="fas fa-shopping-cart me-1"></i>
                            订单管理
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="tenant-tabContent">
                    
                    <!-- 用户管理标签页 -->
                    <div class="tab-pane fade show active" id="users" role="tabpanel">
                        <% if (users.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>姓名</th>
                                            <th>邮箱</th>
                                            <th>角色</th>
                                            <th>状态</th>
                                            <th>农场名称</th>
                                            <th>电话</th>
                                            <th>最后登录</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% users.forEach(function(user) { %>
                                            <tr>
                                                <td><%= user.username %></td>
                                                <td><%= user.name %></td>
                                                <td><%= user.email %></td>
                                                <td>
                                                    <% if (user.role === 'admin') { %>
                                                        <span class="badge bg-danger">管理员</span>
                                                    <% } else if (user.role === 'manager') { %>
                                                        <span class="badge bg-warning">经理</span>
                                                    <% } else { %>
                                                        <span class="badge bg-primary">用户</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% if (user.status === 'active') { %>
                                                        <span class="badge bg-success">活跃</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger">停用</span>
                                                    <% } %>
                                                </td>
                                                <td><%= user.farmName || '未填写' %></td>
                                                <td><%= user.phone || '未填写' %></td>
                                                <td>
                                                    <% if (user.lastLoginAt) { %>
                                                        <%= new Date(user.lastLoginAt).toLocaleString() %>
                                                    <% } else { %>
                                                        从未登录
                                                    <% } %>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>暂无用户</h5>
                            </div>
                        <% } %>
                    </div>

                    <!-- 鹅群管理标签页 -->
                    <div class="tab-pane fade" id="flocks" role="tabpanel">
                        <% if (flocks.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>鹅群名称</th>
                                            <th>品种</th>
                                            <th>当前数量</th>
                                            <th>总数量</th>
                                            <th>负责人</th>
                                            <th>状态</th>
                                            <th>建立日期</th>
                                            <th>位置</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% flocks.forEach(function(flock) { %>
                                            <tr>
                                                <td><%= flock.name %></td>
                                                <td><%= flock.breed %></td>
                                                <td><%= flock.currentCount %></td>
                                                <td><%= flock.totalCount %></td>
                                                <td><%= flock.owner_name || '未指定' %></td>
                                                <td>
                                                    <% if (flock.status === 'active') { %>
                                                        <span class="badge bg-success">活跃</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger">停用</span>
                                                    <% } %>
                                                </td>
                                                <td><%= flock.establishedDate ? new Date(flock.establishedDate).toLocaleDateString() : '未填写' %></td>
                                                <td><%= flock.location || '未填写' %></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-egg fa-3x text-muted mb-3"></i>
                                <h5>暂无鹅群</h5>
                            </div>
                        <% } %>
                    </div>

                    <!-- 生产记录标签页 -->
                    <div class="tab-pane fade" id="production" role="tabpanel">
                        <% if (productions.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>记录日期</th>
                                            <th>鹅群</th>
                                            <th>产蛋数量</th>
                                            <th>记录员</th>
                                            <th>备注</th>
                                            <th>记录时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% productions.forEach(function(prod) { %>
                                            <tr>
                                                <td><%= new Date(prod.recordedDate).toLocaleDateString() %></td>
                                                <td><%= prod.flock_name || '未知鹅群' %></td>
                                                <td><%= prod.eggCount %></td>
                                                <td><%= prod.user_name || '未知' %></td>
                                                <td><%= prod.notes || '无' %></td>
                                                <td><%= new Date(prod.createdAt).toLocaleString() %></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                            <% if (productions.length >= 10) { %>
                                <div class="text-center">
                                    <small class="text-muted">只显示最近10条记录</small>
                                </div>
                            <% } %>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5>暂无生产记录</h5>
                            </div>
                        <% } %>
                    </div>

                    <!-- 健康记录标签页 -->
                    <div class="tab-pane fade" id="health" role="tabpanel">
                        <% if (healthRecords.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>检查日期</th>
                                            <th>鹅群</th>
                                            <th>健康状态</th>
                                            <th>体温</th>
                                            <th>症状</th>
                                            <th>治疗方案</th>
                                            <th>兽医</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% healthRecords.forEach(function(health) { %>
                                            <tr>
                                                <td><%= new Date(health.checkDate).toLocaleDateString() %></td>
                                                <td><%= health.flock_name || '未知鹅群' %></td>
                                                <td>
                                                    <% if (health.healthStatus === 'healthy') { %>
                                                        <span class="badge bg-success">健康</span>
                                                    <% } else if (health.healthStatus === 'warning') { %>
                                                        <span class="badge bg-warning">警告</span>
                                                    <% } else if (health.healthStatus === 'critical') { %>
                                                        <span class="badge bg-danger">严重</span>
                                                    <% } %>
                                                </td>
                                                <td><%= health.temperature || '未测量' %>°C</td>
                                                <td><%= health.symptoms || '无' %></td>
                                                <td><%= health.treatment || '无' %></td>
                                                <td><%= health.veterinarian || '未填写' %></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
                                <h5>暂无健康记录</h5>
                            </div>
                        <% } %>
                    </div>

                    <!-- 财务记录标签页 -->
                    <div class="tab-pane fade" id="financial" role="tabpanel">
                        <% if (financialRecords.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>类型</th>
                                            <th>分类</th>
                                            <th>金额</th>
                                            <th>描述</th>
                                            <th>支付方式</th>
                                            <th>记录员</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% financialRecords.forEach(function(finance) { %>
                                            <tr>
                                                <td><%= new Date(finance.recordDate).toLocaleDateString() %></td>
                                                <td>
                                                    <% if (finance.type === 'income') { %>
                                                        <span class="badge bg-success">收入</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger">支出</span>
                                                    <% } %>
                                                </td>
                                                <td><%= finance.category %></td>
                                                <td>¥<%= parseFloat(finance.amount).toLocaleString() %></td>
                                                <td><%= finance.description %></td>
                                                <td><%= finance.paymentMethod || '未填写' %></td>
                                                <td><%= finance.user_name || '未知' %></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                                <h5>暂无财务记录</h5>
                            </div>
                        <% } %>
                    </div>

                    <!-- 订单管理标签页 -->
                    <div class="tab-pane fade" id="orders" role="tabpanel">
                        <% if (orders.length > 0) { %>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>订单号</th>
                                            <th>下单用户</th>
                                            <th>收货人</th>
                                            <th>订单金额</th>
                                            <th>支付状态</th>
                                            <th>订单状态</th>
                                            <th>下单时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% orders.forEach(function(order) { %>
                                            <tr>
                                                <td><%= order.order_no %></td>
                                                <td><%= order.user_name || '未知用户' %></td>
                                                <td><%= order.contact_name %></td>
                                                <td>¥<%= parseFloat(order.final_amount).toLocaleString() %></td>
                                                <td>
                                                    <% if (order.payment_status === 'paid') { %>
                                                        <span class="badge bg-success">已支付</span>
                                                    <% } else if (order.payment_status === 'pending') { %>
                                                        <span class="badge bg-warning">待支付</span>
                                                    <% } else if (order.payment_status === 'failed') { %>
                                                        <span class="badge bg-danger">支付失败</span>
                                                    <% } else if (order.payment_status === 'refunded') { %>
                                                        <span class="badge bg-secondary">已退款</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% if (order.order_status === 'completed') { %>
                                                        <span class="badge bg-success">已完成</span>
                                                    <% } else if (order.order_status === 'shipped') { %>
                                                        <span class="badge bg-info">已发货</span>
                                                    <% } else if (order.order_status === 'confirmed') { %>
                                                        <span class="badge bg-primary">已确认</span>
                                                    <% } else if (order.order_status === 'pending') { %>
                                                        <span class="badge bg-warning">待处理</span>
                                                    <% } else if (order.order_status === 'cancelled') { %>
                                                        <span class="badge bg-danger">已取消</span>
                                                    <% } %>
                                                </td>
                                                <td><%= new Date(order.created_at).toLocaleString() %></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h5>暂无订单</h5>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>