<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-credit-card me-2"></i>租户订阅管理</h2>
        <a href="/tenants" class="btn btn-secondary">
          <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
      </div>
    </div>
  </div>

  <!-- 演示数据提示 -->
  <% if (typeof isDemo !== 'undefined' && isDemo) { %>
    <div class="row mb-4">
      <div class="col-12">
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>演示模式</strong>
          <%= typeof demoMessage !== 'undefined' ? demoMessage : '当前显示的是演示数据，实际部署时请配置正确的数据库连接。' %>
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      </div>
    </div>
  <% } %>

      <!-- 订阅概览 -->
      <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h5>总租户数</h5>
                  <h3>
                    <%= stats.totalTenants %>
                  </h3>
                </div>
                <div class="ms-3">
                  <i class="fas fa-users fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h5>活跃订阅</h5>
                  <h3>
                    <%= stats.activeSubscriptions %>
                  </h3>
                </div>
                <div class="ms-3">
                  <i class="fas fa-check-circle fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h5>即将到期</h5>
                  <h3>
                    <%= stats.expiringSoon %>
                  </h3>
                </div>
                <div class="ms-3">
                  <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h5>月收入</h5>
                  <h3>¥<%= Utils.formatNumber(stats.monthlyRevenue) %>
                  </h3>
                </div>
                <div class="ms-3">
                  <i class="fas fa-yen-sign fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订阅列表 -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5><i class="fas fa-list me-2"></i>订阅列表</h5>
              <div class="d-flex">
                <select class="form-select me-2" id="planFilter">
                  <option value="">全部套餐</option>
                  <option value="basic">基础版</option>
                  <option value="standard">标准版</option>
                  <option value="premium">高级版</option>
                </select>
                <select class="form-select me-2" id="statusFilter">
                  <option value="">全部状态</option>
                  <option value="active">正常</option>
                  <option value="suspended">暂停</option>
                  <option value="expired">过期</option>
                </select>
              </div>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead>
                    <tr>
                      <th>租户</th>
                      <th>套餐</th>
                      <th>状态</th>
                      <th>开始时间</th>
                      <th>结束时间</th>
                      <th>剩余天数</th>
                      <th>月费用</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% subscriptions.forEach(sub=> { %>
                      <tr>
                        <td>
                          <div class="d-flex align-items-center">
                            <div class="me-3">
                              <i class="fas fa-building text-primary"></i>
                            </div>
                            <div>
                              <strong>
                                <%= sub.company_name %>
                              </strong><br>
                              <small class="text-muted">
                                <%= sub.contact_person %>
                              </small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <% if (sub.subscription_plan==='basic' ) { %>
                            <span class="badge bg-info">基础版</span>
                            <% } else if (sub.subscription_plan==='standard' ) { %>
                              <span class="badge bg-primary">标准版</span>
                              <% } else if (sub.subscription_plan==='premium' ) { %>
                                <span class="badge bg-warning">高级版</span>
                                <% } %>
                        </td>
                        <td>
                          <% if (sub.status==='active' ) { %>
                            <span class="badge bg-success">正常</span>
                            <% } else if (sub.status==='suspended' ) { %>
                              <span class="badge bg-warning">暂停</span>
                              <% } else if (sub.status==='expired' ) { %>
                                <span class="badge bg-danger">过期</span>
                                <% } %>
                        </td>
                        <td>
                          <%= Utils.formatDate(sub.subscription_start, 'YYYY-MM-DD' ) %>
                        </td>
                        <td>
                          <%= Utils.formatDate(sub.subscription_end, 'YYYY-MM-DD' ) %>
                        </td>
                        <td>
                          <% const remainingDays=Math.ceil((new Date(sub.subscription_end) - new Date()) / (1000 * 60 *
                            60 * 24)); %>
                            <% if (remainingDays> 30) { %>
                              <span class="text-success">
                                <%= remainingDays %>天
                              </span>
                              <% } else if (remainingDays> 7) { %>
                                <span class="text-warning">
                                  <%= remainingDays %>天
                                </span>
                                <% } else if (remainingDays> 0) { %>
                                  <span class="text-danger">
                                    <%= remainingDays %>天
                                  </span>
                                  <% } else { %>
                                    <span class="text-muted">已过期</span>
                                    <% } %>
                        </td>
                        <td>
                          <% let price=0; %>
                          <% if (sub.subscription_plan==='basic' ) { %>
                            <% price=99; %>
                          <% } else if (sub.subscription_plan==='standard' ) { %>
                            <% price=199; %>
                          <% } else if (sub.subscription_plan==='premium' ) { %>
                            <% price=399; %>
                          <% } %>
                          <strong>¥<%= price %></strong>
                        </td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary"
                              onclick="renewSubscription(<%= sub.id %>, '<%= sub.subscription_plan %>')">
                              <i class="fas fa-redo"></i> 续费
                            </button>
                            <button class="btn btn-outline-warning" onclick="changeSubscription(<%= sub.id %>)">
                              <i class="fas fa-edit"></i> 变更
                            </button>
                            <a href="/tenants/<%= sub.id %>" class="btn btn-outline-info">
                              <i class="fas fa-eye"></i> 详情
                            </a>
                          </div>
                        </td>
                      </tr>
                      <% }) %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
</div>

<!-- 续费模态框 -->
<div class="modal fade" id="renewModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">订阅续费</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="renewForm">
          <input type="hidden" id="renewTenantId">
          <div class="mb-3">
            <label for="renewMonths" class="form-label">续费月数</label>
            <select class="form-select" id="renewMonths" required>
              <option value="1">1个月</option>
              <option value="3">3个月</option>
              <option value="6">6个月</option>
              <option value="12" selected>12个月</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="renewAmount" class="form-label">续费金额</label>
            <input type="text" class="form-control" id="renewAmount" readonly>
          </div>
          <div class="mb-3">
            <label for="renewNote" class="form-label">备注</label>
            <textarea class="form-control" id="renewNote" rows="3" placeholder="续费备注..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="confirmRenew()">确认续费</button>
      </div>
    </div>
  </div>
</div>

<!-- 变更套餐模态框 -->
<div class="modal fade" id="changeModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">变更套餐</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="changeForm">
          <input type="hidden" id="changeTenantId">
          <div class="mb-3">
            <label for="newPlan" class="form-label">新套餐</label>
            <select class="form-select" id="newPlan" required>
              <option value="basic">基础版 - ¥99/月</option>
              <option value="standard">标准版 - ¥199/月</option>
              <option value="premium">高级版 - ¥399/月</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="changeDate" class="form-label">生效日期</label>
            <input type="date" class="form-control" id="changeDate" required>
          </div>
          <div class="mb-3">
            <label for="changeNote" class="form-label">变更原因</label>
            <textarea class="form-control" id="changeNote" rows="3" placeholder="变更原因..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="confirmChange()">确认变更</button>
      </div>
    </div>
  </div>
</div>

<script>
  let currentPlan = '';

  document.addEventListener('DOMContentLoaded', function () {
    // 设置默认日期
    document.getElementById('changeDate').valueAsDate = new Date();

    // 筛选功能
    document.getElementById('planFilter').addEventListener('change', filterTable);
    document.getElementById('statusFilter').addEventListener('change', filterTable);

    // 续费月数变化时更新金额
    document.getElementById('renewMonths').addEventListener('change', updateRenewAmount);
  });

  function filterTable() {
    const planFilter = document.getElementById('planFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    const url = new URL(window.location);
    url.searchParams.set('plan', planFilter);
    url.searchParams.set('status', statusFilter);
    window.location.href = url.toString();
  }

  function renewSubscription(tenantId, plan) {
    currentPlan = plan;
    document.getElementById('renewTenantId').value = tenantId;
    updateRenewAmount();

    const modal = new bootstrap.Modal(document.getElementById('renewModal'));
    modal.show();
  }

  function updateRenewAmount() {
    const months = parseInt(document.getElementById('renewMonths').value);
    let monthlyPrice = 0;

    switch (currentPlan) {
      case 'basic': monthlyPrice = 99; break;
      case 'standard': monthlyPrice = 199; break;
      case 'premium': monthlyPrice = 399; break;
    }

    const totalAmount = monthlyPrice * months;
    document.getElementById('renewAmount').value = '¥' + totalAmount;
  }

  function confirmRenew() {
    const tenantId = document.getElementById('renewTenantId').value;
    const months = document.getElementById('renewMonths').value;
    const note = document.getElementById('renewNote').value;

    fetch('/tenants/renew-subscription', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenant_id: tenantId,
        months: months,
        note: note
      })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('续费成功');
          location.reload();
        } else {
          alert('续费失败：' + data.message);
        }
      });
  }

  function changeSubscription(tenantId) {
    document.getElementById('changeTenantId').value = tenantId;

    const modal = new bootstrap.Modal(document.getElementById('changeModal'));
    modal.show();
  }

  function confirmChange() {
    const tenantId = document.getElementById('changeTenantId').value;
    const newPlan = document.getElementById('newPlan').value;
    const changeDate = document.getElementById('changeDate').value;
    const note = document.getElementById('changeNote').value;

    fetch('/tenants/change-subscription', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenant_id: tenantId,
        new_plan: newPlan,
        effective_date: changeDate,
        note: note
      })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('套餐变更成功');
          location.reload();
        } else {
          alert('变更失败：' + data.message);
        }
      });
  }
</script>