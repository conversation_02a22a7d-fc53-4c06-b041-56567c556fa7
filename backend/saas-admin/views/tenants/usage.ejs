<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2"></i>租户使用情况分析</h2>
                <div>
                    <button class="btn btn-info me-2" onclick="exportUsageReport()">
                        <i class="fas fa-download me-1"></i>导出报告
                    </button>
                    <a href="/tenants" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form class="row g-3" id="filterForm">
                        <div class="col-md-3">
                            <label for="dateRange" class="form-label">时间范围</label>
                            <select class="form-select" id="dateRange" name="dateRange">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近3个月</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tenantFilter" class="form-label">租户</label>
                            <select class="form-select" id="tenantFilter" name="tenant">
                                <option value="">全部租户</option>
                                <% tenants.forEach(tenant => { %>
                                    <option value="<%= tenant.id %>"><%= tenant.company_name %></option>
                                <% }) %>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="planFilter" class="form-label">套餐类型</label>
                            <select class="form-select" id="planFilter" name="plan">
                                <option value="">全部套餐</option>
                                <option value="basic">基础版</option>
                                <option value="standard">标准版</option>
                                <option value="premium">高级版</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-primary" onclick="updateCharts()" style="margin-top: 32px;">
                                <i class="fas fa-search me-1"></i>筛选
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用概览 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>总用户数</h5>
                            <h3><%= stats.totalUsers %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>活跃用户</h5>
                            <h3><%= stats.activeUsers %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>存储使用</h5>
                            <h3><%= Utils.formatNumber(stats.totalStorage, 1) %>GB</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5>API调用</h5>
                            <h3><%= Utils.formatNumber(stats.apiCalls) %></h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-plug fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用趋势图表 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>用户活跃度趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="userActivityChart" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area me-2"></i>存储使用趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="storageUsageChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能使用统计 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie me-2"></i>功能使用分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="featureUsageChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>套餐使用对比</h5>
                </div>
                <div class="card-body">
                    <canvas id="planComparisonChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细使用列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>租户使用详情</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usageTable">
                            <thead>
                                <tr>
                                    <th>租户</th>
                                    <th>套餐</th>
                                    <th>用户数</th>
                                    <th>存储使用</th>
                                    <th>最后登录</th>
                                    <th>本月登录次数</th>
                                    <th>本月API调用</th>
                                    <th>使用率</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% usageData.forEach(usage => { %>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building text-primary"></i>
                                                </div>
                                                <div>
                                                    <strong><%= usage.company_name %></strong><br>
                                                    <small class="text-muted"><%= usage.contact_person %></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (usage.subscription_plan === 'basic') { %>
                                                <span class="badge bg-info">基础版</span>
                                            <% } else if (usage.subscription_plan === 'standard') { %>
                                                <span class="badge bg-primary">标准版</span>
                                            <% } else if (usage.subscription_plan === 'premium') { %>
                                                <span class="badge bg-warning">高级版</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <span class="fw-bold"><%= usage.current_users %></span>
                                            <small class="text-muted">/ <%= usage.max_users %></small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="flex-grow-1">
                                                    <div class="progress" style="height: 6px;">
                                                        <% const storagePercent = (usage.storage_used / usage.storage_limit) * 100; %>
                                                        <div class="progress-bar <%= storagePercent > 80 ? 'bg-danger' : storagePercent > 60 ? 'bg-warning' : 'bg-success' %>" 
                                                             style="width: <%= Math.min(storagePercent, 100) %>%"></div>
                                                    </div>
                                                    <small class="text-muted">
                                                        <%= Utils.formatNumber(usage.storage_used, 1) %>GB / <%= usage.storage_limit %>GB
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <% if (usage.last_login_time) { %>
                                                <%= Utils.formatDate(usage.last_login_time, 'MM-DD HH:mm') %>
                                            <% } else { %>
                                                <span class="text-muted">从未登录</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><%= usage.monthly_logins || 0 %></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><%= Utils.formatNumber(usage.monthly_api_calls || 0) %></span>
                                        </td>
                                        <td>
                                            <% const activityScore = Math.min(100, (usage.monthly_logins * 10 + (usage.monthly_api_calls || 0) / 100)); %>
                                            <% if (activityScore >= 80) { %>
                                                <span class="badge bg-success">高活跃</span>
                                            <% } else if (activityScore >= 40) { %>
                                                <span class="badge bg-warning">中活跃</span>
                                            <% } else { %>
                                                <span class="badge bg-danger">低活跃</span>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // 用户活跃度趋势图
    const activityCtx = document.getElementById('userActivityChart').getContext('2d');
    const activityData = <%- JSON.stringify(activityTrend || {}) %>;
    
    new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: activityData.labels || [],
            datasets: [{
                label: '活跃用户数',
                data: activityData.activeUsers || [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.4,
                fill: true
            }, {
                label: '总用户数',
                data: activityData.totalUsers || [],
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 存储使用趋势图
    const storageCtx = document.getElementById('storageUsageChart').getContext('2d');
    const storageData = <%- JSON.stringify(storageTrend || {}) %>;
    
    new Chart(storageCtx, {
        type: 'area',
        data: {
            labels: storageData.labels || [],
            datasets: [{
                label: '存储使用量(GB)',
                data: storageData.usage || [],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + 'GB';
                        }
                    }
                }
            }
        }
    });

    // 功能使用分布图
    const featureCtx = document.getElementById('featureUsageChart').getContext('2d');
    const featureData = <%- JSON.stringify(featureUsage || {}) %>;
    
    new Chart(featureCtx, {
        type: 'doughnut',
        data: {
            labels: featureData.labels || ['鹅群管理', '生产记录', '健康监控', '财务管理', '库存管理', '报表分析'],
            datasets: [{
                data: featureData.usage || [85, 92, 78, 65, 58, 72],
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 套餐使用对比图
    const planCtx = document.getElementById('planComparisonChart').getContext('2d');
    const planData = <%- JSON.stringify(planComparison || {}) %>;
    
    new Chart(planCtx, {
        type: 'bar',
        data: {
            labels: ['基础版', '标准版', '高级版'],
            datasets: [{
                label: '租户数量',
                data: planData.tenantCounts || [45, 32, 18],
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: '平均活跃度',
                data: planData.activityScores || [68, 75, 82],
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'left'
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    max: 100,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function updateCharts() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // 重新加载页面带参数
    window.location.search = params.toString();
}

function exportUsageReport() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // 下载使用报告
    window.open('/tenants/usage/export?' + params.toString());
}
</script>