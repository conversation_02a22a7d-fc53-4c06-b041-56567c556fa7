<div class="row">
  <div class="col-12">
    <!-- 头部统计卡片 -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3>
              <%= tenants.length %>
            </h3>
            <p>总租户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-building"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>
              <%= tenants.filter(t=> t.status === 'active').length %>
            </h3>
            <p>活跃租户</p>
          </div>
          <div class="icon">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3>
              <%= tenants.reduce((sum, t)=> sum + parseInt(t.user_count), 0) %>
            </h3>
            <p>总用户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-users"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
          <div class="inner">
            <h3>¥<%= tenants.reduce((sum, t)=> sum + parseFloat(t.total_revenue || 0), 0).toLocaleString() %></h3>
            <p>总收入</p>
          </div>
          <div class="icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 租户列表卡片 -->
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>
            租户管理
          </h5>
          <div class="btn-group">
            <a href="/tenants/create" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>
              创建租户
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="selectAllTenants()">
              <i class="fas fa-check-square me-1"></i>
              全选
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="batchUpdateTenantStatus('suspended')">
              <i class="fas fa-pause me-1"></i>
              批量暂停
            </button>
            <button type="button" class="btn btn-outline-success" onclick="batchUpdateTenantStatus('active')">
              <i class="fas fa-play me-1"></i>
              批量激活
            </button>
            <button type="button" class="btn btn-success" onclick="exportTenants()">
              <i class="fas fa-download me-1"></i>
              导出数据
            </button>
            <a href="/tenants/subscriptions" class="btn btn-warning">
              <i class="fas fa-calendar-check me-1"></i>
              订阅管理
            </a>
            <a href="/tenants/usage" class="btn btn-info">
              <i class="fas fa-chart-bar me-1"></i>
              使用统计
            </a>
          </div>
        </div>
      </div>
      <div class="card-body">
        <% if (tenants && tenants.length> 0) { %>
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th width="50">
                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAllTenants()">
                  </th>
                  <th>租户信息</th>
                  <th>类型</th>
                  <th>订阅计划</th>
                  <th>用户数</th>
                  <th>鹅群数</th>
                  <th>收入</th>
                  <th>状态</th>
                  <th>订阅到期</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% tenants.forEach(function(tenant) { %>
                  <tr>
                    <td>
                      <input type="checkbox" class="tenant-checkbox" value="<%= tenant.id %>">
                    </td>
                    <td>
                      <div>
                        <strong>
                          <%= tenant.tenant_name %>
                        </strong>
                        <br>
                        <small class="text-muted">
                          代码: <%= tenant.tenant_code %>
                            <br>
                            联系人: <%= tenant.contact_person %>
                              <br>
                              电话: <%= tenant.contact_phone %>
                        </small>
                      </div>
                    </td>
                    <td>
                      <% if (tenant.tenant_type==='individual' ) { %>
                        <span class="badge bg-primary">个人</span>
                        <% } else if (tenant.tenant_type==='enterprise' ) { %>
                          <span class="badge bg-success">企业</span>
                          <% } else if (tenant.tenant_type==='cooperative' ) { %>
                            <span class="badge bg-info">合作社</span>
                            <% } %>
                    </td>
                    <td>
                      <% if (tenant.subscription_plan==='basic' ) { %>
                        <span class="badge bg-secondary">基础版</span>
                        <% } else if (tenant.subscription_plan==='standard' ) { %>
                          <span class="badge bg-primary">标准版</span>
                          <% } else if (tenant.subscription_plan==='premium' ) { %>
                            <span class="badge bg-warning">高级版</span>
                            <% } else if (tenant.subscription_plan==='enterprise' ) { %>
                              <span class="badge bg-success">企业版</span>
                              <% } %>
                    </td>
                    <td>
                      <span class="badge bg-info">
                        <%= tenant.user_count %>
                      </span>
                      <small class="text-muted">/ <%= tenant.max_users %></small>
                    </td>
                    <td>
                      <span class="badge bg-warning">
                        <%= tenant.flock_count %>
                      </span>
                      <small class="text-muted">/ <%= tenant.max_flocks %></small>
                    </td>
                    <td>
                      <strong>¥<%= (parseFloat(tenant.total_revenue) || 0).toLocaleString() %></strong>
                    </td>
                    <td>
                      <% if (tenant.status==='active' ) { %>
                        <span class="badge bg-success">活跃</span>
                        <% } else if (tenant.status==='suspended' ) { %>
                          <span class="badge bg-warning">暂停</span>
                          <% } else { %>
                            <span class="badge bg-danger">停用</span>
                            <% } %>
                    </td>
                    <td>
                      <% if (tenant.subscription_end) { %>
                        <%= new Date(tenant.subscription_end).toLocaleDateString() %>
                          <% const today=new Date(); const endDate=new Date(tenant.subscription_end); const
                            diffTime=endDate.getTime() - today.getTime(); const diffDays=Math.ceil(diffTime / (1000 * 60
                            * 60 * 24)); %>
                            <br>
                            <% if (diffDays < 0) { %>
                              <small class="text-danger">已过期 <%= Math.abs(diffDays) %> 天</small>
                              <% } else if (diffDays <=30) { %>
                                <small class="text-warning">还剩 <%= diffDays %> 天</small>
                                <% } else { %>
                                  <small class="text-success">还剩 <%= diffDays %> 天</small>
                                  <% } %>
                                    <% } else { %>
                                      <span class="text-muted">无期限</span>
                                      <% } %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="/tenants/<%= tenant.id %>/details" class="btn btn-sm btn-info" title="查看详情">
                          <i class="fas fa-eye"></i>
                        </a>
                        <a href="/tenants/<%= tenant.id %>/edit" class="btn btn-sm btn-warning" title="编辑">
                          <i class="fas fa-edit"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                  <% }); %>
              </tbody>
            </table>
          </div>
          <% } else { %>
            <div class="text-center py-5">
              <i class="fas fa-building fa-4x text-muted mb-3"></i>
              <h4>暂无租户</h4>
              <p class="text-muted">还没有创建任何租户</p>
              <a href="/tenants/create" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                创建第一个租户
              </a>
            </div>
            <% } %>
      </div>
    </div>
  </div>
</div>

<!-- 成功提示 -->
<% if (typeof success !=='undefined' ) { %>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      let message = '';
            <% if (success === 'created') { %>
        message = '租户创建成功！';
            <% } else if (success === 'updated') { %>
        message = '租户更新成功！';
            <% } %>
            
            if (message) {
        // 创建成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', function () {
          document.body.removeChild(toast);
        });
      }
    });
  </script>
  <% } %>

    <script>
      // 批量操作功能

      // 全选/取消全选
      function toggleSelectAllTenants() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const tenantCheckboxes = document.querySelectorAll('.tenant-checkbox');

        tenantCheckboxes.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked;
        });
      }

      // 选择所有租户
      function selectAllTenants() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        selectAllCheckbox.checked = true;
        toggleSelectAllTenants();
      }

      // 获取选中的租户ID
      function getSelectedTenantIds() {
        const checkedBoxes = document.querySelectorAll('.tenant-checkbox:checked');
        return Array.from(checkedBoxes).map(checkbox => checkbox.value);
      }

      // 批量更新租户状态
      function batchUpdateTenantStatus(status) {
        const selectedIds = getSelectedTenantIds();

        if (selectedIds.length === 0) {
          showToast('请先选择要操作的租户', 'error');
          return;
        }

        const statusText = status === 'active' ? '激活' : status === 'suspended' ? '暂停' : '更新';

        if (confirm(`确定要批量${statusText}选中的 ${selectedIds.length} 个租户吗？`)) {
          // 显示加载状态
          showToast(`正在批量${statusText}租户...`, 'info');

          // 模拟API调用
          setTimeout(() => {
            showToast(`批量${statusText}功能开发中...`, 'info');
          }, 1000);
        }
      }

      // 租户导出功能
      function exportTenants() {
        // 显示导出选项模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-download me-2"></i>
            导出租户数据
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="exportForm">
            <div class="mb-3">
              <label class="form-label">导出格式</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="format" value="excel" id="formatExcel" checked>
                <label class="form-check-label" for="formatExcel">
                  <i class="fas fa-file-excel text-success me-2"></i>
                  Excel (.xlsx)
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="format" value="csv" id="formatCsv">
                <label class="form-check-label" for="formatCsv">
                  <i class="fas fa-file-csv text-info me-2"></i>
                  CSV (.csv)
                </label>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">导出范围</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="scope" value="all" id="scopeAll" checked>
                <label class="form-check-label" for="scopeAll">
                  所有租户
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="scope" value="active" id="scopeActive">
                <label class="form-check-label" for="scopeActive">
                  仅活跃租户
                </label>
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="includeStats" checked>
                <label class="form-check-label" for="includeStats">
                  包含统计信息
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-success" onclick="performExport()">
            <i class="fas fa-download me-1"></i>
            开始导出
          </button>
        </div>
      </div>
    </div>
  `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭后移除DOM
        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
        });
      }

      function performExport() {
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);

        const exportParams = {
          format: formData.get('format'),
          scope: formData.get('scope'),
          includeStats: document.getElementById('includeStats').checked
        };

        // 显示加载状态
        const exportBtn = document.querySelector('.modal-footer .btn-success');
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
        exportBtn.disabled = true;

        // 构建导出URL
        const params = new URLSearchParams(exportParams);
        const exportUrl = `/api/tenants/export?${params}`;

        // 执行导出
        fetch(exportUrl)
          .then(response => {
            if (!response.ok) {
              throw new Error('导出失败');
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              // 创建下载链接
              const downloadUrl = data.downloadUrl || exportUrl;
              const link = document.createElement('a');
              link.href = downloadUrl;
              link.download = data.filename || \`租户数据_\${new Date().toISOString().split('T')[0]}.\${exportParams.format === 'excel' ? 'xlsx' : 'csv'}\`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 显示成功提示
        showToast('导出成功！', 'success');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
        modal.hide();
      } else {
        throw new Error(data.message || '导出失败');
      }
    })
    .catch(error => {
      console.error('导出失败:', error);
      showToast(error.message || '导出失败，请稍后重试', 'error');
    })
    .finally(() => {
      // 恢复按钮状态
      exportBtn.innerHTML = originalText;
      exportBtn.disabled = false;
    });
}

function showToast(message, type = 'info') {
  const toast = document.createElement('div');
  toast.className = \`toast align-items-center text-bg-\${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0 position-fixed top-0 end-0 m-3\`;
  toast.style.zIndex = '9999';
  toast.innerHTML = \`
    <div class="d-flex">
      <div class="toast-body">
        <i class="fas fa-\${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        \${message}
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
    </div>
  \`;

  document.body.appendChild(toast);
  const bsToast = new bootstrap.Toast(toast);
  bsToast.show();

  toast.addEventListener('hidden.bs.toast', () => {
    document.body.removeChild(toast);
  });
}
    </script>