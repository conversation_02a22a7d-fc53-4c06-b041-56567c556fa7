<%- contentFor('title') %>
鹅群管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">鹅群管理</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFlockModal">
            <i class="bi bi-plus-lg"></i> 新增鹅群
        </button>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总鹅群数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalFlocks">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-collection text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">健康鹅群</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="healthyFlocks">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-heart text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">总鹅数量</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalGeese">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-grid-3x3-gap text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">平均成活率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgSurvivalRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-percent text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">搜索筛选</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="searchFlock" class="form-label">搜索鹅群</label>
                    <input type="text" class="form-control" id="searchFlock" placeholder="输入鹅群名称或编号">
                </div>
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">状态筛选</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="healthy">健康</option>
                        <option value="sick">生病</option>
                        <option value="treatment">治疗中</option>
                        <option value="quarantine">隔离中</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="breedFilter" class="form-label">品种筛选</label>
                    <select class="form-select" id="breedFilter">
                        <option value="">全部品种</option>
                        <option value="白鹅">白鹅</option>
                        <option value="灰鹅">灰鹅</option>
                        <option value="黑鹅">黑鹅</option>
                        <option value="混合">混合品种</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="ageFilter" class="form-label">年龄筛选</label>
                    <select class="form-select" id="ageFilter">
                        <option value="">全部年龄</option>
                        <option value="young">幼鹅 (0-3月)</option>
                        <option value="juvenile">青年鹅 (3-6月)</option>
                        <option value="adult">成年鹅 (6月+)</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="searchBtn">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 鹅群列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">鹅群列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download"></i> 导出数据
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" id="exportExcel"><i class="bi bi-file-earmark-excel"></i> Excel</a></li>
                    <li><a class="dropdown-item" href="#" id="exportPdf"><i class="bi bi-file-earmark-pdf"></i> PDF</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="flocksTable">
                    <thead>
                        <tr>
                            <th>鹅群编号</th>
                            <th>鹅群名称</th>
                            <th>品种</th>
                            <th>数量</th>
                            <th>健康状态</th>
                            <th>成活率</th>
                            <th>饲养员</th>
                            <th>创建日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="flocksTableBody">
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="鹅群列表分页" class="mt-3">
                <ul class="pagination justify-content-center" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 新增鹅群模态框 -->
<div class="modal fade" id="addFlockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增鹅群</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addFlockForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="flockName" class="form-label">鹅群名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="flockName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="flockCode" class="form-label">鹅群编号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="flockCode" name="code" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="breed" class="form-label">品种 <span class="text-danger">*</span></label>
                                <select class="form-select" id="breed" name="breed" required>
                                    <option value="">请选择品种</option>
                                    <option value="白鹅">白鹅</option>
                                    <option value="灰鹅">灰鹅</option>
                                    <option value="黑鹅">黑鹅</option>
                                    <option value="混合">混合品种</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="initialCount" class="form-label">初始数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="initialCount" name="initial_count" min="1" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="keeper" class="form-label">饲养员 <span class="text-danger">*</span></label>
                                <select class="form-select" id="keeper" name="keeper_id" required>
                                    <option value="">请选择饲养员</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">养殖区域</label>
                                <input type="text" class="form-control" id="location" name="location" placeholder="如：A区1号栏">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">备注说明</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑鹅群模态框 -->
<div class="modal fade" id="editFlockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑鹅群</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editFlockForm">
                <input type="hidden" id="editFlockId" name="id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFlockName" class="form-label">鹅群名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editFlockName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFlockCode" class="form-label">鹅群编号</label>
                                <input type="text" class="form-control" id="editFlockCode" name="code" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBreed" class="form-label">品种 <span class="text-danger">*</span></label>
                                <select class="form-select" id="editBreed" name="breed" required>
                                    <option value="">请选择品种</option>
                                    <option value="白鹅">白鹅</option>
                                    <option value="灰鹅">灰鹅</option>
                                    <option value="黑鹅">黑鹅</option>
                                    <option value="混合">混合品种</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editCurrentCount" class="form-label">当前数量</label>
                                <input type="number" class="form-control" id="editCurrentCount" name="current_count" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editKeeper" class="form-label">饲养员 <span class="text-danger">*</span></label>
                                <select class="form-select" id="editKeeper" name="keeper_id" required>
                                    <option value="">请选择饲养员</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editLocation" class="form-label">养殖区域</label>
                                <input type="text" class="form-control" id="editLocation" name="location">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editStatus" class="form-label">健康状态</label>
                                <select class="form-select" id="editStatus" name="status">
                                    <option value="healthy">健康</option>
                                    <option value="sick">生病</option>
                                    <option value="treatment">治疗中</option>
                                    <option value="quarantine">隔离中</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editSurvivalRate" class="form-label">成活率 (%)</label>
                                <input type="number" class="form-control" id="editSurvivalRate" name="survival_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">备注说明</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">更新</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentPage = 1;
    const pageSize = 10;

    // 页面加载时初始化数据
    loadStatistics();
    loadKeepers();
    loadFlocks();

    // 加载统计数据
    function loadStatistics() {
        $.ajax({
            url: '/api/flocks/statistics',
            method: 'GET',
            success: function(data) {
                $('#totalFlocks').text(data.total_flocks || 0);
                $('#healthyFlocks').text(data.healthy_flocks || 0);
                $('#totalGeese').text(data.total_geese || 0);
                $('#avgSurvivalRate').text((data.avg_survival_rate || 0).toFixed(1) + '%');
            },
            error: function() {
                console.error('加载统计数据失败');
            }
        });
    }

    // 加载饲养员列表
    function loadKeepers() {
        $.ajax({
            url: '/api/users/keepers',
            method: 'GET',
            success: function(data) {
                const keeperSelect = $('#keeper, #editKeeper');
                keeperSelect.find('option:not(:first)').remove();
                data.forEach(keeper => {
                    keeperSelect.append(`<option value="${keeper.id}">${keeper.name}</option>`);
                });
            },
            error: function() {
                console.error('加载饲养员列表失败');
            }
        });
    }

    // 加载鹅群列表
    function loadFlocks(page = 1) {
        const params = {
            page: page,
            pageSize: pageSize,
            search: $('#searchFlock').val(),
            status: $('#statusFilter').val(),
            breed: $('#breedFilter').val(),
            age: $('#ageFilter').val()
        };

        $.ajax({
            url: '/api/flocks',
            method: 'GET',
            data: params,
            success: function(data) {
                renderFlocksTable(data.flocks);
                renderPagination(data.pagination);
            },
            error: function() {
                showAlert('加载鹅群列表失败', 'error');
            }
        });
    }

    // 渲染鹅群表格
    function renderFlocksTable(flocks) {
        const tbody = $('#flocksTableBody');
        tbody.empty();

        if (flocks.length === 0) {
            tbody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
            return;
        }

        flocks.forEach(flock => {
            const statusClass = getStatusClass(flock.status);
            const statusText = getStatusText(flock.status);
            
            const row = `
                <tr>
                    <td>${flock.code}</td>
                    <td>${flock.name}</td>
                    <td>${flock.breed}</td>
                    <td>${flock.current_count}</td>
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td>${(flock.survival_rate || 0).toFixed(1)}%</td>
                    <td>${flock.keeper_name || '-'}</td>
                    <td>${new Date(flock.created_at).toLocaleDateString()}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="viewFlock(${flock.id})">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editFlock(${flock.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteFlock(${flock.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // 获取状态样式类
    function getStatusClass(status) {
        switch (status) {
            case 'healthy': return 'bg-success';
            case 'sick': return 'bg-warning';
            case 'treatment': return 'bg-info';
            case 'quarantine': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // 获取状态文本
    function getStatusText(status) {
        switch (status) {
            case 'healthy': return '健康';
            case 'sick': return '生病';
            case 'treatment': return '治疗中';
            case 'quarantine': return '隔离中';
            default: return '未知';
        }
    }

    // 渲染分页
    function renderPagination(pagination) {
        const paginationEl = $('#pagination');
        paginationEl.empty();

        if (pagination.totalPages <= 1) return;

        // 上一页
        const prevDisabled = pagination.currentPage === 1 ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage - 1}">上一页</a>
            </li>
        `);

        // 页码
        for (let i = 1; i <= pagination.totalPages; i++) {
            const activeClass = i === pagination.currentPage ? 'active' : '';
            paginationEl.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        // 下一页
        const nextDisabled = pagination.currentPage === pagination.totalPages ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" data-page="${pagination.currentPage + 1}">下一页</a>
            </li>
        `);

        currentPage = pagination.currentPage;
    }

    // 分页点击事件
    $(document).on('click', '.page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && page !== currentPage) {
            loadFlocks(page);
        }
    });

    // 搜索按钮点击
    $('#searchBtn').click(function() {
        loadFlocks(1);
    });

    // 重置按钮点击
    $('#resetBtn').click(function() {
        $('#searchFlock').val('');
        $('#statusFilter').val('');
        $('#breedFilter').val('');
        $('#ageFilter').val('');
        loadFlocks(1);
    });

    // 新增鹅群表单提交
    $('#addFlockForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '/api/flocks',
            method: 'POST',
            data: $(this).serialize(),
            success: function(data) {
                $('#addFlockModal').modal('hide');
                $('#addFlockForm')[0].reset();
                showAlert('鹅群创建成功', 'success');
                loadFlocks(currentPage);
                loadStatistics();
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '创建失败';
                showAlert(error, 'error');
            }
        });
    });

    // 编辑鹅群表单提交
    $('#editFlockForm').submit(function(e) {
        e.preventDefault();
        const flockId = $('#editFlockId').val();
        
        $.ajax({
            url: `/api/flocks/${flockId}`,
            method: 'PUT',
            data: $(this).serialize(),
            success: function(data) {
                $('#editFlockModal').modal('hide');
                showAlert('鹅群更新成功', 'success');
                loadFlocks(currentPage);
                loadStatistics();
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '更新失败';
                showAlert(error, 'error');
            }
        });
    });

    // 导出Excel
    $('#exportExcel').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            search: $('#searchFlock').val(),
            status: $('#statusFilter').val(),
            breed: $('#breedFilter').val(),
            age: $('#ageFilter').val(),
            format: 'excel'
        });
        window.open(`/api/flocks/export?${params}`);
    });

    // 导出PDF
    $('#exportPdf').click(function(e) {
        e.preventDefault();
        const params = new URLSearchParams({
            search: $('#searchFlock').val(),
            status: $('#statusFilter').val(),
            breed: $('#breedFilter').val(),
            age: $('#ageFilter').val(),
            format: 'pdf'
        });
        window.open(`/api/flocks/export?${params}`);
    });
});

// 查看鹅群详情
function viewFlock(flockId) {
    window.location.href = `/flocks/${flockId}`;
}

// 编辑鹅群
function editFlock(flockId) {
    $.ajax({
        url: `/api/flocks/${flockId}`,
        method: 'GET',
        success: function(data) {
            $('#editFlockId').val(data.id);
            $('#editFlockName').val(data.name);
            $('#editFlockCode').val(data.code);
            $('#editBreed').val(data.breed);
            $('#editCurrentCount').val(data.current_count);
            $('#editKeeper').val(data.keeper_id);
            $('#editLocation').val(data.location);
            $('#editStatus').val(data.status);
            $('#editSurvivalRate').val(data.survival_rate);
            $('#editDescription').val(data.description);
            $('#editFlockModal').modal('show');
        },
        error: function() {
            showAlert('加载鹅群信息失败', 'error');
        }
    });
}

// 删除鹅群
function deleteFlock(flockId) {
    if (confirm('确定要删除这个鹅群吗？此操作不可恢复！')) {
        $.ajax({
            url: `/api/flocks/${flockId}`,
            method: 'DELETE',
            success: function() {
                showAlert('鹅群删除成功', 'success');
                loadFlocks(currentPage);
                loadStatistics();
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '删除失败';
                showAlert(error, 'error');
            }
        });
    }
}

// 显示提示消息
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('body').append(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').alert('close');
    }, 3000);
}
</script>
