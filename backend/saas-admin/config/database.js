const mysql = require('mysql2/promise');

const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'smart_goose',
    charset: 'utf8mb4',
    timezone: '+08:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 10,
    queueLimit: 0
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test connection function
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Database pool connected successfully');
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
}

// Enhanced query function with error handling
async function query(sql, params = []) {
    try {
        console.log(`[DB Query] ${sql}`, params.length > 0 ? `[Params: ${JSON.stringify(params)}]` : '');
        const [rows] = await pool.execute(sql, params);
        return rows;
    } catch (error) {
        console.error('[DB Error]', error.message);
        console.error('[SQL]', sql);
        console.error('[Params]', params);
        throw error;
    }
}

// Transaction helper
async function transaction(callback) {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();
        const result = await callback(connection);
        await connection.commit();
        return result;
    } catch (error) {
        await connection.rollback();
        throw error;
    } finally {
        connection.release();
    }
}

// Common database operations
const db = {
    // Raw query execution
    execute: query,
    
    // Get single record
    async findOne(table, conditions = {}, fields = '*') {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        const sql = `SELECT ${fields} FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''} LIMIT 1`;
        const params = Object.values(conditions);
        const results = await query(sql, params);
        return results.length > 0 ? results[0] : null;
    },
    
    // Get multiple records
    async findMany(table, conditions = {}, fields = '*', orderBy = 'id DESC', limit = null) {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        let sql = `SELECT ${fields} FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''}`;
        if (orderBy) sql += ` ORDER BY ${orderBy}`;
        if (limit) sql += ` LIMIT ${limit}`;
        
        const params = Object.values(conditions);
        return await query(sql, params);
    },
    
    // Insert record
    async insert(table, data) {
        const fields = Object.keys(data).join(', ');
        const placeholders = Object.keys(data).map(() => '?').join(', ');
        const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`;
        const params = Object.values(data);
        const result = await query(sql, params);
        return { insertId: result.insertId, affectedRows: result.affectedRows };
    },
    
    // Update records
    async update(table, data, conditions) {
        const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
        const params = [...Object.values(data), ...Object.values(conditions)];
        const result = await query(sql, params);
        return { affectedRows: result.affectedRows };
    },
    
    // Delete records
    async delete(table, conditions) {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
        const params = Object.values(conditions);
        const result = await query(sql, params);
        return { affectedRows: result.affectedRows };
    },
    
    // Count records
    async count(table, conditions = {}) {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        const sql = `SELECT COUNT(*) as count FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''}`;
        const params = Object.values(conditions);
        const result = await query(sql, params);
        return result[0].count;
    },
    
    // Paginated query
    async paginate(table, conditions = {}, page = 1, pageSize = 20, orderBy = 'id DESC', fields = '*') {
        const offset = (page - 1) * pageSize;
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        
        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''}`;
        const countResult = await query(countSql, Object.values(conditions));
        const total = countResult[0].total;
        
        // Get paginated data
        const dataSql = `SELECT ${fields} FROM ${table}${whereClause ? ` WHERE ${whereClause}` : ''} ORDER BY ${orderBy} LIMIT ${pageSize} OFFSET ${offset}`;
        const data = await query(dataSql, Object.values(conditions));
        
        return {
            data,
            pagination: {
                page,
                pageSize,
                total,
                totalPages: Math.ceil(total / pageSize),
                hasNext: page < Math.ceil(total / pageSize),
                hasPrev: page > 1
            }
        };
    },
    
    // Transaction helper
    transaction,
    
    // Connection pool
    pool,
    
    // Test connection
    testConnection
};

// Initialize database connection test
testConnection();

module.exports = db;