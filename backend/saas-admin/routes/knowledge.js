const express = require('express');
const router = express.Router();
const db = require('../config/database');

// 知识库管理主页
router.get('/', async (req, res) => {
    try {
        // 获取知识库文章列表
        const articles = await db.findMany('knowledge_articles', {}, '*', 'created_at DESC', 20);
        
        // 获取统计数据
        const totalArticles = await db.count('knowledge_articles');
        const publishedArticles = await db.count('knowledge_articles', { status: 'published' });
        const draftArticles = await db.count('knowledge_articles', { status: 'draft' });
        
        const stats = {
            total: totalArticles,
            published: publishedArticles,
            draft: draftArticles,
            views: 12580 // 模拟数据
        };
        
        res.render('knowledge/index', {
            title: '知识库管理',
            articles: articles,
            stats: stats
        });
        
    } catch (error) {
        console.error('获取知识库列表失败:', error);
        
        // 提供模拟数据
        const mockArticles = [
            {
                id: 1,
                title: '鹅的饲养管理技术',
                category: '饲养技术',
                status: 'published',
                views: 1250,
                created_at: new Date(),
                author: '系统管理员'
            },
            {
                id: 2,
                title: '鹅病防治指南',
                category: '疾病防治',
                status: 'published',
                views: 980,
                created_at: new Date(),
                author: '系统管理员'
            }
        ];
        
        const mockStats = {
            total: 25,
            published: 20,
            draft: 5,
            views: 12580
        };
        
        res.render('knowledge/index', {
            title: '知识库管理',
            articles: mockArticles,
            stats: mockStats,
            isDemo: true
        });
    }
});

// 创建知识文章页面
router.get('/create', (req, res) => {
    res.render('knowledge/create', {
        title: '创建知识文章'
    });
});

// 创建知识文章处理
router.post('/create', async (req, res) => {
    try {
        const {
            title,
            content,
            category,
            tags,
            keywords,
            difficulty,
            readTime,
            summary,
            status,
            publishTime,
            isRecommended,
            allowComments
        } = req.body;

        // 验证必填字段
        if (!title || !content || !category) {
            return res.status(400).render('error', {
                title: '错误',
                message: '标题、内容和分类为必填项'
            });
        }

        // 处理标签数据
        let processedTags = [];
        if (tags) {
            if (typeof tags === 'string') {
                try {
                    processedTags = JSON.parse(tags);
                } catch (e) {
                    processedTags = tags.split(',').map(tag => tag.trim());
                }
            } else if (Array.isArray(tags)) {
                processedTags = tags;
            }
        }

        const articleData = {
            title,
            content,
            category,
            tags: JSON.stringify(processedTags),
            keywords,
            difficulty: difficulty || 'beginner',
            readTime: parseInt(readTime) || null,
            summary,
            status: status || 'draft',
            publishTime: publishTime ? new Date(publishTime) : null,
            isRecommended: isRecommended === 'on' || isRecommended === true,
            allowComments: allowComments !== 'false' && allowComments !== false,
            author_id: req.session.user?.id || 1,
            views: 0,
            created_at: new Date(),
            updated_at: new Date()
        };

        await db.insert('knowledge_articles', articleData);

        res.redirect('/knowledge?success=created');

    } catch (error) {
        console.error('创建知识文章失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '创建知识文章失败',
            error: error
        });
    }
});

// 编辑知识文章页面
router.get('/:id/edit', async (req, res) => {
    try {
        const articleId = req.params.id;
        const article = await db.findOne('knowledge_articles', { id: articleId });
        
        if (!article) {
            return res.status(404).render('error', {
                title: '错误',
                message: '知识文章不存在'
            });
        }
        
        res.render('knowledge/edit', {
            title: '编辑知识文章',
            article: article
        });
        
    } catch (error) {
        console.error('获取知识文章失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取知识文章失败',
            error: error
        });
    }
});

// 更新知识文章处理
router.post('/:id/update', async (req, res) => {
    try {
        const articleId = req.params.id;
        const {
            title,
            content,
            category,
            tags,
            keywords,
            difficulty,
            readTime,
            summary,
            status,
            publishTime,
            isRecommended,
            allowComments
        } = req.body;

        // 验证必填字段
        if (!title || !content || !category) {
            return res.status(400).render('error', {
                title: '错误',
                message: '标题、内容和分类为必填项'
            });
        }

        // 处理标签数据
        let processedTags = [];
        if (tags) {
            if (typeof tags === 'string') {
                try {
                    processedTags = JSON.parse(tags);
                } catch (e) {
                    processedTags = tags.split(',').map(tag => tag.trim());
                }
            } else if (Array.isArray(tags)) {
                processedTags = tags;
            }
        }

        const updateData = {
            title,
            content,
            category,
            tags: JSON.stringify(processedTags),
            keywords,
            difficulty: difficulty || 'beginner',
            readTime: parseInt(readTime) || null,
            summary,
            status: status || 'draft',
            publishTime: publishTime ? new Date(publishTime) : null,
            isRecommended: isRecommended === 'on' || isRecommended === true,
            allowComments: allowComments !== 'false' && allowComments !== false,
            updated_at: new Date()
        };

        await db.update('knowledge_articles', updateData, { id: articleId });

        res.redirect(`/knowledge/${articleId}/edit?success=updated`);

    } catch (error) {
        console.error('更新知识文章失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '更新知识文章失败',
            error: error
        });
    }
});

// 删除知识文章
router.delete('/:id', async (req, res) => {
    try {
        const articleId = req.params.id;
        
        await db.delete('knowledge_articles', { id: articleId });
        
        res.json({
            success: true,
            message: '知识文章删除成功'
        });
        
    } catch (error) {
        console.error('删除知识文章失败:', error);
        res.status(500).json({
            success: false,
            message: '删除知识文章失败',
            error: error.message
        });
    }
});

// 分类管理页面
router.get('/categories', async (req, res) => {
    try {
        const categories = await db.findMany('knowledge_categories', {}, '*', 'sort_order ASC');
        
        res.render('knowledge/categories', {
            title: '知识库分类管理',
            categories: categories
        });
        
    } catch (error) {
        console.error('获取知识库分类失败:', error);
        
        // 提供模拟数据
        const mockCategories = [
            { id: 1, name: '饲养技术', description: '鹅的饲养管理相关技术', article_count: 8, sort_order: 1 },
            { id: 2, name: '疾病防治', description: '鹅病预防和治疗方法', article_count: 6, sort_order: 2 },
            { id: 3, name: '营养配方', description: '鹅的营养需求和饲料配方', article_count: 5, sort_order: 3 },
            { id: 4, name: '繁殖技术', description: '鹅的繁殖和孵化技术', article_count: 4, sort_order: 4 }
        ];
        
        res.render('knowledge/categories', {
            title: '知识库分类管理',
            categories: mockCategories,
            isDemo: true
        });
    }
});

// 标签管理页面
router.get('/tags', async (req, res) => {
    try {
        const tags = await db.findMany('knowledge_tags', {}, '*', 'usage_count DESC');
        
        res.render('knowledge/tags', {
            title: '知识库标签管理',
            tags: tags
        });
        
    } catch (error) {
        console.error('获取知识库标签失败:', error);
        
        // 提供模拟数据
        const mockTags = [
            { id: 1, name: '饲料', usage_count: 15, color: '#007bff' },
            { id: 2, name: '疫苗', usage_count: 12, color: '#28a745' },
            { id: 3, name: '孵化', usage_count: 8, color: '#ffc107' },
            { id: 4, name: '营养', usage_count: 10, color: '#17a2b8' }
        ];
        
        res.render('knowledge/tags', {
            title: '知识库标签管理',
            tags: mockTags,
            isDemo: true
        });
    }
});

// API路由 - 支持AJAX请求

// API: 创建知识文章
router.post('/', async (req, res) => {
    try {
        const {
            title,
            content,
            category,
            tags,
            keywords,
            difficulty,
            readTime,
            summary,
            status,
            publishTime,
            isRecommended,
            allowComments
        } = req.body;

        // 验证必填字段
        if (!title || !content || !category) {
            return res.status(400).json({
                success: false,
                message: '标题、内容和分类为必填项'
            });
        }

        // 处理标签数据
        let processedTags = [];
        if (tags) {
            if (typeof tags === 'string') {
                try {
                    processedTags = JSON.parse(tags);
                } catch (e) {
                    processedTags = tags.split(',').map(tag => tag.trim());
                }
            } else if (Array.isArray(tags)) {
                processedTags = tags;
            }
        }

        const articleData = {
            title,
            content,
            category,
            tags: JSON.stringify(processedTags),
            keywords,
            difficulty: difficulty || 'beginner',
            readTime: parseInt(readTime) || null,
            summary,
            status: status || 'draft',
            publishTime: publishTime ? new Date(publishTime) : null,
            isRecommended: isRecommended === true,
            allowComments: allowComments !== false,
            author_id: req.session.user?.id || 1,
            views: 0,
            created_at: new Date(),
            updated_at: new Date()
        };

        const newArticle = await db.insert('knowledge_articles', articleData);

        res.json({
            success: true,
            message: '知识文章创建成功',
            data: newArticle
        });

    } catch (error) {
        console.error('创建知识文章失败:', error);
        res.status(500).json({
            success: false,
            message: '创建知识文章失败',
            error: error.message
        });
    }
});

// API: 更新知识文章
router.put('/:id', async (req, res) => {
    try {
        const articleId = req.params.id;
        const {
            title,
            content,
            category,
            tags,
            keywords,
            difficulty,
            readTime,
            summary,
            status,
            publishTime,
            isRecommended,
            allowComments
        } = req.body;

        // 验证必填字段
        if (!title || !content || !category) {
            return res.status(400).json({
                success: false,
                message: '标题、内容和分类为必填项'
            });
        }

        // 处理标签数据
        let processedTags = [];
        if (tags) {
            if (typeof tags === 'string') {
                try {
                    processedTags = JSON.parse(tags);
                } catch (e) {
                    processedTags = tags.split(',').map(tag => tag.trim());
                }
            } else if (Array.isArray(tags)) {
                processedTags = tags;
            }
        }

        const updateData = {
            title,
            content,
            category,
            tags: JSON.stringify(processedTags),
            keywords,
            difficulty: difficulty || 'beginner',
            readTime: parseInt(readTime) || null,
            summary,
            status: status || 'draft',
            publishTime: publishTime ? new Date(publishTime) : null,
            isRecommended: isRecommended === true,
            allowComments: allowComments !== false,
            updated_at: new Date()
        };

        await db.update('knowledge_articles', updateData, { id: articleId });

        res.json({
            success: true,
            message: '知识文章更新成功'
        });

    } catch (error) {
        console.error('更新知识文章失败:', error);
        res.status(500).json({
            success: false,
            message: '更新知识文章失败',
            error: error.message
        });
    }
});

// API: 获取文章列表（用于相关文章选择）
router.get('/api/articles', async (req, res) => {
    try {
        const articles = await db.findMany('knowledge_articles',
            { status: 'published' },
            'id, title, category',
            'created_at DESC',
            50
        );

        res.json({
            success: true,
            data: { articles }
        });

    } catch (error) {
        console.error('获取文章列表失败:', error);

        // 提供模拟数据
        const mockArticles = [
            { id: 1, title: '鹅的饲养管理技术', category: '饲养技术' },
            { id: 2, title: '鹅病防治指南', category: '疾病防治' },
            { id: 3, title: '鹅的营养需求分析', category: '营养配方' }
        ];

        res.json({
            success: true,
            data: { articles: mockArticles }
        });
    }
});

module.exports = router;
