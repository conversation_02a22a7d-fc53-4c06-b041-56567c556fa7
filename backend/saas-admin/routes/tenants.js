const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');
const tenantController = require('../../controllers/tenant.controller');

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goose_saas_platform',
    charset: 'utf8mb4'
};

// 租户列表页面
router.get('/', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取租户列表
        const [tenants] = await connection.execute(`
            SELECT 
                t.*,
                COUNT(DISTINCT f.id) as flock_count,
                0 as total_revenue,
                0 as user_count
            FROM tenants t
            LEFT JOIN flocks f ON t.id = f.tenant_id AND f.status = 'active'
            GROUP BY t.id
            ORDER BY t.created_at DESC
        `);
        
        await connection.end();
        
        res.render('tenants/index', { 
            title: '租户管理',
            tenants: tenants
        });
    } catch (error) {
        console.error('获取租户列表失败:', error);
        res.status(500).render('error', { 
            title: '错误',
            message: '获取租户列表失败',
            error: error
        });
    }
});

// 租户详情页面（包含该租户的所有业务数据）
router.get('/:id/details', async (req, res) => {
    try {
        const tenantId = req.params.id;
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取租户基本信息
        const [tenantRows] = await connection.execute(
            'SELECT * FROM tenants WHERE id = ?',
            [tenantId]
        );
        
        if (tenantRows.length === 0) {
            return res.status(404).render('error', {
                title: '租户不存在',
                message: '指定的租户不存在'
            });
        }
        
        const tenant = tenantRows[0];
        
        // 获取该租户的用户 (暂时返回空数组，用户表当前没有tenant_id字段)
        const users = [];
        
        // 获取该租户的鹅群
        const [flocks] = await connection.execute(`
            SELECT f.*
            FROM flocks f
            WHERE f.tenant_id = ?
            ORDER BY f.created_at DESC
        `, [tenantId]);
        
        // 暂时设置空数组，等后续实现具体业务表
        const productions = [];
        const healthRecords = [];
        const financialRecords = [];
        const orders = [];
        const usageStats = [];
        
        await connection.end();
        
        res.render('tenants/details', {
            title: `租户详情 - ${tenant.tenant_name}`,
            tenant,
            users,
            flocks,
            productions,
            healthRecords,
            financialRecords,
            orders,
            usageStats
        });
        
    } catch (error) {
        console.error('获取租户详情失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取租户详情失败',
            error: error
        });
    }
});

// 创建租户页面
router.get('/create', (req, res) => {
    res.render('tenants/create', {
        title: '创建租户'
    });
});

// 创建租户处理
router.post('/create', async (req, res) => {
    try {
        const {
            tenantCode,
            companyName,
            contactName,
            contactPhone,
            contactEmail,
            address,
            businessLicense,
            industry,
            scale,
            subscriptionPlan,
            subscriptionStartDate,
            subscriptionEndDate,
            maxUsers,
            maxFlocks
        } = req.body;
        
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute(`
            INSERT INTO tenants (
                tenantCode, companyName, contactName, contactPhone, 
                contactEmail, address, businessLicense, industry,
                scale, subscriptionPlan, subscriptionStartDate, 
                subscriptionEndDate, maxUsers, maxFlocks, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
        `, [
            tenantCode, companyName, contactName, contactPhone,
            contactEmail, address, businessLicense, industry,
            scale, subscriptionPlan, subscriptionStartDate,
            subscriptionEndDate, maxUsers, maxFlocks
        ]);
        
        await connection.end();
        
        res.redirect('/tenants?success=created');
        
    } catch (error) {
        console.error('创建租户失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '创建租户失败',
            error: error
        });
    }
});

// 编辑租户页面
router.get('/:id/edit', async (req, res) => {
    try {
        const tenantId = req.params.id;
        const connection = await mysql.createConnection(dbConfig);
        
        const [tenantRows] = await connection.execute(
            'SELECT * FROM tenants WHERE id = ?',
            [tenantId]
        );
        
        await connection.end();
        
        if (tenantRows.length === 0) {
            return res.status(404).render('error', {
                title: '租户不存在',
                message: '指定的租户不存在'
            });
        }
        
        res.render('tenants/edit', {
            title: '编辑租户',
            tenant: tenantRows[0]
        });
        
    } catch (error) {
        console.error('获取租户信息失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取租户信息失败',
            error: error
        });
    }
});

// 更新租户处理
router.post('/:id/update', async (req, res) => {
    try {
        const tenantId = req.params.id;
        const {
            companyName,
            contactName,
            contactPhone,
            contactEmail,
            address,
            businessLicense,
            industry,
            scale,
            subscriptionPlan,
            subscriptionStartDate,
            subscriptionEndDate,
            maxUsers,
            maxFlocks,
            status
        } = req.body;
        
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute(`
            UPDATE tenants SET 
                companyName = ?, contactName = ?, contactPhone = ?, 
                contactEmail = ?, address = ?, businessLicense = ?, 
                industry = ?, scale = ?, subscriptionPlan = ?, 
                subscriptionStartDate = ?, subscriptionEndDate = ?, maxUsers = ?, 
                maxFlocks = ?, status = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [
            companyName, contactName, contactPhone, contactEmail,
            address, businessLicense, industry, scale,
            subscriptionPlan, subscriptionStartDate, subscriptionEndDate,
            maxUsers, maxFlocks, status, tenantId
        ]);
        
        await connection.end();
        
        res.redirect(`/tenants/${tenantId}/details?success=updated`);
        
    } catch (error) {
        console.error('更新租户失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '更新租户失败',
            error: error
        });
    }
});

// 订阅管理页面
router.get('/subscriptions', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);

        // 首先检查表结构，确定正确的字段名
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'tenants'
            AND TABLE_SCHEMA = DATABASE()
            AND (COLUMN_NAME LIKE '%subscription%end%' OR COLUMN_NAME = 'subscription_end')
        `);

        console.log('找到的订阅结束日期字段:', columns);

        // 根据实际字段名构建查询，优先使用下划线命名
        let endDateField = 'subscription_end'; // 默认下划线命名
        if (columns.length > 0) {
            // 优先选择 subscription_end，其次是 subscription_end_date
            const preferredField = columns.find(col => col.COLUMN_NAME === 'subscription_end');
            if (preferredField) {
                endDateField = 'subscription_end';
            } else {
                endDateField = columns[0].COLUMN_NAME;
            }
        }

        console.log('使用的字段名:', endDateField);

        // 检查字段是否存在
        const [fieldCheck] = await connection.execute(`
            SELECT COUNT(*) as field_exists
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'tenants'
            AND TABLE_SCHEMA = DATABASE()
            AND COLUMN_NAME = ?
        `, [endDateField]);

        if (fieldCheck[0].field_exists === 0) {
            console.log('字段不存在，使用模拟数据');
            throw new Error(`字段 ${endDateField} 不存在`);
        }

        const [subscriptions] = await connection.execute(`
            SELECT
                t.*,
                DATEDIFF(t.${endDateField}, CURDATE()) as days_remaining,
                CASE
                    WHEN t.${endDateField} IS NULL THEN 'no_subscription'
                    WHEN t.${endDateField} < CURDATE() THEN 'expired'
                    WHEN DATEDIFF(t.${endDateField}, CURDATE()) <= 30 THEN 'expiring'
                    ELSE 'active'
                END as subscription_status
            FROM tenants t
            ORDER BY t.${endDateField} ASC
        `);

        await connection.end();

        // 计算统计数据
        const stats = {
            totalTenants: subscriptions.length,
            activeSubscriptions: subscriptions.filter(s => s.subscription_status === 'active').length,
            expiringSoon: subscriptions.filter(s => s.subscription_status === 'expiring').length,
            expired: subscriptions.filter(s => s.subscription_status === 'expired').length
        };

        res.render('tenants/subscriptions', {
            title: '订阅管理',
            subscriptions,
            stats,
            endDateField
        });

    } catch (error) {
        console.error('获取订阅信息失败:', error);

        // 如果是数据库相关错误，使用模拟数据
        if (error.code === 'ER_BAD_FIELD_ERROR' ||
            error.code === 'ER_NO_SUCH_TABLE' ||
            error.message.includes('字段') ||
            error.message.includes('不存在') ||
            error.message.includes('Unknown column')) {

            console.log('数据库错误，使用模拟数据。错误信息:', error.message);

            const mockSubscriptions = [
                {
                    id: 1,
                    tenant_code: 'DEMO001',
                    company_name: '示例养殖场A',
                    contact_name: '张三',
                    contact_phone: '13800138001',
                    contact_email: '<EMAIL>',
                    subscription_plan: 'standard',
                    subscription_start: '2024-01-01',
                    subscription_end: '2024-12-31',
                    status: 'active',
                    days_remaining: 120,
                    subscription_status: 'active',
                    max_users: 20,
                    max_flocks: 50
                },
                {
                    id: 2,
                    tenant_code: 'DEMO002',
                    company_name: '绿色养殖场',
                    contact_name: '李四',
                    contact_phone: '13800138002',
                    contact_email: '<EMAIL>',
                    subscription_plan: 'premium',
                    subscription_start: '2024-02-01',
                    subscription_end: '2024-11-30',
                    status: 'active',
                    days_remaining: 25,
                    subscription_status: 'expiring',
                    max_users: 50,
                    max_flocks: 100
                },
                {
                    id: 3,
                    tenant_code: 'DEMO003',
                    company_name: '智慧农业科技',
                    contact_name: '王五',
                    contact_phone: '13800138003',
                    contact_email: '<EMAIL>',
                    subscription_plan: 'enterprise',
                    subscription_start: '2023-12-01',
                    subscription_end: '2024-01-15',
                    status: 'active',
                    days_remaining: -30,
                    subscription_status: 'expired',
                    max_users: 100,
                    max_flocks: 200
                }
            ];

            // 计算模拟数据的统计信息
            const mockStats = {
                totalTenants: mockSubscriptions.length,
                activeSubscriptions: mockSubscriptions.filter(s => s.subscription_status === 'active').length,
                expiringSoon: mockSubscriptions.filter(s => s.subscription_status === 'expiring').length,
                expired: mockSubscriptions.filter(s => s.subscription_status === 'expired').length
            };

            return res.render('tenants/subscriptions', {
                title: '订阅管理',
                subscriptions: mockSubscriptions,
                stats: mockStats,
                isDemo: true,
                demoMessage: '当前显示的是演示数据，数据库字段可能需要更新'
            });
        }

        res.status(500).render('error', {
            title: '错误',
            message: '获取订阅信息失败',
            error: error
        });
    }
});

// 使用统计页面
router.get('/usage', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取使用统计数据 (简化查询，用户表暂无tenant_id字段)
        const [usage] = await connection.execute(`
            SELECT 
                t.id,
                t.company_name as companyName,
                t.max_users as maxUsers,
                t.max_flocks as maxFlocks,
                0 as current_users,
                COUNT(DISTINCT tf.id) as current_flocks,
                COALESCE(SUM(tf.currentCount), 0) as current_geese,
                0 as user_usage_percent,
                ROUND(COUNT(DISTINCT tf.id) / NULLIF(t.max_flocks, 0) * 100, 1) as flock_usage_percent
            FROM tenants t
            LEFT JOIN flocks tf ON t.id = tf.tenant_id AND tf.status = 'active'
            WHERE t.status = 'active'
            GROUP BY t.id, t.company_name, t.max_users, t.max_flocks
            ORDER BY flock_usage_percent DESC
        `);
        
        // 获取简化的租户列表用于筛选
        const [tenants] = await connection.execute(`
            SELECT id, company_name FROM tenants WHERE status = 'active' ORDER BY company_name
        `);
        
        // Calculate overall stats for the overview cards (简化查询)
        const [overallStats] = await connection.execute(`
            SELECT 
                COUNT(DISTINCT t.id) as totalUsers,
                COUNT(DISTINCT CASE WHEN t.status = 'active' THEN t.id END) as activeUsers,
                SUM(COALESCE(tf.currentCount, 0)) * 0.001 as totalStorage,
                COUNT(DISTINCT t.id) * 1000 as apiCalls
            FROM tenants t
            LEFT JOIN flocks tf ON t.id = tf.tenant_id AND tf.status = 'active'
            WHERE t.status = 'active'
        `);
        
        await connection.end();
        
        const stats = {
            totalUsers: overallStats[0].totalUsers || 0,
            activeUsers: overallStats[0].activeUsers || 0,  
            totalStorage: overallStats[0].totalStorage || 0,
            apiCalls: overallStats[0].apiCalls || 0
        };

        // Generate sample chart data for trends
        const activityTrend = {
            labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天'],
            activeUsers: [45, 52, 48, 61, 55, 67, 73],
            totalUsers: [95, 98, 102, 105, 108, 112, 115]
        };
        
        const storageTrend = {
            labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天'],
            usage: [12.3, 13.1, 13.8, 14.2, 14.9, 15.3, 15.8]
        };
        
        const featureUsage = {
            labels: ['鹅群管理', '生产记录', '健康监控', '财务管理', '库存管理', '报表分析'],
            usage: [85, 92, 78, 65, 58, 72]
        };
        
        const planComparison = {
            tenantCounts: [45, 32, 18],
            activityScores: [68, 75, 82]
        };

        // Enhanced usage data with additional fields
        const usageData = usage.map(u => ({
            ...u,
            company_name: u.companyName,
            contact_person: '联系人',
            subscription_plan: 'standard',
            storage_used: Math.random() * 10 + 2,
            storage_limit: 20,
            last_login_time: new Date(),
            monthly_logins: Math.floor(Math.random() * 50) + 10,
            monthly_api_calls: Math.floor(Math.random() * 5000) + 500
        }));
        
        res.render('tenants/usage', {
            title: '使用统计',
            usage,
            tenants,
            stats,
            usageData,
            activityTrend,
            storageTrend,
            featureUsage,
            planComparison
        });
        
    } catch (error) {
        console.error('获取使用统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取使用统计失败',
            error: error
        });
    }
});

// API路由 - 导出租户数据
router.get('/api/tenants/export', tenantController.exportTenants);

module.exports = router;