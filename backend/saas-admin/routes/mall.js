const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goose_saas_platform',
    charset: 'utf8mb4'
};

// 商城首页
router.get('/', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取统计数据
        const [stats] = await connection.execute(`
            SELECT 
                (SELECT COUNT(*) FROM mall_products WHERE status = 'active') as totalProducts,
                (SELECT COUNT(*) FROM mall_orders) as totalOrders,
                (SELECT COUNT(*) FROM mall_categories WHERE status = 'active') as totalCategories,
                (SELECT SUM(total_amount) FROM mall_orders WHERE order_status = 'completed') as totalRevenue
        `);
        
        await connection.end();
        
        res.render('mall/index', {
            title: '商城管理',
            stats: stats[0] || {
                totalProducts: 0,
                totalOrders: 0,
                totalCategories: 0,
                totalRevenue: 0
            }
        });
        
    } catch (error) {
        console.error('获取商城数据失败:', error);
        res.render('mall/index', {
            title: '商城管理',
            stats: {
                totalProducts: 0,
                totalOrders: 0,
                totalCategories: 0,
                totalRevenue: 0
            },
            error: '数据库连接失败，显示默认数据'
        });
    }
});

// 商品管理
router.get('/products', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [products] = await connection.execute(`
            SELECT p.*, c.name as category_name 
            FROM mall_products p
            LEFT JOIN mall_categories c ON p.category_id = c.id
            ORDER BY p.created_at DESC
        `);
        
        await connection.end();
        
        res.render('mall/products', {
            title: '商品管理',
            products: products
        });
        
    } catch (error) {
        console.error('获取商品列表失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取商品列表失败',
            error: error
        });
    }
});

// 分类管理
router.get('/categories', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 创建表如果不存在
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS mall_categories (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL COMMENT '分类名称',
                description TEXT COMMENT '分类描述',
                parent_id INT COMMENT '父级分类ID',
                sort_order INT DEFAULT 0 COMMENT '排序',
                icon VARCHAR(500) COMMENT '分类图标',
                status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_parent_id (parent_id),
                INDEX idx_status (status),
                INDEX idx_sort_order (sort_order)
            ) ENGINE=InnoDB COMMENT='商城分类表'
        `);
        
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS mall_products (
                id INT PRIMARY KEY AUTO_INCREMENT,
                category_id INT COMMENT '分类ID',
                name VARCHAR(200) NOT NULL COMMENT '商品名称',
                price DECIMAL(10,2) NOT NULL COMMENT '价格',
                stock_qty INT DEFAULT 0 COMMENT '库存数量',
                status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                FOREIGN KEY (category_id) REFERENCES mall_categories(id),
                INDEX idx_category_id (category_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB COMMENT='商城商品表'
        `);
        
        // 插入示例数据
        const [existingCategories] = await connection.execute('SELECT COUNT(*) as count FROM mall_categories');
        if (existingCategories[0].count === 0) {
            await connection.execute(`
                INSERT INTO mall_categories (name, description, sort_order, status) VALUES 
                ('饲料用品', '各类鹅饲料和营养补充品', 1, 'active'),
                ('养殖设备', '鹅舍建设和养殖设备', 2, 'active'),
                ('医疗用品', '疫苗、药品和医疗器具', 3, 'active'),
                ('鹅产品', '鹅蛋、鹅肉等农产品', 4, 'active')
            `);
        }
        
        const [categories] = await connection.execute(`
            SELECT c.*, 
                   COUNT(p.id) as product_count
            FROM mall_categories c
            LEFT JOIN mall_products p ON c.id = p.category_id
            GROUP BY c.id
            ORDER BY c.created_at DESC
        `);
        
        // 计算统计数据
        const stats = {
            totalCategories: categories.length,
            activeCategories: categories.filter(c => c.status === 'active').length,
            categoriesWithProducts: categories.filter(c => c.product_count > 0).length,
            emptyCategories: categories.filter(c => c.product_count === 0).length
        };
        
        await connection.end();
        
        res.render('mall/categories', {
            title: '商品分类管理',
            categories: categories,
            stats: stats
        });
        
    } catch (error) {
        console.error('获取分类列表失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取分类列表失败',
            error: error
        });
    }
});

// 订单管理
router.get('/orders', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 创建订单表如果不存在
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS mall_orders (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
                tenant_id INT COMMENT '租户ID',
                user_id INT COMMENT '用户ID',
                total_amount DECIMAL(10,2) DEFAULT 0 COMMENT '订单金额',
                status ENUM('pending', 'paid', 'shipped', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '订单状态',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_tenant_id (tenant_id),
                INDEX idx_user_id (user_id),
                INDEX idx_status (status),
                INDEX idx_order_no (order_no)
            ) ENGINE=InnoDB COMMENT='商城订单表'
        `);
        
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS mall_order_items (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_id INT NOT NULL COMMENT '订单ID',
                product_id INT COMMENT '商品ID',
                product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
                price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
                quantity INT NOT NULL COMMENT '数量',
                subtotal DECIMAL(10,2) NOT NULL COMMENT '小计',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (order_id) REFERENCES mall_orders(id),
                INDEX idx_order_id (order_id),
                INDEX idx_product_id (product_id)
            ) ENGINE=InnoDB COMMENT='商城订单项表'
        `);
        
        // 插入示例数据
        const [existingOrders] = await connection.execute('SELECT COUNT(*) as count FROM mall_orders');
        if (existingOrders[0].count === 0) {
            await connection.execute(`
                INSERT INTO mall_orders (order_no, tenant_id, user_id, total_amount, status) VALUES 
                ('ORD202408260001', 1, 1, 299.50, 'paid'),
                ('ORD202408260002', 2, 2, 156.80, 'pending'),
                ('ORD202408260003', 1, 1, 88.20, 'completed')
            `);
        }
        
        const [orders] = await connection.execute(`
            SELECT o.*, 
                   t.company_name as tenant_name,
                   'Demo User' as user_name,
                   COUNT(oi.id) as item_count
            FROM mall_orders o
            LEFT JOIN tenants t ON o.tenant_id = t.id
            LEFT JOIN mall_order_items oi ON o.id = oi.order_id
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT 100
        `);
        
        // 计算统计数据
        const [statsData] = await connection.execute(`
            SELECT 
                COUNT(*) as totalOrders,
                SUM(total_amount) as totalRevenue,
                COUNT(CASE WHEN order_status = 'pending' THEN 1 END) as pendingOrders,
                COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completedOrders
            FROM mall_orders
        `);
        
        await connection.end();
        
        const stats = {
            totalOrders: statsData[0].totalOrders || 0,
            totalRevenue: parseFloat(statsData[0].totalRevenue || 0).toFixed(2),
            pendingOrders: statsData[0].pendingOrders || 0,
            completedOrders: statsData[0].completedOrders || 0
        };
        
        res.render('mall/orders', {
            title: '订单管理',
            orders: orders,
            stats: stats
        });
        
    } catch (error) {
        console.error('获取订单列表失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取订单列表失败',
            error: error
        });
    }
});

// 库存管理
router.get('/inventory', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取所有分类
        const [categories] = await connection.execute(`
            SELECT * FROM mall_categories 
            WHERE status = 'active'
            ORDER BY created_at DESC
        `);
        
        // 获取库存统计
        const [statsResult] = await connection.execute(`
            SELECT 
                COUNT(*) as totalProducts,
                SUM(stock_qty) as totalStock,
                COUNT(CASE WHEN stock_qty <= 10 THEN 1 END) as lowStockCount,
                AVG(price) as avgPrice
            FROM mall_products 
            WHERE status = 'active'
        `);
        
        const [inventory] = await connection.execute(`
            SELECT p.*, c.name as category_name,
                   CASE 
                       WHEN p.stock_qty <= 10 THEN 'low'
                       WHEN p.stock_qty <= 50 THEN 'medium'
                       ELSE 'high'
                   END as stock_level
            FROM mall_products p
            LEFT JOIN mall_categories c ON p.category_id = c.id
            WHERE p.status = 'active'
            ORDER BY p.stock_qty ASC
        `);
        
        await connection.end();
        
        res.render('mall/inventory', {
            title: '库存管理',
            inventory: inventory,
            categories: categories,
            stats: statsResult[0] || {
                totalProducts: 0,
                totalStock: 0,
                lowStockCount: 0,
                avgPrice: 0
            }
        });
        
    } catch (error) {
        console.error('获取库存列表失败:', error);
        
        // 提供默认数据
        const defaultStats = {
            totalProducts: 0,
            totalStock: 0,
            lowStockCount: 0,
            avgPrice: 0
        };
        
        const defaultCategories = [
            { id: 1, name: '饲料用品', status: 'active' },
            { id: 2, name: '养殖设备', status: 'active' },
            { id: 3, name: '医疗用品', status: 'active' }
        ];
        
        res.render('mall/inventory', {
            title: '库存管理',
            inventory: [],
            categories: defaultCategories,
            stats: defaultStats,
            error: '数据库连接失败，显示默认数据'
        });
    }
});

module.exports = router;