const express = require('express');
const router = express.Router();
const db = require('../config/database');

// 公告管理主页
router.get('/', async (req, res) => {
    try {
        // 获取公告列表
        const announcements = await db.findMany('announcements', {}, '*', 'created_at DESC', 20);
        
        // 获取统计数据
        const totalAnnouncements = await db.count('announcements');
        const activeAnnouncements = await db.count('announcements', { status: 'active' });
        const scheduledAnnouncements = await db.count('announcements', { status: 'scheduled' });
        
        const stats = {
            total: totalAnnouncements,
            active: activeAnnouncements,
            scheduled: scheduledAnnouncements,
            totalViews: 8520 // 模拟数据
        };
        
        res.render('announcements/index', {
            title: '公告管理',
            announcements: announcements,
            stats: stats
        });
        
    } catch (error) {
        console.error('获取公告列表失败:', error);
        
        // 提供模拟数据
        const mockAnnouncements = [
            {
                id: 1,
                title: '系统维护通知',
                content: '系统将于本周六凌晨2:00-4:00进行维护升级',
                type: 'system',
                priority: 'high',
                status: 'active',
                target_audience: 'all',
                views: 1250,
                created_at: new Date(),
                publish_time: new Date(),
                expire_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
            },
            {
                id: 2,
                title: '新功能上线公告',
                content: '智能分析功能已正式上线，欢迎体验',
                type: 'feature',
                priority: 'medium',
                status: 'active',
                target_audience: 'premium',
                views: 890,
                created_at: new Date(),
                publish_time: new Date(),
                expire_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            }
        ];
        
        const mockStats = {
            total: 15,
            active: 8,
            scheduled: 3,
            totalViews: 8520
        };
        
        res.render('announcements/index', {
            title: '公告管理',
            announcements: mockAnnouncements,
            stats: mockStats,
            isDemo: true
        });
    }
});

// 创建公告页面
router.get('/create', (req, res) => {
    res.render('announcements/create', {
        title: '创建公告'
    });
});

// 创建公告处理
router.post('/create', async (req, res) => {
    try {
        const { 
            title, 
            content, 
            type, 
            priority, 
            target_audience, 
            publish_time, 
            expire_time,
            status 
        } = req.body;
        
        const announcementData = {
            title,
            content,
            type: type || 'general',
            priority: priority || 'medium',
            target_audience: target_audience || 'all',
            publish_time: publish_time || new Date(),
            expire_time: expire_time || null,
            status: status || 'draft',
            author_id: req.session.user?.id || 1,
            created_at: new Date(),
            updated_at: new Date()
        };
        
        await db.insert('announcements', announcementData);
        
        res.redirect('/announcements?success=created');
        
    } catch (error) {
        console.error('创建公告失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '创建公告失败',
            error: error
        });
    }
});

// 编辑公告页面
router.get('/:id/edit', async (req, res) => {
    try {
        const announcementId = req.params.id;
        const announcement = await db.findOne('announcements', { id: announcementId });
        
        if (!announcement) {
            return res.status(404).render('error', {
                title: '错误',
                message: '公告不存在'
            });
        }
        
        res.render('announcements/edit', {
            title: '编辑公告',
            announcement: announcement
        });
        
    } catch (error) {
        console.error('获取公告失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取公告失败',
            error: error
        });
    }
});

// 更新公告处理
router.post('/:id/update', async (req, res) => {
    try {
        const announcementId = req.params.id;
        const { 
            title, 
            content, 
            type, 
            priority, 
            target_audience, 
            publish_time, 
            expire_time,
            status 
        } = req.body;
        
        const updateData = {
            title,
            content,
            type,
            priority,
            target_audience,
            publish_time,
            expire_time,
            status,
            updated_at: new Date()
        };
        
        await db.update('announcements', updateData, { id: announcementId });
        
        res.redirect(`/announcements/${announcementId}/edit?success=updated`);
        
    } catch (error) {
        console.error('更新公告失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '更新公告失败',
            error: error
        });
    }
});

// 删除公告
router.delete('/:id', async (req, res) => {
    try {
        const announcementId = req.params.id;
        
        await db.delete('announcements', { id: announcementId });
        
        res.json({
            success: true,
            message: '公告删除成功'
        });
        
    } catch (error) {
        console.error('删除公告失败:', error);
        res.status(500).json({
            success: false,
            message: '删除公告失败',
            error: error.message
        });
    }
});

// 发布公告
router.post('/:id/publish', async (req, res) => {
    try {
        const announcementId = req.params.id;
        
        await db.update('announcements', {
            status: 'active',
            publish_time: new Date(),
            updated_at: new Date()
        }, { id: announcementId });
        
        res.json({
            success: true,
            message: '公告发布成功'
        });
        
    } catch (error) {
        console.error('发布公告失败:', error);
        res.status(500).json({
            success: false,
            message: '发布公告失败',
            error: error.message
        });
    }
});

// 撤回公告
router.post('/:id/withdraw', async (req, res) => {
    try {
        const announcementId = req.params.id;
        
        await db.update('announcements', {
            status: 'draft',
            updated_at: new Date()
        }, { id: announcementId });
        
        res.json({
            success: true,
            message: '公告撤回成功'
        });
        
    } catch (error) {
        console.error('撤回公告失败:', error);
        res.status(500).json({
            success: false,
            message: '撤回公告失败',
            error: error.message
        });
    }
});

// 公告统计页面
router.get('/stats', async (req, res) => {
    try {
        // 获取公告统计数据
        const stats = {
            totalAnnouncements: await db.count('announcements'),
            activeAnnouncements: await db.count('announcements', { status: 'active' }),
            totalViews: 8520, // 模拟数据
            avgViews: 568 // 模拟数据
        };
        
        res.render('announcements/stats', {
            title: '公告统计',
            stats: stats
        });
        
    } catch (error) {
        console.error('获取公告统计失败:', error);
        
        // 提供模拟数据
        const mockStats = {
            totalAnnouncements: 15,
            activeAnnouncements: 8,
            totalViews: 8520,
            avgViews: 568
        };
        
        res.render('announcements/stats', {
            title: '公告统计',
            stats: mockStats,
            isDemo: true
        });
    }
});

// 发布公告
router.post('/:id/publish', async (req, res) => {
    try {
        const announcementId = req.params.id;

        const updateData = {
            status: 'active',
            publish_time: new Date(),
            updated_at: new Date()
        };

        await db.update('announcements', updateData, { id: announcementId });

        res.json({
            success: true,
            message: '公告发布成功'
        });

    } catch (error) {
        console.error('发布公告失败:', error);
        res.status(500).json({
            success: false,
            message: '发布公告失败',
            error: error.message
        });
    }
});

// 撤回公告
router.post('/:id/withdraw', async (req, res) => {
    try {
        const announcementId = req.params.id;

        const updateData = {
            status: 'draft',
            updated_at: new Date()
        };

        await db.update('announcements', updateData, { id: announcementId });

        res.json({
            success: true,
            message: '公告撤回成功'
        });

    } catch (error) {
        console.error('撤回公告失败:', error);
        res.status(500).json({
            success: false,
            message: '撤回公告失败',
            error: error.message
        });
    }
});

// API路由 - 支持AJAX请求

// API: 创建公告
router.post('/', async (req, res) => {
    try {
        const {
            title,
            content,
            summary,
            type,
            priority,
            target_audience,
            publish_time,
            expire_time,
            status,
            is_top,
            is_important,
            send_notification
        } = req.body;

        // 验证必填字段
        if (!title || !content) {
            return res.status(400).json({
                success: false,
                message: '标题和内容为必填项'
            });
        }

        const announcementData = {
            title,
            content,
            summary,
            type: type || 'general',
            priority: priority || 'medium',
            target_audience: target_audience || 'all',
            publish_time: publish_time ? new Date(publish_time) : new Date(),
            expire_time: expire_time ? new Date(expire_time) : null,
            status: status || 'draft',
            is_top: is_top === true,
            is_important: is_important === true,
            author_id: req.session.user?.id || 1,
            views: 0,
            created_at: new Date(),
            updated_at: new Date()
        };

        const newAnnouncement = await db.insert('announcements', announcementData);

        // 如果需要发送通知
        if (send_notification && status === 'active') {
            // 这里可以添加发送通知的逻辑
            console.log('发送公告通知:', title);
        }

        res.json({
            success: true,
            message: '公告创建成功',
            data: newAnnouncement
        });

    } catch (error) {
        console.error('创建公告失败:', error);
        res.status(500).json({
            success: false,
            message: '创建公告失败',
            error: error.message
        });
    }
});

// API: 更新公告
router.put('/:id', async (req, res) => {
    try {
        const announcementId = req.params.id;
        const {
            title,
            content,
            summary,
            type,
            priority,
            target_audience,
            publish_time,
            expire_time,
            status,
            is_top,
            is_important
        } = req.body;

        // 验证必填字段
        if (!title || !content) {
            return res.status(400).json({
                success: false,
                message: '标题和内容为必填项'
            });
        }

        const updateData = {
            title,
            content,
            summary,
            type: type || 'general',
            priority: priority || 'medium',
            target_audience: target_audience || 'all',
            publish_time: publish_time ? new Date(publish_time) : null,
            expire_time: expire_time ? new Date(expire_time) : null,
            status: status || 'draft',
            is_top: is_top === true,
            is_important: is_important === true,
            updated_at: new Date()
        };

        await db.update('announcements', updateData, { id: announcementId });

        res.json({
            success: true,
            message: '公告更新成功'
        });

    } catch (error) {
        console.error('更新公告失败:', error);
        res.status(500).json({
            success: false,
            message: '更新公告失败',
            error: error.message
        });
    }
});

module.exports = router;
