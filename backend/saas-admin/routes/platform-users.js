const express = require('express');
const router = express.Router();
const db = require('../config/database');

// 平台用户管理主页
router.get('/', async (req, res) => {
    try {
        // 获取用户列表
        const users = await db.findMany('platform_users', {}, '*', 'created_at DESC', 20);
        
        // 获取统计数据
        const totalUsers = await db.count('platform_users');
        const activeUsers = await db.count('platform_users', { status: 'active' });
        const adminUsers = await db.count('platform_users', { role: 'admin' });
        
        const stats = {
            total: totalUsers,
            active: activeUsers,
            admin: adminUsers,
            newToday: 5 // 模拟数据
        };
        
        res.render('platform-users/index', {
            title: '平台用户管理',
            users: users,
            stats: stats
        });
        
    } catch (error) {
        console.error('获取平台用户列表失败:', error);
        
        // 提供模拟数据
        const mockUsers = [
            {
                id: 1,
                username: 'admin',
                email: '<EMAIL>',
                name: '系统管理员',
                role: 'super_admin',
                status: 'active',
                last_login: new Date(),
                created_at: new Date('2024-01-01'),
                login_count: 1250
            },
            {
                id: 2,
                username: 'manager1',
                email: '<EMAIL>',
                name: '运营经理',
                role: 'admin',
                status: 'active',
                last_login: new Date(),
                created_at: new Date('2024-01-15'),
                login_count: 890
            },
            {
                id: 3,
                username: 'support1',
                email: '<EMAIL>',
                name: '客服专员',
                role: 'support',
                status: 'active',
                last_login: new Date(),
                created_at: new Date('2024-02-01'),
                login_count: 456
            }
        ];
        
        const mockStats = {
            total: 15,
            active: 12,
            admin: 3,
            newToday: 2
        };
        
        res.render('platform-users/index', {
            title: '平台用户管理',
            users: mockUsers,
            stats: mockStats,
            isDemo: true
        });
    }
});

// 创建平台用户页面
router.get('/create', (req, res) => {
    res.render('platform-users/create', {
        title: '创建平台用户'
    });
});

// 创建平台用户处理
router.post('/create', async (req, res) => {
    try {
        const { username, email, name, role, password } = req.body;
        
        // 检查用户名和邮箱是否已存在
        const existingUserByUsername = await db.findOne('platform_users', { username });
        const existingUserByEmail = email ? await db.findOne('platform_users', { email }) : null;
        const existingUser = existingUserByUsername || existingUserByEmail;
        
        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: '用户名或邮箱已存在'
            });
        }
        
        const userData = {
            username,
            email,
            name,
            role: role || 'support',
            password: password, // 实际应用中需要加密
            status: 'active',
            created_at: new Date(),
            created_by: req.session.user?.id || 1
        };
        
        await db.insert('platform_users', userData);
        
        res.redirect('/platform-users?success=created');
        
    } catch (error) {
        console.error('创建平台用户失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '创建平台用户失败',
            error: error
        });
    }
});

// 编辑平台用户页面
router.get('/:id/edit', async (req, res) => {
    try {
        const userId = req.params.id;
        const user = await db.findOne('platform_users', { id: userId });
        
        if (!user) {
            return res.status(404).render('error', {
                title: '错误',
                message: '用户不存在'
            });
        }
        
        res.render('platform-users/edit', {
            title: '编辑平台用户',
            user: user
        });
        
    } catch (error) {
        console.error('获取平台用户失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取平台用户失败',
            error: error
        });
    }
});

// 更新平台用户处理
router.post('/:id/update', async (req, res) => {
    try {
        const userId = req.params.id;
        const { email, name, role, status } = req.body;
        
        const updateData = {
            email,
            name,
            role,
            status,
            updated_at: new Date()
        };
        
        await db.update('platform_users', updateData, { id: userId });
        
        res.redirect(`/platform-users/${userId}/edit?success=updated`);
        
    } catch (error) {
        console.error('更新平台用户失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '更新平台用户失败',
            error: error
        });
    }
});

// 删除平台用户
router.delete('/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        
        // 检查是否为超级管理员
        const user = await db.findOne('platform_users', { id: userId });
        if (user && user.role === 'super_admin') {
            return res.status(400).json({
                success: false,
                message: '不能删除超级管理员账户'
            });
        }
        
        await db.delete('platform_users', { id: userId });
        
        res.json({
            success: true,
            message: '用户删除成功'
        });
        
    } catch (error) {
        console.error('删除平台用户失败:', error);
        res.status(500).json({
            success: false,
            message: '删除平台用户失败',
            error: error.message
        });
    }
});

// 重置用户密码
router.post('/:id/reset-password', async (req, res) => {
    try {
        const userId = req.params.id;
        const { new_password } = req.body;
        
        const updateData = {
            password: new_password, // 实际应用中需要加密
            password_reset_at: new Date(),
            updated_at: new Date()
        };
        
        await db.update('platform_users', updateData, { id: userId });
        
        res.json({
            success: true,
            message: '密码重置成功'
        });
        
    } catch (error) {
        console.error('重置密码失败:', error);
        res.status(500).json({
            success: false,
            message: '重置密码失败',
            error: error.message
        });
    }
});

// 用户权限管理页面
router.get('/:id/permissions', async (req, res) => {
    try {
        const userId = req.params.id;
        const user = await db.findOne('platform_users', { id: userId });
        
        if (!user) {
            return res.status(404).render('error', {
                title: '错误',
                message: '用户不存在'
            });
        }
        
        // 获取用户权限
        const permissions = [
            { module: 'tenant_management', name: '租户管理', granted: true },
            { module: 'user_management', name: '用户管理', granted: true },
            { module: 'system_settings', name: '系统设置', granted: false },
            { module: 'api_management', name: 'API管理', granted: true },
            { module: 'data_export', name: '数据导出', granted: false }
        ];
        
        res.render('platform-users/permissions', {
            title: '用户权限管理',
            user: user,
            permissions: permissions
        });
        
    } catch (error) {
        console.error('获取用户权限失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取用户权限失败',
            error: error
        });
    }
});

// 更新用户权限
router.post('/:id/permissions', async (req, res) => {
    try {
        const userId = req.params.id;
        const { permissions } = req.body;
        
        // 这里应该更新用户权限到数据库
        // await db.update('user_permissions', { permissions: JSON.stringify(permissions) }, { user_id: userId });
        
        res.json({
            success: true,
            message: '权限更新成功'
        });
        
    } catch (error) {
        console.error('更新用户权限失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户权限失败',
            error: error.message
        });
    }
});

// 用户活动日志页面
router.get('/:id/logs', async (req, res) => {
    try {
        const userId = req.params.id;
        const user = await db.findOne('platform_users', { id: userId });
        
        if (!user) {
            return res.status(404).render('error', {
                title: '错误',
                message: '用户不存在'
            });
        }
        
        // 获取用户活动日志
        const logs = [
            {
                id: 1,
                action: '登录系统',
                ip_address: '*************',
                user_agent: 'Mozilla/5.0...',
                created_at: new Date(),
                status: 'success'
            },
            {
                id: 2,
                action: '创建租户',
                details: '创建租户: 示例养殖场',
                ip_address: '*************',
                created_at: new Date(),
                status: 'success'
            }
        ];
        
        res.render('platform-users/logs', {
            title: '用户活动日志',
            user: user,
            logs: logs
        });
        
    } catch (error) {
        console.error('获取用户活动日志失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取用户活动日志失败',
            error: error
        });
    }
});

module.exports = router;
