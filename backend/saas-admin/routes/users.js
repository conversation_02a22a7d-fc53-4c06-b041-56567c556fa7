const express = require('express');
const router = express.Router();
const db = require('../config/database');

// Users listing page
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';
        
        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;
        
        // Add search conditions
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }
        
        // Build query
        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt
            FROM users ${finalWhere}
            ORDER BY created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);
        
        const pagination = {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
            hasNext: page < Math.ceil(total / pageSize),
            hasPrev: page > 1
        };
        
        res.render('users/index', {
            title: '用户管理',
            users,
            pagination,
            filters: { search, role, status }
        });
        
    } catch (error) {
        console.error('Users page error:', error);
        res.render('error', {
            title: 'Error',
            message: '无法加载用户列表',
            error: error
        });
    }
});

// User profile page
router.get('/profile', (req, res) => {
    res.render('users/profile', {
        title: '个人资料'
    });
});

module.exports = router;