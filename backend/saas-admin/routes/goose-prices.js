const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goose_saas_platform',
    charset: 'utf8mb4'
};

// 今日鹅价列表页面
router.get('/', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取今日鹅价列表
        const [prices] = await connection.execute(`
            SELECT * FROM goose_prices 
            WHERE is_published = TRUE
            ORDER BY date DESC, region ASC, breed ASC
            LIMIT 50
        `);
        
        // 计算统计数据
        const [statsData] = await connection.execute(`
            SELECT 
                AVG(price) as avgPrice,
                MAX(price) as maxPrice,
                MIN(price) as minPrice,
                COUNT(*) as totalRecords
            FROM goose_prices 
            WHERE is_published = TRUE 
            AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        `);
        
        // 获取地区列表
        const [regionData] = await connection.execute(`
            SELECT DISTINCT region FROM goose_prices 
            WHERE is_published = TRUE 
            ORDER BY region
        `);
        
        // 获取最近7天的图表数据
        const [chartDataRaw] = await connection.execute(`
            SELECT 
                date,
                AVG(price) as avgPrice
            FROM goose_prices 
            WHERE is_published = TRUE 
            AND date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY date
            ORDER BY date ASC
        `);
        
        await connection.end();
        
        const stats = {
            currentPrice: parseFloat(statsData[0].avgPrice || 0).toFixed(2),
            maxPrice: parseFloat(statsData[0].maxPrice || 0).toFixed(2),
            minPrice: parseFloat(statsData[0].minPrice || 0).toFixed(2),
            monthlyChange: 2.3 // 示例数据，实际应该计算
        };
        
        const regions = regionData.map(r => r.region);
        
        const chartData = {
            labels: chartDataRaw.map(d => d.date),
            prices: chartDataRaw.map(d => parseFloat(d.avgPrice || 0).toFixed(2))
        };
        
        res.render('goose-prices/index', {
            title: '今日鹅价管理',
            prices: prices,
            stats: stats,
            regions: regions,
            chartData: chartData
        });
        
    } catch (error) {
        console.error('获取鹅价列表失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取鹅价列表失败',
            error: error
        });
    }
});

// 创建鹅价页面
router.get('/create', (req, res) => {
    res.render('goose-prices/create', {
        title: '发布今日鹅价'
    });
});

// 创建鹅价处理
router.post('/create', async (req, res) => {
    try {
        const {
            date,
            region,
            breed,
            price,
            unit,
            market_name,
            notes,
            is_published
        } = req.body;
        
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute(`
            INSERT INTO goose_prices (
                date, region, breed, price, unit, market_name, is_published
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            date, region, breed, price, unit, market_name, is_published === 'on'
        ]);
        
        await connection.end();
        
        res.redirect('/goose-prices?success=created');
        
    } catch (error) {
        console.error('发布鹅价失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '发布鹅价失败',
            error: error
        });
    }
});

// 价格趋势分析页面
router.get('/trends', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);

        // 获取查询参数
        const { dateRange = '30', region = '', gooseType = '' } = req.query;

        // 获取地区列表
        const [regionData] = await connection.execute(`
            SELECT DISTINCT region FROM goose_prices
            WHERE is_published = TRUE
            ORDER BY region
        `);

        // 获取最近指定天数的价格趋势
        const [trends] = await connection.execute(`
            SELECT
                date,
                region,
                breed,
                unit,
                price,
                market_name
            FROM goose_prices
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            AND is_published = true
            ${region ? 'AND region = ?' : ''}
            ${gooseType ? 'AND breed = ?' : ''}
            ORDER BY date DESC, region ASC
        `, [dateRange, ...(region ? [region] : []), ...(gooseType ? [gooseType] : [])]);

        // 处理趋势数据用于图表显示
        const trendData = {
            labels: [],
            prices: []
        };

        const regionData_chart = {
            labels: [],
            prices: []
        };

        const typeData = {
            labels: [],
            prices: []
        };

        if (trends.length > 0) {
            // 按日期分组计算平均价格
            const dateGroups = {};
            const regionGroups = {};
            const typeGroups = {};

            trends.forEach(trend => {
                const dateKey = trend.date;
                if (!dateGroups[dateKey]) {
                    dateGroups[dateKey] = { total: 0, count: 0 };
                }
                dateGroups[dateKey].total += parseFloat(trend.price || 0);
                dateGroups[dateKey].count += 1;

                // 地区分组
                if (!regionGroups[trend.region]) {
                    regionGroups[trend.region] = { total: 0, count: 0 };
                }
                regionGroups[trend.region].total += parseFloat(trend.price || 0);
                regionGroups[trend.region].count += 1;

                // 品种分组
                if (!typeGroups[trend.breed]) {
                    typeGroups[trend.breed] = { total: 0, count: 0 };
                }
                typeGroups[trend.breed].total += parseFloat(trend.price || 0);
                typeGroups[trend.breed].count += 1;
            });

            // 生成趋势图数据
            Object.keys(dateGroups).sort().forEach(date => {
                trendData.labels.push(date);
                trendData.prices.push((dateGroups[date].total / dateGroups[date].count).toFixed(2));
            });

            // 生成地区分布数据
            Object.keys(regionGroups).forEach(region => {
                regionData_chart.labels.push(region);
                regionData_chart.prices.push((regionGroups[region].total / regionGroups[region].count).toFixed(2));
            });

            // 生成品种对比数据
            Object.keys(typeGroups).forEach(type => {
                typeData.labels.push(type);
                typeData.prices.push((typeGroups[type].total / typeGroups[type].count).toFixed(2));
            });
        }

        await connection.end();

        const regions = regionData.map(r => r.region);

        res.render('goose-prices/trends', {
            title: '鹅价趋势分析',
            trends: trends,
            regions: regions,
            trendData: trendData,
            regionData: regionData_chart,
            typeData: typeData,
            currentFilters: {
                dateRange,
                region,
                gooseType
            }
        });

    } catch (error) {
        console.error('获取价格趋势失败:', error);

        // 如果数据库表不存在，提供模拟数据
        const mockRegions = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '重庆'];
        const mockTrendData = {
            labels: ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
            prices: ['16.50', '16.80', '17.20', '16.90', '17.10']
        };
        const mockRegionData = {
            labels: ['北京', '上海', '广州', '深圳'],
            prices: ['18.50', '17.80', '16.20', '17.90']
        };
        const mockTypeData = {
            labels: ['白鹅', '灰鹅', '狮头鹅', '太湖鹅'],
            prices: ['16.80', '17.20', '19.50', '18.30']
        };

        res.render('goose-prices/trends', {
            title: '鹅价趋势分析',
            trends: [],
            regions: mockRegions,
            trendData: mockTrendData,
            regionData: mockRegionData,
            typeData: mockTypeData,
            currentFilters: {
                dateRange: '30',
                region: '',
                gooseType: ''
            },
            isDemo: true
        });
    }
});

module.exports = router;