const express = require('express');
const router = express.Router();
const db = require('../config/database');

// Dashboard API routes
router.get('/dashboard/stats', async (req, res) => {
    try {
        // Get dashboard statistics (same logic as dashboard route)
        const totalUsers = await db.count('users');
        
        const activeUsersResult = await db.execute(`
            SELECT COUNT(*) as count FROM users 
            WHERE last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
            AND status = 'active'
        `);
        const activeUsers = activeUsersResult[0].count || 0;
        
        const totalFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks');
        const totalFlocks = totalFlocksResult[0].count || 0;
        
        const activeFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks WHERE status = "active"');
        const activeFlocks = activeFlocksResult[0].count || 0;
        
        const totalGeeseResult = await db.execute('SELECT SUM(currentCount) as total FROM flocks WHERE status = "active"');
        const totalGeese = totalGeeseResult[0].total || 0;
        
        // Use default values for missing tables
        const todayEggs = 0; // production_records table doesn't exist
        const monthlyRevenue = 0; // financial_records table doesn't exist  
        const monthlyExpenses = 0; // financial_records table doesn't exist
        
        const stats = {
            totalUsers,
            activeUsers,
            totalFlocks,
            activeFlocks,
            totalGeese,
            todayEggs,
            monthlyRevenue,
            monthlyExpenses,
            monthlyProfit: monthlyRevenue - monthlyExpenses
        };
        
        res.json({
            success: true,
            data: stats
        });
        
    } catch (error) {
        console.error('Dashboard stats API error:', error);
        res.status(500).json({
            success: false,
            message: '获取统计数据失败'
        });
    }
});

// Users API routes
router.get('/users/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';
        
        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt 
            FROM users ${finalWhere} 
            ORDER BY created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Users list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});

// Create user
router.post('/users/create', async (req, res) => {
    try {
        const { username, email, password, name, role = 'user', farmName } = req.body;
        
        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名、邮箱和密码为必填项'
            });
        }
        
        // Check if username or email already exists
        const existingUser = await db.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );
        
        if (existingUser.length > 0) {
            return res.status(400).json({
                success: false,
                message: '用户名或邮箱已存在'
            });
        }
        
        // Hash password
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash(password, 12);
        
        // Insert user
        const result = await db.execute(
            'INSERT INTO users (username, email, password_hash, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)',
            [username, email, hashedPassword, name, role, 'active']
        );
        
        res.json({
            success: true,
            message: '用户创建成功',
            data: { id: result.insertId }
        });
        
    } catch (error) {
        console.error('Create user API error:', error);
        res.status(500).json({
            success: false,
            message: '创建用户失败'
        });
    }
});

// Update user
router.put('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const { name, email, role, status, farmName } = req.body;
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Update user
        const updateFields = [];
        const updateValues = [];
        
        if (name !== undefined) {
            updateFields.push('full_name = ?');
            updateValues.push(name);
        }
        if (email !== undefined) {
            updateFields.push('email = ?');
            updateValues.push(email);
        }
        if (role !== undefined) {
            updateFields.push('role = ?');
            updateValues.push(role);
        }
        if (status !== undefined) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        
        if (updateFields.length > 0) {
            updateValues.push(userId);
            await db.execute(
                `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
                updateValues
            );
        }
        
        res.json({
            success: true,
            message: '用户更新成功'
        });
        
    } catch (error) {
        console.error('Update user API error:', error);
        res.status(500).json({
            success: false,
            message: '更新用户失败'
        });
    }
});

// Delete user
router.delete('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Don't allow deleting admin users
        if (user.role === 'admin') {
            return res.status(400).json({
                success: false,
                message: '不能删除管理员用户'
            });
        }
        
        // Delete user (cascading deletes will handle related records)
        await db.execute('DELETE FROM users WHERE id = ?', [userId]);
        
        res.json({
            success: true,
            message: '用户删除成功'
        });
        
    } catch (error) {
        console.error('Delete user API error:', error);
        res.status(500).json({
            success: false,
            message: '删除用户失败'
        });
    }
});

// Flocks API routes
router.get('/flocks/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const userId = req.query.userId;
        const status = req.query.status || '';
        
        let conditions = {};
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR f.breed LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `f.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `
            SELECT COUNT(*) as total 
            FROM flocks f 
            ${finalWhere}
        `;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT f.*, t.company_name as tenant_name
            FROM flocks f 
            LEFT JOIN tenants t ON f.tenant_id = t.id 
            ${finalWhere}
            ORDER BY f.created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const flocks = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                flocks,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Flocks list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅群列表失败'
        });
    }
});

// Users API endpoint (redirect to existing users/list)
router.get('/users', async (req, res) => {
    try {
        // Redirect to the existing users/list endpoint
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';

        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt
            FROM users ${finalWhere}
            ORDER BY created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: users,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Users API error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});

// Tenants API endpoint
router.get('/tenants', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const status = req.query.status || '';

        let conditions = {};
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (company_name LIKE ? OR contact_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM tenants ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total || 0;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, company_name, contact_name, email, phone, status, subscription_plan,
                   subscription_status, created_at as createdAt, updated_at as updatedAt
            FROM tenants ${finalWhere}
            ORDER BY created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const tenants = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: tenants,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Tenants API error:', error);
        res.status(500).json({
            success: false,
            message: '获取租户列表失败'
        });
    }
});

// Flocks API endpoint (redirect to existing flocks/list)
router.get('/flocks', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const status = req.query.status || '';

        let conditions = {};
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR f.breed LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `f.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM flocks f ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total || 0;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT f.*, t.company_name as tenant_name
            FROM flocks f
            LEFT JOIN tenants t ON f.tenant_id = t.id
            ${finalWhere}
            ORDER BY f.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const flocks = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: flocks,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Flocks API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅群列表失败'
        });
    }
});

// Production API endpoint
router.get('/production', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const flockId = req.query.flockId;
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        let conditions = {};
        if (flockId) conditions.flock_id = flockId;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR pr.notes LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }

        if (dateFrom) {
            searchSql += ' AND pr.record_date >= ?';
            searchParams.push(dateFrom);
        }

        if (dateTo) {
            searchSql += ' AND pr.record_date <= ?';
            searchParams.push(dateTo);
        }

        const whereClauses = Object.keys(conditions).map(key => `pr.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Mock data since production_records table might not exist
        const mockProduction = [
            {
                id: 1,
                flock_id: 1,
                flock_name: '示例鹅群1',
                record_date: new Date().toISOString().split('T')[0],
                egg_count: 120,
                feed_consumption: 50.5,
                water_consumption: 80.2,
                notes: '正常生产',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                flock_id: 2,
                flock_name: '示例鹅群2',
                record_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                egg_count: 95,
                feed_consumption: 45.2,
                water_consumption: 75.8,
                notes: '产蛋量略低',
                created_at: new Date(Date.now() - 86400000).toISOString()
            }
        ];

        const total = mockProduction.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = mockProduction.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Production API error:', error);
        res.status(500).json({
            success: false,
            message: '获取生产记录失败'
        });
    }
});

// Finance API endpoint
router.get('/finance', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const type = req.query.type; // 'income' or 'expense'
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock financial data
        const mockFinance = [
            {
                id: 1,
                type: 'income',
                category: '鹅蛋销售',
                amount: 2500.00,
                description: '本周鹅蛋销售收入',
                transaction_date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                type: 'expense',
                category: '饲料采购',
                amount: 800.00,
                description: '购买优质饲料',
                transaction_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                type: 'expense',
                category: '兽医费用',
                amount: 300.00,
                description: '疫苗接种费用',
                transaction_date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockFinance;
        if (type) {
            filteredData = filteredData.filter(item => item.type === type);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Finance API error:', error);
        res.status(500).json({
            success: false,
            message: '获取财务记录失败'
        });
    }
});

// Inventory API endpoint
router.get('/inventory', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const lowStock = req.query.lowStock === 'true';

        // Mock inventory data
        const mockInventory = [
            {
                id: 1,
                item_name: '优质鹅饲料',
                category: 'feed',
                current_stock: 500,
                unit: 'kg',
                min_stock_level: 100,
                max_stock_level: 1000,
                unit_price: 3.50,
                supplier: '绿源饲料公司',
                last_updated: new Date().toISOString()
            },
            {
                id: 2,
                item_name: '鹅蛋包装盒',
                category: 'packaging',
                current_stock: 50,
                unit: '个',
                min_stock_level: 100,
                max_stock_level: 500,
                unit_price: 0.80,
                supplier: '包装材料厂',
                last_updated: new Date().toISOString()
            },
            {
                id: 3,
                item_name: '疫苗',
                category: 'medical',
                current_stock: 20,
                unit: '支',
                min_stock_level: 10,
                max_stock_level: 50,
                unit_price: 15.00,
                supplier: '兽医药品公司',
                last_updated: new Date().toISOString()
            }
        ];

        let filteredData = mockInventory;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (lowStock) {
            filteredData = filteredData.filter(item => item.current_stock <= item.min_stock_level);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Inventory API error:', error);
        res.status(500).json({
            success: false,
            message: '获取库存列表失败'
        });
    }
});

// Reports API endpoint
router.get('/reports', async (req, res) => {
    try {
        const reportType = req.query.type || 'summary';
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock report data
        const mockReports = {
            summary: {
                totalFlocks: 15,
                totalGeese: 1250,
                totalEggsThisMonth: 3500,
                totalRevenueThisMonth: 12500.00,
                totalExpensesThisMonth: 4200.00,
                profitThisMonth: 8300.00,
                averageEggsPerDay: 116.7,
                feedConsumptionThisMonth: 850.5
            },
            production: [
                { date: '2025-08-20', eggs: 115, feed: 28.5 },
                { date: '2025-08-21', eggs: 118, feed: 29.2 },
                { date: '2025-08-22', eggs: 112, feed: 27.8 },
                { date: '2025-08-23', eggs: 120, feed: 30.1 },
                { date: '2025-08-24', eggs: 116, feed: 28.9 }
            ],
            financial: [
                { month: '2025-01', income: 10500, expense: 3800, profit: 6700 },
                { month: '2025-02', income: 11200, expense: 4100, profit: 7100 },
                { month: '2025-03', income: 12800, expense: 4500, profit: 8300 },
                { month: '2025-04', income: 13500, expense: 4800, profit: 8700 },
                { month: '2025-05', income: 14200, expense: 5200, profit: 9000 }
            ]
        };

        res.json({
            success: true,
            data: mockReports[reportType] || mockReports.summary
        });

    } catch (error) {
        console.error('Reports API error:', error);
        res.status(500).json({
            success: false,
            message: '获取报表数据失败'
        });
    }
});

// Goose Prices API endpoint
router.get('/goose-prices', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const region = req.query.region;
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock goose price data
        const mockPrices = [
            {
                id: 1,
                region: '华东地区',
                price_per_kg: 28.50,
                price_type: 'live_goose',
                market_name: '上海农产品批发市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'up',
                change_percent: 2.5,
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                region: '华北地区',
                price_per_kg: 26.80,
                price_type: 'live_goose',
                market_name: '北京新发地市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'stable',
                change_percent: 0.2,
                created_at: new Date().toISOString()
            },
            {
                id: 3,
                region: '华南地区',
                price_per_kg: 30.20,
                price_type: 'live_goose',
                market_name: '广州江南果菜批发市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'down',
                change_percent: -1.8,
                created_at: new Date().toISOString()
            }
        ];

        let filteredData = mockPrices;
        if (region) {
            filteredData = filteredData.filter(item => item.region.includes(region));
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Goose prices API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅价信息失败'
        });
    }
});

// Mall Products API endpoint
router.get('/mall/products', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const status = req.query.status || 'active';

        // Mock mall products data
        const mockProducts = [
            {
                id: 1,
                name: '新鲜鹅蛋',
                category: 'eggs',
                price: 2.50,
                stock: 500,
                status: 'active',
                description: '农场直供新鲜鹅蛋，营养丰富',
                image_url: '/images/products/goose-eggs.jpg',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                name: '优质鹅肉',
                category: 'meat',
                price: 35.00,
                stock: 50,
                status: 'active',
                description: '散养鹅肉，肉质鲜美',
                image_url: '/images/products/goose-meat.jpg',
                created_at: new Date().toISOString()
            },
            {
                id: 3,
                name: '鹅绒被',
                category: 'products',
                price: 299.00,
                stock: 20,
                status: 'active',
                description: '100%鹅绒填充，保暖舒适',
                image_url: '/images/products/goose-down.jpg',
                created_at: new Date().toISOString()
            }
        ];

        let filteredData = mockProducts;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (status) {
            filteredData = filteredData.filter(item => item.status === status);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Mall products API error:', error);
        res.status(500).json({
            success: false,
            message: '获取商品列表失败'
        });
    }
});

// Knowledge API endpoint
router.get('/knowledge', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const search = req.query.search || '';

        // Mock knowledge base data
        const mockKnowledge = [
            {
                id: 1,
                title: '鹅的饲养管理技术',
                category: 'breeding',
                content: '鹅是水禽，具有适应性强、生长快、耐粗饲等特点...',
                author: '养殖专家',
                views: 1250,
                likes: 89,
                status: 'published',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '鹅病防治指南',
                category: 'health',
                content: '鹅的常见疾病包括鹅瘟、鹅副粘病毒病、禽霍乱等...',
                author: '兽医专家',
                views: 980,
                likes: 67,
                status: 'published',
                created_at: new Date(Date.now() - 86400000).toISOString(),
                updated_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                title: '鹅蛋孵化技术要点',
                category: 'breeding',
                content: '鹅蛋孵化期为30-31天，孵化温度和湿度控制很重要...',
                author: '孵化专家',
                views: 756,
                likes: 45,
                status: 'published',
                created_at: new Date(Date.now() - 172800000).toISOString(),
                updated_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockKnowledge;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (search) {
            filteredData = filteredData.filter(item =>
                item.title.includes(search) || item.content.includes(search)
            );
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Knowledge API error:', error);
        res.status(500).json({
            success: false,
            message: '获取知识库内容失败'
        });
    }
});

// Announcements API endpoint
router.get('/announcements', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const status = req.query.status || 'published';
        const type = req.query.type;

        // Mock announcements data
        const mockAnnouncements = [
            {
                id: 1,
                title: '系统维护通知',
                content: '系统将于本周六凌晨2:00-4:00进行维护升级，期间可能影响正常使用...',
                type: 'maintenance',
                status: 'published',
                priority: 'high',
                author: '系统管理员',
                publish_date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '新功能上线公告',
                content: '我们很高兴地宣布，智能数据分析功能已正式上线...',
                type: 'feature',
                status: 'published',
                priority: 'medium',
                author: '产品团队',
                publish_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 86400000).toISOString(),
                updated_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                title: '春季养殖注意事项',
                content: '春季是鹅群繁殖的重要季节，请注意以下几个方面...',
                type: 'notice',
                status: 'published',
                priority: 'low',
                author: '养殖顾问',
                publish_date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 172800000).toISOString(),
                updated_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockAnnouncements;
        if (status) {
            filteredData = filteredData.filter(item => item.status === status);
        }
        if (type) {
            filteredData = filteredData.filter(item => item.type === type);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Announcements API error:', error);
        res.status(500).json({
            success: false,
            message: '获取公告列表失败'
        });
    }
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

module.exports = router;