const express = require('express');
const router = express.Router();
const db = require('../config/database');

// Dashboard API routes
router.get('/dashboard/stats', async (req, res) => {
    try {
        // Get dashboard statistics (same logic as dashboard route)
        const totalUsers = await db.count('users');
        
        const activeUsersResult = await db.execute(`
            SELECT COUNT(*) as count FROM users 
            WHERE last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
            AND status = 'active'
        `);
        const activeUsers = activeUsersResult[0].count || 0;
        
        const totalFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks');
        const totalFlocks = totalFlocksResult[0].count || 0;
        
        const activeFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks WHERE status = "active"');
        const activeFlocks = activeFlocksResult[0].count || 0;
        
        const totalGeeseResult = await db.execute('SELECT SUM(currentCount) as total FROM flocks WHERE status = "active"');
        const totalGeese = totalGeeseResult[0].total || 0;
        
        // Use default values for missing tables
        const todayEggs = 0; // production_records table doesn't exist
        const monthlyRevenue = 0; // financial_records table doesn't exist  
        const monthlyExpenses = 0; // financial_records table doesn't exist
        
        const stats = {
            totalUsers,
            activeUsers,
            totalFlocks,
            activeFlocks,
            totalGeese,
            todayEggs,
            monthlyRevenue,
            monthlyExpenses,
            monthlyProfit: monthlyRevenue - monthlyExpenses
        };
        
        res.json({
            success: true,
            data: stats
        });
        
    } catch (error) {
        console.error('Dashboard stats API error:', error);
        res.status(500).json({
            success: false,
            message: '获取统计数据失败'
        });
    }
});

// Users API routes
router.get('/users/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';
        
        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt 
            FROM users ${finalWhere} 
            ORDER BY created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Users list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});

// Create user
router.post('/users/create', async (req, res) => {
    try {
        const { username, email, password, name, role = 'user', farmName } = req.body;
        
        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名、邮箱和密码为必填项'
            });
        }
        
        // Check if username or email already exists
        const existingUser = await db.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );
        
        if (existingUser.length > 0) {
            return res.status(400).json({
                success: false,
                message: '用户名或邮箱已存在'
            });
        }
        
        // Hash password
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash(password, 12);
        
        // Insert user
        const result = await db.execute(
            'INSERT INTO users (username, email, password_hash, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)',
            [username, email, hashedPassword, name, role, 'active']
        );
        
        res.json({
            success: true,
            message: '用户创建成功',
            data: { id: result.insertId }
        });
        
    } catch (error) {
        console.error('Create user API error:', error);
        res.status(500).json({
            success: false,
            message: '创建用户失败'
        });
    }
});

// Update user
router.put('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const { name, email, role, status, farmName } = req.body;
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Update user
        const updateFields = [];
        const updateValues = [];
        
        if (name !== undefined) {
            updateFields.push('full_name = ?');
            updateValues.push(name);
        }
        if (email !== undefined) {
            updateFields.push('email = ?');
            updateValues.push(email);
        }
        if (role !== undefined) {
            updateFields.push('role = ?');
            updateValues.push(role);
        }
        if (status !== undefined) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        
        if (updateFields.length > 0) {
            updateValues.push(userId);
            await db.execute(
                `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
                updateValues
            );
        }
        
        res.json({
            success: true,
            message: '用户更新成功'
        });
        
    } catch (error) {
        console.error('Update user API error:', error);
        res.status(500).json({
            success: false,
            message: '更新用户失败'
        });
    }
});

// Delete user
router.delete('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Don't allow deleting admin users
        if (user.role === 'admin') {
            return res.status(400).json({
                success: false,
                message: '不能删除管理员用户'
            });
        }
        
        // Delete user (cascading deletes will handle related records)
        await db.execute('DELETE FROM users WHERE id = ?', [userId]);
        
        res.json({
            success: true,
            message: '用户删除成功'
        });
        
    } catch (error) {
        console.error('Delete user API error:', error);
        res.status(500).json({
            success: false,
            message: '删除用户失败'
        });
    }
});

// Flocks API routes
router.get('/flocks/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const userId = req.query.userId;
        const status = req.query.status || '';
        
        let conditions = {};
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR f.breed LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `f.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `
            SELECT COUNT(*) as total 
            FROM flocks f 
            ${finalWhere}
        `;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT f.*, t.company_name as tenant_name
            FROM flocks f 
            LEFT JOIN tenants t ON f.tenant_id = t.id 
            ${finalWhere}
            ORDER BY f.created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const flocks = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                flocks,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Flocks list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅群列表失败'
        });
    }
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

module.exports = router;