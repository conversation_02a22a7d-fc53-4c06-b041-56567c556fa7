const express = require('express');
const router = express.Router();
const db = require('../config/database');

// API管理主页
router.get('/', async (req, res) => {
    try {
        // 获取API统计数据
        const stats = {
            totalApis: 45,
            activeApis: 42,
            deprecatedApis: 3,
            totalCalls: 125680,
            todayCalls: 3250,
            avgResponseTime: 120,
            errorRate: 0.02
        };
        
        // 获取API列表
        const apis = [
            {
                id: 1,
                name: '用户认证API',
                endpoint: '/api/v1/auth/login',
                method: 'POST',
                version: 'v1',
                status: 'active',
                calls_today: 450,
                avg_response_time: 85,
                error_rate: 0.01,
                last_called: new Date()
            },
            {
                id: 2,
                name: '鹅群列表API',
                endpoint: '/api/v1/flocks',
                method: 'GET',
                version: 'v1',
                status: 'active',
                calls_today: 320,
                avg_response_time: 120,
                error_rate: 0.005,
                last_called: new Date()
            },
            {
                id: 3,
                name: '价格数据API',
                endpoint: '/api/v1/prices',
                method: 'GET',
                version: 'v1',
                status: 'active',
                calls_today: 280,
                avg_response_time: 95,
                error_rate: 0.02,
                last_called: new Date()
            }
        ];
        
        res.render('api-management/index', {
            title: 'API管理',
            stats: stats,
            apis: apis
        });
        
    } catch (error) {
        console.error('获取API管理数据失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取API管理数据失败',
            error: error
        });
    }
});

// API文档页面
router.get('/docs', (req, res) => {
    const apiGroups = [
        {
            name: '认证相关',
            apis: [
                { name: '用户登录', method: 'POST', endpoint: '/api/v1/auth/login' },
                { name: '用户登出', method: 'POST', endpoint: '/api/v1/auth/logout' },
                { name: '刷新Token', method: 'POST', endpoint: '/api/v1/auth/refresh' }
            ]
        },
        {
            name: '鹅群管理',
            apis: [
                { name: '获取鹅群列表', method: 'GET', endpoint: '/api/v1/flocks' },
                { name: '创建鹅群', method: 'POST', endpoint: '/api/v1/flocks' },
                { name: '更新鹅群', method: 'PUT', endpoint: '/api/v1/flocks/:id' },
                { name: '删除鹅群', method: 'DELETE', endpoint: '/api/v1/flocks/:id' }
            ]
        },
        {
            name: '价格数据',
            apis: [
                { name: '获取价格列表', method: 'GET', endpoint: '/api/v1/prices' },
                { name: '获取价格趋势', method: 'GET', endpoint: '/api/v1/prices/trends' },
                { name: '获取价格统计', method: 'GET', endpoint: '/api/v1/prices/stats' }
            ]
        }
    ];
    
    res.render('api-management/docs', {
        title: 'API文档',
        apiGroups: apiGroups
    });
});

// API密钥管理页面
router.get('/keys', async (req, res) => {
    try {
        // 获取API密钥列表
        const apiKeys = [
            {
                id: 1,
                name: '移动端应用',
                key: 'ak_live_***************',
                status: 'active',
                permissions: ['read', 'write'],
                calls_today: 1250,
                calls_limit: 10000,
                created_at: new Date('2024-01-15'),
                last_used: new Date()
            },
            {
                id: 2,
                name: '第三方集成',
                key: 'ak_live_***************',
                status: 'active',
                permissions: ['read'],
                calls_today: 450,
                calls_limit: 5000,
                created_at: new Date('2024-02-01'),
                last_used: new Date()
            }
        ];
        
        res.render('api-management/keys', {
            title: 'API密钥管理',
            apiKeys: apiKeys
        });
        
    } catch (error) {
        console.error('获取API密钥失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取API密钥失败',
            error: error
        });
    }
});

// 创建API密钥
router.post('/keys/create', async (req, res) => {
    try {
        const { name, permissions, calls_limit } = req.body;
        
        // 生成API密钥
        const apiKey = 'ak_live_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        
        const keyData = {
            name,
            key: apiKey,
            permissions: JSON.stringify(permissions || ['read']),
            calls_limit: calls_limit || 1000,
            status: 'active',
            created_at: new Date(),
            created_by: req.session.user?.id || 1
        };
        
        // 这里应该保存到数据库
        // await db.insert('api_keys', keyData);
        
        res.json({
            success: true,
            message: 'API密钥创建成功',
            data: {
                key: apiKey,
                name: name
            }
        });
        
    } catch (error) {
        console.error('创建API密钥失败:', error);
        res.status(500).json({
            success: false,
            message: '创建API密钥失败',
            error: error.message
        });
    }
});

// 撤销API密钥
router.post('/keys/:id/revoke', async (req, res) => {
    try {
        const keyId = req.params.id;
        
        // 这里应该更新数据库
        // await db.update('api_keys', { status: 'revoked' }, { id: keyId });
        
        res.json({
            success: true,
            message: 'API密钥已撤销'
        });
        
    } catch (error) {
        console.error('撤销API密钥失败:', error);
        res.status(500).json({
            success: false,
            message: '撤销API密钥失败',
            error: error.message
        });
    }
});

// API调用统计页面
router.get('/stats', async (req, res) => {
    try {
        const stats = {
            totalCalls: 125680,
            todayCalls: 3250,
            successRate: 99.8,
            avgResponseTime: 120,
            topApis: [
                { endpoint: '/api/v1/auth/login', calls: 15680 },
                { endpoint: '/api/v1/flocks', calls: 12450 },
                { endpoint: '/api/v1/prices', calls: 9870 }
            ],
            callsByHour: [
                { hour: '00:00', calls: 45 },
                { hour: '01:00', calls: 32 },
                { hour: '02:00', calls: 28 },
                { hour: '03:00', calls: 35 },
                { hour: '04:00', calls: 42 }
            ]
        };
        
        res.render('api-management/stats', {
            title: 'API调用统计',
            stats: stats
        });
        
    } catch (error) {
        console.error('获取API统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取API统计失败',
            error: error
        });
    }
});

// API监控页面
router.get('/monitoring', async (req, res) => {
    try {
        const monitoring = {
            systemStatus: 'healthy',
            uptime: '99.9%',
            responseTime: {
                current: 120,
                avg24h: 115,
                p95: 180,
                p99: 250
            },
            errorRates: {
                current: 0.02,
                avg24h: 0.015,
                threshold: 0.05
            },
            alerts: [
                {
                    id: 1,
                    type: 'warning',
                    message: 'API响应时间略高于平均值',
                    timestamp: new Date(),
                    resolved: false
                }
            ]
        };
        
        res.render('api-management/monitoring', {
            title: 'API监控',
            monitoring: monitoring
        });
        
    } catch (error) {
        console.error('获取API监控数据失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取API监控数据失败',
            error: error
        });
    }
});

// API版本管理页面
router.get('/versions', (req, res) => {
    const versions = [
        {
            version: 'v2',
            status: 'development',
            release_date: null,
            features: ['新增批量操作', '优化响应速度', '增强安全性'],
            breaking_changes: ['移除废弃的字段', '修改认证方式']
        },
        {
            version: 'v1',
            status: 'stable',
            release_date: new Date('2024-01-01'),
            features: ['基础CRUD操作', '用户认证', '数据统计'],
            breaking_changes: []
        }
    ];
    
    res.render('api-management/versions', {
        title: 'API版本管理',
        versions: versions
    });
});

module.exports = router;
