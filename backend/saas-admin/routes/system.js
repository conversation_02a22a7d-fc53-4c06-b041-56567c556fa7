const express = require('express');
const router = express.Router();
const db = require('../config/database');

// 系统设置主页
router.get('/', async (req, res) => {
    try {
        // 获取系统配置
        const systemConfig = {
            siteName: '智慧养鹅SaaS平台',
            siteDescription: '专业的养鹅管理SaaS解决方案',
            adminEmail: '<EMAIL>',
            timezone: 'Asia/Shanghai',
            language: 'zh-CN',
            maintenanceMode: false,
            registrationEnabled: true,
            emailVerificationRequired: true,
            maxFileUploadSize: 10, // MB
            sessionTimeout: 30, // minutes
            backupFrequency: 'daily',
            logLevel: 'info'
        };

        res.render('system/index', {
            title: '系统设置',
            systemConfig: systemConfig
        });

    } catch (error) {
        console.error('获取系统设置失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取系统设置失败',
            error: error
        });
    }
});

// 更新系统设置
router.post('/update', async (req, res) => {
    try {
        const {
            siteName,
            siteDescription,
            adminEmail,
            timezone,
            language,
            maintenanceMode,
            registrationEnabled,
            emailVerificationRequired,
            maxFileUploadSize,
            sessionTimeout,
            backupFrequency,
            logLevel
        } = req.body;

        const configData = {
            siteName,
            siteDescription,
            adminEmail,
            timezone,
            language,
            maintenanceMode: maintenanceMode === 'on',
            registrationEnabled: registrationEnabled === 'on',
            emailVerificationRequired: emailVerificationRequired === 'on',
            maxFileUploadSize: parseInt(maxFileUploadSize),
            sessionTimeout: parseInt(sessionTimeout),
            backupFrequency,
            logLevel,
            updated_at: new Date(),
            updated_by: req.session.user?.id || 1
        };

        // 这里应该保存到数据库或配置文件
        // await db.update('system_config', configData, { id: 1 });

        res.redirect('/system?success=updated');

    } catch (error) {
        console.error('更新系统设置失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '更新系统设置失败',
            error: error
        });
    }
});

// 系统日志页面
router.get('/logs', async (req, res) => {
    try {
        const { level = 'all', date = '', limit = 100 } = req.query;

        // 获取系统日志
        const logs = [
            {
                id: 1,
                level: 'info',
                message: '用户登录成功',
                details: { userId: 1, ip: '*************' },
                timestamp: new Date(),
                source: 'auth'
            },
            {
                id: 2,
                level: 'warning',
                message: 'API调用频率过高',
                details: { endpoint: '/api/v1/flocks', count: 1000 },
                timestamp: new Date(),
                source: 'api'
            },
            {
                id: 3,
                level: 'error',
                message: '数据库连接失败',
                details: { error: 'Connection timeout' },
                timestamp: new Date(),
                source: 'database'
            }
        ];

        res.render('system/logs', {
            title: '系统日志',
            logs: logs,
            currentFilters: { level, date, limit }
        });

    } catch (error) {
        console.error('获取系统日志失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取系统日志失败',
            error: error
        });
    }
});

// 系统监控页面
router.get('/monitoring', async (req, res) => {
    try {
        const monitoring = {
            systemStatus: 'healthy',
            uptime: '15天 8小时 32分钟',
            cpuUsage: 45.2,
            memoryUsage: 68.5,
            diskUsage: 32.8,
            networkIO: {
                inbound: 125.6,
                outbound: 89.3
            },
            databaseStatus: 'connected',
            redisStatus: 'connected',
            queueStatus: 'running',
            services: [
                { name: 'Web服务器', status: 'running', port: 3000 },
                { name: '数据库', status: 'running', port: 3306 },
                { name: 'Redis缓存', status: 'running', port: 6379 },
                { name: '消息队列', status: 'running', port: 5672 }
            ]
        };

        res.render('system/monitoring', {
            title: '系统监控',
            monitoring: monitoring
        });

    } catch (error) {
        console.error('获取系统监控数据失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取系统监控数据失败',
            error: error
        });
    }
});

// 数据备份页面
router.get('/backup', async (req, res) => {
    try {
        const backups = [
            {
                id: 1,
                filename: 'backup_20241201_020000.sql',
                size: '125.6 MB',
                type: 'auto',
                status: 'completed',
                created_at: new Date('2024-12-01 02:00:00')
            },
            {
                id: 2,
                filename: 'backup_20241130_020000.sql',
                size: '123.2 MB',
                type: 'auto',
                status: 'completed',
                created_at: new Date('2024-11-30 02:00:00')
            },
            {
                id: 3,
                filename: 'manual_backup_20241129_150000.sql',
                size: '122.8 MB',
                type: 'manual',
                status: 'completed',
                created_at: new Date('2024-11-29 15:00:00')
            }
        ];

        const backupConfig = {
            autoBackup: true,
            frequency: 'daily',
            time: '02:00',
            retention: 30, // days
            compression: true
        };

        res.render('system/backup', {
            title: '数据备份',
            backups: backups,
            backupConfig: backupConfig
        });

    } catch (error) {
        console.error('获取备份列表失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取备份列表失败',
            error: error
        });
    }
});

module.exports = router;
