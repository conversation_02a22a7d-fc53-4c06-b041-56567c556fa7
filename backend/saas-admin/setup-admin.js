#!/usr/bin/env node

const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function setupAdmin() {
    let connection;
    
    try {
        console.log('🔧 设置管理员账户...');
        
        // 数据库配置
        const dbConfig = {
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'goose_saas_platform',
            charset: 'utf8mb4'
        };
        
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 创建users表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                role VARCHAR(20) DEFAULT 'admin',
                status VARCHAR(20) DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ users表创建成功');
        
        // 生成密码哈希
        const passwordHash = await bcrypt.hash('admin123', 10);
        
        // 插入管理员账户
        await connection.execute(`
            INSERT IGNORE INTO users (username, email, password_hash, full_name, role, status)
            VALUES (?, ?, ?, ?, ?, ?)
        `, ['admin', '<EMAIL>', passwordHash, '系统管理员', 'admin', 'active']);
        
        console.log('✅ 管理员账户创建成功');
        console.log('📋 登录信息:');
        console.log('   用户名: admin');
        console.log('   密码: admin123');
        console.log('   邮箱: <EMAIL>');
        
        // 验证账户
        const [users] = await connection.execute(
            'SELECT username, email, full_name, role, status FROM users WHERE username = ?',
            ['admin']
        );
        
        if (users.length > 0) {
            console.log('✅ 账户验证成功:', users[0]);
        }
        
    } catch (error) {
        console.error('❌ 设置失败:', error.message);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 运行设置
if (require.main === module) {
    setupAdmin().then(() => {
        console.log('🏁 管理员账户设置完成');
        process.exit(0);
    }).catch(error => {
        console.error('设置过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = { setupAdmin };
