-- 创建缺失的数据库表结构
USE goose_saas_platform;

-- 创建flocks表（鹅群管理）
CREATE TABLE IF NOT EXISTS flocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT,
    flock_name VARCHAR(100) NOT NULL,
    breed VARCHAR(50),
    currentCount INT DEFAULT 0,
    maxCapacity INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- 创建mall_orders表（商城订单）
CREATE TABLE IF NOT EXISTS mall_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT,
    user_id INT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) DEFAULT 0,
    order_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建mall_products表（商城产品）
CREATE TABLE IF NOT EXISTS mall_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0,
    stock_qty INT DEFAULT 0,
    category_id INT,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建mall_categories表（商城分类）
CREATE TABLE IF NOT EXISTS mall_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建goose_prices表（鹅价管理）
CREATE TABLE IF NOT EXISTS goose_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    region VARCHAR(100) NOT NULL,
    breed VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) DEFAULT 'per_kg',
    market_name VARCHAR(100),
    date DATE NOT NULL,
    is_published BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建announcements表（公告管理）
CREATE TABLE IF NOT EXISTS announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    type VARCHAR(50) DEFAULT 'info',
    status VARCHAR(20) DEFAULT 'published',
    priority INT DEFAULT 0,
    is_top BOOLEAN DEFAULT FALSE,
    target_audience VARCHAR(50) DEFAULT 'all',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建knowledge_articles表（知识库）
CREATE TABLE IF NOT EXISTS knowledge_articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content LONGTEXT,
    category VARCHAR(100),
    tags VARCHAR(255),
    status VARCHAR(20) DEFAULT 'published',
    views INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入一些测试数据
INSERT IGNORE INTO flocks (tenant_id, flock_name, breed, currentCount, maxCapacity) VALUES
(1, '白鹅群1号', '白鹅', 150, 200),
(1, '灰鹅群A组', '灰鹅', 80, 100);

INSERT IGNORE INTO mall_products (name, description, price, stock_qty, category_id) VALUES
('优质鹅蛋', '新鲜农家鹅蛋', 3.50, 1000, 1),
('有机鹅肉', '散养有机鹅肉', 35.00, 50, 1),
('鹅绒被', '天然鹅绒保暖被', 299.00, 20, 2);

INSERT IGNORE INTO mall_categories (name, description) VALUES
('农产品', '各类农产品和生鲜食品'),
('生活用品', '日常生活用品和家居用品');

INSERT IGNORE INTO mall_orders (tenant_id, user_id, order_number, total_amount, order_status) VALUES
(1, 1, 'ORD20250826001', 108.50, 'pending'),
(1, 1, 'ORD20250826002', 299.00, 'completed');

INSERT IGNORE INTO goose_prices (region, breed, price, market_name, date) VALUES
('北京', '白鹅', 28.50, '新发地农产品市场', CURDATE()),
('上海', '白鹅', 30.00, '江阳水产市场', CURDATE()),
('广州', '灰鹅', 32.00, '黄沙水产市场', CURDATE()),
('深圳', '白鹅', 29.50, '海吉星农产品市场', CURDATE());

INSERT IGNORE INTO announcements (title, content, type, status, priority, created_by) VALUES
('系统维护通知', '系统将于今晚22:00-23:00进行维护升级，请合理安排使用时间。', 'info', 'published', 1, 1),
('新功能上线', '鹅价实时监控功能已上线，欢迎体验！', 'feature', 'published', 2, 1),
('重要提醒', '请及时更新您的联系方式，以便接收重要通知。', 'warning', 'published', 3, 1);

INSERT IGNORE INTO knowledge_articles (title, content, category, tags, created_by) VALUES
('鹅类养殖基础知识', '本文介绍鹅类养殖的基本知识和技巧...', '养殖技术', '养殖,基础,鹅类', 1),
('饲料配比指南', '科学的饲料配比是养鹅成功的关键...', '饲料管理', '饲料,配比,营养', 1),
('疾病预防手册', '常见鹅类疾病的预防和处理方法...', '健康管理', '疾病,预防,健康', 1);

-- 添加一些测试租户数据
INSERT IGNORE INTO tenants (id, company_name, contact_name, contact_email, contact_phone) VALUES
(1, '智慧农场', '张经理', '<EMAIL>', '13800138001');

-- 确保用户表中的admin用户有tenant_id
UPDATE users SET tenant_id = 1 WHERE username = 'admin' AND tenant_id IS NULL;