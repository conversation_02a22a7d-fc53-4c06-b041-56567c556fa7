/* Custom CSS for SAAS Admin */

:root {
  --primary-color: #2563eb;
  --secondary-color: #1d4ed8;
  --accent-color: #3b82f6;
  --light-blue: #dbeafe;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #06b6d4;
  --dark-color: #1f2937;
  --light-color: #f8fafc;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-content p {
  margin-top: 1rem;
  font-size: 1.1rem;
}

/* Custom Card Styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 10px 10px 0 0 !important;
  border-bottom: none;
}

/* Statistics Cards */
.stat-card {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
}

.stat-card.success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-card.danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.stat-card.warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: #333;
}

.stat-card.info {
  background: linear-gradient(135deg, var(--info-color), var(--accent-color));
}

.stat-card h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.stat-card p {
  margin: 0.5rem 0 0;
  opacity: 0.9;
}

.stat-card i {
  font-size: 3rem;
  opacity: 0.3;
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Button Styles */
.btn {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(245, 158, 11, 0.4);
}

/* Form Styles */
.form-control {
  border-radius: 8px;
  border: 2px solid #e1e5e9;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-select {
  border-radius: 8px;
  border: 2px solid #e1e5e9;
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Table Styles */
.table {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background-color: rgba(37, 99, 235, 0.08);
}

.table tbody td {
  vertical-align: middle;
  padding: 1rem 0.75rem;
}

/* Modal Styles */
.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 15px 15px 0 0;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

/* Badge Styles */
.badge {
  border-radius: 20px;
  padding: 0.5em 0.8em;
  font-weight: 600;
}

/* Alert Styles */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Sidebar Customizations */
.main-sidebar {
  background: linear-gradient(180deg, #1e40af 0%, #1d4ed8 50%, #1e3a8a 100%) !important;
}

.brand-link {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.nav-sidebar .nav-link {
  border-radius: 8px;
  margin: 0.2rem 0.5rem;
  transition: all 0.3s ease;
}

.nav-sidebar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(5px);
}

.nav-sidebar .nav-link.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  color: white !important;
}

/* Navbar Customizations */
.main-header {
  border-bottom: none !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Content Wrapper */
.content-wrapper {
  background: #f8f9fa;
}

/* Pagination */
.pagination .page-link {
  border-radius: 8px;
  margin: 0 0.2rem;
  border: 2px solid #e1e5e9;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

/* Charts Container */
.chart-container {
  position: relative;
  height: 400px;
  background: white;
  border-radius: 15px;
  padding: 1rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Search Box */
.search-box {
  position: relative;
}

.search-box .form-control {
  padding-left: 2.5rem;
}

.search-box .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  z-index: 5;
}

/* Status Badges */
.status-active {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.status-inactive {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.status-warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #333;
}

.status-danger {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
  color: white;
}

/* Info Box 组件优化 */
.info-box {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  min-height: 80px;
}

.info-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.info-box-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 10px;
  color: white;
  font-size: 1.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.info-box-content {
  flex: 1;
  min-width: 0;
  /* 防止内容溢出 */
}

.info-box-text {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-box-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.progress {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-bar {
  height: 100%;
  transition: width 0.6s ease;
}

.progress-description {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
}

/* 系统状态监控特殊样式 */
.system-monitoring .info-box {
  border-left: 4px solid transparent;
}

.system-monitoring .info-box.status-healthy {
  border-left-color: #10b981;
}

.system-monitoring .info-box.status-warning {
  border-left-color: #f59e0b;
}

.system-monitoring .info-box.status-danger {
  border-left-color: #ef4444;
}

.system-monitoring .info-box.status-info {
  border-left-color: #06b6d4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stat-card h3 {
    font-size: 2rem;
  }

  .stat-card i {
    font-size: 2rem;
  }

  .table-responsive {
    border-radius: 10px;
  }

  /* Info Box 响应式优化 */
  .info-box {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
    min-height: auto;
  }

  .info-box-icon {
    margin-right: 0;
    margin-bottom: 1rem;
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .info-box-content {
    width: 100%;
  }

  .info-box-text {
    white-space: normal;
    text-align: center;
  }

  .info-box-number {
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  .info-box-number {
    font-size: 1.1rem;
  }

  .info-box-text {
    font-size: 0.8rem;
  }

  .info-box-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

/* 业务数据统计区域优化 */
.business-stats .info-box {
  margin-bottom: 1.5rem;
}

/* 用户活跃度卡片优化 */
.user-activity-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.user-activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.activity-chart-container {
  position: relative;
  height: 300px;
  padding: 1rem;
}

/* 数据加载状态 */
.data-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  color: #6c757d;
}

.data-loading .spinner-border {
  width: 2rem;
  height: 2rem;
  margin-right: 0.5rem;
}

/* 数据为空状态 */
.data-empty {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.data-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* 错误状态样式 */
.data-error {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
}

.data-error i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* 刷新按钮动画 */
.btn-refresh {
  transition: transform 0.3s ease;
}

.btn-refresh:hover {
  transform: rotate(180deg);
}

.btn-refresh.refreshing {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}