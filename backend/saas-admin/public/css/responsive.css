/* 响应式设计优化 - 智慧养鹅管理系统 */

/* 基础响应式设置 */
* {
    box-sizing: border-box;
}

/* 移动端优先的媒体查询 */

/* 超小屏幕 (手机, 小于576px) */
@media (max-width: 575.98px) {
    /* 容器调整 */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* 卡片间距调整 */
    .card {
        margin-bottom: 15px;
        border-radius: 8px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    /* 按钮组响应式 */
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
        border-radius: 4px !important;
    }
    
    /* 表格响应式 */
    .table-responsive {
        border: none;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 8px 4px;
        vertical-align: middle;
    }
    
    /* 隐藏不重要的列 */
    .table .d-none-mobile {
        display: none !important;
    }
    
    /* 统计卡片调整 */
    .small-box {
        margin-bottom: 15px;
    }
    
    .small-box .inner h3 {
        font-size: 24px;
    }
    
    /* 导航栏调整 */
    .navbar-brand {
        font-size: 16px;
    }
    
    /* 侧边栏调整 */
    .sidebar {
        width: 100%;
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    /* 主内容区调整 */
    .main-content {
        margin-left: 0;
        padding-top: 70px;
    }
    
    /* 模态框调整 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    /* 表单调整 */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    /* 图表容器调整 */
    .chart-area,
    .chart-pie {
        height: 250px !important;
    }
}

/* 小屏幕 (平板, 576px 到 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    /* 统计卡片2列布局 */
    .stats-row .col-xl-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    /* 表格字体稍大 */
    .table {
        font-size: 13px;
    }
    
    /* 按钮组可以横向显示 */
    .btn-group {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }
    
    /* 图表容器调整 */
    .chart-area,
    .chart-pie {
        height: 300px !important;
    }
}

/* 中等屏幕 (平板, 768px 到 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* 侧边栏可以显示 */
    .sidebar {
        width: 200px;
        position: fixed;
        left: 0;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    /* 统计卡片可以4列显示 */
    .stats-row .col-xl-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    
    /* 图表容器正常大小 */
    .chart-area {
        height: 320px !important;
    }
    
    .chart-pie {
        height: 280px !important;
    }
}

/* 大屏幕 (桌面, 992px 到 1199.98px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .sidebar {
        width: 250px;
    }
    
    .main-content {
        margin-left: 250px;
    }
    
    /* 图表容器标准大小 */
    .chart-area {
        height: 350px !important;
    }
    
    .chart-pie {
        height: 300px !important;
    }
}

/* 超大屏幕 (大桌面, 1200px 及以上) */
@media (min-width: 1200px) {
    .sidebar {
        width: 280px;
    }
    
    .main-content {
        margin-left: 280px;
    }
    
    /* 图表容器大尺寸 */
    .chart-area {
        height: 400px !important;
    }
    
    .chart-pie {
        height: 350px !important;
    }
}

/* 通用响应式工具类 */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

@media (max-width: 767.98px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .mobile-hide {
        display: none !important;
    }
}

/* 响应式文本大小 */
@media (max-width: 575.98px) {
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.3rem; }
    h3 { font-size: 1.1rem; }
    h4 { font-size: 1rem; }
    h5 { font-size: 0.9rem; }
    h6 { font-size: 0.8rem; }
}

/* 响应式间距 */
@media (max-width: 575.98px) {
    .mb-4 { margin-bottom: 1rem !important; }
    .mb-3 { margin-bottom: 0.75rem !important; }
    .p-4 { padding: 1rem !important; }
    .p-3 { padding: 0.75rem !important; }
}

/* 响应式表格优化 */
@media (max-width: 767.98px) {
    .table-responsive-stack {
        display: block;
    }
    
    .table-responsive-stack thead {
        display: none;
    }
    
    .table-responsive-stack tbody,
    .table-responsive-stack tr,
    .table-responsive-stack td {
        display: block;
        width: 100%;
    }
    
    .table-responsive-stack tr {
        border: 1px solid #dee2e6;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 5px;
    }
    
    .table-responsive-stack td {
        border: none;
        padding: 5px 0;
        text-align: left;
    }
    
    .table-responsive-stack td:before {
        content: attr(data-label) ": ";
        font-weight: bold;
        display: inline-block;
        width: 100px;
    }
}

/* 响应式导航 */
.mobile-nav-toggle {
    display: none;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1051;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    font-size: 18px;
}

@media (max-width: 767.98px) {
    .mobile-nav-toggle {
        display: block;
    }
}

/* 响应式图片 */
.img-responsive {
    max-width: 100%;
    height: auto;
}

/* 响应式视频 */
.video-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    overflow: hidden;
}

.video-responsive iframe,
.video-responsive object,
.video-responsive embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
    
    .btn {
        display: none !important;
    }
}
