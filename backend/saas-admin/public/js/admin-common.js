// Common JavaScript utilities for SAAS Admin
class AdminUtils {
    constructor() {
        this.toastElement = document.getElementById('mainToast');
        this.toastInstance = this.toastElement ? new bootstrap.Toast(this.toastElement) : null;
        this.loadingOverlay = document.getElementById('loadingOverlay');
        
        // Initialize axios defaults
        axios.defaults.timeout = 30000;
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        
        // Setup axios interceptors
        this.setupAxiosInterceptors();
    }
    
    // Setup axios request/response interceptors
    setupAxiosInterceptors() {
        // Request interceptor
        axios.interceptors.request.use(
            config => {
                console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            error => {
                console.error('[API Request Error]', error);
                return Promise.reject(error);
            }
        );
        
        // Response interceptor
        axios.interceptors.response.use(
            response => {
                console.log(`[API Response] ${response.status} ${response.config.url}`);
                return response;
            },
            error => {
                console.error('[API Response Error]', error);
                
                if (error.response) {
                    // Handle authentication errors
                    if (error.response.status === 401) {
                        this.showToast('登录已过期，请重新登录', 'danger');
                        setTimeout(() => {
                            window.location.href = '/auth/login';
                        }, 2000);
                        return Promise.reject(error);
                    }
                    
                    // Handle other HTTP errors
                    const message = error.response.data?.message || `请求失败 (${error.response.status})`;
                    this.showToast(message, 'danger');
                } else if (error.request) {
                    this.showToast('网络错误，请检查连接', 'danger');
                } else {
                    this.showToast('请求配置错误', 'danger');
                }
                
                return Promise.reject(error);
            }
        );
    }
    
    // Show toast notification
    showToast(message, type = 'info', duration = 5000) {
        if (!this.toastInstance) return;
        
        const toastMessage = document.getElementById('toastMessage');
        const toast = document.getElementById('mainToast');
        
        // Set message and style
        toastMessage.innerHTML = message;
        
        // Remove existing classes and add new one
        toast.className = 'toast align-items-center border-0';
        switch (type) {
            case 'success':
                toast.classList.add('text-bg-success');
                break;
            case 'danger':
            case 'error':
                toast.classList.add('text-bg-danger');
                break;
            case 'warning':
                toast.classList.add('text-bg-warning');
                break;
            default:
                toast.classList.add('text-bg-info');
        }
        
        // Show toast
        this.toastInstance.show();
        
        // Auto hide after duration
        if (duration > 0) {
            setTimeout(() => {
                this.toastInstance.hide();
            }, duration);
        }
    }
    
    // Show/hide loading overlay
    showLoading(message = '处理中...') {
        if (this.loadingOverlay) {
            const loadingText = this.loadingOverlay.querySelector('p');
            if (loadingText) loadingText.textContent = message;
            this.loadingOverlay.style.display = 'flex';
        }
    }
    
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }
    
    // Confirm dialog
    confirm(message, callback, title = '确认操作') {
        if (window.confirm(`${title}\n\n${message}`)) {
            if (typeof callback === 'function') {
                callback();
            }
        }
    }
    
    // Format date
    formatDate(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
        const date = new Date(dateString);
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    
    // Format number with thousands separator
    formatNumber(num, decimals = 0) {
        if (isNaN(num)) return '0';
        return Number(num).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }
    
    // Format currency
    formatCurrency(amount) {
        return '¥' + this.formatNumber(amount, 2);
    }
    
    // Debounce function
    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }
    
    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    // Validate form
    validateForm(formElement) {
        if (!formElement) return false;
        
        const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
    
    // Get form data as object
    getFormData(formElement) {
        const formData = new FormData(formElement);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            // Handle multiple values for same key (checkboxes)
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }
    
    // Set form data from object
    setFormData(formElement, data) {
        for (let key in data) {
            const input = formElement.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = data[key];
                } else {
                    input.value = data[key];
                }
            }
        }
    }
    
    // Reset form
    resetForm(formElement) {
        if (formElement) {
            formElement.reset();
            formElement.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
        }
    }
}

// Create global instance
const Utils = new AdminUtils();

// Logout function
async function logout() {
    try {
        Utils.showLoading('正在退出...');
        const response = await axios.post('/auth/logout');
        
        if (response.data.success) {
            Utils.showToast('已成功退出登录', 'success');
            setTimeout(() => {
                window.location.href = '/auth/login';
            }, 1000);
        }
    } catch (error) {
        console.error('Logout error:', error);
        // Force redirect even if request fails
        window.location.href = '/auth/login';
    } finally {
        Utils.hideLoading();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin utilities initialized');
    
    // Active menu highlighting
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.nav-sidebar .nav-link');
    
    sidebarLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
            
            // If it's a submenu item, open parent menu
            const parentItem = link.closest('.nav-treeview');
            if (parentItem) {
                const parentLink = parentItem.previousElementSibling;
                if (parentLink) {
                    parentLink.classList.add('active');
                    parentItem.style.display = 'block';
                }
            }
        }
    });
    
    // Form validation on submit
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!Utils.validateForm(this)) {
                e.preventDefault();
                Utils.showToast('请填写所有必填字段', 'warning');
            }
        });
    });
});