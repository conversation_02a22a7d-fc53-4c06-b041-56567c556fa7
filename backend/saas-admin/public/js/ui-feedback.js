/**
 * UI反馈和加载动画库
 * 智慧养鹅管理系统
 */

class UIFeedback {
    constructor() {
        this.loadingOverlay = null;
        this.toastContainer = null;
        this.progressBar = null;
        this.init();
    }

    init() {
        // 创建Toast容器
        this.createToastContainer();
        // 创建加载遮罩
        this.createLoadingOverlay();
        // 创建进度条
        this.createProgressBar();
        // 添加样式
        this.addStyles();
    }

    // 创建Toast容器
    createToastContainer() {
        this.toastContainer = document.createElement('div');
        this.toastContainer.id = 'toast-container';
        this.toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        this.toastContainer.style.zIndex = '9999';
        document.body.appendChild(this.toastContainer);
    }

    // 创建加载遮罩
    createLoadingOverlay() {
        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.id = 'loading-overlay';
        this.loadingOverlay.className = 'loading-overlay';
        this.loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;
        this.loadingOverlay.style.display = 'none';
        document.body.appendChild(this.loadingOverlay);
    }

    // 创建进度条
    createProgressBar() {
        this.progressBar = document.createElement('div');
        this.progressBar.id = 'progress-bar';
        this.progressBar.className = 'progress-bar-container';
        this.progressBar.innerHTML = `
            <div class="progress-bar-fill"></div>
            <div class="progress-bar-text">0%</div>
        `;
        this.progressBar.style.display = 'none';
        document.body.appendChild(this.progressBar);
    }

    // 添加样式
    addStyles() {
        if (document.getElementById('ui-feedback-styles')) return;

        const style = document.createElement('style');
        style.id = 'ui-feedback-styles';
        style.textContent = `
            /* 加载遮罩样式 */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }

            .loading-content {
                background: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }

            .loading-text {
                color: #333;
                font-size: 16px;
                font-weight: 500;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 进度条样式 */
            .progress-bar-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: rgba(0, 0, 0, 0.1);
                z-index: 9998;
            }

            .progress-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #28a745);
                width: 0%;
                transition: width 0.3s ease;
            }

            .progress-bar-text {
                position: absolute;
                top: 10px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
            }

            /* Toast样式 */
            .custom-toast {
                min-width: 300px;
                margin-bottom: 10px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
            }

            .custom-toast.success {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
            }

            .custom-toast.error {
                background: linear-gradient(135deg, #dc3545, #e74c3c);
                color: white;
            }

            .custom-toast.warning {
                background: linear-gradient(135deg, #ffc107, #fd7e14);
                color: #212529;
            }

            .custom-toast.info {
                background: linear-gradient(135deg, #17a2b8, #007bff);
                color: white;
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            /* 按钮加载状态 */
            .btn-loading {
                position: relative;
                pointer-events: none;
            }

            .btn-loading::after {
                content: '';
                position: absolute;
                width: 16px;
                height: 16px;
                top: 50%;
                left: 50%;
                margin-left: -8px;
                margin-top: -8px;
                border: 2px solid transparent;
                border-top-color: currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            /* 脉冲动画 */
            .pulse {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.05);
                }
                100% {
                    transform: scale(1);
                }
            }

            /* 摇摆动画 */
            .shake {
                animation: shake 0.5s;
            }

            @keyframes shake {
                0%, 100% {
                    transform: translateX(0);
                }
                10%, 30%, 50%, 70%, 90% {
                    transform: translateX(-5px);
                }
                20%, 40%, 60%, 80% {
                    transform: translateX(5px);
                }
            }

            /* 淡入动画 */
            .fade-in {
                animation: fadeIn 0.5s ease-in;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 显示加载遮罩
    showLoading(text = '加载中...') {
        if (this.loadingOverlay) {
            this.loadingOverlay.querySelector('.loading-text').textContent = text;
            this.loadingOverlay.style.display = 'flex';
        }
    }

    // 隐藏加载遮罩
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    // 显示Toast消息
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `custom-toast ${type}`;
        
        const icon = this.getToastIcon(type);
        toast.innerHTML = `
            <div class="d-flex align-items-center p-3">
                <i class="${icon} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close btn-close-white ms-2" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        this.toastContainer.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);

        return toast;
    }

    // 获取Toast图标
    getToastIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 显示进度条
    showProgress(percentage = 0) {
        if (this.progressBar) {
            this.progressBar.style.display = 'block';
            this.updateProgress(percentage);
        }
    }

    // 更新进度条
    updateProgress(percentage) {
        if (this.progressBar) {
            const fill = this.progressBar.querySelector('.progress-bar-fill');
            const text = this.progressBar.querySelector('.progress-bar-text');
            
            fill.style.width = `${percentage}%`;
            text.textContent = `${Math.round(percentage)}%`;
        }
    }

    // 隐藏进度条
    hideProgress() {
        if (this.progressBar) {
            this.progressBar.style.display = 'none';
        }
    }

    // 按钮加载状态
    setButtonLoading(button, loading = true) {
        if (loading) {
            button.classList.add('btn-loading');
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = '处理中...';
        } else {
            button.classList.remove('btn-loading');
            button.disabled = false;
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
                delete button.dataset.originalText;
            }
        }
    }

    // 确认对话框
    confirm(message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">确认操作</h5>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary confirm-btn">确认</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        modal.querySelector('.confirm-btn').addEventListener('click', () => {
            bootstrapModal.hide();
            if (onConfirm) onConfirm();
        });

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
            if (onCancel) onCancel();
        });

        bootstrapModal.show();
    }

    // 添加动画类
    addAnimation(element, animationClass, duration = 1000) {
        element.classList.add(animationClass);
        setTimeout(() => {
            element.classList.remove(animationClass);
        }, duration);
    }
}

// 创建全局实例
window.UIFeedback = new UIFeedback();

// 便捷方法
window.showLoading = (text) => UIFeedback.showLoading(text);
window.hideLoading = () => UIFeedback.hideLoading();
window.showToast = (message, type, duration) => UIFeedback.showToast(message, type, duration);
window.showProgress = (percentage) => UIFeedback.showProgress(percentage);
window.updateProgress = (percentage) => UIFeedback.updateProgress(percentage);
window.hideProgress = () => UIFeedback.hideProgress();
window.setButtonLoading = (button, loading) => UIFeedback.setButtonLoading(button, loading);
window.confirmAction = (message, onConfirm, onCancel) => UIFeedback.confirm(message, onConfirm, onCancel);
window.addAnimation = (element, animationClass, duration) => UIFeedback.addAnimation(element, animationClass, duration);
