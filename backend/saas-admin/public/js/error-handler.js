/**
 * 错误处理机制
 * 智慧养鹅管理系统
 */

class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 100;
        this.init();
    }

    init() {
        // 监听全局错误
        this.setupGlobalErrorHandlers();
        // 创建错误显示容器
        this.createErrorContainer();
        // 添加样式
        this.addStyles();
    }

    // 设置全局错误处理器
    setupGlobalErrorHandlers() {
        // JavaScript运行时错误
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Promise未捕获的拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // 资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError({
                    type: 'Resource Load Error',
                    message: `Failed to load resource: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }

    // 创建错误显示容器
    createErrorContainer() {
        this.errorContainer = document.createElement('div');
        this.errorContainer.id = 'error-container';
        this.errorContainer.className = 'error-container position-fixed';
        this.errorContainer.style.display = 'none';
        document.body.appendChild(this.errorContainer);
    }

    // 添加样式
    addStyles() {
        if (document.getElementById('error-handler-styles')) return;

        const style = document.createElement('style');
        style.id = 'error-handler-styles';
        style.textContent = `
            /* 错误容器样式 */
            .error-container {
                top: 20px;
                right: 20px;
                max-width: 400px;
                z-index: 10001;
            }

            .error-alert {
                margin-bottom: 10px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .error-alert::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: currentColor;
            }

            .error-alert.error {
                background: #fff5f5;
                border: 1px solid #fed7d7;
                color: #c53030;
            }

            .error-alert.warning {
                background: #fffbeb;
                border: 1px solid #fbd38d;
                color: #d69e2e;
            }

            .error-alert.info {
                background: #ebf8ff;
                border: 1px solid #90cdf4;
                color: #3182ce;
            }

            .error-alert.network {
                background: #f0fff4;
                border: 1px solid #9ae6b4;
                color: #38a169;
            }

            .error-content {
                padding: 15px;
                padding-left: 20px;
            }

            .error-title {
                font-weight: 600;
                margin-bottom: 5px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .error-message {
                font-size: 14px;
                line-height: 1.4;
                margin-bottom: 8px;
            }

            .error-details {
                font-size: 12px;
                opacity: 0.8;
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid rgba(0, 0, 0, 0.1);
            }

            .error-actions {
                margin-top: 10px;
                display: flex;
                gap: 8px;
            }

            .error-btn {
                padding: 4px 8px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .error-btn.retry {
                background: #4299e1;
                color: white;
            }

            .error-btn.retry:hover {
                background: #3182ce;
            }

            .error-btn.report {
                background: #ed8936;
                color: white;
            }

            .error-btn.report:hover {
                background: #dd6b20;
            }

            .error-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                opacity: 0.6;
                transition: opacity 0.2s;
            }

            .error-close:hover {
                opacity: 1;
            }

            /* 网络状态指示器 */
            .network-status {
                position: fixed;
                top: 10px;
                left: 50%;
                transform: translateX(-50%);
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
                z-index: 10002;
                transition: all 0.3s ease;
            }

            .network-status.online {
                background: #48bb78;
                color: white;
            }

            .network-status.offline {
                background: #e53e3e;
                color: white;
            }

            /* 加载失败重试按钮 */
            .retry-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10003;
            }

            .retry-content {
                background: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
                margin: 20px;
            }

            .retry-icon {
                font-size: 48px;
                color: #e53e3e;
                margin-bottom: 20px;
            }

            .retry-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 10px;
                color: #2d3748;
            }

            .retry-message {
                color: #718096;
                margin-bottom: 20px;
                line-height: 1.5;
            }

            .retry-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 处理错误
    handleError(error) {
        // 记录错误
        this.logError(error);
        
        // 根据错误类型显示不同的处理方式
        if (this.isNetworkError(error)) {
            this.handleNetworkError(error);
        } else if (this.isValidationError(error)) {
            this.handleValidationError(error);
        } else if (this.isAuthError(error)) {
            this.handleAuthError(error);
        } else {
            this.showErrorAlert(error);
        }
    }

    // 记录错误
    logError(error) {
        this.errorLog.unshift({
            ...error,
            id: Date.now() + Math.random(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });

        // 限制日志大小
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }

        // 在开发环境下打印到控制台
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.error('Error logged:', error);
        }
    }

    // 判断是否为网络错误
    isNetworkError(error) {
        return error.message && (
            error.message.includes('fetch') ||
            error.message.includes('network') ||
            error.message.includes('Failed to load resource') ||
            error.status >= 500
        );
    }

    // 判断是否为验证错误
    isValidationError(error) {
        return error.status === 400 || error.status === 422;
    }

    // 判断是否为认证错误
    isAuthError(error) {
        return error.status === 401 || error.status === 403;
    }

    // 处理网络错误
    handleNetworkError(error) {
        this.showErrorAlert({
            ...error,
            type: 'network',
            title: '网络连接问题',
            message: '请检查您的网络连接，然后重试',
            showRetry: true
        });
    }

    // 处理验证错误
    handleValidationError(error) {
        this.showErrorAlert({
            ...error,
            type: 'warning',
            title: '输入验证失败',
            message: error.message || '请检查您的输入信息',
            showRetry: false
        });
    }

    // 处理认证错误
    handleAuthError(error) {
        this.showErrorAlert({
            ...error,
            type: 'error',
            title: '认证失败',
            message: '您的登录已过期，请重新登录',
            showRetry: false,
            actions: [{
                text: '重新登录',
                action: () => window.location.href = '/login'
            }]
        });
    }

    // 显示错误提示
    showErrorAlert(error) {
        const alert = document.createElement('div');
        alert.className = `error-alert ${error.type || 'error'}`;
        
        const actionsHtml = error.actions ? error.actions.map(action => 
            `<button class="error-btn ${action.class || 'retry'}" onclick="(${action.action.toString()})()">${action.text}</button>`
        ).join('') : '';

        const retryHtml = error.showRetry ? 
            `<button class="error-btn retry" onclick="window.location.reload()">重试</button>` : '';

        alert.innerHTML = `
            <div class="error-content">
                <div class="error-title">
                    <span><i class="fas fa-exclamation-triangle me-2"></i>${error.title || '发生错误'}</span>
                    <button class="error-close" onclick="this.closest('.error-alert').remove()">×</button>
                </div>
                <div class="error-message">${error.message}</div>
                ${error.details ? `<div class="error-details">${error.details}</div>` : ''}
                ${(actionsHtml || retryHtml) ? `<div class="error-actions">${actionsHtml}${retryHtml}</div>` : ''}
            </div>
        `;

        this.errorContainer.appendChild(alert);
        this.errorContainer.style.display = 'block';

        // 自动移除（除非是严重错误）
        if (error.type !== 'error') {
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => {
                        if (alert.parentElement) {
                            alert.remove();
                            if (this.errorContainer.children.length === 0) {
                                this.errorContainer.style.display = 'none';
                            }
                        }
                    }, 300);
                }
            }, 5000);
        }
    }

    // 显示网络状态
    showNetworkStatus() {
        const status = document.createElement('div');
        status.className = 'network-status';
        status.id = 'network-status';
        document.body.appendChild(status);

        const updateStatus = () => {
            if (navigator.onLine) {
                status.className = 'network-status online';
                status.textContent = '网络连接正常';
                setTimeout(() => status.style.display = 'none', 2000);
            } else {
                status.className = 'network-status offline';
                status.textContent = '网络连接断开';
                status.style.display = 'block';
            }
        };

        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);
        
        updateStatus();
    }

    // 获取错误日志
    getErrorLog() {
        return this.errorLog;
    }

    // 清空错误日志
    clearErrorLog() {
        this.errorLog = [];
    }

    // 导出错误日志
    exportErrorLog() {
        const logData = JSON.stringify(this.errorLog, null, 2);
        const blob = new Blob([logData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `error-log-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // 手动报告错误
    reportError(message, details = {}) {
        this.handleError({
            type: 'Manual Report',
            message: message,
            details: JSON.stringify(details),
            timestamp: new Date().toISOString()
        });
    }
}

// 创建全局实例
window.ErrorHandler = new ErrorHandler();

// 便捷方法
window.reportError = (message, details) => ErrorHandler.reportError(message, details);
window.getErrorLog = () => ErrorHandler.getErrorLog();
window.clearErrorLog = () => ErrorHandler.clearErrorLog();
window.exportErrorLog = () => ErrorHandler.exportErrorLog();

// 启动网络状态监控
ErrorHandler.showNetworkStatus();
