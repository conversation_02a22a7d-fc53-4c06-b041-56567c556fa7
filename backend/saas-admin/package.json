{"name": "smart-goose-saas-admin", "version": "1.0.0", "description": "Smart Goose SAAS Management Platform - Comprehensive Admin Center", "main": "server.js", "scripts": {"start": "NODE_ENV=production node server.js", "dev": "NODE_ENV=development nodemon server.js", "test": "jest", "lint": "eslint .", "setup": "node scripts/setup-database.js"}, "dependencies": {"express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-session": "^1.17.3", "express-rate-limit": "^6.7.0", "mysql2": "^3.6.0", "bcrypt": "^5.1.0", "helmet": "^7.0.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "eslint": "^8.44.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["saas", "admin", "goose-management", "multi-tenant", "platform"], "author": "Smart Goose Team", "license": "MIT"}