-- 智慧养鹅系统基础测试数据

-- 添加更多用户数据（使用简单的哈希密码，实际应该是bcrypt）
INSERT IGNORE INTO users (username, email, name, password, role, status, farmName, phone, createdAt, lastLoginAt) VALUES
('manager1', '<EMAIL>', '张经理', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'manager', 'active', '阳光养鹅场', '13800138001', '2024-01-15 10:00:00', '2024-08-25 16:30:00'),
('user1', '<EMAIL>', '李小明', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'user', 'active', '绿野养鹅合作社', '13800138002', '2024-02-20 14:30:00', '2024-08-26 09:15:00'),
('user2', '<EMAIL>', '王大华', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'user', 'active', '山水养鹅场', '13800138003', '2024-03-10 11:20:00', '2024-08-25 20:45:00');

-- 添加更多鹅群数据
INSERT IGNORE INTO flocks (name, breed, currentCount, totalCount, userId, status, establishedDate, location, description, batchNumber) VALUES
('大白鹅群D', '大白鹅', 156, 200, 2, 'active', '2024-03-01', '东区养殖场', '产蛋旺盛期', 'BATCH004'),
('灰鹅群E', '灰鹅', 89, 120, 2, 'active', '2024-04-15', '南区养殖场', '健康状况良好', 'BATCH005'),
('幼鹅群F', '混合品种', 234, 300, 3, 'active', '2024-06-01', '西区养殖场', '快速成长期', 'BATCH006');

-- 添加生产记录数据
INSERT IGNORE INTO production_records (flockId, userId, recordedDate, eggCount, notes) VALUES
(1, 1, '2024-08-26', 89, '天气良好，产蛋正常'),
(2, 2, '2024-08-26', 67, '有部分鹅身体不适'),
(3, 3, '2024-08-26', 134, '新增产蛋箱，产量提升'),
(1, 1, '2024-08-25', 91, '昨日正常'),
(2, 2, '2024-08-25', 71, '产蛋稳定'),
(3, 3, '2024-08-25', 128, '产蛋良好');

-- 添加健康记录数据
INSERT IGNORE INTO health_records (flockId, userId, checkDate, healthStatus, temperature, symptoms, treatment, veterinarian, notes) VALUES
(1, 1, '2024-08-26', 'healthy', 39.5, NULL, NULL, '张兽医', '整体健康状况良好'),
(2, 2, '2024-08-26', 'warning', 40.2, '部分个体食欲不振', '维生素补充', '李兽医', '需要密切观察'),
(3, 3, '2024-08-25', 'healthy', 39.8, NULL, NULL, '张兽医', '健康检查正常');

-- 添加财务记录数据
INSERT IGNORE INTO financial_records (userId, type, category, amount, description, recordDate, paymentMethod) VALUES
(2, 'income', 'egg_sales', 2580.00, '鸡蛋销售收入', '2024-08-25', 'bank_transfer'),
(2, 'expense', 'feed_cost', 890.00, '采购饲料', '2024-08-24', 'cash'),
(3, 'income', 'egg_sales', 1920.00, '鸡蛋批发', '2024-08-25', 'wechat_pay'),
(3, 'expense', 'veterinary', 350.00, '兽医检查费用', '2024-08-23', 'alipay'),
(2, 'expense', 'utilities', 245.00, '电费水费', '2024-08-20', 'bank_transfer');

-- 检查数据插入结果
SELECT '=== 用户数据统计 ===' as info;
SELECT role, COUNT(*) as count FROM users GROUP BY role;

SELECT '=== 鹅群数据统计 ===' as info;
SELECT status, COUNT(*) as count FROM flocks GROUP BY status;

SELECT '=== 生产记录统计 ===' as info;
SELECT DATE(recordedDate) as date, COUNT(*) as records, SUM(eggCount) as total_eggs FROM production_records GROUP BY DATE(recordedDate) ORDER BY date DESC LIMIT 5;