// 防疫流程控制器
const db = require('../config/database');

class VaccinationController {
  // 获取防疫模板列表
  static async getTemplates(req, res) {
    try {
      const [templates] = await db.sequelize.query(`
        SELECT 
          id, name, description, total_days, breed, total_quantity, status,
          created_at, updated_at
        FROM vaccination_templates 
        WHERE status = 'active'
        ORDER BY created_at DESC
      `);

      res.json({
        success: true,
        data: templates
      });
    } catch (error) {
      console.error('获取防疫模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取防疫模板失败',
        error: error.message
      });
    }
  }

  // 获取模板的防疫步骤
  static async getTemplateSteps(req, res) {
    try {
      const { templateId } = req.params;
      
      const [steps] = await db.sequelize.query(`
        SELECT 
          id, template_id, day_age, start_day, end_day,
          prevention_disease, medication, dosage_instructions,
          cost, priority, category, sort_order
        FROM vaccination_steps 
        WHERE template_id = ?
        ORDER BY start_day ASC, sort_order ASC
      `, {
        replacements: [templateId]
      });

      res.json({
        success: true,
        data: steps
      });
    } catch (error) {
      console.error('获取防疫步骤失败:', error);
      res.status(500).json({
        success: false,
        message: '获取防疫步骤失败',
        error: error.message
      });
    }
  }

  // 为鹅群创建防疫记录（开始执行防疫流程）
  static async createFlockVaccination(req, res) {
    try {
      const { flockId, templateId, startDate, totalGeese, notes } = req.body;
      
      // 创建鹅群防疫记录
      const [result] = await db.sequelize.query(`
        INSERT INTO flock_vaccination_records 
        (flock_id, template_id, start_date, total_geese, notes, status) 
        VALUES (?, ?, ?, ?, ?, 'active')
      `, {
        replacements: [flockId, templateId, startDate, totalGeese, notes || '']
      });

      const flockRecordId = result.insertId;

      // 获取该模板的所有步骤
      const [steps] = await db.sequelize.query(`
        SELECT id, start_day, end_day, day_age, prevention_disease, medication, dosage_instructions
        FROM vaccination_steps 
        WHERE template_id = ?
        ORDER BY start_day ASC
      `, {
        replacements: [templateId]
      });

      // 为每个步骤创建任务记录
      const startDateObj = new Date(startDate);
      for (const step of steps) {
        const scheduledDate = new Date(startDateObj);
        scheduledDate.setDate(scheduledDate.getDate() + step.start_day - 1);
        
        await db.sequelize.query(`
          INSERT INTO vaccination_task_records 
          (flock_record_id, step_id, scheduled_date, status) 
          VALUES (?, ?, ?, 'pending')
        `, {
          replacements: [flockRecordId, step.id, scheduledDate.toISOString().split('T')[0]]
        });

        // 创建提醒
        await db.sequelize.query(`
          INSERT INTO vaccination_reminders 
          (task_record_id, remind_date, title, content, type) 
          SELECT 
            vtr.id,
            vtr.scheduled_date,
            CONCAT('防疫提醒：', vs.day_age, ' - ', SUBSTRING(vs.prevention_disease, 1, 50)),
            CONCAT(vs.medication, '\\n', vs.dosage_instructions),
            CASE 
              WHEN vs.category = 'vaccination' THEN 'vaccination'
              WHEN vs.category = 'medication' THEN 'medication'
              ELSE 'observation'
            END
          FROM vaccination_task_records vtr
          JOIN vaccination_steps vs ON vtr.step_id = vs.id
          WHERE vtr.flock_record_id = ? AND vtr.step_id = ?
        `, {
          replacements: [flockRecordId, step.id]
        });
      }

      res.json({
        success: true,
        message: '防疫计划创建成功',
        data: { flockRecordId }
      });
    } catch (error) {
      console.error('创建防疫记录失败:', error);
      res.status(500).json({
        success: false,
        message: '创建防疫记录失败',
        error: error.message
      });
    }
  }

  // 获取鹅群的防疫任务列表（待办事项）
  static async getFlockTasks(req, res) {
    try {
      const { flockId } = req.params;
      const { status = 'all', date, limit = 50 } = req.query;
      
      let whereClause = 'WHERE fvr.flock_id = ?';
      let params = [flockId];
      
      if (status !== 'all') {
        whereClause += ' AND vtr.status = ?';
        params.push(status);
      }
      
      if (date) {
        whereClause += ' AND vtr.scheduled_date = ?';
        params.push(date);
      }
      
      const [tasks] = await db.sequelize.query(`
        SELECT 
          vtr.id as task_id,
          vtr.scheduled_date,
          vtr.actual_date,
          vtr.status,
          vtr.executor_name,
          vtr.notes as task_notes,
          vs.day_age,
          vs.prevention_disease,
          vs.medication,
          vs.dosage_instructions,
          vs.priority,
          vs.category,
          vs.cost,
          fvr.total_geese,
          fvr.start_date,
          DATEDIFF(vtr.scheduled_date, fvr.start_date) + 1 as current_day
        FROM vaccination_task_records vtr
        JOIN vaccination_steps vs ON vtr.step_id = vs.id
        JOIN flock_vaccination_records fvr ON vtr.flock_record_id = fvr.id
        ${whereClause}
        ORDER BY vtr.scheduled_date ASC, vs.sort_order ASC
        LIMIT ?
      `, {
        replacements: [...params, parseInt(limit)]
      });

      res.json({
        success: true,
        data: tasks
      });
    } catch (error) {
      console.error('获取防疫任务失败:', error);
      res.status(500).json({
        success: false,
        message: '获取防疫任务失败',
        error: error.message
      });
    }
  }

  // 获取今天的待办事项
  static async getTodayTasks(req, res) {
    try {
      const { userId } = req.params;
      const today = new Date().toISOString().split('T')[0];
      
      const [tasks] = await db.sequelize.query(`
        SELECT 
          vtr.id as task_id,
          vtr.scheduled_date,
          vtr.status,
          vs.day_age,
          vs.prevention_disease,
          vs.medication,
          vs.dosage_instructions,
          vs.priority,
          vs.category,
          fvr.flock_id,
          fvr.total_geese,
          f.name as flock_name,
          DATEDIFF(vtr.scheduled_date, fvr.start_date) + 1 as current_day
        FROM vaccination_task_records vtr
        JOIN vaccination_steps vs ON vtr.step_id = vs.id
        JOIN flock_vaccination_records fvr ON vtr.flock_record_id = fvr.id
        LEFT JOIN flocks f ON fvr.flock_id = f.id
        WHERE vtr.scheduled_date = ? 
        AND vtr.status IN ('pending', 'in_progress')
        AND fvr.status = 'active'
        ORDER BY vs.priority DESC, vtr.scheduled_date ASC
      `, {
        replacements: [today]
      });

      res.json({
        success: true,
        data: tasks
      });
    } catch (error) {
      console.error('获取今日任务失败:', error);
      res.status(500).json({
        success: false,
        message: '获取今日任务失败',
        error: error.message
      });
    }
  }

  // 更新任务状态（标记完成、进行中等）
  static async updateTaskStatus(req, res) {
    try {
      const { taskId } = req.params;
      const { 
        status, 
        executorName, 
        actualDate, 
        actualDosage, 
        mortalityCount = 0,
        sideEffects,
        effectivenessScore,
        cost,
        notes,
        photos 
      } = req.body;
      
      await db.sequelize.query(`
        UPDATE vaccination_task_records 
        SET 
          status = ?,
          executor_name = ?,
          actual_date = ?,
          actual_dosage = ?,
          mortality_count = ?,
          side_effects = ?,
          effectiveness_score = ?,
          cost = ?,
          notes = ?,
          photos = ?,
          updated_at = NOW()
        WHERE id = ?
      `, {
        replacements: [
          status,
          executorName,
          actualDate || new Date().toISOString().split('T')[0],
          actualDosage,
          mortalityCount,
          sideEffects,
          effectivenessScore,
          cost,
          notes,
          photos ? JSON.stringify(photos) : null,
          taskId
        ]
      });

      res.json({
        success: true,
        message: '任务状态更新成功'
      });
    } catch (error) {
      console.error('更新任务状态失败:', error);
      res.status(500).json({
        success: false,
        message: '更新任务状态失败',
        error: error.message
      });
    }
  }

  // 获取防疫统计报告
  static async getVaccinationStats(req, res) {
    try {
      const { flockId, templateId } = req.query;
      
      let whereClause = '1=1';
      let params = [];
      
      if (flockId) {
        whereClause += ' AND fvr.flock_id = ?';
        params.push(flockId);
      }
      
      if (templateId) {
        whereClause += ' AND fvr.template_id = ?';
        params.push(templateId);
      }
      
      const [stats] = await db.sequelize.query(`
        SELECT 
          COUNT(DISTINCT fvr.id) as total_flocks,
          COUNT(vtr.id) as total_tasks,
          SUM(CASE WHEN vtr.status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
          SUM(CASE WHEN vtr.status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
          SUM(CASE WHEN vtr.status = 'in_progress' THEN 1 ELSE 0 END) as inprogress_tasks,
          AVG(vtr.cost) as avg_cost,
          SUM(vtr.cost) as total_cost,
          AVG(vtr.effectiveness_score) as avg_effectiveness,
          SUM(vtr.mortality_count) as total_mortality
        FROM flock_vaccination_records fvr
        LEFT JOIN vaccination_task_records vtr ON fvr.id = vtr.flock_record_id
        WHERE ${whereClause}
      `, {
        replacements: params
      });

      res.json({
        success: true,
        data: stats[0] || {}
      });
    } catch (error) {
      console.error('获取防疫统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取防疫统计失败',
        error: error.message
      });
    }
  }
}

module.exports = VaccinationController;