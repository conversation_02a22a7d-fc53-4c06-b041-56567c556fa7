/**
 * 租户管理控制器
 * Tenant Management Controller
 */

const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

/**
 * 获取租户列表
 */
const getTenants = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, plan } = req.query;

    // 模拟租户数据
    const tenants = [
      {
        id: 1,
        tenantCode: 'DEMO001',
        name: '示例农场A',
        contactName: '张三',
        contactEmail: '<EMAIL>',
        contactPhone: '13800138001',
        status: 'active',
        subscriptionPlan: 'standard',
        subscriptionStartDate: '2024-01-01T00:00:00Z',
        subscriptionEndDate: '2024-12-31T23:59:59Z',
        features: ['health', 'production', 'oa', 'shop'],
        userCount: 25,
        dataUsage: '2.5GB',
        apiCalls: 8520,
        createdAt: '2023-12-01T10:00:00Z',
        lastActiveAt: '2024-01-15T14:30:00Z'
      },
      {
        id: 2,
        tenantCode: 'DEMO002',
        name: '绿色养殖场',
        contactName: '李四',
        contactEmail: '<EMAIL>',
        contactPhone: '13800138002',
        status: 'active',
        subscriptionPlan: 'premium',
        subscriptionStartDate: '2024-01-01T00:00:00Z',
        subscriptionEndDate: '2024-12-31T23:59:59Z',
        features: ['health', 'production', 'oa', 'shop', 'ai'],
        userCount: 45,
        dataUsage: '5.2GB',
        apiCalls: 15670,
        createdAt: '2023-11-15T09:30:00Z',
        lastActiveAt: '2024-01-15T16:20:00Z'
      }
    ];

    // 过滤租户
    let filteredTenants = tenants;
    if (status) {
      filteredTenants = filteredTenants.filter(t => t.status === status);
    }
    if (plan) {
      filteredTenants = filteredTenants.filter(t => t.subscriptionPlan === plan);
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedTenants = filteredTenants.slice(startIndex, endIndex);

    const result = {
      tenants: paginatedTenants,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total: filteredTenants.length,
        totalPages: Math.ceil(filteredTenants.length / limit)
      },
      summary: {
        totalTenants: tenants.length,
        activeTenants: tenants.filter(t => t.status === 'active').length,
        totalUsers: tenants.reduce((sum, t) => sum + t.userCount, 0),
        totalApiCalls: tenants.reduce((sum, t) => sum + t.apiCalls, 0)
      }
    };

    res.json(generateSuccessResponse(result));
  } catch (error) {
    console.error('获取租户列表失败:', error);
    res.status(500).json(generateErrorResponse('获取租户列表失败'));
  }
};

/**
 * 创建租户
 */
const createTenant = async (req, res) => {
  try {
    const {
      tenantCode,
      name,
      contactName,
      contactEmail,
      contactPhone,
      subscriptionPlan = 'basic',
      features = []
    } = req.body;

    if (!tenantCode || !name || !contactName || !contactEmail) {
      return res.status(400).json(generateErrorResponse('租户代码、名称、联系人和邮箱不能为空'));
    }

    const newTenant = {
      id: Date.now(),
      tenantCode,
      name,
      contactName,
      contactEmail,
      contactPhone,
      status: 'active',
      subscriptionPlan,
      subscriptionStartDate: new Date().toISOString(),
      subscriptionEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后
      features,
      userCount: 0,
      dataUsage: '0GB',
      apiCalls: 0,
      createdAt: new Date().toISOString(),
      lastActiveAt: null
    };

    // 这里应该保存到数据库
    console.log('创建租户:', newTenant);

    res.json(generateSuccessResponse(newTenant));
  } catch (error) {
    console.error('创建租户失败:', error);
    res.status(500).json(generateErrorResponse('创建租户失败'));
  }
};

/**
 * 获取租户详情
 */
const getTenantDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟租户详情数据
    const tenant = {
      id: parseInt(id),
      tenantCode: 'DEMO001',
      name: '示例农场A',
      description: '专业的鹅类养殖场，致力于绿色生态养殖',
      contactName: '张三',
      contactEmail: '<EMAIL>',
      contactPhone: '13800138001',
      address: '浙江省杭州市西湖区某某路123号',
      status: 'active',
      subscriptionPlan: 'standard',
      subscriptionStartDate: '2024-01-01T00:00:00Z',
      subscriptionEndDate: '2024-12-31T23:59:59Z',
      features: ['health', 'production', 'oa', 'shop'],
      settings: {
        timezone: 'Asia/Shanghai',
        language: 'zh-CN',
        currency: 'CNY',
        notifications: true
      },
      statistics: {
        userCount: 25,
        flockCount: 15,
        totalGeese: 2500,
        dataUsage: '2.5GB',
        apiCalls: 8520,
        storageUsage: '1.8GB'
      },
      createdAt: '2023-12-01T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z',
      lastActiveAt: '2024-01-15T14:30:00Z'
    };

    res.json(generateSuccessResponse(tenant));
  } catch (error) {
    console.error('获取租户详情失败:', error);
    res.status(500).json(generateErrorResponse('获取租户详情失败'));
  }
};

/**
 * 更新租户信息
 */
const updateTenant = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 这里应该更新数据库中的租户信息
    console.log(`更新租户 ${id}:`, updateData);

    res.json(generateSuccessResponse({
      tenantId: parseInt(id),
      message: '租户信息更新成功',
      updatedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('更新租户信息失败:', error);
    res.status(500).json(generateErrorResponse('更新租户信息失败'));
  }
};

/**
 * 删除租户
 */
const deleteTenant = async (req, res) => {
  try {
    const { id } = req.params;

    // 这里应该删除数据库中的租户信息
    console.log(`删除租户 ${id}`);

    res.json(generateSuccessResponse({
      tenantId: parseInt(id),
      message: '租户删除成功',
      deletedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('删除租户失败:', error);
    res.status(500).json(generateErrorResponse('删除租户失败'));
  }
};

/**
 * 激活租户
 */
const activateTenant = async (req, res) => {
  try {
    const { id } = req.params;

    // 这里应该更新数据库中的租户状态
    console.log(`激活租户 ${id}`);

    res.json(generateSuccessResponse({
      tenantId: parseInt(id),
      status: 'active',
      message: '租户激活成功',
      activatedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('激活租户失败:', error);
    res.status(500).json(generateErrorResponse('激活租户失败'));
  }
};

/**
 * 暂停租户
 */
const suspendTenant = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    // 这里应该更新数据库中的租户状态
    console.log(`暂停租户 ${id}，原因: ${reason}`);

    res.json(generateSuccessResponse({
      tenantId: parseInt(id),
      status: 'suspended',
      reason,
      message: '租户暂停成功',
      suspendedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('暂停租户失败:', error);
    res.status(500).json(generateErrorResponse('暂停租户失败'));
  }
};

/**
 * 获取租户统计
 */
const getTenantStatistics = async (req, res) => {
  try {
    const statistics = {
      overview: {
        totalTenants: 156,
        activeTenants: 142,
        suspendedTenants: 8,
        trialTenants: 6,
        expiredTenants: 0
      },
      byPlan: {
        basic: 45,
        standard: 67,
        premium: 30,
        enterprise: 14
      },
      growth: {
        thisMonth: 12,
        lastMonth: 8,
        growthRate: '50%'
      },
      usage: {
        totalUsers: 3520,
        totalApiCalls: 1250000,
        totalDataUsage: '256GB',
        avgUsagePerTenant: '1.6GB'
      },
      revenue: {
        thisMonth: 125600,
        lastMonth: 118200,
        growthRate: '6.3%'
      }
    };

    res.json(generateSuccessResponse(statistics));
  } catch (error) {
    console.error('获取租户统计失败:', error);
    res.status(500).json(generateErrorResponse('获取租户统计失败'));
  }
};

/**
 * 获取租户数据
 */
const getTenantData = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, dataType } = req.query;

    // 模拟租户数据
    const data = {
      tenantId: parseInt(id),
      dateRange: {
        start: startDate || '2024-01-01',
        end: endDate || '2024-01-15'
      },
      apiUsage: [
        { date: '2024-01-01', calls: 520 },
        { date: '2024-01-02', calls: 680 },
        { date: '2024-01-03', calls: 750 }
      ],
      userActivity: [
        { date: '2024-01-01', activeUsers: 18 },
        { date: '2024-01-02', activeUsers: 22 },
        { date: '2024-01-03', activeUsers: 25 }
      ],
      dataUsage: [
        { date: '2024-01-01', usage: 2.1 },
        { date: '2024-01-02', usage: 2.3 },
        { date: '2024-01-03', usage: 2.5 }
      ]
    };

    res.json(generateSuccessResponse(data));
  } catch (error) {
    console.error('获取租户数据失败:', error);
    res.status(500).json(generateErrorResponse('获取租户数据失败'));
  }
};

/**
 * 获取租户业务数据
 */
const getTenantBusiness = async (req, res) => {
  try {
    const { id } = req.params;

    // 模拟租户业务数据
    const businessData = {
      tenantId: parseInt(id),
      flocks: {
        total: 15,
        healthy: 14,
        warning: 1,
        critical: 0
      },
      production: {
        dailyEggs: 1250,
        monthlyEggs: 38750,
        efficiency: 95.2
      },
      health: {
        totalRecords: 456,
        aiDiagnosisCount: 89,
        alertCount: 3
      },
      oa: {
        pendingApprovals: 5,
        completedApprovals: 120,
        approvalRate: 96
      },
      finance: {
        monthlyRevenue: 185600,
        monthlyExpense: 142300,
        profit: 43300
      }
    };

    res.json(generateSuccessResponse(businessData));
  } catch (error) {
    console.error('获取租户业务数据失败:', error);
    res.status(500).json(generateErrorResponse('获取租户业务数据失败'));
  }
};

/**
 * 获取租户用户
 */
const getTenantUsers = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20, role, status } = req.query;

    // 模拟租户用户数据
    const users = [
      {
        id: 1,
        username: 'admin',
        name: '管理员',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        lastLoginAt: '2024-01-15T14:30:00Z',
        createdAt: '2023-12-01T10:00:00Z'
      },
      {
        id: 2,
        username: 'manager1',
        name: '张经理',
        email: '<EMAIL>',
        role: 'manager',
        status: 'active',
        lastLoginAt: '2024-01-15T13:20:00Z',
        createdAt: '2023-12-02T11:00:00Z'
      }
    ];

    // 过滤用户
    let filteredUsers = users;
    if (role) {
      filteredUsers = filteredUsers.filter(u => u.role === role);
    }
    if (status) {
      filteredUsers = filteredUsers.filter(u => u.status === status);
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    const result = {
      tenantId: parseInt(id),
      users: paginatedUsers,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      }
    };

    res.json(generateSuccessResponse(result));
  } catch (error) {
    console.error('获取租户用户失败:', error);
    res.status(500).json(generateErrorResponse('获取租户用户失败'));
  }
};

/**
 * 导出租户数据
 */
const exportTenants = async (req, res) => {
  try {
    const { format = 'excel', scope = 'all', includeStats = 'true' } = req.query;

    // 获取租户数据（这里使用模拟数据，实际应该从数据库查询）
    let tenants = mockTenants;

    // 根据范围筛选
    if (scope === 'active') {
      tenants = tenants.filter(tenant => tenant.status === 'active');
    }

    // 准备导出数据
    const exportData = tenants.map(tenant => ({
      '租户ID': tenant.id,
      '租户代码': tenant.tenantCode,
      '租户名称': tenant.name,
      '联系人': tenant.contactName,
      '联系邮箱': tenant.contactEmail,
      '联系电话': tenant.contactPhone || '',
      '状态': tenant.status === 'active' ? '活跃' : tenant.status === 'suspended' ? '暂停' : '非活跃',
      '订阅计划': tenant.subscriptionPlan === 'basic' ? '基础版' : tenant.subscriptionPlan === 'premium' ? '高级版' : '企业版',
      '用户数量': tenant.userCount,
      '数据使用量': tenant.dataUsage,
      'API调用次数': tenant.apiCalls,
      '创建时间': new Date(tenant.createdAt).toLocaleString('zh-CN'),
      '最后活跃': tenant.lastActiveAt ? new Date(tenant.lastActiveAt).toLocaleString('zh-CN') : '从未活跃'
    }));

    // 统计信息
    const stats = includeStats === 'true' ? {
      总租户数: tenants.length,
      活跃租户: tenants.filter(t => t.status === 'active').length,
      暂停租户: tenants.filter(t => t.status === 'suspended').length,
      基础版租户: tenants.filter(t => t.subscriptionPlan === 'basic').length,
      高级版租户: tenants.filter(t => t.subscriptionPlan === 'premium').length,
      企业版租户: tenants.filter(t => t.subscriptionPlan === 'enterprise').length,
      导出时间: new Date().toLocaleString('zh-CN')
    } : null;

    const filename = `租户数据_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`;

    res.json(generateSuccessResponse({
      filename,
      format,
      data: exportData,
      stats,
      total: exportData.length,
      message: `成功导出 ${exportData.length} 条租户数据`
    }));

  } catch (error) {
    console.error('导出租户数据失败:', error);
    res.status(500).json(generateErrorResponse('导出失败，请稍后重试'));
  }
};

module.exports = {
  getTenants,
  createTenant,
  getTenantDetail,
  updateTenant,
  deleteTenant,
  activateTenant,
  suspendTenant,
  getTenantStatistics,
  getTenantData,
  getTenantBusiness,
  getTenantUsers,
  exportTenants
};