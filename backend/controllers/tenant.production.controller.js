/**
 * 租户生产管理控制器 - 多租户生产管理
 * Tenant Production Controller - Multi-tenant production management
 */

/**
 * 获取生产记录列表
 */
exports.getProductionRecords = async (req, res) => {
  try {
    const { page = 1, limit = 10, flockId, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const replacements = [];

    if (flockId) {
      whereClause += ' AND pr.flockId = ?';
      replacements.push(flockId);
    }

    if (startDate) {
      whereClause += ' AND pr.recordDate >= ?';
      replacements.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND pr.recordDate <= ?';
      replacements.push(endDate);
    }

    // 获取总数
    const [countResult] = await req.tenantDb.query(
      `SELECT COUNT(*) as total FROM production_records pr ${whereClause}`,
      { replacements }
    );
    const total = countResult[0].total;

    // 获取分页数据
    const [productionRecords] = await req.tenantDb.query(
      `SELECT pr.*, f.name as flockName, f.batchNumber, u.nickname as recorderName
       FROM production_records pr
       LEFT JOIN flocks f ON pr.flockId = f.id
       LEFT JOIN users u ON pr.recorderId = u.id
       ${whereClause}
       ORDER BY pr.recordDate DESC
       LIMIT ? OFFSET ?`,
      {
        replacements: [...replacements, parseInt(limit), parseInt(offset)]
      }
    );

    res.json({
      success: true,
      data: {
        productionRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取生产记录失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取生产记录失败'
    });
  }
};

/**
 * 创建生产记录
 */
exports.createProductionRecord = async (req, res) => {
  try {
    const { flockId, recordDate, eggCount, weight, notes } = req.body;

    // 验证必填字段
    if (!flockId || !recordDate || eggCount === undefined) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 验证鹅群是否存在
    const [flock] = await req.tenantDb.query(
      'SELECT id FROM flocks WHERE id = ? AND status = "active"',
      { replacements: [flockId] }
    );

    if (flock.length === 0) {
      return res.status(404).json({
        success: false,
        message: '鹅群不存在或已停用'
      });
    }

    // 创建生产记录
    const [result] = await req.tenantDb.query(
      `INSERT INTO production_records (
        flockId, recorderId, recordDate, eggCount, weight, notes, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, NOW())`,
      {
        replacements: [
          flockId,
          req.userId,
          recordDate,
          eggCount,
          weight || null,
          notes || ''
        ]
      }
    );

    res.status(201).json({
      success: true,
      message: '生产记录创建成功',
      data: {
        id: result.insertId
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建生产记录失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '创建生产记录失败'
    });
  }
};

/**
 * 获取生产统计
 */
exports.getProductionStats = async (req, res) => {
  try {
    const { flockId, days = 30 } = req.query;

    let whereClause = 'WHERE 1=1';
    const replacements = [];

    if (flockId) {
      whereClause += ' AND flockId = ?';
      replacements.push(flockId);
    }

    whereClause += ' AND recordDate >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    replacements.push(parseInt(days));

    // 获取生产统计
    const [productionStats] = await req.tenantDb.query(
      `SELECT 
        COUNT(*) as totalRecords,
        SUM(eggCount) as totalEggs,
        AVG(eggCount) as avgDailyEggs,
        MAX(eggCount) as maxDailyEggs,
        MIN(eggCount) as minDailyEggs
       FROM production_records 
       ${whereClause}`,
      { replacements }
    );

    res.json({
      success: true,
      data: productionStats[0] || {
        totalRecords: 0,
        totalEggs: 0,
        avgDailyEggs: 0,
        maxDailyEggs: 0,
        minDailyEggs: 0
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取生产统计失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取生产统计失败'
    });
  }
};

/**
 * 获取生产趋势分析
 */
exports.getProductionTrends = async (req, res) => {
  try {
    const { flockId, days = 30 } = req.query;

    let whereClause = 'WHERE 1=1';
    const replacements = [];

    if (flockId) {
      whereClause += ' AND flockId = ?';
      replacements.push(flockId);
    }

    whereClause += ' AND recordDate >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    replacements.push(parseInt(days));

    // 获取生产趋势
    const [productionTrend] = await req.tenantDb.query(
      `SELECT 
        DATE(recordDate) as date,
        SUM(eggCount) as totalEggs,
        AVG(eggCount) as avgEggs,
        COUNT(*) as recordCount
       FROM production_records 
       ${whereClause}
       GROUP BY DATE(recordDate)
       ORDER BY date`,
      { replacements }
    );

    res.json({
      success: true,
      data: productionTrend || []
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取生产趋势失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取生产趋势失败'
    });
  }
};
