/**
 * 租户健康管理控制器 - 多租户健康管理
 * Tenant Health Controller - Multi-tenant health management
 */

/**
 * 获取健康记录列表
 */
exports.getHealthRecords = async (req, res) => {
  try {
    const { page = 1, limit = 10, flockId, status, checkType } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const replacements = [];

    if (flockId) {
      whereClause += ' AND hr.flockId = ?';
      replacements.push(flockId);
    }

    if (status) {
      whereClause += ' AND hr.result = ?';
      replacements.push(status);
    }

    if (checkType) {
      whereClause += ' AND hr.checkType = ?';
      replacements.push(checkType);
    }

    // 获取总数
    const [countResult] = await req.tenantDb.query(
      `SELECT COUNT(*) as total FROM health_records hr ${whereClause}`,
      { replacements }
    );
    const total = countResult[0].total;

    // 获取分页数据
    const [healthRecords] = await req.tenantDb.query(
      `SELECT hr.*, f.name as flockName, f.batchNumber, u.nickname as checkerName
       FROM health_records hr
       LEFT JOIN flocks f ON hr.flockId = f.id
       LEFT JOIN users u ON hr.checkerId = u.id
       ${whereClause}
       ORDER BY hr.checkDate DESC
       LIMIT ? OFFSET ?`,
      {
        replacements: [...replacements, parseInt(limit), parseInt(offset)]
      }
    );

    res.json({
      success: true,
      data: {
        healthRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取健康记录失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取健康记录失败'
    });
  }
};

/**
 * 创建健康记录
 */
exports.createHealthRecord = async (req, res) => {
  try {
    const {
      flockId,
      checkType,
      checkDate,
      symptoms,
      temperature,
      weight,
      result,
      diagnosis,
      treatment,
      medicine,
      dosage,
      notes
    } = req.body;

    // 验证必填字段
    if (!flockId || !checkType || !checkDate || !result) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 验证鹅群是否存在
    const [flock] = await req.tenantDb.query(
      'SELECT id FROM flocks WHERE id = ? AND status = "active"',
      { replacements: [flockId] }
    );

    if (flock.length === 0) {
      return res.status(404).json({
        success: false,
        message: '鹅群不存在或已停用'
      });
    }

    // 创建健康记录
    const [result_db] = await req.tenantDb.query(
      `INSERT INTO health_records (
        flockId, checkerId, checkType, checkDate, symptoms, temperature,
        weight, result, diagnosis, treatment, medicine, dosage, notes, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      {
        replacements: [
          flockId,
          req.userId,
          checkType,
          checkDate,
          symptoms || '',
          temperature || null,
          weight || null,
          result,
          diagnosis || '',
          treatment || '',
          medicine || '',
          dosage || '',
          notes || ''
        ]
      }
    );

    res.status(201).json({
      success: true,
      message: '健康记录创建成功',
      data: {
        id: result_db.insertId
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建健康记录失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '创建健康记录失败'
    });
  }
};

/**
 * 获取健康统计
 */
exports.getHealthStats = async (req, res) => {
  try {
    const { flockId, days = 30 } = req.query;

    let whereClause = 'WHERE 1=1';
    const replacements = [];

    if (flockId) {
      whereClause += ' AND flockId = ?';
      replacements.push(flockId);
    }

    whereClause += ' AND checkDate >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    replacements.push(parseInt(days));

    // 获取健康统计
    const [healthStats] = await req.tenantDb.query(
      `SELECT 
        COUNT(*) as totalChecks,
        SUM(CASE WHEN result = 'healthy' THEN 1 ELSE 0 END) as healthyCount,
        SUM(CASE WHEN result = 'sick' THEN 1 ELSE 0 END) as sickCount,
        SUM(CASE WHEN result = 'warning' THEN 1 ELSE 0 END) as warningCount,
        AVG(CASE WHEN temperature IS NOT NULL THEN temperature END) as avgTemperature,
        AVG(CASE WHEN weight IS NOT NULL THEN weight END) as avgWeight
       FROM health_records 
       ${whereClause}`,
      { replacements }
    );

    // 获取健康趋势
    const [healthTrend] = await req.tenantDb.query(
      `SELECT 
        DATE(checkDate) as date,
        COUNT(*) as totalChecks,
        SUM(CASE WHEN result = 'healthy' THEN 1 ELSE 0 END) as healthyCount,
        SUM(CASE WHEN result = 'sick' THEN 1 ELSE 0 END) as sickCount
       FROM health_records 
       ${whereClause}
       GROUP BY DATE(checkDate)
       ORDER BY date`,
      { replacements }
    );

    res.json({
      success: true,
      data: {
        stats: healthStats[0] || {
          totalChecks: 0,
          healthyCount: 0,
          sickCount: 0,
          warningCount: 0,
          avgTemperature: null,
          avgWeight: null
        },
        trend: healthTrend || []
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取健康统计失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取健康统计失败'
    });
  }
};

/**
 * AI健康诊断
 */
exports.aiDiagnosis = async (req, res) => {
  try {
    const { symptoms, images, flockId } = req.body;

    if (!symptoms && !images) {
      return res.status(400).json({
        success: false,
        message: '请提供症状描述或图片'
      });
    }

    // 模拟AI诊断结果
    const diagnosis = {
      confidence: 0.85,
      possibleDiseases: [
        {
          name: '禽流感',
          probability: 0.35,
          symptoms: ['发热', '呼吸困难', '食欲不振'],
          treatment: '隔离治疗，使用抗病毒药物'
        },
        {
          name: '肠炎',
          probability: 0.25,
          symptoms: ['腹泻', '精神萎靡', '体重下降'],
          treatment: '调整饲料，使用消炎药物'
        }
      ],
      recommendations: [
        '立即隔离患病鹅只',
        '清洁和消毒养殖环境',
        '联系兽医进行进一步检查',
        '记录症状变化情况'
      ]
    };

    res.json({
      success: true,
      message: 'AI诊断完成',
      data: diagnosis
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('AI诊断失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: 'AI诊断失败'
    });
  }
};
