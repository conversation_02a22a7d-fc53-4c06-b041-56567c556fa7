/**
 * 租户认证控制器 - 处理微信小程序登录和用户认证
 * Tenant Authentication Controller - Handle WeChat Mini Program login and user authentication
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
const crypto = require('crypto');

/**
 * 微信小程序登录
 * 基于微信小程序最佳实践实现
 */
exports.wechatLogin = async (req, res) => {
  try {
    const { code, userInfo, signature, rawData, encryptedData, iv } = req.body;
    const tenant = req.tenant;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少微信登录code',
        code: 'WECHAT_CODE_MISSING'
      });
    }

    // 1. 获取租户的微信小程序配置
    const miniprogramConfig = await getTenantMiniprogramConfig(tenant.id);
    if (!miniprogramConfig) {
      return res.status(500).json({
        success: false,
        message: '租户小程序配置不存在',
        code: 'MINIPROGRAM_CONFIG_NOT_FOUND'
      });
    }

    // 2. 调用微信API获取session_key和openid
    const wechatApiUrl = 'https://api.weixin.qq.com/sns/jscode2session';
    const wechatParams = {
      appid: miniprogramConfig.appId,
      secret: miniprogramConfig.appSecret,
      js_code: code,
      grant_type: 'authorization_code'
    };

    const wechatResponse = await axios.get(wechatApiUrl, {
      params: wechatParams
    });
    const { openid, session_key, unionid, errcode, errmsg } =
      wechatResponse.data;

    if (errcode) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('微信登录失败:', errcode, errmsg); } catch(_) {}

      return res.status(400).json({
        success: false,
        message: '微信登录失败',
        code: 'WECHAT_LOGIN_FAILED',
        details: errmsg
      });
    }

    // 3. 验证数据签名（可选，增强安全性）
    if (signature && rawData && session_key) {
      const hash = crypto.createHash('sha1');
      hash.update(rawData + session_key);
      const calculatedSignature = hash.digest('hex');

      if (signature !== calculatedSignature) {
        return res.status(400).json({
          success: false,
          message: '数据签名验证失败',
          code: 'SIGNATURE_VERIFICATION_FAILED'
        });
      }
    }

    // 4. 查找或创建用户
    let user = await findUserByOpenId(req.tenantDb, openid);

    if (!user) {
      // 创建新用户
      const userData = {
        openid: openid,
        unionid: unionid,
        nickname: userInfo?.nickName || '微信用户',
        avatar: userInfo?.avatarUrl || '',
        gender: userInfo?.gender || 0,
        city: userInfo?.city || '',
        province: userInfo?.province || '',
        country: userInfo?.country || '',
        status: 'active',
        role: 'user',
        lastLoginAt: new Date(),
        createdAt: new Date()
      };

      user = await createUser(req.tenantDb, userData);
    } else {
      // 更新现有用户的登录时间和信息
      await updateUserLoginInfo(req.tenantDb, user.id, {
        nickname: userInfo?.nickName || user.nickname,
        avatar: userInfo?.avatarUrl || user.avatar,
        lastLoginAt: new Date()
      });

      user.lastLoginAt = new Date();
    }

    // 5. 生成JWT令牌
    const tokenPayload = {
      userId: user.id,
      tenantId: tenant.id,
      openid: openid,
      role: user.role
    };

    const accessToken = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required'); })(),
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { userId: user.id, tenantId: tenant.id },
      process.env.JWT_REFRESH_SECRET || 'refresh_secret',
      { expiresIn: '30d' }
    );

    // 6. 保存session_key（用于后续解密用户敏感数据）
    await saveUserSession(req.tenantDb, user.id, session_key);

    // 7. 记录登录日志
    await recordLoginLog(req.tenantDb, user.id, req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          role: user.role,
          status: user.status
        },
        tenant: {
          tenantCode: tenant.tenantCode,
          companyName: tenant.companyName,
          subscriptionPlan: tenant.subscriptionPlan
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: 7 * 24 * 60 * 60 // 7天，单位：秒
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('微信登录失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试',
      code: 'LOGIN_INTERNAL_ERROR'
    });
  }
};

/**
 * 获取用户信息
 */
exports.getProfile = async (req, res) => {
  try {
    const user = req.user;
    const tenant = req.tenant;

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          gender: user.gender,
          city: user.city,
          province: user.province,
          country: user.country,
          role: user.role,
          status: user.status,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        },
        tenant: {
          tenantCode: tenant.tenantCode,
          companyName: tenant.companyName,
          subscriptionPlan: tenant.subscriptionPlan,
          features: tenant.features ? JSON.parse(tenant.features) : []
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户信息失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
};

/**
 * 刷新令牌
 */
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: '缺少刷新令牌',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    // 验证刷新令牌
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || 'refresh_secret'
    );

    // 检查用户是否存在且有效
    const [userResults] = await req.tenantDb.query(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      {
        replacements: [decoded.userId],
        type: req.tenantDb.QueryTypes.SELECT
      }
    );

    if (userResults.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已停用',
        code: 'USER_NOT_FOUND'
      });
    }

    const user = userResults[0];

    // 生成新的访问令牌
    const tokenPayload = {
      userId: user.id,
      tenantId: req.tenant.id,
      openid: user.openid,
      role: user.role
    };

    const newAccessToken = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required'); })(),
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        accessToken: newAccessToken,
        expiresIn: 7 * 24 * 60 * 60
      }
    });
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌无效或已过期',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('刷新令牌失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '令牌刷新失败'
    });
  }
};

/**
 * 获取租户小程序配置
 */
async function getTenantMiniprogramConfig(tenantId) {
  try {
    const tenantDatabaseManager = require('../models/tenant-database.model');

    const [results] = await saasDb.query(
      'SELECT * FROM tenant_miniprogram_configs WHERE tenantId = ? AND status = "production"',
      {
        replacements: [tenantId],
        type: saasDb.QueryTypes.SELECT
      }
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取租户小程序配置失败:', error); } catch(_) {}

    return null;
  }
}

/**
 * 根据OpenID查找用户
 */
async function findUserByOpenId(tenantDb, openid) {
  try {
    const [results] = await tenantDb.query(
      'SELECT * FROM users WHERE openid = ?',
      {
        replacements: [openid],
        type: tenantDb.QueryTypes.SELECT
      }
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('查找用户失败:', error); } catch(_) {}

    return null;
  }
}

/**
 * 创建新用户
 */
async function createUser(tenantDb, userData) {
  try {
    const [result] = await tenantDb.query(
      `INSERT INTO users (openid, unionid, nickname, avatar, gender, city, province, country, status, role, lastLoginAt, createdAt) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      {
        replacements: [
          userData.openid,
          userData.unionid,
          userData.nickname,
          userData.avatar,
          userData.gender,
          userData.city,
          userData.province,
          userData.country,
          userData.status,
          userData.role,
          userData.lastLoginAt,
          userData.createdAt
        ]
      }
    );

    return {
      id: result.insertId,
      ...userData
    };
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建用户失败:', error); } catch(_) {}

    throw error;
  }
}

/**
 * 更新用户登录信息
 */
async function updateUserLoginInfo(tenantDb, userId, updateData) {
  try {
    await tenantDb.query(
      'UPDATE users SET nickname = ?, avatar = ?, lastLoginAt = ? WHERE id = ?',
      {
        replacements: [
          updateData.nickname,
          updateData.avatar,
          updateData.lastLoginAt,
          userId
        ]
      }
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新用户登录信息失败:', error); } catch(_) {}

  }
}

/**
 * 保存用户会话信息
 */
async function saveUserSession(tenantDb, userId, sessionKey) {
  try {
    await tenantDb.query(
      `INSERT INTO user_sessions (userId, sessionKey, createdAt) 
       VALUES (?, ?, ?) 
       ON DUPLICATE KEY UPDATE sessionKey = ?, createdAt = ?`,
      {
        replacements: [userId, sessionKey, new Date(), sessionKey, new Date()]
      }
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('保存用户会话失败:', error); } catch(_) {}

  }
}

/**
 * 记录登录日志
 */
async function recordLoginLog(tenantDb, userId, ipAddress, userAgent) {
  try {
    await tenantDb.query(
      'INSERT INTO login_logs (userId, ipAddress, userAgent, loginAt) VALUES (?, ?, ?, ?)',
      {
        replacements: [userId, ipAddress, userAgent, new Date()]
      }
    );
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('记录登录日志失败:', error); } catch(_) {}

  }
}
