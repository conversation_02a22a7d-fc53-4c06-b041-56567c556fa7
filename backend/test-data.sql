-- 智慧养鹅系统测试数据插入脚本
-- 用于支持全面联调测试

-- 添加更多用户数据
INSERT IGNORE INTO users (id, username, email, name, password, role, status, farmName, phone, avatar, createdAt, lastLoginAt) VALUES
(2, 'manager1', '<EMAIL>', '张经理', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'manager', 'active', '阳光养鹅场', '13800138001', NULL, '2024-01-15 10:00:00', '2024-08-25 16:30:00'),
(4, 'user1', '<EMAIL>', '李小明', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'user', 'active', '绿野养鹅合作社', '13800138002', NULL, '2024-02-20 14:30:00', '2024-08-26 09:15:00'),
(5, 'user2', '<EMAIL>', '王大华', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'user', 'active', '山水养鹅场', '13800138003', NULL, '2024-03-10 11:20:00', '2024-08-25 20:45:00'),
(6, 'inactive_user', '<EMAIL>', '停用用户', '$2b$10$8K1p/a0dF0A9LXVN5K3K3.OXVyNwv9bh8r9JCqjKm3K8L0/zYcB2u', 'user', 'inactive', '暂停养鹅场', '13800138004', NULL, '2024-01-01 10:00:00', NULL);

-- 添加更多鹅群数据
INSERT IGNORE INTO flocks (id, name, breed, currentCount, totalCount, userId, status, establishedDate, location, description, batchNumber, createdAt) VALUES
(4, '大白鹅群D', '大白鹅', 156, 200, 2, 'active', '2024-03-01', '东区养殖场', '产蛋旺盛期', 'BATCH004', '2024-03-05'),
(5, '灰鹅群E', '灰鹅', 89, 120, 4, 'active', '2024-04-15', '南区养殖场', '健康状况良好', 'BATCH005', '2024-04-20'),
(6, '幼鹅群F', '混合品种', 234, 300, 5, 'active', '2024-06-01', '西区养殖场', '快速成长期', 'BATCH006', '2024-06-05'),
(7, '停用鹅群', '大白鹅', 45, 100, 6, 'inactive', '2024-01-01', '北区养殖场', '已停用', 'BATCH007', '2024-01-05');

-- 添加生产记录数据
INSERT IGNORE INTO production_records (id, flockId, userId, recordedDate, eggCount, notes, recordedBy, createdAt) VALUES
(5, 1, 1, '2024-08-26', 89, '天气良好，产蛋正常', 'admin', '2024-08-26 08:00:00'),
(6, 2, 2, '2024-08-26', 67, '有2只鹅身体不适，已隔离', 'manager1', '2024-08-26 08:30:00'),
(7, 4, 2, '2024-08-26', 134, '新增产蛋箱，产量提升', 'manager1', '2024-08-26 09:00:00'),
(8, 5, 4, '2024-08-25', 76, '正常生产', 'user1', '2024-08-25 18:00:00'),
(9, 6, 5, '2024-08-25', 198, '产蛋高峰期', 'user2', '2024-08-25 19:30:00'),
(10, 1, 1, '2024-08-25', 91, '昨日正常', 'admin', '2024-08-25 08:00:00'),
(11, 2, 2, '2024-08-25', 71, '产蛋稳定', 'manager1', '2024-08-25 08:30:00'),
(12, 4, 2, '2024-08-25', 128, '产蛋良好', 'manager1', '2024-08-25 09:00:00');

-- 添加健康记录数据
INSERT IGNORE INTO health_records (id, flockId, userId, checkDate, healthStatus, temperature, symptoms, treatment, veterinarian, notes, createdAt) VALUES
(1, 1, 1, '2024-08-26', 'healthy', 39.5, NULL, NULL, '张兽医', '整体健康状况良好', '2024-08-26 10:00:00'),
(2, 2, 2, '2024-08-26', 'warning', 40.2, '部分个体食欲不振', '维生素补充', '李兽医', '需要密切观察', '2024-08-26 10:30:00'),
(3, 4, 2, '2024-08-25', 'healthy', 39.8, NULL, NULL, '张兽医', '健康检查正常', '2024-08-25 14:00:00'),
(4, 5, 4, '2024-08-24', 'healthy', 39.6, NULL, '预防性疫苗', '王兽医', '疫苗接种完成', '2024-08-24 16:00:00'),
(5, 6, 5, '2024-08-23', 'critical', 41.0, '发热、呼吸急促', '抗生素治疗', '李兽医', '紧急治疗中，已隔离', '2024-08-23 12:00:00');

-- 添加财务记录数据
INSERT IGNORE INTO financial_records (id, userId, type, category, amount, description, recordDate, paymentMethod, receipt, createdAt) VALUES
(1, 2, 'income', 'egg_sales', 2580.00, '鸡蛋销售收入', '2024-08-25', 'bank_transfer', 'receipt_001.jpg', '2024-08-25 16:00:00'),
(2, 2, 'expense', 'feed_cost', 890.00, '采购饲料', '2024-08-24', 'cash', 'receipt_002.jpg', '2024-08-24 10:30:00'),
(3, 4, 'income', 'egg_sales', 1920.00, '鸡蛋批发', '2024-08-25', 'wechat_pay', NULL, '2024-08-25 18:30:00'),
(4, 5, 'income', 'egg_sales', 3150.00, '大批量蛋品销售', '2024-08-25', 'bank_transfer', 'receipt_003.jpg', '2024-08-25 20:00:00'),
(5, 2, 'expense', 'veterinary', 350.00, '兽医检查费用', '2024-08-23', 'alipay', 'receipt_004.jpg', '2024-08-23 14:00:00'),
(6, 4, 'expense', 'utilities', 245.00, '电费水费', '2024-08-20', 'bank_transfer', NULL, '2024-08-20 09:00:00'),
(7, 5, 'expense', 'equipment', 1200.00, '购买新产蛋箱', '2024-08-18', 'bank_transfer', 'receipt_005.jpg', '2024-08-18 15:30:00');

-- 添加公告数据
INSERT IGNORE INTO announcements (id, title, content, type, priority, target_audience, is_published, publish_date, expire_date, author_id, created_at) VALUES
(1, '系统维护通知', '系统将于本周末进行例行维护，预计维护时间2小时', 'system', 'high', 'all', 1, '2024-08-26 00:00:00', '2024-08-30 23:59:59', 1, '2024-08-26 00:00:00'),
(2, '新功能发布', '新增了AI智能诊断功能，欢迎大家试用', 'feature', 'medium', 'all', 1, '2024-08-20 00:00:00', '2024-09-20 23:59:59', 1, '2024-08-20 00:00:00'),
(3, '禽流感预防提醒', '近期注意禽流感预防，做好消毒工作', 'alert', 'high', 'users', 1, '2024-08-15 00:00:00', '2024-09-15 23:59:59', 1, '2024-08-15 00:00:00');

-- 添加知识库数据
INSERT IGNORE INTO knowledge_base (id, title, content, category, tags, status, author_id, view_count, like_count, created_at) VALUES
(1, '鹅的饲养管理要点', '详细介绍鹅类饲养的关键技术要点...', 'breeding', '["饲养", "管理", "技术"]', 'published', 1, 156, 23, '2024-08-01 10:00:00'),
(2, '常见鹅病预防与治疗', '介绍鹅类常见疾病的预防和治疗方法...', 'health', '["疾病", "预防", "治疗", "健康"]', 'published', 1, 234, 45, '2024-07-25 14:30:00'),
(3, '提高产蛋率的有效方法', '分享提高鹅产蛋率的实用技巧...', 'production', '["产蛋", "效率", "技巧"]', 'published', 1, 189, 37, '2024-07-20 16:00:00');

-- 更新用户统计数据
UPDATE users SET lastLoginAt = '2024-08-26 02:55:00' WHERE id = 1;
UPDATE users SET lastLoginAt = '2024-08-25 18:30:00' WHERE id = 2;
UPDATE users SET lastLoginAt = '2024-08-26 09:15:00' WHERE id = 4;
UPDATE users SET lastLoginAt = '2024-08-25 20:45:00' WHERE id = 5;