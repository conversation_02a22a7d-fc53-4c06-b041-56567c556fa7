-- 最简化的测试数据插入

-- 添加健康记录数据（简化版）
INSERT IGNORE INTO health_records (flockId, userId, checkDate, healthStatus, symptoms, treatment, veterinarian, notes) VALUES
(1, 1, '2024-08-26', 'healthy', NULL, NULL, '张兽医', '整体健康状况良好'),
(2, 1, '2024-08-26', 'warning', '部分个体食欲不振', '维生素补充', '李兽医', '需要密切观察'),
(3, 3, '2024-08-25', 'healthy', NULL, NULL, '张兽医', '健康检查正常');

-- 检查结果
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as total_flocks FROM flocks;
SELECT COUNT(*) as total_health_records FROM health_records;
SELECT COUNT(*) as total_production_records FROM production_records;

-- 显示最近的生产数据
SELECT f.name as flock_name, p.recordedDate, p.eggCount 
FROM production_records p 
JOIN flocks f ON p.flockId = f.id 
ORDER BY p.recordedDate DESC LIMIT 5;