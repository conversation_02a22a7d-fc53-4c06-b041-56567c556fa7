const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'smart_goose',
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000
});

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

// 执行查询的封装函数
async function query(sql, params = []) {
  try {
    const [results] = await pool.execute(sql, params);
    return [results];
  } catch (error) {
    console.error('SQL查询错误:', error);
    console.error('SQL语句:', sql);
    console.error('参数:', params);
    throw error;
  }
}

// 获取连接（用于事务）
async function getConnection() {
  return await pool.getConnection();
}

// 关闭连接池
async function closePool() {
  await pool.end();
  console.log('数据库连接池已关闭');
}

// 初始化数据库
async function initDatabase() {
  try {
    console.log('🔧 开始初始化数据库...');
    
    // 检查数据库连接
    const connected = await testConnection();
    if (!connected) {
      throw new Error('数据库连接失败，无法初始化');
    }

    // 检查是否需要初始化表结构
    const [tables] = await query("SHOW TABLES");
    if (tables.length === 0) {
      console.log('📋 数据库为空，开始创建表结构...');
      
      // 这里可以执行 schema.sql 文件
      // 由于这是演示，我们简单检查关键表是否存在
      const keyTables = ['platform_admins', 'tenants', 'users', 'wechat_users'];
      
      for (const table of keyTables) {
        const [exists] = await query(`SHOW TABLES LIKE '${table}'`);
        if (exists.length === 0) {
          console.log(`⚠️  表 ${table} 不存在，请先执行 schema.sql 创建数据库表结构`);
        }
      }
    }

    console.log('✅ 数据库初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    return false;
  }
}

// 数据库健康检查
async function healthCheck() {
  try {
    await query('SELECT 1');
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
}

module.exports = {
  pool,
  query,
  getConnection,
  closePool,
  testConnection,
  initDatabase,
  healthCheck
};