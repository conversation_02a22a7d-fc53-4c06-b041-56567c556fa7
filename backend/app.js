const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
require('dotenv').config();

const db = require('./database/connection');
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());
app.use(compression());

// CORS配置
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:3000,http://localhost:4000').split(',');
    
    // 允许没有origin的请求（如移动应用）
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后重试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`);
  next();
});

// 路由配置
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);

// 启用关键API路由
try {
  app.use('/api/v1/users', require('./routes/user.routes'));
  console.log('✅ 用户路由加载成功');
} catch (error) {
  console.warn('⚠️ 用户路由加载失败:', error.message);
}

try {
  app.use('/api/v1/health', require('./routes/health.routes'));
  console.log('✅ 健康路由加载成功');
} catch (error) {
  console.warn('⚠️ 健康路由加载失败:', error.message);
}

try {
  app.use('/api/v1/production-records', require('./routes/production-records.routes'));
  console.log('✅ 生产记录路由加载成功');
} catch (error) {
  console.warn('⚠️ 生产记录路由加载失败:', error.message);
}

try {
  app.use('/api/v1/flocks', require('./routes/flock.routes'));
  console.log('✅ 鹅群路由加载成功');
} catch (error) {
  console.warn('⚠️ 鹅群路由加载失败:', error.message);
}

// 健康检查端点
app.get('/api/health', async (req, res) => {
  try {
    const dbHealth = await db.healthCheck();
    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      service: 'Smart Goose API',
      version: '1.0.0',
      database: dbHealth,
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务健康检查失败',
      error: error.message
    });
  }
});

// 管理员登录端点
app.post('/api/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查询管理员用户
    const [admins] = await db.query(
      'SELECT * FROM platform_admins WHERE username = ? AND status = "active"',
      [username]
    );

    if (admins.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const admin = admins[0];
    
    // 验证密码 (这里应该使用bcrypt比较)
    const bcrypt = require('bcrypt');
    const isValidPassword = await bcrypt.compare(password, admin.password);
    
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { 
        adminId: admin.id,
        username: admin.username,
        role: admin.role
      },
      process.env.JWT_ADMIN_SECRET || process.env.JWT_SECRET,
      { expiresIn: '8h' }
    );

    // 更新最后登录时间
    await db.query(
      'UPDATE platform_admins SET last_login_time = CURRENT_TIMESTAMP WHERE id = ?',
      [admin.id]
    );

    // 记录登录日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'admin',
      admin.id,
      'login',
      'auth',
      '管理员登录系统',
      req.ip,
      req.get('User-Agent')
    ]);

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          name: admin.name,
          role: admin.role,
          email: admin.email
        }
      }
    });

  } catch (error) {
    console.error('管理员登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请重试'
    });
  }
});

// 404处理 - 移到所有路由之后
// app.use('*', (req, res) => {
//   res.status(404).json({
//     success: false,
//     message: 'API端点不存在',
//     path: req.originalUrl
//   });
// });

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  // CORS错误
  if (error.message === 'Not allowed by CORS') {
    return res.status(403).json({
      success: false,
      message: '跨域请求被拒绝'
    });
  }
  
  // JSON解析错误
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      success: false,
      message: '请求数据格式错误'
    });
  }
  
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，准备关闭服务器...');
  
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    
    // 关闭数据库连接
    await db.closePool();
    
    console.log('服务器优雅关闭完成');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，准备关闭服务器...');
  
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    
    // 关闭数据库连接
    await db.closePool();
    
    console.log('服务器优雅关闭完成');  
    process.exit(0);
  });
});

// 404处理 - 放在所有路由之后
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API端点不存在',
    path: req.originalUrl
  });
});

// 启动服务器
const startServer = async () => {
  try {
    // 初始化数据库
    const dbInitialized = await db.initDatabase();
    if (!dbInitialized) {
      console.error('数据库初始化失败，服务器无法启动');
      process.exit(1);
    }

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 智慧养鹅API服务器已启动`);
      console.log(`📡 监听端口: ${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
      
      if (process.env.NODE_ENV !== 'production') {
        console.log(`🔗 健康检查: http://localhost:${PORT}/api/health`);
        console.log(`📱 小程序API: http://localhost:${PORT}/api/auth`);
        console.log(`🔧 管理后台API: http://localhost:${PORT}/api/admin`);
      }
    });

    // 保存server实例用于优雅关闭
    global.server = server;

  } catch (error) {
    console.error('启动服务器失败:', error);
    process.exit(1);
  }
};

// 启动应用
startServer();

module.exports = app;