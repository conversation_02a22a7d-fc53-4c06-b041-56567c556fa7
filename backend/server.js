/**
 * 智慧养鹅SAAS平台 - 统一服务器入口
 * Smart Goose SAAS Platform - Unified Server Entry Point
 *
 * 整合微信小程序API、租户管理后台、SAAS平台管理三个模块
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const session = require('express-session');
const expressLayouts = require('express-ejs-layouts');

// 导入中间件
const {
  requestLogger,
  errorHandler,
  notFoundHandler,
  Logger
} = require('./middleware/errorHandler');
const {
  responseMiddleware,
  globalErrorHandler
} = require('./middleware/response.middleware');
const {
  identifyTenant,
  requireTenantPermission,
  authenticateTenantUser,
  rateLimitTenant,
  recordTenantUsage
} = require('./middleware/tenant.middleware');

// 导入数据库管理器
const tenantDatabaseManager = require('./models/tenant-database.model');

// 加载环境变量
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'development'}` });

// 创建Express应用
const app = express();

// 基础中间件配置
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ['\'self\''],
        styleSrc: [
          '\'self\'',
          '\'unsafe-inline\'',
          'https://cdn.jsdelivr.net',
          'https://cdnjs.cloudflare.com'
        ],
        scriptSrc: [
          '\'self\'',
          '\'unsafe-inline\'',
          'https://cdn.jsdelivr.net',
          'https://cdnjs.cloudflare.com'
        ],
        imgSrc: ['\'self\'', 'data:', 'https:'],
        fontSrc: [
          '\'self\'',
          'https://cdn.jsdelivr.net',
          'https://cdnjs.cloudflare.com'
        ],
        connectSrc: ['\'self\'', 'https://api.weixin.qq.com']
      }
    }
  })
);

app.use(
  cors({
    origin:
      process.env.NODE_ENV === 'production'
        ? ['https://your-domain.com', 'https://admin.your-domain.com']
        : [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002'
        ],
    credentials: true
  })
);

app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 会话配置
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'smart_goose_saas_2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      httpOnly: true
    }
  })
);

// 请求日志中间件
app.use(requestLogger);

// 统一响应格式中间件
app.use(responseMiddleware);

// 设置模板引擎
app.set('view engine', 'ejs');
app.use(expressLayouts);
app.set('layout extractScripts', true);
app.set('layout extractStyles', true);

// 静态文件服务配置
app.use('/static', express.static(path.join(__dirname, 'admin', 'public')));
app.use(
  '/saas-admin/static',
  express.static(path.join(__dirname, 'saas-admin', 'public'))
);
app.use('/assets', express.static(path.join(__dirname, '..', 'assets')));

// 模板辅助函数
app.locals.formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};

app.locals.formatDateTime = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleString('zh-CN');
};

app.locals.formatBytes = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 全局变量中间件
app.use((req, res, next) => {
  res.locals.currentPath = req.path;
  res.locals.moment = require('moment');
  res.locals.user = req.session.user || null;
  res.locals.appInfo = {
    name: 'Smart Goose SAAS',
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development'
  };
  next();
});

// 初始化SAAS平台数据库连接
tenantDatabaseManager.initSaasConnection().catch((error) => {
  Logger.error('SAAS平台数据库初始化失败:', error);
  process.exit(1);
});

// ============================================================================
// API路由配置 (微信小程序和租户API)
// ============================================================================

// 根路径 - API状态信息
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '智慧养鹅SAAS平台API服务',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    services: {
      api: 'Tenant API for WeChat Mini Program',
      admin: 'Tenant Admin Dashboard',
      saas: 'SAAS Platform Management'
    }
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 测试路由 - 验证我们的统一API路由是否工作
app.get('/test-unified-api', (req, res) => {
  res.json({
    success: true,
    message: '统一API路由正常工作',
    timestamp: Date.now()
  });
});

// ============================================================================
// 统一API路由配置 (基于RESTful规范)
// ============================================================================

// 启用统一API路由
app.use('/api', require('./routes/api-unified'));

// 旧版API路由 (兼容性支持，保持向后兼容)
app.use('/api/v1/auth', require('./routes/auth'));
app.use('/api/v1/home', require('./routes/home.routes'));

// 启用关键API路由
try {
  app.use('/api/v1/users', require('./routes/user.routes'));
} catch (error) {
  console.warn('⚠️ 用户路由加载失败:', error.message);
}

try {
  app.use('/api/v1/health', require('./routes/health.routes'));
} catch (error) {
  console.warn('⚠️ 健康路由加载失败:', error.message);
}

try {
  app.use('/api/v1/production-records', require('./routes/production-records.routes'));
} catch (error) {
  console.warn('⚠️ 生产记录路由加载失败:', error.message);
}

try {
  app.use('/api/v1/flocks', require('./routes/flock.routes'));
} catch (error) {
  console.warn('⚠️ 鹅群路由加载失败:', error.message);
}

// 多租户API路由 (面向小程序的多租户接口) - 暂时禁用
// app.use(
//   '/api/v1/tenant',
//   identifyTenant,
//   require('./routes/tenant-api.routes')
// );

// 🆕 小程序管理功能API路由 - 暂时禁用
// app.use('/api/v1/tenant/management', require('./routes/management.routes'));

// ============================================================================
// 🚫 租户Web管理后台已迁移至小程序端 - 相关路由已禁用
// ============================================================================

// 保留API文档相关功能 (仍然有用于开发和调试)
app.use('/admin', (req, res, next) => {
  app.set('views', path.join(__dirname, 'admin', 'views'));
  next();
});

// 保留的有用路由
app.use('/admin/api-docs', require('./admin/routes/api-docs.routes')); // 📚 API文档

// 租户Web管理功能已迁移至小程序端

// ============================================================================
// SAAS平台管理路由配置
// ============================================================================

// 设置SAAS管理视图路径
app.use('/saas-admin', (req, res, next) => {
  app.set('views', path.join(__dirname, 'saas-admin', 'views'));
  next();
});

// SAAS平台管理路由 - 暂时禁用
// app.use('/saas-admin', require('./saas-admin/routes/platform.routes'));
// app.use('/saas-admin', require('./saas-admin/routes/cross-tenant.routes')); // 跨租户数据监控
// app.use(
//   '/saas-admin/ai-config',
//   require('./saas-admin/routes/ai-config.routes')
// );
// app.use('/saas-admin/logs', require('./saas-admin/routes/logs.routes'));
// app.use(
//   '/saas-admin/knowledge',
//   require('./saas-admin/routes/knowledge.routes')
// );
// app.use(
//   '/saas-admin/announcements',
//   require('./saas-admin/routes/announcement.routes')
// );
// app.use('/saas-admin/pricing', require('./saas-admin/routes/price.routes'));

// ============================================================================
// 错误处理
// ============================================================================

// 404处理中间件
app.use(notFoundHandler);

// 统一错误处理中间件
app.use((err, req, res, next) => {
  // 管理后台错误处理
  if (req.originalUrl.startsWith('/admin')) {
    Logger.error('Admin error', {
      url: req.originalUrl,
      method: req.method,
      error: err.message,
      stack: err.stack
    });

    app.set('views', path.join(__dirname, 'admin', 'views'));
    return res.status(500).render('error', {
      title: '服务器错误 - 管理后台',
      layout: 'layout',
      error:
        process.env.NODE_ENV === 'development'
          ? err
          : { message: '服务器内部错误' }
    });
  }

  // SAAS管理后台错误处理
  if (req.originalUrl.startsWith('/saas-admin')) {
    Logger.error('SAAS Admin error', {
      url: req.originalUrl,
      method: req.method,
      error: err.message,
      stack: err.stack
    });

    if (req.accepts('html')) {
      app.set('views', path.join(__dirname, 'saas-admin', 'views'));
      return res.status(500).render('error', {
        title: '服务器错误 - SAAS管理平台',
        layout: false,
        error:
          process.env.NODE_ENV === 'development'
            ? err
            : { message: '服务器内部错误' }
      });
    }
  }

  // API错误处理
  globalErrorHandler(err, req, res, next);
});

// ============================================================================
// 服务器启动
// ============================================================================

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';

// 优雅关闭处理
process.on('SIGTERM', () => {
  Logger.info('收到SIGTERM信号，开始优雅关闭...');
  tenantDatabaseManager.closeAllConnections();
  process.exit(0);
});

process.on('SIGINT', () => {
  Logger.info('收到SIGINT信号，开始优雅关闭...');
  tenantDatabaseManager.closeAllConnections();
  process.exit(0);
});

// 启动服务器
const server = app.listen(PORT, HOST, () => {
  Logger.info('智慧养鹅SAAS平台启动成功', {
    banner: true,
    host: HOST,
    port: PORT,
    services: {
      api: `http://${HOST}:${PORT}/api/v1`,
      admin: `http://${HOST}:${PORT}/admin`,
      saas: `http://${HOST}:${PORT}/saas-admin`,
      docs: `http://${HOST}:${PORT}/admin/api-docs`,
      health: `http://${HOST}:${PORT}/health`
    },
    environment: process.env.NODE_ENV || 'development',
    startedAt: new Date().toLocaleString()
  });
});

// 设置服务器超时
server.timeout = 30000; // 30秒

module.exports = app;
