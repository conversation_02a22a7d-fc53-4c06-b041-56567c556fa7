#!/bin/bash

# Test script to install and configure Playwright MCP
echo "=== Playwright MCP Configuration Guide ==="
echo

echo "1. Installing Playwright MCP..."
echo "Run this command in your terminal (with proper permissions):"
echo "npm install -g @playwright/mcp playwright"
echo

echo "2. Alternative local installation:"
echo "cd /Volumes/DATA/千问/智慧养鹅全栈"
echo "npm install @playwright/mcp playwright --save-dev"
echo

echo "3. Test Playwright installation:"
echo "npx playwright --version"
echo

echo "4. Configuration files created:"
echo "- .mcp.json: Project-level MCP server configuration"
echo "- settings.local.json: Updated with Playwright permissions"
echo

echo "5. To use Playwright MCP in Claude Code:"
echo "- Restart Claude Code"
echo "- You should see mcp__playwright__* tools available"
echo "- Use tools like: mcp__playwright__navigate, mcp__playwright__click, etc."
echo

echo "=== Manual Installation Steps ==="
echo "If you encounter permission issues, run these commands in Terminal:"
echo
echo "# Fix npm permissions:"
echo "sudo chown -R \$(whoami) \$(npm config get prefix)/{lib/node_modules,bin,share}"
echo
echo "# Install Playwright:"
echo "npm install -g playwright @playwright/mcp"
echo
echo "# Install browser binaries:"
echo "npx playwright install"
echo

# Check current configuration
echo "=== Current Configuration Status ==="
if [ -f "/Volumes/DATA/千问/智慧养鹅全栈/.mcp.json" ]; then
    echo "✓ .mcp.json file exists"
else
    echo "✗ .mcp.json file missing"
fi

if grep -q "enableAllProjectMcpServers" "/Users/<USER>/.claude/settings.local.json" 2>/dev/null; then
    echo "✓ Claude settings updated with MCP server enablement"
else
    echo "✗ Claude settings need MCP server enablement"
fi

if grep -q "mcp__playwright__" "/Users/<USER>/.claude/settings.local.json" 2>/dev/null; then
    echo "✓ Playwright permissions added to Claude settings"
else
    echo "✗ Playwright permissions missing from Claude settings"
fi

echo
echo "=== Next Steps ==="
echo "1. Run the manual installation commands above in your terminal"
echo "2. Restart Claude Code"
echo "3. Test with a simple playwright command"
echo "4. You should see playwright tools available as mcp__playwright__*"