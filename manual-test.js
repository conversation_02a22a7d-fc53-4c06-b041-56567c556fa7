const axios = require('axios');

/**
 * 手动功能测试脚本
 * 验证SAAS后台管理系统关键功能
 */

const BASE_URL = 'http://localhost:4000';
const TEST_USER = {
    username: 'admin',
    password: 'admin123'
};

// 存储session cookie
let sessionCookie = '';

async function testLogin() {
    console.log('\n🔐 测试登录功能...');
    
    try {
        const response = await axios.post(`${BASE_URL}/auth/login`, {
            username: TEST_USER.username,
            password: TEST_USER.password
        });
        
        console.log('✅ 登录API响应:', response.status);
        
        // 获取session cookie
        const cookies = response.headers['set-cookie'];
        if (cookies) {
            sessionCookie = cookies.join('; ');
            console.log('✅ 获取到session');
        }
        
        return true;
    } catch (error) {
        console.error('❌ 登录失败:', error.response?.data || error.message);
        return false;
    }
}

async function testPage(path, pageName) {
    console.log(`\n📄 测试${pageName}页面...`);
    
    try {
        const response = await axios.get(`${BASE_URL}${path}`, {
            headers: {
                'Cookie': sessionCookie
            }
        });
        
        if (response.status === 200) {
            console.log(`✅ ${pageName}页面加载成功 (${response.status})`);
            
            // 检查页面内容
            const content = response.data;
            if (content.includes('<!DOCTYPE html')) {
                console.log(`✅ ${pageName}页面返回HTML内容`);
            }
            
            return true;
        } else {
            console.log(`⚠️ ${pageName}页面状态: ${response.status}`);
            return false;
        }
    } catch (error) {
        if (error.response?.status === 302) {
            console.log(`⚠️ ${pageName}页面重定向 (${error.response.status})`);
            return true;
        } else {
            console.error(`❌ ${pageName}页面失败:`, error.response?.status || error.message);
            return false;
        }
    }
}

async function testAPI(path, method = 'GET', data = null, apiName = '') {
    console.log(`\n🔗 测试API ${method} ${path} ${apiName ? '(' + apiName + ')' : ''}...`);
    
    try {
        const config = {
            method: method,
            url: `${BASE_URL}${path}`,
            headers: {
                'Cookie': sessionCookie,
                'Content-Type': 'application/json'
            }
        };
        
        if (data) {
            config.data = data;
        }
        
        const response = await axios(config);
        
        if (response.status >= 200 && response.status < 300) {
            console.log(`✅ API调用成功 (${response.status})`);
            return true;
        } else {
            console.log(`⚠️ API状态: ${response.status}`);
            return false;
        }
    } catch (error) {
        if (error.response?.status === 302) {
            console.log(`⚠️ API重定向 (${error.response.status})`);
            return true;
        } else {
            console.error(`❌ API调用失败:`, error.response?.status || error.message);
            return false;
        }
    }
}

async function runComprehensiveTest() {
    console.log('🚀 开始SAAS后台管理系统功能测试\n');
    console.log('=' * 50);
    
    let passedTests = 0;
    let totalTests = 0;
    
    // 1. 测试登录
    totalTests++;
    if (await testLogin()) {
        passedTests++;
    }
    
    // 如果登录失败，停止测试
    if (!sessionCookie) {
        console.log('\n❌ 登录失败，无法继续测试');
        return;
    }
    
    // 2. 测试核心页面
    const pages = [
        ['/', '首页'],
        ['/dashboard', '仪表板'],
        ['/users', '用户管理'],
        ['/tenants', '租户管理'],
        ['/flocks', '鹅群管理'],
        ['/production', '生产记录'],
        ['/health', '健康管理'],
        ['/finance', '财务管理'],
        ['/inventory', '库存管理'],
        ['/mall', '商城管理'],
        ['/goose-prices', '鹅价管理'],
        ['/reports', '报表中心'],
        ['/system', '系统设置'],
        ['/announcements', '公告管理'],
        ['/knowledge', '知识库']
    ];
    
    for (const [path, name] of pages) {
        totalTests++;
        if (await testPage(path, name)) {
            passedTests++;
        }
        
        // 短暂延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 3. 测试API端点
    const apis = [
        ['/api/dashboard/stats', 'GET', null, '仪表板统计'],
        ['/health', 'GET', null, '健康检查'],
    ];
    
    for (const [path, method, data, name] of apis) {
        totalTests++;
        if (await testAPI(path, method, data, name)) {
            passedTests++;
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 4. 测试子页面功能
    const subPages = [
        ['/tenants/create', '创建租户'],
        ['/goose-prices/trends', '价格趋势'],
        ['/mall/products', '商品管理'],
        ['/mall/orders', '订单管理'],
        ['/mall/categories', '分类管理'],
        ['/mall/inventory', '库存管理'],
        ['/tenants/subscriptions', '订阅管理'],
        ['/tenants/usage', '使用统计']
    ];
    
    for (const [path, name] of subPages) {
        totalTests++;
        if (await testPage(path, name)) {
            passedTests++;
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 5. 输出测试结果
    console.log('\n' + '=' * 50);
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
    console.log(`📈 成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有功能测试通过！系统运行正常！');
    } else {
        console.log(`\n⚠️ 有 ${totalTests - passedTests} 个功能存在问题，需要进一步检查`);
    }
    
    console.log('\n📝 测试完成时间:', new Date().toLocaleString());
}

// 执行测试
runComprehensiveTest().catch(console.error);