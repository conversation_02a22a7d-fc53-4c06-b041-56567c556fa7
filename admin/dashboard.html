<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智慧养鹅平台 - 后台管理</title>
  <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      background: #f5f7fa;
    }

    .admin-container {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 250px;
      background: #304156;
      transition: width 0.3s;
    }

    .sidebar.collapsed {
      width: 64px;
    }

    .sidebar-header {
      height: 60px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      border-bottom: 1px solid #434a50;
    }

    .logo {
      font-size: 24px;
      color: #fff;
      margin-right: 12px;
    }

    .logo-text {
      color: #fff;
      font-weight: bold;
      white-space: nowrap;
      transition: opacity 0.3s;
    }

    .collapsed .logo-text {
      opacity: 0;
      width: 0;
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .header {
      height: 60px;
      background: #fff;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }

    .header-left {
      display: flex;
      align-items: center;
    }

    .menu-toggle {
      font-size: 20px;
      cursor: pointer;
      margin-right: 20px;
      color: #606266;
    }

    .breadcrumb {
      color: #606266;
      font-size: 14px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .content-area {
      flex: 1;
      padding: 20px;
      overflow: auto;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .stat-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .stat-icon.pending {
      background: #f39c12;
    }

    .stat-icon.approved {
      background: #27ae60;
    }

    .stat-icon.rejected {
      background: #e74c3c;
    }

    .stat-icon.total {
      background: #3498db;
    }

    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .stat-label {
      color: #7f8c8d;
      font-size: 14px;
    }

    .chart-section {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
      margin-bottom: 30px;
    }

    .chart-card {
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .chart-title {
      font-size: 18px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .recent-applications {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .application-header {
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: between;
      align-items: center;
    }

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #2c3e50;
    }

    .view-all-btn {
      color: #409eff;
      text-decoration: none;
      font-size: 14px;
    }

    .application-list {
      padding: 0;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="admin-container">
      <!-- 侧边栏 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="logo">🦢</div>
          <div class="logo-text">智慧养鹅</div>
        </div>

        <el-menu :default-active="activeMenu" :collapse="sidebarCollapsed" background-color="#304156"
          text-color="#bfcbd9" active-text-color="#409eff" router>

          <el-menu-item index="/dashboard" @click="navigateTo('dashboard')">
            <el-icon>
              <House />
            </el-icon>
            <span>仪表板</span>
          </el-menu-item>

          <el-sub-menu index="approval">
            <template #title>
              <el-icon>
                <Document />
              </el-icon>
              <span>审批管理</span>
            </template>
            <el-menu-item index="/approval/pending" @click="navigateTo('approval-pending')">
              <el-icon>
                <Clock />
              </el-icon>
              <span>待审批</span>
            </el-menu-item>
            <el-menu-item index="/approval/history" @click="navigateTo('approval-history')">
              <el-icon>
                <Finished />
              </el-icon>
              <span>审批历史</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="tenant">
            <template #title>
              <el-icon><Office-building /></el-icon>
              <span>租户管理</span>
            </template>
            <el-menu-item index="/tenant/list" @click="navigateTo('tenant-list')">租户列表</el-menu-item>
            <el-menu-item index="/tenant/statistics" @click="navigateTo('tenant-stats')">数据统计</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="user">
            <template #title>
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/user/list" @click="navigateTo('user-list')">用户列表</el-menu-item>
            <el-menu-item index="/user/roles" @click="navigateTo('user-roles')">角色权限</el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/system" @click="navigateTo('system-config')">
            <el-icon>
              <Setting />
            </el-icon>
            <span>系统设置</span>
          </el-menu-item>

          <el-menu-item index="/logs" @click="navigateTo('system-logs')">
            <el-icon><Document-copy /></el-icon>
            <span>操作日志</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 顶部头部 -->
        <div class="header">
          <div class="header-left">
            <div class="menu-toggle" @click="toggleSidebar">
              <el-icon>
                <Menu />
              </el-icon>
            </div>
            <div class="breadcrumb">{{ currentPageTitle }}</div>
          </div>

          <div class="header-right">
            <el-badge :value="unreadNotifications" class="notification-badge">
              <el-icon size="20" style="cursor: pointer;">
                <Bell />
              </el-icon>
            </el-badge>

            <el-dropdown @command="handleUserCommand">
              <div style="display: flex; align-items: center; cursor: pointer;">
                <el-avatar :size="32" src="/images/admin-avatar.png"></el-avatar>
                <span style="margin-left: 8px;">{{ adminInfo.name }}</span>
                <el-icon style="margin-left: 4px;">
                  <ArrowDown />
                </el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="password">修改密码</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 仪表板页面 -->
          <div v-if="currentPage === 'dashboard'">
            <!-- 统计卡片 -->
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-header">
                  <div>
                    <div class="stat-number">{{ statistics.pending.total }}</div>
                    <div class="stat-label">待审批申请</div>
                  </div>
                  <div class="stat-icon pending">📋</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-header">
                  <div>
                    <div class="stat-number">{{ statistics.today.approved }}</div>
                    <div class="stat-label">今日已审批</div>
                  </div>
                  <div class="stat-icon approved">✅</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-header">
                  <div>
                    <div class="stat-number">{{ statistics.today.rejected }}</div>
                    <div class="stat-label">今日已拒绝</div>
                  </div>
                  <div class="stat-icon rejected">❌</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-header">
                  <div>
                    <div class="stat-number">{{ statistics.pending.tenant + statistics.pending.user }}</div>
                    <div class="stat-label">总申请数</div>
                  </div>
                  <div class="stat-icon total">📊</div>
                </div>
              </div>
            </div>

            <!-- 图表区域 -->
            <div class="chart-section">
              <div class="chart-card">
                <div class="chart-title">审批趋势图</div>
                <div id="trendChart" style="height: 300px;"></div>
              </div>

              <div class="chart-card">
                <div class="chart-title">申请类型分布</div>
                <div id="typeChart" style="height: 300px;"></div>
              </div>
            </div>

            <!-- 最近申请 -->
            <div class="recent-applications">
              <div class="application-header">
                <div class="section-title">最近申请</div>
                <a href="#" class="view-all-btn" @click="navigateTo('approval-pending')">查看全部</a>
              </div>

              <el-table :data="recentApplications" style="width: 100%">
                <el-table-column prop="applicationType" label="申请类型" width="120">
                  <template #default="scope">
                    <el-tag :type="scope.row.application_type === 'tenant' ? 'primary' : 'success'">
                      {{ scope.row.application_type === 'tenant' ? '租户申请' : '用户申请' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="farm_name" label="申请人/养殖场" min-width="200">
                  <template #default="scope">
                    {{ scope.row.farm_name || scope.row.real_name }}
                  </template>
                </el-table-column>

                <el-table-column prop="contact_info" label="联系方式" width="150">
                  <template #default="scope">
                    {{ scope.row.tenant_phone || scope.row.user_phone }}
                  </template>
                </el-table-column>

                <el-table-column prop="created_at" label="申请时间" width="180">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.created_at) }}
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag type="warning">待审批</el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button size="small" type="primary" @click="approveApplication(scope.row, 'approve')">
                      通过
                    </el-button>
                    <el-button size="small" type="danger" @click="approveApplication(scope.row, 'reject')">
                      拒绝
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 其他页面内容 -->
          <div v-else>
            <el-result icon="info" title="页面开发中" sub-title="该功能页面正在开发中，敬请期待！">
              <template #extra>
                <el-button type="primary" @click="navigateTo('dashboard')">返回仪表板</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>
    </div>

    <!-- 审批确认对话框 -->
    <el-dialog v-model="approvalDialog.visible" :title="approvalDialog.title" width="500px">
      <el-form :model="approvalDialog.form" label-width="80px">
        <el-form-item label="审批意见">
          <el-input v-model="approvalDialog.form.comment" type="textarea" :rows="4" placeholder="请输入审批意见（可选）">
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="approvalDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmApproval" :loading="approvalDialog.loading">
          确认{{ approvalDialog.action === 'approve' ? '通过' : '拒绝' }}
        </el-button>
      </template>
    </el-dialog>
  </div>

  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.iife.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>

  <script>
    const { createApp, reactive, ref, onMounted } = Vue;

    createApp({
      setup() {
        const sidebarCollapsed = ref(false);
        const currentPage = ref('dashboard');
        const activeMenu = ref('/dashboard');
        const unreadNotifications = ref(5);

        const adminInfo = reactive({
          name: '系统管理员',
          role: 'admin'
        });

        const statistics = reactive({
          pending: {
            tenant: 0,
            user: 0,
            total: 0
          },
          today: {
            approved: 0,
            rejected: 0
          }
        });

        const recentApplications = ref([]);

        const approvalDialog = reactive({
          visible: false,
          title: '',
          action: '',
          record: null,
          loading: false,
          form: {
            comment: ''
          }
        });

        const currentPageTitle = ref('仪表板');

        // 切换侧边栏
        const toggleSidebar = () => {
          sidebarCollapsed.value = !sidebarCollapsed.value;
        };

        // 页面导航
        const navigateTo = (page) => {
          currentPage.value = page;
          activeMenu.value = '/' + page.replace('-', '/');

          const pageTitles = {
            'dashboard': '仪表板',
            'approval-pending': '待审批申请',
            'approval-history': '审批历史',
            'tenant-list': '租户列表',
            'tenant-stats': '租户统计',
            'user-list': '用户列表',
            'user-roles': '角色权限',
            'system-config': '系统设置',
            'system-logs': '操作日志'
          };

          currentPageTitle.value = pageTitles[page] || '未知页面';
        };

        // 处理用户下拉菜单命令
        const handleUserCommand = (command) => {
          switch (command) {
            case 'profile':
              ElMessage.info('个人信息功能开发中');
              break;
            case 'password':
              ElMessage.info('修改密码功能开发中');
              break;
            case 'logout':
              handleLogout();
              break;
          }
        };

        // 退出登录
        const handleLogout = () => {
          ElMessageBox.confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            localStorage.removeItem('admin_token');
            window.location.href = '/admin/login.html';
          });
        };

        // 加载统计数据
        const loadStatistics = async () => {
          try {
            const token = localStorage.getItem('admin_token');
            const response = await axios.get('/api/admin/statistics', {
              headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
              // 更新统计数据，适配新的API响应格式
              const data = response.data.data;
              statistics.pending.tenant = data.pending.tenant;
              statistics.pending.user = data.pending.user;
              statistics.pending.total = data.pending.total;
              statistics.today.approved = data.today.approved;
              statistics.today.rejected = data.today.rejected;
            }
          } catch (error) {
            console.error('加载统计数据失败:', error);
            // 使用模拟数据作为后备
            statistics.pending.total = 12;
            statistics.today.approved = 8;
            statistics.today.rejected = 2;
          }
        };

        // 加载最近申请
        const loadRecentApplications = async () => {
          try {
            const token = localStorage.getItem('admin_token');
            const response = await axios.get('/api/admin/pending?limit=10', {
              headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
              recentApplications.value = response.data.data.list;
            }
          } catch (error) {
            console.error('加载最近申请失败:', error);
            // 使用模拟数据作为后备
            recentApplications.value = [
              {
                application_type: 'tenant',
                approval_id: 1,
                farm_name: '示例农场申请',
                tenant_phone: '13800138001',
                real_name: '张三',
                created_at: new Date().toISOString()
              }
            ];
          }
        };

        // 审批申请
        const approveApplication = (record, action) => {
          approvalDialog.record = record;
          approvalDialog.action = action;
          approvalDialog.title = action === 'approve' ? '审批通过' : '审批拒绝';
          approvalDialog.visible = true;
          approvalDialog.form.comment = '';
        };

        // 确认审批
        const confirmApproval = async () => {
          try {
            approvalDialog.loading = true;
            const token = localStorage.getItem('admin_token');

            const response = await axios.post(
              `/api/admin/${approvalDialog.record.approval_id}/approve`,
              {
                action: approvalDialog.action,
                comment: approvalDialog.form.comment
              },
              {
                headers: { Authorization: `Bearer ${token}` }
              }
            );

            if (response.data.success) {
              ElMessage.success(`${approvalDialog.action === 'approve' ? '审批通过' : '审批拒绝'}成功`);
              approvalDialog.visible = false;

              // 重新加载数据
              await Promise.all([loadStatistics(), loadRecentApplications()]);
            } else {
              throw new Error(response.data.message);
            }
          } catch (error) {
            ElMessage.error(error.response?.data?.message || error.message || '操作失败');
          } finally {
            approvalDialog.loading = false;
          }
        };

        // 格式化日期时间
        const formatDateTime = (dateString) => {
          if (!dateString) return '';
          const date = new Date(dateString);
          return date.toLocaleString('zh-CN');
        };

        // 组件挂载时加载数据
        onMounted(async () => {
          // 检查登录状态
          const token = localStorage.getItem('admin_token');
          if (!token) {
            window.location.href = '/admin/login.html';
            return;
          }

          // 加载数据
          await Promise.all([
            loadStatistics(),
            loadRecentApplications()
          ]);
        });

        return {
          sidebarCollapsed,
          currentPage,
          activeMenu,
          currentPageTitle,
          unreadNotifications,
          adminInfo,
          statistics,
          recentApplications,
          approvalDialog,
          toggleSidebar,
          navigateTo,
          handleUserCommand,
          handleLogout,
          approveApplication,
          confirmApproval,
          formatDateTime
        };
      }
    }).use(ElementPlus).mount('#app');

    // 注册图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }
  </script>
</body>

</html>