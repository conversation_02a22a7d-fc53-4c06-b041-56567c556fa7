<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养鹅平台 - 后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Helvetica Neue', Arial, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 900px;
            height: 500px;
            display: flex;
        }

        .login-banner {
            flex: 1;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            position: relative;
        }

        .login-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .banner-content {
            z-index: 1;
            text-align: center;
        }

        .banner-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .banner-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .banner-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .login-form {
            flex: 1;
            padding: 3rem 2.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .form-subtitle {
            color: #666;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .login-input {
            margin-bottom: 1.5rem;
        }

        .login-input .el-input__inner {
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            font-size: 14px;
        }

        .login-input .el-input__inner:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .login-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            border-radius: 8px;
            padding: 14px;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .login-button:hover {
            background: linear-gradient(45deg, #45a049, #3e8e41);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .forgot-password {
            text-align: center;
            color: #666;
            font-size: 0.85rem;
            text-decoration: none;
            transition: color 0.3s;
        }

        .forgot-password:hover {
            color: #4CAF50;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .login-container {
                width: 95%;
                height: auto;
                flex-direction: column;
            }

            .login-banner {
                height: 200px;
            }

            .login-form {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <!-- 左侧宣传区域 -->
            <div class="login-banner">
                <div class="banner-content">
                    <div class="banner-icon">🦢</div>
                    <div class="banner-title">智慧养鹅平台</div>
                    <div class="banner-subtitle">
                        Professional Goose Farming<br>
                        Management System
                    </div>
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="login-form">
                <h2 class="form-title">后台管理登录</h2>
                <p class="form-subtitle">管理员登录入口，请输入您的账号密码</p>

                <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" @keyup.enter="handleLogin">
                    <el-form-item prop="username">
                        <el-input 
                            v-model="loginForm.username" 
                            placeholder="请输入管理员账号"
                            prefix-icon="User"
                            size="large"
                            class="login-input">
                        </el-input>
                    </el-form-item>

                    <el-form-item prop="password">
                        <el-input 
                            v-model="loginForm.password" 
                            type="password" 
                            placeholder="请输入登录密码"
                            prefix-icon="Lock"
                            size="large"
                            class="login-input"
                            show-password>
                        </el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="primary" 
                            size="large" 
                            class="login-button"
                            style="width: 100%"
                            :loading="loading"
                            @click="handleLogin">
                            {{ loading ? '登录中...' : '登录' }}
                        </el-button>
                    </el-form-item>
                </el-form>

                <a href="#" class="forgot-password">忘记密码？</a>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div v-if="loading" class="loading-overlay">
            <el-icon class="is-loading" style="margin-right: 10px;"><Loading /></el-icon>
            正在验证登录信息...
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.iife.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>

    <script>
        const { createApp, reactive, ref } = Vue;

        createApp({
            setup() {
                const loginFormRef = ref();
                const loading = ref(false);

                const loginForm = reactive({
                    username: localStorage.getItem('admin_username') || '',
                    password: '',
                    remember: !!localStorage.getItem('admin_username')
                });

                const loginRules = {
                    username: [
                        { required: true, message: '请输入管理员账号', trigger: 'blur' },
                        { min: 3, message: '账号长度至少3个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入登录密码', trigger: 'blur' },
                        { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
                    ]
                };

                // 处理登录
                const handleLogin = async () => {
                    try {
                        const valid = await loginFormRef.value.validate();
                        if (!valid) return;

                        loading.value = true;

                        const response = await axios.post('/api/admin/login', {
                            username: loginForm.username,
                            password: loginForm.password
                        });

                        if (response.data.success) {
                            // 保存token
                            localStorage.setItem('admin_token', response.data.data.token);
                            
                            // 记住密码
                            if (loginForm.remember) {
                                localStorage.setItem('admin_username', loginForm.username);
                            } else {
                                localStorage.removeItem('admin_username');
                            }

                            ElMessage.success('登录成功，正在跳转...');
                            
                            // 跳转到管理后台
                            setTimeout(() => {
                                window.location.href = '/admin/dashboard.html';
                            }, 1000);
                        } else {
                            throw new Error(response.data.message || '登录失败');
                        }

                    } catch (error) {
                        console.error('登录失败:', error);
                        ElMessage.error(error.response?.data?.message || error.message || '登录失败，请重试');
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loginFormRef,
                    loginForm,
                    loginRules,
                    loading,
                    handleLogin
                };
            }
        }).use(ElementPlus).mount('#app');

        // 注册图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    </script>
</body>
</html>