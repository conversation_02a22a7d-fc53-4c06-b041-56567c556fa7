// 测试修复后的模块功能
const axios = require('axios');

const baseURL = 'http://localhost:4000';
let cookies = '';

async function login() {
    try {
        const loginData = {
            username: 'admin',
            password: 'admin123'
        };

        const loginResponse = await axios.post(`${baseURL}/auth/login`, loginData, {
            maxRedirects: 0,
            validateStatus: (status) => status < 400
        });

        if (loginResponse.headers['set-cookie']) {
            cookies = loginResponse.headers['set-cookie']
                .map(cookie => cookie.split(';')[0])
                .join('; ');
        }

        console.log('✅ 登录成功');
        return true;
    } catch (error) {
        console.log('❌ 登录失败:', error.response?.status, error.response?.statusText);
        return false;
    }
}

async function testModule(path, moduleName) {
    try {
        const response = await axios.get(`${baseURL}${path}`, {
            headers: {
                'Cookie': cookies
            }
        });

        if (response.status === 200) {
            console.log(`✅ ${moduleName} 页面访问成功`);
            
            // 检查是否有错误信息
            const html = response.data;
            if (html.includes('Server Error') || html.includes('is not defined')) {
                console.log(`⚠️  ${moduleName} 页面存在JavaScript错误`);
                return false;
            } else if (html.includes('Page Not Found')) {
                console.log(`❌ ${moduleName} 页面未找到`);
                return false;
            } else {
                console.log(`✅ ${moduleName} 页面内容正常`);
                return true;
            }
        }
    } catch (error) {
        console.log(`❌ ${moduleName} 访问失败:`, error.response?.status, error.response?.statusText);
        return false;
    }
}

async function runTests() {
    console.log('🚀 开始测试修复后的模块功能...\n');
    
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.log('无法继续测试，登录失败');
        return;
    }

    // 测试各个模块
    const testCases = [
        { path: '/mall/categories', name: '商城分类管理' },
        { path: '/goose-prices', name: '鹅价管理' },
        { path: '/tenants/usage', name: '租户使用统计' },
        { path: '/tenants', name: '租户管理' },
        { path: '/users', name: '用户管理' },
        { path: '/flocks', name: '鹅群管理' },
        { path: '/production', name: '生产管理' },
        { path: '/health', name: '健康管理' },
        { path: '/finance', name: '财务管理' }
    ];

    let successCount = 0;
    for (const testCase of testCases) {
        const result = await testModule(testCase.path, testCase.name);
        if (result) successCount++;
        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
    }

    console.log(`\n📊 测试完成: ${successCount}/${testCases.length} 个模块正常工作`);
    const successRate = ((successCount / testCases.length) * 100).toFixed(1);
    console.log(`📈 成功率: ${successRate}%`);
}

runTests().catch(console.error);