<!--components/ai-finance-analysis/ai-finance-analysis.wxml-->
<!-- AI智能分析组件 - 优化版 -->
<view class="ai-analysis-container">
  <!-- AI智能分析标题区域 -->
  <view class="ai-analysis-header">
    <view class="ai-header-icon">🤖</view>
    <text class="ai-header-title">AI智能分析</text>
  </view>
  
  <!-- 四个功能模块 -->
  <view class="ai-modules-grid">
    <!-- 财务分析 -->
    <view class="ai-module-item" 
          bindtap="onModuleTap" 
          data-type="financial-analysis">
      <view class="module-icon-container" style="background-color: rgba(0, 102, 204, 0.1);">
        <text class="module-icon" style="color: #0066CC;">📊</text>
      </view>
      <view class="module-content">
        <text class="module-title">财务分析</text>
        <text class="module-desc">深度分析财务状况</text>
      </view>
    </view>
    
    <!-- 财务预测 -->
    <view class="ai-module-item" 
          bindtap="onModuleTap" 
          data-type="financial-forecast">
      <view class="module-icon-container" style="background-color: rgba(233, 30, 99, 0.1);">
        <text class="module-icon" style="color: #e91e63;">🔮</text>
      </view>
      <view class="module-content">
        <text class="module-title">财务预测</text>
        <text class="module-desc">预测未来3-12月</text>
      </view>
    </view>
    
    <!-- 智能建议 -->
    <view class="ai-module-item" 
          bindtap="onModuleTap" 
          data-type="smart-suggestions">
      <view class="module-icon-container" style="background-color: rgba(255, 193, 7, 0.1);">
        <text class="module-icon" style="color: #ffc107;">💡</text>
      </view>
      <view class="module-content">
        <text class="module-title">智能建议</text>
        <text class="module-desc">个性化改进方案</text>
      </view>
    </view>
    
    <!-- 导出报表 -->
    <view class="ai-module-item" 
          bindtap="onModuleTap" 
          data-type="export-reports">
      <view class="module-icon-container" style="background-color: rgba(96, 125, 139, 0.1);">
        <text class="module-icon" style="color: #607D8B;">📋</text>
      </view>
      <view class="module-content">
        <text class="module-title">导出报表</text>
        <text class="module-desc">生成详细报告</text>
      </view>
    </view>
  </view>
  
  <!-- 时间选择区域 -->
  <view class="date-selector-section">
    <view class="date-range-container">
      <view class="date-label">开始日期</view>
      <picker mode="date" 
              value="{{startDate}}" 
              bindchange="onStartDateChange"
              class="date-picker">
        <view class="picker-content">
          <text class="date-text">{{startDate}}</text>
          <text class="date-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="date-range-container">
      <view class="date-label">结束日期</view>
      <picker mode="date" 
              value="{{endDate}}" 
              bindchange="onEndDateChange"
              class="date-picker">
        <view class="picker-content">
          <text class="date-text">{{endDate}}</text>
          <text class="date-icon">📅</text>
        </view>
      </picker>
    </view>
  </view>
  
  <!-- 快捷筛选按钮 -->
  <view class="filter-buttons-container">
    <view class="filter-option {{selectedFilter === 'expense' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="expense">
      费用报表
    </view>
    <view class="filter-option {{selectedFilter === 'income' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="income">
      收入报表
    </view>
    <view class="filter-option {{selectedFilter === 'category' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="category">
      分类统计
    </view>
    <view class="filter-option {{selectedFilter === 'trend' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="trend">
      趋势分析
    </view>
  </view>
  
  <!-- 财务汇总信息 -->
  <view class="finance-summary-section">
    <view class="summary-title">财务汇总</view>
    <view class="summary-grid">
      <view class="summary-item">
        <text class="summary-label">总收入</text>
        <text class="summary-value income">¥{{totalIncome || '0.00'}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">总支出</text>
        <text class="summary-value expense">¥{{totalExpense || '0.00'}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">待审批</text>
        <text class="summary-value pending">¥{{pendingAmount || '0.00'}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">已通过</text>
        <text class="summary-value approved">¥{{approvedAmount || '0.00'}}</text>
      </view>
    </view>
  </view>
</view>