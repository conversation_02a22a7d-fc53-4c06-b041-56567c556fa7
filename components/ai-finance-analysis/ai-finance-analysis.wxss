/* components/ai-finance-analysis/ai-finance-analysis.wxss */

/* AI智能分析容器 - 与顶栏颜色一致的背景 */
.ai-analysis-container {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin: var(--space-lg);
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

/* AI分析标题区域 */
.ai-analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.ai-header-icon {
  font-size: 48rpx;
  margin-right: var(--space-lg);
  animation: float 2s ease-in-out infinite;
}

.ai-header-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-inverse);
  letter-spacing: 1rpx;
}

/* AI模块网格 - 2x2布局 */
.ai-modules-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

/* AI模块项目 */
.ai-module-item {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  min-height: 120rpx;
}

.ai-module-item:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.25);
}

/* 模块图标容器 */
.module-icon-container {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-md);
  flex-shrink: 0;
}

.module-icon {
  font-size: 32rpx;
}

/* 模块内容 */
.module-content {
  flex: 1;
  min-width: 0;
}

.module-title {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-inverse);
  margin-bottom: var(--space-xs);
  line-height: 1.2;
}

.module-desc {
  display: block;
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
}

/* 日期选择器区域 */
.date-selector-section {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.date-range-container {
  flex: 1;
}

.date-label {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-sm);
}

.date-picker {
  width: 100%;
}

.picker-content {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.picker-content:active {
  background-color: rgba(255, 255, 255, 0.25);
}

.date-text {
  font-size: var(--text-base);
  color: var(--text-inverse);
  font-weight: 500;
}

.date-icon {
  font-size: 32rpx;
  opacity: 0.8;
}

/* 筛选按钮容器 */
.filter-buttons-container {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.filter-option {
  padding: var(--space-sm) var(--space-lg);
  background-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  flex: 1;
  text-align: center;
  min-width: 140rpx;
  white-space: nowrap;
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-option.active {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--primary);
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 财务汇总区域 */
.finance-summary-section {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.summary-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-inverse);
  margin-bottom: var(--space-lg);
  text-align: center;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.summary-label {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-sm);
}

.summary-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-inverse);
}

.summary-value.income {
  color: #4CAF50;
}

.summary-value.expense {
  color: #FF5722;
}

.summary-value.pending {
  color: #FFC107;
}

.summary-value.approved {
  color: #2196F3;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6rpx);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .ai-analysis-container {
    margin: var(--space-md);
    padding: var(--space-lg);
  }
  
  .ai-modules-grid {
    gap: var(--space-md);
  }
  
  .ai-module-item {
    min-height: 100rpx;
    padding: var(--space-md);
  }
  
  .module-icon-container {
    width: 56rpx;
    height: 56rpx;
  }
  
  .module-icon {
    font-size: 28rpx;
  }
  
  .module-title {
    font-size: var(--text-sm);
  }
  
  .module-desc {
    font-size: var(--text-xs);
  }
  
  .date-selector-section {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .filter-buttons-container {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .filter-option {
    min-width: auto;
    flex: none;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
}