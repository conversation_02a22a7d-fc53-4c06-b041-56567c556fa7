// components/ai-finance-analysis/ai-finance-analysis.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 外部传入的财务数据
    financeData: {
      type: Object,
      value: {}
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 用户权限级别
    userRole: {
      type: String,
      value: 'employee'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认时间范围
    startDate: '',
    endDate: '',
    
    // 当前选中的筛选条件
    selectedFilter: 'expense',
    
    // 财务汇总数据
    totalIncome: '0.00',
    totalExpense: '0.00', 
    pendingAmount: '0.00',
    approvedAmount: '0.00'
  },

  /**
   * 组件生命周期函数
   */
  lifetimes: {
    attached() {
      this.initializeDates();
      this.loadFinanceSummary();
    },
    
    ready() {
      // 组件布局完成后的逻辑
      this.triggerEvent('ready');
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化日期范围
     */
    initializeDates() {
      const today = new Date();
      const endDate = this.formatDate(today);
      
      // 默认显示最近3个月的数据
      const startDateObj = new Date(today);
      startDateObj.setMonth(startDateObj.getMonth() - 3);
      const startDate = this.formatDate(startDateObj);
      
      this.setData({
        startDate,
        endDate
      });
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    /**
     * 加载财务汇总数据
     */
    async loadFinanceSummary() {
      try {
        // 这里可以调用API获取实际数据
        // 暂时使用模拟数据
        const summaryData = {
          totalIncome: '128,500.00',
          totalExpense: '95,200.00',
          pendingAmount: '12,800.00',
          approvedAmount: '82,400.00'
        };
        
        this.setData({
          ...summaryData
        });
        
        // 触发数据更新事件
        this.triggerEvent('summaryUpdated', summaryData);
      } catch (error) {
        console.error('加载财务汇总数据失败:', error);
      }
    },

    /**
     * AI模块点击事件
     */
    onModuleTap(e) {
      const { type } = e.currentTarget.dataset;
      console.log('AI模块点击:', type);
      
      // 根据不同的模块类型触发不同的事件
      this.triggerEvent('moduleClick', {
        type,
        startDate: this.data.startDate,
        endDate: this.data.endDate,
        filter: this.data.selectedFilter
      });
    },

    /**
     * 开始日期改变
     */
    onStartDateChange(e) {
      const startDate = e.detail.value;
      this.setData({ startDate });
      
      // 验证日期范围
      this.validateDateRange();
      
      // 触发日期改变事件
      this.triggerEvent('dateRangeChanged', {
        startDate,
        endDate: this.data.endDate
      });
      
      // 重新加载数据
      this.loadFinanceSummary();
    },

    /**
     * 结束日期改变
     */
    onEndDateChange(e) {
      const endDate = e.detail.value;
      this.setData({ endDate });
      
      // 验证日期范围
      this.validateDateRange();
      
      // 触发日期改变事件
      this.triggerEvent('dateRangeChanged', {
        startDate: this.data.startDate,
        endDate
      });
      
      // 重新加载数据
      this.loadFinanceSummary();
    },

    /**
     * 验证日期范围
     */
    validateDateRange() {
      const { startDate, endDate } = this.data;
      
      if (new Date(startDate) > new Date(endDate)) {
        wx.showToast({
          title: '开始日期不能大于结束日期',
          icon: 'none',
          duration: 2000
        });
        return false;
      }
      
      // 检查日期范围是否超过1年
      const diffTime = new Date(endDate) - new Date(startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays > 365) {
        wx.showToast({
          title: '日期范围不能超过1年',
          icon: 'none',
          duration: 2000
        });
        return false;
      }
      
      return true;
    },

    /**
     * 筛选条件点击
     */
    onFilterTap(e) {
      const { filter } = e.currentTarget.dataset;
      
      this.setData({ selectedFilter: filter });
      
      // 触发筛选改变事件
      this.triggerEvent('filterChanged', {
        filter,
        startDate: this.data.startDate,
        endDate: this.data.endDate
      });
      
      // 重新加载数据
      this.loadFinanceSummary();
    },

    /**
     * 外部调用的数据更新方法
     */
    updateSummaryData(data) {
      this.setData({
        totalIncome: data.totalIncome || '0.00',
        totalExpense: data.totalExpense || '0.00',
        pendingAmount: data.pendingAmount || '0.00',
        approvedAmount: data.approvedAmount || '0.00'
      });
    },

    /**
     * 获取当前选中的参数
     */
    getCurrentParams() {
      return {
        startDate: this.data.startDate,
        endDate: this.data.endDate,
        selectedFilter: this.data.selectedFilter
      };
    }
  }
})