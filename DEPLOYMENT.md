# 智慧养鹅平台全栈部署指南

## 📋 项目架构概览

### 系统组成
- **小程序端**: 微信小程序，用户端应用
- **后端API**: Node.js + Express + MySQL
- **后台管理**: 基于Vue3 + Element Plus的Web管理系统
- **数据库**: MySQL 8.0+

### 权限系统架构
- **平台管理员**: 审批养殖场和用户申请
- **养殖场管理员**: 管理养殖场和员工
- **养殖场员工**: 日常操作权限

## 🚀 部署步骤

### 1. 数据库部署

#### 安装MySQL
```bash
# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### 创建数据库
```sql
CREATE DATABASE smart_goose DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'goose_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON smart_goose.* TO 'goose_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 导入数据库结构
```bash
mysql -u goose_user -p smart_goose < database/schema.sql
```

### 2. 后端API部署

#### 环境要求
- Node.js 16.0+
- npm 8.0+

#### 安装依赖
```bash
cd backend
npm install
```

#### 环境配置
创建 `.env` 文件：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=goose_user
DB_PASSWORD=your_secure_password
DB_NAME=smart_goose

# JWT密钥
JWT_SECRET=your_jwt_secret_key_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_ADMIN_SECRET=your_admin_jwt_secret_key_here

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 服务器配置
PORT=3000
NODE_ENV=production

# CORS配置
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
```

#### 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start

# 使用PM2管理进程
pm2 start ecosystem.config.js
```

#### PM2配置文件 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'smart-goose-api',
    script: 'app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### 3. 后台管理系统部署

#### 配置Web服务器 (Nginx)
```nginx
server {
    listen 80;
    server_name admin.yourdomain.com;
    root /var/www/smart-goose/admin;
    index login.html;

    location / {
        try_files $uri $uri/ /login.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 部署静态文件
```bash
# 复制管理后台文件到Web目录
sudo cp -r admin/* /var/www/smart-goose/admin/
sudo chown -R nginx:nginx /var/www/smart-goose/admin/
sudo chmod -R 755 /var/www/smart-goose/admin/
```

### 4. 小程序端配置

#### 配置API地址
在 `utils/wechat-auth-system.js` 中更新API地址：
```javascript
constructor() {
    this.baseUrl = 'https://api.yourdomain.com'; // 生产环境API地址
    this.currentUser = null;
}
```

#### 微信小程序配置
1. 在微信公众平台配置服务器域名
2. 配置request合法域名：`https://api.yourdomain.com`
3. 上传小程序代码并提交审核

## 🔧 初始化配置

### 1. 创建平台管理员
```sql
INSERT INTO platform_admins (username, password, name, email, role) VALUES 
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LEa52PVxmKT/6oVjG', '系统管理员', '<EMAIL>', 'super_admin');
-- 密码: 123456 (请在生产环境中修改)
```

### 2. 配置系统参数
```sql
UPDATE system_configs SET config_value = 'wx1234567890abcdef' WHERE config_key = 'wechat.app_id';
UPDATE system_configs SET config_value = 'your_wechat_app_secret' WHERE config_key = 'wechat.app_secret';
UPDATE system_configs SET config_value = '智慧养鹅管理平台' WHERE config_key = 'system.platform_name';
UPDATE system_configs SET config_value = '************' WHERE config_key = 'system.contact_phone';
```

## 🛡️ 安全配置

### 1. 防火墙配置
```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 2. SSL证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        # ... 其他proxy配置
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

### 3. 数据库安全
```bash
# 执行MySQL安全脚本
sudo mysql_secure_installation

# 配置my.cnf
[mysqld]
bind-address = 127.0.0.1
skip-networking = false
skip-name-resolve
```

## 📊 监控与日志

### 1. 日志配置
```javascript
// 在app.js中添加日志中间件
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

// 创建日志目录
const logDirectory = path.join(__dirname, 'logs');
fs.existsSync(logDirectory) || fs.mkdirSync(logDirectory);

// 创建访问日志流
const accessLogStream = fs.createWriteStream(path.join(logDirectory, 'access.log'), { flags: 'a' });

app.use(morgan('combined', { stream: accessLogStream }));
```

### 2. 系统监控
```bash
# 安装监控工具
npm install -g pm2
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## 🧪 测试验证

### 1. 后端API测试
```bash
# 测试数据库连接
curl -X GET http://localhost:3000/api/health

# 测试管理员登录
curl -X POST http://localhost:3000/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

### 2. 功能测试清单
- [ ] 小程序用户注册流程
- [ ] 养殖场申请提交
- [ ] 后台管理员审批
- [ ] 用户权限验证
- [ ] AI财务分析功能
- [ ] 数据备份恢复

## 🔄 备份与恢复

### 1. 数据库备份
```bash
#!/bin/bash
# 创建备份脚本 backup.sh
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="smart_goose_backup_$DATE.sql"

mkdir -p $BACKUP_DIR
mysqldump -u goose_user -p smart_goose > $BACKUP_DIR/$BACKUP_FILE
gzip $BACKUP_DIR/$BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

### 2. 定时备份
```bash
# 添加到crontab
0 2 * * * /path/to/backup.sh
```

## 📚 维护指南

### 1. 常用命令
```bash
# 查看PM2进程状态
pm2 status

# 重启应用
pm2 restart smart-goose-api

# 查看日志
pm2 logs smart-goose-api

# 监控资源使用
pm2 monit
```

### 2. 故障排查
- **数据库连接失败**: 检查数据库服务状态和配置
- **权限验证失败**: 检查JWT密钥配置
- **小程序无法登录**: 检查微信API配置和域名设置
- **管理后台无法访问**: 检查Nginx配置和静态文件权限

### 3. 性能优化
- 配置MySQL查询缓存
- 使用Redis缓存热点数据  
- 配置CDN加速静态资源
- 优化数据库索引

## 📞 技术支持

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.yourdomain.com
- 问题反馈: https://github.com/yourorg/smart-goose/issues

---

**部署完成后，请务必修改默认密码和密钥，确保系统安全！**