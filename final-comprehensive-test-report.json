{"timestamp": "2025-08-26T14:48:14.376Z", "testDuration": 308, "modules": [{"name": "仪表盘", "path": "/dashboard", "critical": true, "status": 200, "responseTime": 82, "contentLength": 43374, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "用户管理", "path": "/users", "critical": true, "status": 200, "responseTime": 6, "contentLength": 31579, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "租户管理", "path": "/tenants", "critical": true, "status": 200, "responseTime": 23, "contentLength": 36551, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "鹅群管理", "path": "/flocks", "critical": false, "status": 200, "responseTime": 5, "contentLength": 22382, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "生产管理", "path": "/production", "critical": false, "status": 200, "responseTime": 6, "contentLength": 22382, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "健康管理", "path": "/health", "critical": false, "status": 200, "responseTime": 6, "contentLength": 22382, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "财务管理", "path": "/finance", "critical": false, "status": 200, "responseTime": 3, "contentLength": 22994, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "库存管理", "path": "/inventory", "critical": false, "status": 200, "responseTime": 3, "contentLength": 23002, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "统计报告", "path": "/reports", "critical": true, "status": 200, "responseTime": 3, "contentLength": 22979, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "系统设置", "path": "/system", "critical": true, "status": 200, "responseTime": 3, "contentLength": 22978, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "鹅价管理", "path": "/goose-prices", "critical": false, "status": 200, "responseTime": 11, "contentLength": 61629, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "商城管理", "path": "/mall", "critical": false, "status": 200, "responseTime": 8, "contentLength": 26801, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "知识库", "path": "/knowledge", "critical": false, "status": 200, "responseTime": 7, "contentLength": 45893, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "公告管理", "path": "/announcements", "critical": false, "status": 200, "responseTime": 6, "contentLength": 53608, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "API管理", "path": "/api-management", "critical": true, "status": 200, "responseTime": 3, "contentLength": 29102, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "平台用户", "path": "/platform-users", "critical": true, "status": 200, "responseTime": 6, "contentLength": 31985, "success": true, "hasRealError": false, "errorDetails": null}], "apis": [{"name": "仪表盘统计API", "path": "/api/dashboard/stats", "status": 200, "responseTime": 3, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "用户列表API", "path": "/api/users/list", "status": 200, "responseTime": 1, "success": true, "hasRealError": false, "errorDetails": null}, {"name": "健康检查API", "path": "/api/health", "status": 200, "responseTime": 1, "success": true, "hasRealError": false, "errorDetails": null}], "summary": {"totalModules": 16, "successfulModules": 16, "failedModules": 0, "criticalModules": 7, "criticalSuccess": 7, "criticalFailed": 0, "totalApis": 3, "successfulApis": 3, "failedApis": 0, "overallSuccessRate": "100.0"}}