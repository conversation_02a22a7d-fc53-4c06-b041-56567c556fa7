<!--防疫任务列表页面-->
<view class="vaccination-container">
  <!-- 顶部统计栏 -->
  <view class="stats-bar">
    <view class="stat-item">
      <view class="stat-number">{{stats.total}}</view>
      <view class="stat-label">总任务</view>
    </view>
    <view class="stat-item">
      <view class="stat-number stat-pending">{{stats.pending}}</view>
      <view class="stat-label">待执行</view>
    </view>
    <view class="stat-item">
      <view class="stat-number stat-progress">{{stats.inProgress}}</view>
      <view class="stat-label">执行中</view>
    </view>
    <view class="stat-item">
      <view class="stat-number stat-completed">{{stats.completed}}</view>
      <view class="stat-label">已完成</view>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="onTabChange" data-index="0">
      今日任务
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="onTabChange" data-index="1">
      全部任务
    </view>
    <view class="tab-item {{currentTab === 2 ? 'active' : ''}}" bindtap="onTabChange" data-index="2">
      历史记录
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar" wx:if="{{currentTab !== 0}}">
    <view class="filter-section">
      <view class="filter-label">状态筛选:</view>
      <view class="filter-options">
        <view class="filter-option {{statusFilter === 'all' ? 'active' : ''}}" 
              bindtap="onStatusFilterChange" data-status="all">全部</view>
        <view class="filter-option {{statusFilter === 'pending' ? 'active' : ''}}" 
              bindtap="onStatusFilterChange" data-status="pending">待执行</view>
        <view class="filter-option {{statusFilter === 'completed' ? 'active' : ''}}" 
              bindtap="onStatusFilterChange" data-status="completed">已完成</view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-label">日期:</view>
      <picker mode="date" value="{{selectedDate}}" bindchange="onDateChange">
        <view class="date-picker">{{selectedDate || '选择日期'}}</view>
      </picker>
    </view>
  </view>

  <!-- 任务列表 -->
  <scroll-view class="task-list" scroll-y enable-flex refresher-enabled="{{true}}" 
               refresher-triggered="{{refreshing}}">
    <block wx:if="{{!loading && tasks.length > 0}}">
      <view class="task-item" wx:for="{{tasks}}" wx:key="task_id" bindtap="onTaskTap" data-task="{{item}}">
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-title">
            <text class="day-age">{{item.day_age}}</text>
            <text class="category-tag category-{{item.category}}">{{item.categoryText}}</text>
          </view>
          <view class="task-status">
            <text class="status-text" style="color: {{item.statusColor}}">{{item.statusText}}</text>
            <text class="priority-tag priority-{{item.priority}}">{{item.priorityText}}</text>
          </view>
        </view>

        <!-- 任务内容 -->
        <view class="task-content">
          <view class="prevention-disease" wx:if="{{item.prevention_disease}}">
            <text class="label">预防疾病:</text>
            <text class="content">{{item.prevention_disease}}</text>
          </view>
          <view class="medication" wx:if="{{item.medication}}">
            <text class="label">用药方案:</text>
            <text class="content">{{item.medication}}</text>
          </view>
        </view>

        <!-- 任务信息 -->
        <view class="task-info">
          <view class="info-item">
            <text class="icon">📅</text>
            <text class="text">{{item.formattedDate}}</text>
            <text class="today-tag" wx:if="{{item.isToday}}">今天</text>
            <text class="overdue-tag" wx:if="{{item.isOverdue}}">已逾期</text>
          </view>
          <view class="info-item" wx:if="{{item.total_geese}}">
            <text class="icon">🦢</text>
            <text class="text">{{item.total_geese}}只</text>
          </view>
          <view class="info-item" wx:if="{{item.cost}}">
            <text class="icon">💰</text>
            <text class="text">¥{{item.cost}}</text>
          </view>
        </view>

        <!-- 快速操作 -->
        <view class="task-actions" wx:if="{{item.status === 'pending'}}">
          <button class="action-btn complete-btn" size="mini" 
                  bindtap="onQuickComplete" data-task="{{item}}" catchtap="true">
            快速完成
          </button>
          <button class="action-btn detail-btn" size="mini" 
                  bindtap="goToTaskDetail" data-task="{{item}}" catchtap="true">
            查看详情
          </button>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && tasks.length === 0}}">
      <image class="empty-icon" src="/images/icons/empty.png"></image>
      <view class="empty-text">暂无防疫任务</view>
      <button class="add-plan-btn" bindtap="onAddPlan" wx:if="{{currentTab !== 0}}">
        创建防疫计划
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="bar-btn" bindtap="onAddPlan">
      <text class="btn-icon">➕</text>
      <text class="btn-text">新建计划</text>
    </button>
    <button class="bar-btn" bindtap="onViewStats">
      <text class="btn-icon">📊</text>
      <text class="btn-text">查看统计</text>
    </button>
  </view>
</view>