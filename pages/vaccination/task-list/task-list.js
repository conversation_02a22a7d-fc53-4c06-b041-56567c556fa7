// 防疫任务页面 
const { request } = require('../../utils/api');
const { formatDate, showToast, showLoading, hideLoading } = require('../../utils/util');

Page({
  data: {
    tasks: [],
    loading: false,
    selectedDate: '',
    statusFilter: 'all', // all, pending, in_progress, completed
    currentTab: 0, // 0: 今日任务, 1: 全部任务, 2: 历史记录
    stats: {
      total: 0,
      pending: 0,
      completed: 0,
      inProgress: 0
    },
    refreshing: false
  },

  onLoad(options) {
    const { flockId, date } = options;
    this.setData({ 
      flockId: flockId || '',
      selectedDate: date || formatDate(new Date(), 'YYYY-MM-DD')
    });
    this.loadTasks();
  },

  onShow() {
    this.loadTasks();
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadTasks().finally(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    });
  },

  // 加载防疫任务
  async loadTasks() {
    const { flockId, currentTab, selectedDate, statusFilter } = this.data;
    
    try {
      showLoading('加载中...');
      this.setData({ loading: true });

      let apiPath = '';
      let params = {};

      if (currentTab === 0) {
        // 今日任务
        apiPath = '/api/v2/vaccination/today-tasks/' + wx.getStorageSync('userId');
      } else if (flockId) {
        // 特定鹅群的任务
        apiPath = '/api/v2/vaccination/flocks/' + flockId + '/tasks';
        params = { status: statusFilter === 'all' ? undefined : statusFilter };
        if (selectedDate) params.date = selectedDate;
      } else {
        showToast('请先选择鹅群', 'error');
        return;
      }

      const response = await request({
        url: apiPath,
        method: 'GET',
        data: params
      });

      if (response.success) {
        this.processTaskData(response.data);
      } else {
        showToast(response.message || '加载失败', 'error');
      }
    } catch (error) {
      console.error('加载防疫任务失败:', error);
      showToast('加载失败，请重试', 'error');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 处理任务数据
  processTaskData(tasks) {
    const stats = {
      total: tasks.length,
      pending: 0,
      completed: 0,
      inProgress: 0
    };

    const processedTasks = tasks.map(task => {
      // 统计状态
      if (task.status === 'pending') stats.pending++;
      else if (task.status === 'completed') stats.completed++;
      else if (task.status === 'in_progress') stats.inProgress++;

      return {
        ...task,
        statusText: this.getStatusText(task.status),
        statusColor: this.getStatusColor(task.status),
        priorityText: this.getPriorityText(task.priority),
        categoryText: this.getCategoryText(task.category),
        formattedDate: formatDate(new Date(task.scheduled_date)),
        isOverdue: new Date(task.scheduled_date) < new Date() && task.status === 'pending',
        isToday: task.scheduled_date === formatDate(new Date(), 'YYYY-MM-DD')
      };
    });

    this.setData({ 
      tasks: processedTasks,
      stats 
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待执行',
      'in_progress': '执行中', 
      'completed': '已完成',
      'skipped': '已跳过',
      'failed': '执行失败'
    };
    return statusMap[status] || status;
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      'pending': '#f39c12',
      'in_progress': '#3498db',
      'completed': '#27ae60',
      'skipped': '#95a5a6',
      'failed': '#e74c3c'
    };
    return colorMap[status] || '#666';
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高',
      'medium': '中',
      'low': '低'
    };
    return priorityMap[priority] || priority;
  },

  // 获取类别文本
  getCategoryText(category) {
    const categoryMap = {
      'vaccination': '疫苗接种',
      'medication': '投药',
      'care': '护理',
      'feeding': '饲喂管理'
    };
    return categoryMap[category] || category;
  },

  // 切换标签页
  onTabChange(e) {
    const currentTab = e.detail.index;
    this.setData({ currentTab });
    this.loadTasks();
  },

  // 状态筛选
  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ statusFilter: status });
    this.loadTasks();
  },

  // 日期选择
  onDateChange(e) {
    this.setData({ selectedDate: e.detail.value });
    this.loadTasks();
  },

  // 任务点击
  onTaskTap(e) {
    const task = e.currentTarget.dataset.task;
    wx.navigateTo({
      url: `/pages/vaccination/task-detail/task-detail?taskId=${task.task_id}&flockId=${this.data.flockId}`
    });
  },

  // 快速标记完成
  async onQuickComplete(e) {
    const task = e.currentTarget.dataset.task;
    
    try {
      showLoading('更新中...');
      
      const response = await request({
        url: `/api/v2/vaccination/tasks/${task.task_id}/status`,
        method: 'PATCH',
        data: {
          status: 'completed',
          executorName: wx.getStorageSync('userInfo')?.name || '用户',
          actualDate: formatDate(new Date(), 'YYYY-MM-DD'),
          notes: '快速完成'
        }
      });

      if (response.success) {
        showToast('标记成功', 'success');
        this.loadTasks();
      } else {
        showToast(response.message || '更新失败', 'error');
      }
    } catch (error) {
      console.error('更新任务状态失败:', error);
      showToast('更新失败，请重试', 'error');
    } finally {
      hideLoading();
    }
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const task = e.currentTarget.dataset.task;
    wx.navigateTo({
      url: `/pages/vaccination/task-detail/task-detail?taskId=${task.task_id}`
    });
  },

  // 新增防疫计划
  onAddPlan() {
    wx.navigateTo({
      url: '/pages/vaccination/create-plan/create-plan'
    });
  },

  // 查看统计
  onViewStats() {
    wx.navigateTo({
      url: `/pages/vaccination/stats/stats?flockId=${this.data.flockId}`
    });
  }
});