/* 防疫任务列表样式 */
.vaccination-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 统计栏 */
.stats-bar {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx 20rpx;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
  margin-top: 8rpx;
}

.stat-pending { color: #f39c12; }
.stat-progress { color: #3498db; }
.stat-completed { color: #27ae60; }

/* 标签页 */
.tabs {
  display: flex;
  background: white;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1890ff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #1890ff;
  border-radius: 2rpx;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
  white-space: nowrap;
}

.filter-options {
  display: flex;
  flex: 1;
}

.filter-option {
  padding: 8rpx 16rpx;
  margin-right: 12rpx;
  background: #f5f5f5;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #666;
}

.filter-option.active {
  background: #1890ff;
  color: white;
}

.date-picker {
  padding: 8rpx 16rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
}

/* 任务列表 */
.task-list {
  flex: 1;
  height: calc(100vh - 400rpx);
}

.task-item {
  background: white;
  margin: 16rpx 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
}

.task-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 8rpx;
  height: 100%;
  background: #1890ff;
  border-radius: 4rpx 0 0 4rpx;
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.task-title {
  flex: 1;
}

.day-age {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.category-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: white;
}

.category-vaccination { background: #e74c3c; }
.category-medication { background: #3498db; }
.category-care { background: #27ae60; }
.category-feeding { background: #f39c12; }

.task-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-text {
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.priority-tag {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
}

.priority-high { background: #e74c3c; }
.priority-medium { background: #f39c12; }
.priority-low { background: #95a5a6; }

/* 任务内容 */
.task-content {
  margin-bottom: 20rpx;
}

.task-content view {
  margin-bottom: 12rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.task-content .label {
  color: #666;
  margin-right: 8rpx;
}

.task-content .content {
  color: #333;
}

/* 任务信息 */
.task-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.info-item .icon {
  margin-right: 8rpx;
  font-size: 20rpx;
}

.today-tag {
  padding: 2rpx 8rpx;
  margin-left: 8rpx;
  background: #27ae60;
  color: white;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.overdue-tag {
  padding: 2rpx 8rpx;
  margin-left: 8rpx;
  background: #e74c3c;
  color: white;
  border-radius: 8rpx;
  font-size: 20rpx;
}

/* 快速操作 */
.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 8rpx 20rpx !important;
  border-radius: 32rpx;
  font-size: 24rpx !important;
  border: none;
}

.complete-btn {
  background: #27ae60 !important;
  color: white !important;
}

.detail-btn {
  background: #3498db !important;
  color: white !important;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.add-plan-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  border-top: 1rpx solid #e0e0e0;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.bar-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f8f8;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 0;
  margin: 0 8rpx;
}

.bar-btn:first-child {
  margin-left: 0;
}

.bar-btn:last-child {
  margin-right: 0;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
  color: #333;
}