<!--防疫任务详情页面-->
<view class="task-detail-container" wx:if="{{!loading}}">
  <!-- 任务基本信息 -->
  <view class="task-header">
    <view class="task-title">
      <text class="day-age">{{task.day_age}}</text>
      <text class="category-tag category-{{task.category}}">{{task.categoryText}}</text>
    </view>
    <view class="task-status">
      <text class="status-text" style="color: {{task.statusColor}}">{{task.statusText}}</text>
      <text class="priority-tag priority-{{task.priority}}">{{task.priorityText}}</text>
    </view>
    <text class="overdue-tag" wx:if="{{task.isOverdue}}">已逾期</text>
  </view>

  <scroll-view class="detail-content" scroll-y>
    <!-- 疾病预防信息 -->
    <view class="info-section">
      <view class="section-title">
        <text class="icon">🦠</text>
        <text class="title">预防疾病</text>
      </view>
      <view class="section-content">
        <text class="content-text">{{task.prevention_disease || '暂无'}}</text>
      </view>
    </view>

    <!-- 用药方案 -->
    <view class="info-section">
      <view class="section-title">
        <text class="icon">💊</text>
        <text class="title">用药方案</text>
      </view>
      <view class="section-content">
        <text class="content-text">{{task.medication || '暂无'}}</text>
      </view>
    </view>

    <!-- 用药说明 -->
    <view class="info-section" wx:if="{{task.dosage_instructions}}">
      <view class="section-title">
        <text class="icon">📋</text>
        <text class="title">用药说明</text>
      </view>
      <view class="section-content">
        <text class="content-text">{{task.dosage_instructions}}</text>
      </view>
    </view>

    <!-- 任务时间信息 -->
    <view class="info-section">
      <view class="section-title">
        <text class="icon">⏰</text>
        <text class="title">时间信息</text>
      </view>
      <view class="section-content">
        <view class="info-row">
          <text class="label">计划日期:</text>
          <text class="value">{{task.formattedScheduledDate}}</text>
        </view>
        <view class="info-row" wx:if="{{task.actual_date}}">
          <text class="label">实际日期:</text>
          <text class="value">{{task.formattedActualDate}}</text>
        </view>
        <view class="info-row">
          <text class="label">当前天龄:</text>
          <text class="value">第{{task.current_day}}天</text>
        </view>
      </view>
    </view>

    <!-- 鹅群信息 -->
    <view class="info-section">
      <view class="section-title">
        <text class="icon">🦢</text>
        <text class="title">鹅群信息</text>
      </view>
      <view class="section-content">
        <view class="info-row">
          <text class="label">鹅群数量:</text>
          <text class="value">{{task.total_geese}}只</text>
        </view>
        <view class="info-row" wx:if="{{task.cost}}">
          <text class="label">预计成本:</text>
          <text class="value">¥{{task.cost}}</text>
        </view>
      </view>
    </view>

    <!-- 执行记录表单 -->
    <view class="form-section" wx:if="{{task.status === 'in_progress' || task.status === 'completed'}}">
      <view class="section-title">
        <text class="icon">📝</text>
        <text class="title">执行记录</text>
      </view>
      
      <view class="form-content">
        <view class="form-item">
          <text class="form-label">执行人员 *</text>
          <input class="form-input" 
                 placeholder="请输入执行人员姓名"
                 value="{{formData.executorName}}"
                 data-field="executorName"
                 bindinput="onFormInput" 
                 disabled="{{task.status === 'completed'}}" />
        </view>

        <view class="form-item">
          <text class="form-label">实际用药量</text>
          <textarea class="form-textarea" 
                    placeholder="请输入实际用药量和方法"
                    value="{{formData.actualDosage}}"
                    data-field="actualDosage"
                    bindinput="onFormInput"
                    disabled="{{task.status === 'completed'}}" />
        </view>

        <view class="form-item">
          <text class="form-label">死亡数量</text>
          <input class="form-input" 
                 type="number"
                 placeholder="0"
                 value="{{formData.mortalityCount}}"
                 data-field="mortalityCount"
                 bindinput="onFormInput"
                 disabled="{{task.status === 'completed'}}" />
        </view>

        <view class="form-item">
          <text class="form-label">不良反应</text>
          <textarea class="form-textarea" 
                    placeholder="如有不良反应请详细描述"
                    value="{{formData.sideEffects}}"
                    data-field="sideEffects"
                    bindinput="onFormInput"
                    disabled="{{task.status === 'completed'}}" />
        </view>

        <view class="form-item">
          <text class="form-label">效果评分: {{formData.effectivenessScore}}分</text>
          <slider class="form-slider"
                  min="1" max="10" step="1"
                  value="{{formData.effectivenessScore}}"
                  bindchange="onScoreChange"
                  disabled="{{task.status === 'completed'}}"
                  show-value />
        </view>

        <view class="form-item">
          <text class="form-label">实际成本</text>
          <input class="form-input" 
                 type="digit"
                 placeholder="0.00"
                 value="{{formData.cost}}"
                 data-field="cost"
                 bindinput="onFormInput"
                 disabled="{{task.status === 'completed'}}" />
        </view>

        <view class="form-item">
          <text class="form-label">备注说明</text>
          <textarea class="form-textarea" 
                    placeholder="可记录执行过程中的其他注意事项"
                    value="{{formData.notes}}"
                    data-field="notes"
                    bindinput="onFormInput"
                    disabled="{{task.status === 'completed'}}" />
        </view>

        <!-- 照片记录 -->
        <view class="form-item">
          <text class="form-label">照片记录</text>
          <view class="photo-container">
            <view class="photo-item" wx:for="{{photos}}" wx:key="index">
              <image class="photo-image" 
                     src="{{item}}" 
                     bindtap="onPreviewPhoto" 
                     data-index="{{index}}" 
                     mode="aspectFill" />
              <view class="photo-delete" 
                    bindtap="onDeletePhoto" 
                    data-index="{{index}}"
                    wx:if="{{task.status !== 'completed'}}">✕</view>
            </view>
            <view class="photo-add" 
                  bindtap="onAddPhoto"
                  wx:if="{{photos.length < 3 && task.status !== 'completed'}}">
              <text class="add-icon">📷</text>
              <text class="add-text">添加照片</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部操作按钮 -->
  <view class="action-bar">
    <block wx:if="{{task.status === 'pending'}}">
      <button class="action-btn start-btn" 
              bindtap="onStartTask" 
              loading="{{executing}}">
        开始执行
      </button>
      <button class="action-btn skip-btn" 
              bindtap="onSkipTask">
        跳过
      </button>
    </block>
    
    <block wx:if="{{task.status === 'in_progress'}}">
      <button class="action-btn complete-btn" 
              bindtap="onCompleteTask" 
              loading="{{executing}}">
        完成任务
      </button>
    </block>
    
    <block wx:if="{{task.status === 'completed'}}">
      <view class="completed-info">
        <text class="completed-text">✅ 任务已完成</text>
        <text class="completed-time">完成时间: {{task.formattedActualDate}}</text>
      </view>
    </block>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-text">加载中...</view>
</view>