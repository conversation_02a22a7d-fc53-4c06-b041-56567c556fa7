/* 防疫任务详情样式 */
.task-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 任务头部 */
.task-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32rpx 24rpx;
  color: white;
  position: relative;
}

.task-title {
  margin-bottom: 16rpx;
}

.day-age {
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.category-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  background: rgba(255, 255, 255, 0.2);
}

.task-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
}

.priority-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  background: rgba(255, 255, 255, 0.2);
}

.overdue-tag {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 6rpx 12rpx;
  background: #e74c3c;
  color: white;
  border-radius: 12rpx;
  font-size: 20rpx;
}

/* 详情内容 */
.detail-content {
  flex: 1;
  padding: 24rpx;
}

/* 信息区块 */
.info-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.section-title .icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.section-title .title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.section-content {
  padding: 24rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  color: #666;
  margin-right: 16rpx;
  min-width: 160rpx;
}

.info-row .value {
  color: #333;
  font-weight: 500;
}

/* 表单区块 */
.form-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.form-content {
  padding: 24rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1890ff;
}

.form-input[disabled] {
  background: #f5f5f5;
  color: #999;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #1890ff;
}

.form-textarea[disabled] {
  background: #f5f5f5;
  color: #999;
}

.form-slider {
  margin-top: 16rpx;
}

/* 照片容器 */
.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
}

.photo-add {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  color: #666;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 操作栏 */
.action-bar {
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-btn {
  background: #1890ff;
  color: white;
}

.complete-btn {
  background: #27ae60;
  color: white;
}

.skip-btn {
  background: #95a5a6;
  color: white;
}

.completed-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.completed-text {
  font-size: 32rpx;
  color: #27ae60;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.completed-time {
  font-size: 24rpx;
  color: #666;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}