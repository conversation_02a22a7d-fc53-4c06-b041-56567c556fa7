// 防疫任务详情页面
const { request } = require('../../../utils/api');
const { formatDate, showToast, showLoading, hideLoading } = require('../../../utils/util');

Page({
  data: {
    taskId: '',
    task: null,
    loading: true,
    executing: false,
    photos: [],
    formData: {
      status: '',
      executorName: '',
      actualDosage: '',
      mortalityCount: 0,
      sideEffects: '',
      effectivenessScore: 8,
      cost: 0,
      notes: ''
    }
  },

  onLoad(options) {
    const { taskId } = options;
    this.setData({ taskId });
    this.loadTaskDetail();
  },

  // 加载任务详情
  async loadTaskDetail() {
    try {
      showLoading('加载中...');
      
      const response = await request({
        url: `/api/v2/vaccination/tasks/${this.data.taskId}`,
        method: 'GET'
      });

      if (response.success) {
        const task = response.data;
        this.setData({ 
          task: {
            ...task,
            statusText: this.getStatusText(task.status),
            statusColor: this.getStatusColor(task.status),
            priorityText: this.getPriorityText(task.priority),
            categoryText: this.getCategoryText(task.category),
            formattedScheduledDate: formatDate(new Date(task.scheduled_date)),
            formattedActualDate: task.actual_date ? formatDate(new Date(task.actual_date)) : null,
            isOverdue: new Date(task.scheduled_date) < new Date() && task.status === 'pending'
          },
          'formData.status': task.status,
          'formData.executorName': task.executor_name || wx.getStorageSync('userInfo')?.name || '',
          'formData.actualDosage': task.actual_dosage || '',
          'formData.mortalityCount': task.mortality_count || 0,
          'formData.sideEffects': task.side_effects || '',
          'formData.effectivenessScore': task.effectiveness_score || 8,
          'formData.cost': task.cost || 0,
          'formData.notes': task.notes || ''
        });

        if (task.photos) {
          this.setData({ photos: JSON.parse(task.photos) || [] });
        }
      } else {
        showToast(response.message || '加载失败', 'error');
      }
    } catch (error) {
      console.error('加载任务详情失败:', error);
      showToast('加载失败，请重试', 'error');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待执行',
      'in_progress': '执行中', 
      'completed': '已完成',
      'skipped': '已跳过',
      'failed': '执行失败'
    };
    return statusMap[status] || status;
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      'pending': '#f39c12',
      'in_progress': '#3498db',
      'completed': '#27ae60',
      'skipped': '#95a5a6',
      'failed': '#e74c3c'
    };
    return colorMap[status] || '#666';
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    };
    return priorityMap[priority] || priority;
  },

  // 获取类别文本
  getCategoryText(category) {
    const categoryMap = {
      'vaccination': '疫苗接种',
      'medication': '投药治疗',
      'care': '日常护理',
      'feeding': '饲喂管理'
    };
    return categoryMap[category] || category;
  },

  // 表单输入处理
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 评分改变
  onScoreChange(e) {
    this.setData({
      'formData.effectivenessScore': e.detail.value
    });
  },

  // 添加照片
  async onAddPhoto() {
    try {
      const res = await wx.chooseImage({
        count: 3 - this.data.photos.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      showLoading('上传中...');
      
      const uploadPromises = res.tempFilePaths.map(path => 
        this.uploadImage(path)
      );
      
      const uploadResults = await Promise.all(uploadPromises);
      const newPhotos = [...this.data.photos, ...uploadResults.filter(Boolean)];
      
      this.setData({ photos: newPhotos });
      showToast('照片上传成功', 'success');
    } catch (error) {
      console.error('添加照片失败:', error);
      showToast('照片上传失败', 'error');
    } finally {
      hideLoading();
    }
  },

  // 上传图片
  async uploadImage(filePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: '/api/v2/upload/image',
        filePath,
        name: 'file',
        header: {
          'Authorization': 'Bearer ' + wx.getStorageSync('token')
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data.data.url);
            } else {
              reject(new Error(data.message));
            }
          } catch (e) {
            reject(e);
          }
        },
        fail: reject
      });
    });
  },

  // 删除照片
  onDeletePhoto(e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.photos.filter((_, i) => i !== index);
    this.setData({ photos });
  },

  // 预览照片
  onPreviewPhoto(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.photos[index],
      urls: this.data.photos
    });
  },

  // 开始执行任务
  async onStartTask() {
    if (this.data.task.status !== 'pending') {
      showToast('任务状态不允许执行', 'error');
      return;
    }

    try {
      this.setData({ executing: true });
      
      const response = await request({
        url: `/api/v2/vaccination/tasks/${this.data.taskId}/status`,
        method: 'PATCH',
        data: {
          status: 'in_progress',
          executorName: this.data.formData.executorName
        }
      });

      if (response.success) {
        showToast('任务已开始执行', 'success');
        this.loadTaskDetail();
      } else {
        showToast(response.message || '操作失败', 'error');
      }
    } catch (error) {
      console.error('开始执行任务失败:', error);
      showToast('操作失败，请重试', 'error');
    } finally {
      this.setData({ executing: false });
    }
  },

  // 完成任务
  async onCompleteTask() {
    const { formData } = this.data;
    
    if (!formData.executorName.trim()) {
      showToast('请输入执行人员', 'error');
      return;
    }

    try {
      this.setData({ executing: true });
      
      const response = await request({
        url: `/api/v2/vaccination/tasks/${this.data.taskId}/status`,
        method: 'PATCH',
        data: {
          ...formData,
          status: 'completed',
          actualDate: formatDate(new Date(), 'YYYY-MM-DD'),
          photos: this.data.photos
        }
      });

      if (response.success) {
        showToast('任务执行完成', 'success');
        // 返回上一页并刷新
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        showToast(response.message || '操作失败', 'error');
      }
    } catch (error) {
      console.error('完成任务失败:', error);
      showToast('操作失败，请重试', 'error');
    } finally {
      this.setData({ executing: false });
    }
  },

  // 跳过任务
  async onSkipTask() {
    wx.showModal({
      title: '确认跳过',
      content: '确定要跳过这个防疫任务吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await request({
              url: `/api/v2/vaccination/tasks/${this.data.taskId}/status`,
              method: 'PATCH',
              data: {
                status: 'skipped',
                notes: '手动跳过任务'
              }
            });

            if (response.success) {
              showToast('任务已跳过', 'success');
              this.loadTaskDetail();
            } else {
              showToast(response.message || '操作失败', 'error');
            }
          } catch (error) {
            console.error('跳过任务失败:', error);
            showToast('操作失败，请重试', 'error');
          }
        }
      }
    });
  }
});