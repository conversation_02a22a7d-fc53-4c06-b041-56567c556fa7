// pages/auth/farm-registration/farm-registration.js
const { WechatAuthSystem, USER_STATUS, FARM_STATUS } = require('../../../utils/wechat-auth-system.js');

Page({
  data: {
    loading: false,
    currentStep: 1,
    totalSteps: 4,
    
    // 步骤标题
    stepTitles: [
      '基本信息',
      '养殖详情', 
      '管理员信息',
      '提交审核'
    ],
    
    // 养殖场基本信息
    farmInfo: {
      farmName: '',           // 养殖场名称
      legalName: '',          // 法定名称  
      businessLicense: '',    // 营业执照号
      province: '',           // 省份
      city: '',              // 城市
      district: '',          // 区县
      detailAddress: '',     // 详细地址
      contactPhone: '',      // 联系电话
      establishDate: ''      // 成立时间
    },
    
    // 养殖详情信息
    farmDetails: {
      scale: '',             // 养殖规模
      breedTypes: [],        // 养殖品种
      currentStock: '',      // 当前存栏数
      annualOutput: '',      // 年产量
      hasLicense: false,     // 是否有养殖许可证
      licenseNumber: '',     // 许可证编号
      description: ''        // 养殖场描述
    },
    
    // 管理员信息
    adminInfo: {
      realName: '',          // 真实姓名
      phone: '',             // 手机号码
      idCard: '',            // 身份证号
      position: '场长',       // 职位
      email: '',             // 邮箱
      experience: ''         // 养殖经验
    },
    
    // 微信用户信息
    wechatUserInfo: null,
    
    // 选项数据
    scaleOptions: [
      '小型养殖场（500只以下）',
      '中型养殖场（500-2000只）', 
      '大型养殖场（2000-5000只）',
      '超大型养殖场（5000只以上）'
    ],
    
    breedOptions: [
      { id: 'white_goose', name: '白鹅', checked: false },
      { id: 'gray_goose', name: '灰鹅', checked: false },
      { id: 'lion_head_goose', name: '狮头鹅', checked: false },
      { id: 'sichuan_white_goose', name: '四川白鹅', checked: false },
      { id: 'wannan_white_goose', name: '皖西白鹅', checked: false },
      { id: 'other', name: '其他品种', checked: false }
    ],
    
    // 地区选择
    regions: [],
    regionIndex: [0, 0, 0],
    
    // 日期选择
    dateValue: '',
    
    // 表单验证
    errors: {}
  },

  onLoad(options) {
    console.log('[养殖场注册] 页面加载');
    
    // 获取传入的微信用户信息
    if (options.wechatUserInfo) {
      try {
        const wechatUserInfo = JSON.parse(decodeURIComponent(options.wechatUserInfo));
        this.setData({ wechatUserInfo });
      } catch (error) {
        console.error('解析微信用户信息失败:', error);
      }
    }
    
    this.initializeRegions();
    this.initializeDate();
  },

  // 初始化地区数据
  initializeRegions() {
    // 这里可以从API获取地区数据，现在使用模拟数据
    const regions = [
      ['北京市', '上海市', '广东省', '江苏省', '浙江省', '山东省', '河南省', '湖南省', '湖北省', '四川省'],
      ['北京市', '朝阳区', '海淀区', '丰台区', '石景山区'],
      ['北京市', '东城区', '西城区', '朝阳区', '海淀区']
    ];
    
    this.setData({ regions });
  },

  // 初始化日期
  initializeDate() {
    const today = new Date();
    const currentDate = today.getFullYear() + '-' + 
      String(today.getMonth() + 1).padStart(2, '0') + '-' + 
      String(today.getDate()).padStart(2, '0');
    
    this.setData({ 
      dateValue: currentDate,
      'farmInfo.establishDate': currentDate
    });
  },

  // 下一步
  onNext() {
    if (this.validateCurrentStep()) {
      if (this.data.currentStep < this.data.totalSteps) {
        this.setData({
          currentStep: this.data.currentStep + 1
        });
      }
    }
  },

  // 上一步
  onPrevious() {
    if (this.data.currentStep > 1) {
      this.setData({
        currentStep: this.data.currentStep - 1
      });
    }
  },

  // 验证当前步骤
  validateCurrentStep() {
    const errors = {};
    const { currentStep, farmInfo, farmDetails, adminInfo } = this.data;
    
    switch (currentStep) {
      case 1: // 基本信息
        if (!farmInfo.farmName.trim()) errors.farmName = '请输入养殖场名称';
        if (!farmInfo.legalName.trim()) errors.legalName = '请输入法定名称';
        if (!farmInfo.businessLicense.trim()) errors.businessLicense = '请输入营业执照号';
        if (!farmInfo.contactPhone.trim()) errors.contactPhone = '请输入联系电话';
        if (!farmInfo.detailAddress.trim()) errors.detailAddress = '请输入详细地址';
        break;
        
      case 2: // 养殖详情
        if (!farmDetails.scale) errors.scale = '请选择养殖规模';
        if (farmDetails.breedTypes.length === 0) errors.breedTypes = '请选择至少一种养殖品种';
        if (!farmDetails.currentStock.trim()) errors.currentStock = '请输入当前存栏数';
        break;
        
      case 3: // 管理员信息
        if (!adminInfo.realName.trim()) errors.realName = '请输入真实姓名';
        if (!adminInfo.phone.trim()) errors.phone = '请输入手机号码';
        if (!adminInfo.idCard.trim()) errors.idCard = '请输入身份证号';
        
        // 手机号验证
        if (adminInfo.phone && !/^1[3-9]\d{9}$/.test(adminInfo.phone)) {
          errors.phone = '请输入正确的手机号码';
        }
        
        // 身份证号验证
        if (adminInfo.idCard && !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(adminInfo.idCard)) {
          errors.idCard = '请输入正确的身份证号';
        }
        break;
    }
    
    this.setData({ errors });
    
    if (Object.keys(errors).length > 0) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [field]: value
    });
    
    // 清除该字段的错误
    const fieldName = field.split('.').pop();
    if (this.data.errors[fieldName]) {
      this.setData({
        [`errors.${fieldName}`]: ''
      });
    }
  },

  // 规模选择
  onScaleChange(e) {
    const index = e.detail.value;
    this.setData({
      'farmDetails.scale': this.data.scaleOptions[index]
    });
  },

  // 品种选择
  onBreedChange(e) {
    const values = e.detail.value;
    const breedOptions = this.data.breedOptions.map((item, index) => ({
      ...item,
      checked: values.includes(index.toString())
    }));
    
    const selectedBreeds = breedOptions
      .filter(item => item.checked)
      .map(item => item.name);
    
    this.setData({
      breedOptions,
      'farmDetails.breedTypes': selectedBreeds
    });
  },

  // 地区选择
  onRegionChange(e) {
    const values = e.detail.value;
    const regions = this.data.regions;
    
    this.setData({
      regionIndex: values,
      'farmInfo.province': regions[0][values[0]] || '',
      'farmInfo.city': regions[1][values[1]] || '',
      'farmInfo.district': regions[2][values[2]] || ''
    });
  },

  // 日期选择
  onDateChange(e) {
    this.setData({
      dateValue: e.detail.value,
      'farmInfo.establishDate': e.detail.value
    });
  },

  // 许可证开关
  onLicenseSwitch(e) {
    this.setData({
      'farmDetails.hasLicense': e.detail.value
    });
  },

  // 提交申请
  async onSubmit() {
    if (!this.validateCurrentStep()) {
      return;
    }

    try {
      this.setData({ loading: true });
      
      wx.showLoading({ title: '提交申请中...' });
      
      // 构建完整的申请数据
      const { farmInfo, farmDetails, adminInfo, wechatUserInfo } = this.data;
      
      const completeAdminInfo = {
        ...adminInfo,
        wechatInfo: wechatUserInfo
      };
      
      const completeFarmInfo = {
        ...farmInfo,
        ...farmDetails
      };
      
      // 使用认证系统提交申请
      const authSystem = new WechatAuthSystem();
      const result = await authSystem.registerFarmApplication(
        completeFarmInfo, 
        completeAdminInfo,
        wechatUserInfo?.openid || 'demo_openid'
      );
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showModal({
          title: '申请提交成功',
          content: result.message + '\n\n您可以通过"申请状态查询"功能查看审核进度。',
          showCancel: false,
          confirmText: '我知道了',
          success: () => {
            // 跳转到申请状态页面
            wx.redirectTo({
              url: `/pages/auth/application-status/application-status?applicationId=${result.applicationId}`
            });
          }
        });
      } else {
        wx.showModal({
          title: '申请提交失败',
          content: result.message,
          showCancel: false,
          confirmText: '重试'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交申请失败:', error);
      
      wx.showModal({
        title: '提交失败',
        content: '网络连接异常，请检查网络后重试',
        showCancel: false,
        confirmText: '重试'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 返回登录页
  onBackToLogin() {
    wx.navigateBack({
      delta: 1
    });
  }
});