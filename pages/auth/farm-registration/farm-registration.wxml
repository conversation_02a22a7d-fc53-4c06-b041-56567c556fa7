<!-- pages/auth/farm-registration/farm-registration.wxml -->
<view class="registration-container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">养殖场注册申请</view>
    <view class="subtitle">完善信息，开启智慧养鹅之旅</view>
  </view>

  <!-- 进度条 -->
  <view class="progress-section">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(currentStep / totalSteps) * 100}}%"></view>
    </view>
    <view class="progress-text">第{{currentStep}}步 / 共{{totalSteps}}步</view>
    <view class="step-title">{{stepTitles[currentStep - 1]}}</view>
  </view>

  <!-- 表单内容 -->
  <scroll-view class="form-container" scroll-y="true" enhanced="true">
    
    <!-- 第一步：基本信息 -->
    <view class="step-content" wx:if="{{currentStep === 1}}">
      <view class="form-group">
        <view class="label required">养殖场名称</view>
        <input class="input {{errors.farmName ? 'error' : ''}}" 
               placeholder="请输入养殖场名称" 
               value="{{farmInfo.farmName}}"
               data-field="farmInfo.farmName"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.farmName}}">{{errors.farmName}}</view>
      </view>

      <view class="form-group">
        <view class="label required">法定名称</view>
        <input class="input {{errors.legalName ? 'error' : ''}}" 
               placeholder="请输入法定企业名称" 
               value="{{farmInfo.legalName}}"
               data-field="farmInfo.legalName"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.legalName}}">{{errors.legalName}}</view>
      </view>

      <view class="form-group">
        <view class="label required">营业执照号</view>
        <input class="input {{errors.businessLicense ? 'error' : ''}}" 
               placeholder="请输入营业执照统一社会信用代码" 
               value="{{farmInfo.businessLicense}}"
               data-field="farmInfo.businessLicense"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.businessLicense}}">{{errors.businessLicense}}</view>
      </view>

      <view class="form-group">
        <view class="label required">所在地区</view>
        <picker class="picker" mode="multiSelector" 
                range="{{regions}}" 
                value="{{regionIndex}}"
                bindchange="onRegionChange">
          <view class="picker-display {{!farmInfo.province ? 'placeholder' : ''}}">
            {{farmInfo.province || '请选择'}} {{farmInfo.city}} {{farmInfo.district}}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <view class="label required">详细地址</view>
        <textarea class="textarea {{errors.detailAddress ? 'error' : ''}}" 
                  placeholder="请输入详细地址" 
                  value="{{farmInfo.detailAddress}}"
                  data-field="farmInfo.detailAddress"
                  bindinput="onInputChange" 
                  maxlength="200" />
        <view class="error-text" wx:if="{{errors.detailAddress}}">{{errors.detailAddress}}</view>
      </view>

      <view class="form-group">
        <view class="label required">联系电话</view>
        <input class="input {{errors.contactPhone ? 'error' : ''}}" 
               placeholder="请输入联系电话" 
               type="number"
               value="{{farmInfo.contactPhone}}"
               data-field="farmInfo.contactPhone"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.contactPhone}}">{{errors.contactPhone}}</view>
      </view>

      <view class="form-group">
        <view class="label">成立时间</view>
        <picker class="picker" mode="date" 
                value="{{dateValue}}" 
                bindchange="onDateChange"
                end="{{dateValue}}">
          <view class="picker-display">{{farmInfo.establishDate || '请选择成立时间'}}</view>
        </picker>
      </view>
    </view>

    <!-- 第二步：养殖详情 -->
    <view class="step-content" wx:if="{{currentStep === 2}}">
      <view class="form-group">
        <view class="label required">养殖规模</view>
        <picker class="picker" range="{{scaleOptions}}" bindchange="onScaleChange">
          <view class="picker-display {{!farmDetails.scale ? 'placeholder' : ''}}">
            {{farmDetails.scale || '请选择养殖规模'}}
          </view>
        </picker>
        <view class="error-text" wx:if="{{errors.scale}}">{{errors.scale}}</view>
      </view>

      <view class="form-group">
        <view class="label required">养殖品种</view>
        <checkbox-group class="checkbox-group" bindchange="onBreedChange">
          <view class="checkbox-item" wx:for="{{breedOptions}}" wx:key="id">
            <checkbox value="{{index}}" checked="{{item.checked}}" />
            <text class="checkbox-label">{{item.name}}</text>
          </view>
        </checkbox-group>
        <view class="error-text" wx:if="{{errors.breedTypes}}">{{errors.breedTypes}}</view>
      </view>

      <view class="form-group">
        <view class="label required">当前存栏数</view>
        <input class="input {{errors.currentStock ? 'error' : ''}}" 
               placeholder="请输入当前存栏数量（只）" 
               type="number"
               value="{{farmDetails.currentStock}}"
               data-field="farmDetails.currentStock"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.currentStock}}">{{errors.currentStock}}</view>
      </view>

      <view class="form-group">
        <view class="label">年产量</view>
        <input class="input" 
               placeholder="请输入预计年产量（只）" 
               type="number"
               value="{{farmDetails.annualOutput}}"
               data-field="farmDetails.annualOutput"
               bindinput="onInputChange" />
      </view>

      <view class="form-group">
        <view class="label-row">
          <view class="label">养殖许可证</view>
          <switch checked="{{farmDetails.hasLicense}}" bindchange="onLicenseSwitch" />
        </view>
        <view class="form-sub-group" wx:if="{{farmDetails.hasLicense}}">
          <input class="input" 
                 placeholder="请输入许可证编号" 
                 value="{{farmDetails.licenseNumber}}"
                 data-field="farmDetails.licenseNumber"
                 bindinput="onInputChange" />
        </view>
      </view>

      <view class="form-group">
        <view class="label">养殖场描述</view>
        <textarea class="textarea" 
                  placeholder="请简要介绍您的养殖场特色和优势" 
                  value="{{farmDetails.description}}"
                  data-field="farmDetails.description"
                  bindinput="onInputChange" 
                  maxlength="500" />
      </view>
    </view>

    <!-- 第三步：管理员信息 -->
    <view class="step-content" wx:if="{{currentStep === 3}}">
      <view class="admin-tip">
        <text class="tip-icon">👤</text>
        <text class="tip-text">请填写养殖场主要负责人信息，该人员将成为系统管理员</text>
      </view>

      <view class="form-group">
        <view class="label required">真实姓名</view>
        <input class="input {{errors.realName ? 'error' : ''}}" 
               placeholder="请输入真实姓名" 
               value="{{adminInfo.realName}}"
               data-field="adminInfo.realName"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.realName}}">{{errors.realName}}</view>
      </view>

      <view class="form-group">
        <view class="label required">手机号码</view>
        <input class="input {{errors.phone ? 'error' : ''}}" 
               placeholder="请输入手机号码" 
               type="number"
               value="{{adminInfo.phone}}"
               data-field="adminInfo.phone"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.phone}}">{{errors.phone}}</view>
      </view>

      <view class="form-group">
        <view class="label required">身份证号</view>
        <input class="input {{errors.idCard ? 'error' : ''}}" 
               placeholder="请输入身份证号" 
               value="{{adminInfo.idCard}}"
               data-field="adminInfo.idCard"
               bindinput="onInputChange" />
        <view class="error-text" wx:if="{{errors.idCard}}">{{errors.idCard}}</view>
      </view>

      <view class="form-group">
        <view class="label">职位</view>
        <input class="input" 
               placeholder="请输入职位" 
               value="{{adminInfo.position}}"
               data-field="adminInfo.position"
               bindinput="onInputChange" />
      </view>

      <view class="form-group">
        <view class="label">邮箱地址</view>
        <input class="input" 
               placeholder="请输入邮箱地址（可选）" 
               value="{{adminInfo.email}}"
               data-field="adminInfo.email"
               bindinput="onInputChange" />
      </view>

      <view class="form-group">
        <view class="label">养殖经验</view>
        <textarea class="textarea" 
                  placeholder="请简要描述您的养殖经验和专业背景" 
                  value="{{adminInfo.experience}}"
                  data-field="adminInfo.experience"
                  bindinput="onInputChange" 
                  maxlength="300" />
      </view>
    </view>

    <!-- 第四步：确认提交 -->
    <view class="step-content" wx:if="{{currentStep === 4}}">
      <view class="summary-section">
        <view class="summary-title">请确认申请信息</view>
        
        <view class="summary-group">
          <view class="group-title">养殖场信息</view>
          <view class="summary-item">
            <text class="item-label">养殖场名称：</text>
            <text class="item-value">{{farmInfo.farmName}}</text>
          </view>
          <view class="summary-item">
            <text class="item-label">所在地区：</text>
            <text class="item-value">{{farmInfo.province}} {{farmInfo.city}} {{farmInfo.district}}</text>
          </view>
          <view class="summary-item">
            <text class="item-label">养殖规模：</text>
            <text class="item-value">{{farmDetails.scale}}</text>
          </view>
          <view class="summary-item">
            <text class="item-label">养殖品种：</text>
            <text class="item-value">{{farmDetails.breedTypes.join('、')}}</text>
          </view>
        </view>

        <view class="summary-group">
          <view class="group-title">管理员信息</view>
          <view class="summary-item">
            <text class="item-label">姓名：</text>
            <text class="item-value">{{adminInfo.realName}}</text>
          </view>
          <view class="summary-item">
            <text class="item-label">手机：</text>
            <text class="item-value">{{adminInfo.phone}}</text>
          </view>
          <view class="summary-item">
            <text class="item-label">职位：</text>
            <text class="item-value">{{adminInfo.position}}</text>
          </view>
        </view>
      </view>

      <view class="agreement-section">
        <checkbox-group>
          <view class="agreement-item">
            <checkbox value="agree" checked="{{agreed}}" />
            <text class="agreement-text">我已阅读并同意《智慧养鹅平台服务协议》和《隐私政策》</text>
          </view>
        </checkbox-group>
      </view>
    </view>
  </scroll-view>

  <!-- 底部按钮 -->
  <view class="button-section">
    <button class="btn btn-secondary" 
            wx:if="{{currentStep > 1}}" 
            bindtap="onPrevious">
      上一步
    </button>
    
    <button class="btn btn-primary" 
            wx:if="{{currentStep < totalSteps}}" 
            bindtap="onNext">
      下一步
    </button>
    
    <button class="btn btn-primary" 
            wx:if="{{currentStep === totalSteps}}" 
            loading="{{loading}}"
            bindtap="onSubmit">
      提交申请
    </button>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="btn-link" bindtap="onBackToLogin">
      返回登录页面
    </button>
  </view>
</view>