/* pages/auth/farm-registration/farm-registration.wxss */
.registration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 0 200rpx 0;
}

/* 页面头部 */
.header {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 进度条 */
.progress-section {
  padding: 0 40rpx 30rpx;
  color: white;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: bold;
}

/* 表单容器 */
.form-container {
  flex: 1;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  margin: 20rpx 20rpx 0;
  padding: 40rpx;
  max-height: calc(100vh - 400rpx);
}

.step-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 表单组 */
.form-group {
  margin-bottom: 40rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.label.required::before {
  content: '* ';
  color: #ff4444;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

/* 输入框 */
.input, .textarea {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
  box-sizing: border-box;
}

.input:focus, .textarea:focus {
  border-color: #667eea;
  background: white;
}

.input.error, .textarea.error {
  border-color: #ff4444;
  background: #fff5f5;
}

.textarea {
  height: 120rpx;
  resize: none;
}

/* 选择器 */
.picker {
  width: 100%;
}

.picker-display {
  padding: 25rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
  position: relative;
}

.picker-display::after {
  content: '';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 12rpx;
  height: 12rpx;
  border-right: 2rpx solid #666;
  border-bottom: 2rpx solid #666;
}

.picker-display.placeholder {
  color: #999;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
}

.checkbox-label {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
}

/* 子表单组 */
.form-sub-group {
  margin-top: 20rpx;
  padding-left: 20rpx;
  border-left: 3rpx solid #e5e5e5;
}

/* 错误文本 */
.error-text {
  color: #ff4444;
  font-size: 24rpx;
  margin-top: 10rpx;
}

/* 管理员提示 */
.admin-tip {
  background: #f0f8ff;
  border: 2rpx solid #e6f3ff;
  border-radius: 10rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #0066cc;
  line-height: 1.4;
}

/* 确认页面样式 */
.summary-section {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.summary-group {
  margin-bottom: 30rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e5e5e5;
}

.summary-item {
  display: flex;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.item-label {
  font-size: 26rpx;
  color: #666;
  min-width: 160rpx;
}

.item-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 协议部分 */
.agreement-section {
  margin-bottom: 30rpx;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  padding: 15rpx 0;
}

.agreement-text {
  margin-left: 15rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 按钮区域 */
.button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  background: white;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:active {
  opacity: 0.8;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.btn-secondary:active {
  background: #ebebeb;
}

/* 返回链接 */
.back-section {
  text-align: center;
  padding: 20rpx 0 40rpx;
}

.btn-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  text-decoration: underline;
}

.btn-link:active {
  color: rgba(255, 255, 255, 0.6);
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .header {
    padding: 40rpx 30rpx 30rpx;
  }
  
  .title {
    font-size: 42rpx;
  }
  
  .form-container {
    margin: 15rpx 15rpx 0;
    padding: 30rpx;
  }
  
  .button-section {
    padding: 25rpx 30rpx;
  }
}