// pages/auth/application-status/application-status.js
const { WechatAuthSystem, USER_STATUS, FARM_STATUS } = require('../../../utils/wechat-auth-system.js');

Page({
  data: {
    loading: false,
    applicationId: '',
    applicationInfo: null,
    status: USER_STATUS.PENDING,
    farmInfo: null,
    userInfo: null,
    reviewComment: '',
    reviewTime: null,
    submitTime: '',
    
    // 状态映射
    statusTexts: {
      [USER_STATUS.PENDING]: '审核中',
      [USER_STATUS.APPROVED]: '已通过',
      [USER_STATUS.REJECTED]: '已拒绝',
      [USER_STATUS.SUSPENDED]: '已暂停'
    },
    
    statusIcons: {
      [USER_STATUS.PENDING]: '⏳',
      [USER_STATUS.APPROVED]: '✅',
      [USER_STATUS.REJECTED]: '❌',
      [USER_STATUS.SUSPENDED]: '⏸️'
    },
    
    statusColors: {
      [USER_STATUS.PENDING]: '#ff9500',
      [USER_STATUS.APPROVED]: '#34c759',
      [USER_STATUS.REJECTED]: '#ff3b30',
      [USER_STATUS.SUSPENDED]: '#8e8e93'
    }
  },

  onLoad(options) {
    console.log('[申请状态] 页面加载, 参数:', options);
    
    if (options.applicationId) {
      this.setData({ applicationId: options.applicationId });
      this.loadApplicationStatus();
    } else {
      // 尝试从本地存储获取申请信息
      this.loadLocalApplication();
    }
  },

  onShow() {
    // 页面显示时刷新状态
    if (this.data.applicationId) {
      this.loadApplicationStatus();
    }
  },

  // 从本地存储加载申请信息
  loadLocalApplication() {
    try {
      const pendingApplication = wx.getStorageSync('pending_application');
      if (pendingApplication && pendingApplication.applicationId) {
        this.setData({
          applicationId: pendingApplication.applicationId,
          status: pendingApplication.status,
          submitTime: pendingApplication.submitTime
        });
        this.loadApplicationStatus();
      } else {
        this.showNoApplicationMessage();
      }
    } catch (error) {
      console.error('加载本地申请信息失败:', error);
      this.showNoApplicationMessage();
    }
  },

  // 显示无申请信息提示
  showNoApplicationMessage() {
    wx.showModal({
      title: '未找到申请信息',
      content: '没有找到相关的申请记录，请先提交养殖场注册申请',
      showCancel: false,
      confirmText: '去申请',
      success: () => {
        wx.redirectTo({
          url: '/pages/auth/farm-registration/farm-registration'
        });
      }
    });
  },

  // 加载申请状态
  async loadApplicationStatus() {
    try {
      this.setData({ loading: true });
      
      const authSystem = new WechatAuthSystem();
      const result = await authSystem.checkApplicationStatus(this.data.applicationId);
      
      console.log('[申请状态] 获取到状态信息:', result);
      
      this.setData({
        status: result.status,
        farmInfo: result.farm,
        userInfo: result.user,
        reviewComment: result.reviewComment,
        reviewTime: result.reviewTime,
        loading: false
      });
      
      // 如果状态已批准，提示用户可以正式登录
      if (result.status === USER_STATUS.APPROVED) {
        this.showApprovedMessage();
      }
      
    } catch (error) {
      console.error('加载申请状态失败:', error);
      
      // 使用本地模拟数据
      this.loadMockApplicationStatus();
    }
  },

  // 加载模拟申请状态（开发测试用）
  loadMockApplicationStatus() {
    setTimeout(() => {
      const mockData = {
        status: USER_STATUS.PENDING,
        farm: {
          name: '智慧生态养鹅基地',
          address: '江苏省苏州市昆山市开发区',
          scale: '中型养殖场（500-2000只）',
          breedTypes: ['白鹅', '灰鹅']
        },
        user: {
          realName: '测试管理员',
          phone: '138****8888',
          position: '场长'
        },
        reviewComment: '',
        reviewTime: null,
        submitTime: new Date().toISOString()
      };
      
      this.setData({
        ...mockData,
        loading: false
      });
    }, 1000);
  },

  // 显示审批通过消息
  showApprovedMessage() {
    wx.showModal({
      title: '🎉 审核通过',
      content: '恭喜！您的养殖场申请已通过审核，现在可以正式登录使用智慧养鹅平台了！',
      showCancel: false,
      confirmText: '立即登录',
      success: () => {
        this.performOfficialLogin();
      }
    });
  },

  // 执行正式登录
  async performOfficialLogin() {
    try {
      wx.showLoading({ title: '登录中...' });
      
      const authSystem = new WechatAuthSystem();
      const result = await authSystem.performOfficialLogin('demo_openid');
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: result.message || '登录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('正式登录失败:', error);
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 刷新状态
  onRefresh() {
    if (this.data.applicationId) {
      this.loadApplicationStatus();
    }
  },

  // 重新申请
  onReapply() {
    wx.showModal({
      title: '重新申请',
      content: '确定要重新提交养殖场注册申请吗？',
      success: (res) => {
        if (res.confirm) {
          wx.redirectTo({
            url: '/pages/auth/farm-registration/farm-registration'
          });
        }
      }
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '如需帮助，请联系客服：\n\n📞 客服电话：400-123-4567\n💬 微信客服：service123\n📧 邮箱：<EMAIL>',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 返回登录页
  onBackToLogin() {
    wx.redirectTo({
      url: '/pages/login/login'
    });
  },

  // 格式化时间
  formatTime(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.onRefresh();
    wx.stopPullDownRefresh();
  }
});