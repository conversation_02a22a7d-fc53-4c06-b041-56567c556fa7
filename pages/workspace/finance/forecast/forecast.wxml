<!--pages/workspace/finance/forecast/forecast.wxml-->
<view class="forecast-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">🔮 财务预测</text>
      <text class="page-subtitle">基于AI算法预测未来3-12月财务趋势</text>
    </view>
    <view class="forecast-controls">
      <view class="time-selector">
        <picker range="{{forecastPeriods}}" 
                range-key="label"
                value="{{selectedPeriodIndex}}"
                bindchange="onPeriodChange"
                class="period-picker">
          <view class="picker-display">
            <text class="period-text">{{forecastPeriods[selectedPeriodIndex].label}}</text>
            <text class="dropdown-icon">▼</text>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="ai-loading">
      <view class="loading-brain">🧠</view>
      <view class="loading-text">AI正在分析历史数据...</view>
      <view class="loading-progress">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{loadingProgress}}%"></view>
        </view>
        <text class="progress-text">{{loadingProgress}}%</text>
      </view>
    </view>
  </view>

  <view wx:else>
    <!-- 预测摘要卡片 -->
    <view class="forecast-summary-card">
      <view class="summary-header">
        <text class="summary-title">📊 预测摘要</text>
        <view class="confidence-badge">
          <text class="confidence-label">准确度</text>
          <text class="confidence-value">{{confidenceLevel}}%</text>
        </view>
      </view>
      
      <view class="forecast-highlights">
        <view class="highlight-item positive" wx:if="{{forecastTrend.income > 0}}">
          <text class="highlight-icon">📈</text>
          <text class="highlight-text">预计收入将增长 {{forecastTrend.income}}%</text>
        </view>
        <view class="highlight-item negative" wx:elif="{{forecastTrend.income < 0}}">
          <text class="highlight-icon">📉</text>
          <text class="highlight-text">预计收入将下降 {{Math.abs(forecastTrend.income)}}%</text>
        </view>
        
        <view class="highlight-item {{forecastTrend.expense > 0 ? 'negative' : 'positive'}}">
          <text class="highlight-icon">{{forecastTrend.expense > 0 ? '⬆️' : '⬇️'}}</text>
          <text class="highlight-text">支出预计{{forecastTrend.expense > 0 ? '增加' : '减少'}} {{Math.abs(forecastTrend.expense)}}%</text>
        </view>
        
        <view class="highlight-item {{forecastTrend.profit > 0 ? 'positive' : 'negative'}}">
          <text class="highlight-icon">{{forecastTrend.profit > 0 ? '🟢' : '🔴'}}</text>
          <text class="highlight-text">净利润{{forecastTrend.profit > 0 ? '增长' : '下降'}} {{Math.abs(forecastTrend.profit)}}%</text>
        </view>
      </view>
    </view>

    <!-- 预测图表区域 -->
    <view class="forecast-chart-section">
      <view class="section-title">
        <text class="title-text">📊 趋势预测图</text>
        <text class="title-desc">基于历史数据的智能预测</text>
      </view>
      
      <view class="chart-container">
        <canvas canvas-id="forecastChart" 
                class="forecast-chart"
                disable-scroll="true"
                bindtouchstart="onChartTouchStart"
                bindtouchmove="onChartTouchMove"
                bindtouchend="onChartTouchEnd">
        </canvas>
        
        <!-- 图表图例 -->
        <view class="chart-legend">
          <view class="legend-group">
            <text class="legend-label">历史数据</text>
            <view class="legend-row">
              <view class="legend-item">
                <view class="legend-dot historical income"></view>
                <text class="legend-text">收入</text>
              </view>
              <view class="legend-item">
                <view class="legend-dot historical expense"></view>
                <text class="legend-text">支出</text>
              </view>
            </view>
          </view>
          
          <view class="legend-group">
            <text class="legend-label">预测数据</text>
            <view class="legend-row">
              <view class="legend-item">
                <view class="legend-dot forecast income"></view>
                <text class="legend-text">收入预测</text>
              </view>
              <view class="legend-item">
                <view class="legend-dot forecast expense"></view>
                <text class="legend-text">支出预测</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细预测数据 -->
    <view class="forecast-details-section">
      <view class="section-title">
        <text class="title-text">📋 详细预测</text>
        <text class="title-desc">按月度展示预测数据</text>
      </view>
      
      <view class="details-list">
        <view wx:for="{{forecastData}}" wx:key="month" class="detail-item">
          <view class="detail-header">
            <text class="detail-month">{{item.month}}</text>
            <view class="detail-status {{item.trend}}">
              <text class="status-icon">{{item.trend === 'up' ? '📈' : item.trend === 'down' ? '📉' : '➡️'}}</text>
              <text class="status-text">{{item.trend === 'up' ? '上升' : item.trend === 'down' ? '下降' : '持平'}}</text>
            </view>
          </view>
          
          <view class="detail-metrics">
            <view class="metric-item">
              <text class="metric-label">预测收入</text>
              <text class="metric-value income">¥{{item.income}}</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">预测支出</text>
              <text class="metric-value expense">¥{{item.expense}}</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">预测利润</text>
              <text class="metric-value profit">¥{{item.profit}}</text>
            </view>
          </view>
          
          <view class="detail-confidence">
            <text class="confidence-text">预测可信度: {{item.confidence}}%</text>
            <view class="confidence-bar">
              <view class="confidence-fill" style="width: {{item.confidence}}%;"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 影响因子分析 -->
    <view class="factors-section">
      <view class="section-title">
        <text class="title-text">🎯 影响因子</text>
        <text class="title-desc">对预测结果影响较大的因素</text>
      </view>
      
      <view class="factors-grid">
        <view wx:for="{{influenceFactors}}" wx:key="id" class="factor-card">
          <view class="factor-header">
            <text class="factor-icon">{{item.icon}}</text>
            <text class="factor-name">{{item.name}}</text>
          </view>
          <view class="factor-impact">
            <text class="impact-label">影响度</text>
            <view class="impact-bar">
              <view class="impact-fill" 
                    style="width: {{item.impact}}%; background-color: {{item.color}};"></view>
            </view>
            <text class="impact-value">{{item.impact}}%</text>
          </view>
          <text class="factor-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

    <!-- 预测建议 -->
    <view class="recommendations-section">
      <view class="section-title">
        <text class="title-text">💡 预测建议</text>
        <text class="title-desc">基于预测结果的行动建议</text>
      </view>
      
      <view class="recommendations-list">
        <view wx:for="{{recommendations}}" wx:key="index" class="recommendation-card">
          <view class="recommendation-header">
            <text class="rec-icon">{{item.icon}}</text>
            <view class="rec-priority {{item.priority}}">
              {{item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低'}}优先级
            </view>
          </view>
          <text class="rec-title">{{item.title}}</text>
          <text class="rec-content">{{item.content}}</text>
          <view class="rec-timeline">
            <text class="timeline-label">建议执行时间:</text>
            <text class="timeline-value">{{item.timeline}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="onExportForecast">
        <text class="btn-icon">📊</text>
        <text class="btn-text">导出预测</text>
      </view>
      <view class="action-btn primary" bindtap="onCreatePlan">
        <text class="btn-icon">📋</text>
        <text class="btn-text">制定计划</text>
      </view>
    </view>
  </view>
</view>