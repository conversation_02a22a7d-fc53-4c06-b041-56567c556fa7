// pages/workspace/finance/forecast/forecast.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { checkPageAccess, getCurrentUser } = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    loadingProgress: 0,
    
    // 预测周期选项
    forecastPeriods: [
      { value: 3, label: '3个月' },
      { value: 6, label: '6个月' },
      { value: 9, label: '9个月' },
      { value: 12, label: '12个月' }
    ],
    selectedPeriodIndex: 1, // 默认6个月
    
    // 预测准确度
    confidenceLevel: 87,
    
    // 预测趋势
    forecastTrend: {
      income: 15.2,  // 收入增长百分比
      expense: 8.5,  // 支出增长百分比
      profit: 28.7   // 利润增长百分比
    },
    
    // 详细预测数据
    forecastData: [
      {
        month: '2024年4月',
        income: '135,000',
        expense: '98,500',
        profit: '36,500',
        confidence: 92,
        trend: 'up'
      },
      {
        month: '2024年5月',
        income: '142,000',
        expense: '101,200',
        profit: '40,800',
        confidence: 89,
        trend: 'up'
      },
      {
        month: '2024年6月',
        income: '138,500',
        expense: '103,800',
        profit: '34,700',
        confidence: 85,
        trend: 'down'
      },
      {
        month: '2024年7月',
        income: '145,800',
        expense: '106,200',
        profit: '39,600',
        confidence: 82,
        trend: 'up'
      },
      {
        month: '2024年8月',
        income: '151,200',
        expense: '108,500',
        profit: '42,700',
        confidence: 78,
        trend: 'up'
      },
      {
        month: '2024年9月',
        income: '148,900',
        expense: '111,000',
        profit: '37,900',
        confidence: 75,
        trend: 'down'
      }
    ],
    
    // 影响因子
    influenceFactors: [
      {
        id: 1,
        name: '季节性因素',
        icon: '🌤️',
        impact: 85,
        color: '#1890ff',
        description: '业务具有明显的季节性特征，春夏季收入较高'
      },
      {
        id: 2,
        name: '市场竞争',
        icon: '⚔️',
        impact: 72,
        color: '#fa8c16',
        description: '竞争对手策略变化对收入有较大影响'
      },
      {
        id: 3,
        name: '成本控制',
        icon: '💰',
        impact: 68,
        color: '#52c41a',
        description: '运营成本优化程度直接影响利润水平'
      },
      {
        id: 4,
        name: '政策影响',
        icon: '📋',
        impact: 45,
        color: '#722ed1',
        description: '行业政策变化可能带来成本或收入波动'
      }
    ],
    
    // 预测建议
    recommendations: [
      {
        icon: '📈',
        priority: 'high',
        title: '抓住收入增长机会',
        content: '预测显示4-5月收入将显著增长，建议提前准备充足的产能和人力资源，确保能够满足市场需求。',
        timeline: '立即执行'
      },
      {
        icon: '💡',
        priority: 'high',
        title: '优化6月成本结构',
        content: '预测6月利润会有所下降，建议提前审查成本结构，特别关注可变成本的控制。',
        timeline: '5月中旬前'
      },
      {
        icon: '🎯',
        priority: 'medium',
        title: '建立风险应对机制',
        content: '预测准确度在后期有所降低，建议建立风险监控体系，及时调整经营策略。',
        timeline: '本月内'
      },
      {
        icon: '📊',
        priority: 'medium',
        title: '加强数据收集',
        content: '为提高预测准确性，建议完善数据收集体系，特别是客户行为和市场变化数据。',
        timeline: '持续进行'
      },
      {
        icon: '🔄',
        priority: 'low',
        title: '定期更新预测模型',
        content: '建议每月更新一次预测模型，确保预测结果能够反映最新的业务情况。',
        timeline: '每月1号'
      }
    ],
    
    // 图表数据
    chartData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查页面访问权限
    if (!checkPageAccess('finance/forecast')) {
      wx.switchTab({
        url: '/pages/workspace/workspace'
      });
      return;
    }

    this.loadForecastData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 加载预测数据
   */
  async loadForecastData() {
    try {
      this.setData({ loading: true, loadingProgress: 0 });
      
      // 模拟AI分析过程
      await this.simulateAIAnalysis();
      
      // 并行加载数据
      const [
        trendData,
        detailData,
        factorData,
        recommendations
      ] = await Promise.all([
        this.loadTrendData(),
        this.loadDetailData(),
        this.loadFactorData(),
        this.loadRecommendations()
      ]);
      
      // 绘制预测图表
      this.drawForecastChart();
      
    } catch (error) {
      console.error('加载预测数据失败:', error);
      wx.showToast({
        title: '预测分析失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 模拟AI分析过程
   */
  async simulateAIAnalysis() {
    const steps = [
      { progress: 20, message: '正在分析历史数据...' },
      { progress: 40, message: '识别趋势模式中...' },
      { progress: 60, message: '计算影响因子...' },
      { progress: 80, message: '生成预测模型...' },
      { progress: 100, message: '预测完成！' }
    ];
    
    for (let step of steps) {
      await new Promise(resolve => setTimeout(resolve, 600));
      this.setData({
        loadingProgress: step.progress
      });
    }
  },

  /**
   * 加载趋势数据
   */
  async loadTrendData() {
    try {
      // 实际项目中调用预测API
      // const response = await request.post(API.API_ENDPOINTS.AI.FINANCIAL_FORECAST, {
      //   period: this.data.forecastPeriods[this.data.selectedPeriodIndex].value
      // });
      
      // 模拟图表数据
      const chartData = {
        historical: {
          labels: ['1月', '2月', '3月'],
          income: [95000, 102000, 128500],
          expense: [78000, 85000, 95200]
        },
        forecast: {
          labels: ['4月', '5月', '6月', '7月', '8月', '9月'],
          income: [135000, 142000, 138500, 145800, 151200, 148900],
          expense: [98500, 101200, 103800, 106200, 108500, 111000]
        }
      };
      
      this.setData({ chartData });
      return chartData;
    } catch (error) {
      console.error('加载趋势数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载详细数据
   */
  async loadDetailData() {
    try {
      // 实际项目中调用API
      return this.data.forecastData;
    } catch (error) {
      console.error('加载详细数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载影响因子数据
   */
  async loadFactorData() {
    try {
      // 实际项目中调用AI分析API
      return this.data.influenceFactors;
    } catch (error) {
      console.error('加载影响因子失败:', error);
      throw error;
    }
  },

  /**
   * 加载建议数据
   */
  async loadRecommendations() {
    try {
      // 实际项目中调用AI建议API
      return this.data.recommendations;
    } catch (error) {
      console.error('加载建议失败:', error);
      throw error;
    }
  },

  /**
   * 预测周期改变
   */
  onPeriodChange(e) {
    const selectedPeriodIndex = parseInt(e.detail.value);
    this.setData({ selectedPeriodIndex });
    
    // 重新加载预测数据
    this.loadForecastData();
    
    wx.showToast({
      title: `已切换到${this.data.forecastPeriods[selectedPeriodIndex].label}预测`,
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 绘制预测图表
   */
  drawForecastChart() {
    const chartData = this.data.chartData;
    if (!chartData) return;
    
    const query = wx.createSelectorQuery();
    query.select('.forecast-chart')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0]) return;
        
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;
        ctx.scale(dpr, dpr);
        
        // 绘制预测图表
        this.drawForecastLineChart(ctx, res[0].width, res[0].height, chartData);
      });
  },

  /**
   * 绘制预测折线图
   */
  drawForecastLineChart(ctx, width, height, data) {
    const padding = 60;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 合并历史和预测数据
    const allLabels = [...data.historical.labels, ...data.forecast.labels];
    const allIncomeData = [...data.historical.income, ...data.forecast.income];
    const allExpenseData = [...data.historical.expense, ...data.forecast.expense];
    
    // 获取数据范围
    const maxValue = Math.max(...allIncomeData, ...allExpenseData);
    const minValue = Math.min(...allIncomeData, ...allExpenseData);
    const valueRange = maxValue - minValue;
    
    // 绘制网格线
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
      
      // 绘制Y轴标签
      ctx.fillStyle = '#666';
      ctx.font = '20rpx Arial';
      const value = maxValue - (valueRange / 5) * i;
      ctx.fillText(Math.round(value / 1000) + 'k', 10, y + 3);
    }
    
    // 绘制X轴网格线和标签
    const historyLength = data.historical.labels.length;
    allLabels.forEach((label, index) => {
      const x = padding + (chartWidth / (allLabels.length - 1)) * index;
      
      // 网格线
      ctx.strokeStyle = '#f0f0f0';
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
      
      // 标签
      ctx.fillStyle = index < historyLength ? '#333' : '#999';
      ctx.font = '18rpx Arial';
      ctx.textAlign = 'center';
      ctx.fillText(label, x, height - 20);
    });
    
    // 绘制分割线（历史数据和预测数据的分界线）
    const splitX = padding + (chartWidth / (allLabels.length - 1)) * (historyLength - 1);
    ctx.strokeStyle = '#d9d9d9';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 5]);
    ctx.beginPath();
    ctx.moveTo(splitX, padding);
    ctx.lineTo(splitX, height - padding);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // 绘制历史收入线
    this.drawDataLine(ctx, data.historical.income, 0, historyLength, allLabels.length, chartWidth, chartHeight, padding, maxValue, minValue, '#52c41a', 3, false);
    
    // 绘制历史支出线  
    this.drawDataLine(ctx, data.historical.expense, 0, historyLength, allLabels.length, chartWidth, chartHeight, padding, maxValue, minValue, '#ff4d4f', 3, false);
    
    // 绘制预测收入线
    this.drawDataLine(ctx, [...data.historical.income.slice(-1), ...data.forecast.income], historyLength - 1, data.forecast.length + 1, allLabels.length, chartWidth, chartHeight, padding, maxValue, minValue, '#52c41a', 3, true);
    
    // 绘制预测支出线
    this.drawDataLine(ctx, [...data.historical.expense.slice(-1), ...data.forecast.expense], historyLength - 1, data.forecast.length + 1, allLabels.length, chartWidth, chartHeight, padding, maxValue, minValue, '#ff4d4f', 3, true);
  },

  /**
   * 绘制数据线
   */
  drawDataLine(ctx, data, startIndex, dataLength, totalLength, chartWidth, chartHeight, padding, maxValue, minValue, color, lineWidth, isDashed) {
    const valueRange = maxValue - minValue;
    
    ctx.strokeStyle = color;
    ctx.lineWidth = lineWidth;
    
    if (isDashed) {
      ctx.setLineDash([8, 4]);
      ctx.globalAlpha = 0.8;
    }
    
    ctx.beginPath();
    data.forEach((value, index) => {
      const x = padding + (chartWidth / (totalLength - 1)) * (startIndex + index);
      const y = padding + chartHeight - ((value - minValue) / valueRange) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
    
    // 重置样式
    ctx.setLineDash([]);
    ctx.globalAlpha = 1;
  },

  /**
   * 导出预测报告
   */
  async onExportForecast() {
    wx.showLoading({
      title: '生成预测报告...'
    });
    
    try {
      // 模拟报告生成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      wx.hideLoading();
      wx.showModal({
        title: '预测报告生成成功',
        content: '财务预测报告已生成完成，包含详细预测数据和建议，是否立即查看？',
        confirmText: '立即查看',
        cancelText: '稍后查看',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/workspace/finance/reports/reports?type=forecast'
            });
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 制定计划
   */
  onCreatePlan() {
    wx.showModal({
      title: '制定财务计划',
      content: '基于预测结果制定详细的财务计划，包含预算分配、风险控制等内容。',
      confirmText: '开始制定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到计划制定页面
          wx.navigateTo({
            url: '/pages/workspace/finance/plan/plan?forecast=' + JSON.stringify({
              period: this.data.forecastPeriods[this.data.selectedPeriodIndex],
              data: this.data.forecastData,
              recommendations: this.data.recommendations
            })
          });
        }
      }
    });
  },

  /**
   * 图表触摸事件处理
   */
  onChartTouchStart(e) {
    // 图表交互逻辑
  },

  onChartTouchMove(e) {
    // 图表交互逻辑
  },

  onChartTouchEnd(e) {
    // 图表交互逻辑
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadForecastData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '财务预测报告',
      path: '/pages/workspace/finance/forecast/forecast'
    };
  }
})