/* pages/workspace/finance/forecast/forecast.wxss */

.forecast-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fafbff 0%, #e8f0fe 100%);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
  color: var(--text-inverse);
  padding: var(--space-2xl) var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-lg);
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-sm);
  letter-spacing: 1rpx;
}

.page-subtitle {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
  font-weight: 400;
}

/* 预测控制区域 */
.forecast-controls {
  margin-left: var(--space-lg);
}

.time-selector {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.period-picker {
  min-width: 160rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-sm) var(--space-md);
  gap: var(--space-sm);
}

.period-text {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-inverse);
}

.dropdown-icon {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
  height: 60vh;
}

.ai-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-brain {
  font-size: 100rpx;
  animation: pulse 2s ease-in-out infinite;
  margin-bottom: var(--space-2xl);
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  font-weight: 500;
}

.loading-progress {
  width: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 预测摘要卡片 */
.forecast-summary-card {
  background: var(--bg-primary);
  margin: var(--space-xl);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.summary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.summary-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.confidence-badge {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-xs);
}

.confidence-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.confidence-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--success);
}

/* 预测亮点 */
.forecast-highlights {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.highlight-item.positive {
  background: var(--success-bg);
  border: 1rpx solid var(--success);
}

.highlight-item.negative {
  background: var(--error-bg);
  border: 1rpx solid var(--error);
}

.highlight-icon {
  font-size: 32rpx;
}

.highlight-text {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

/* 预测图表区域 */
.forecast-chart-section {
  margin: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
}

.section-title {
  margin-bottom: var(--space-xl);
}

.title-text {
  display: block;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.title-desc {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.chart-container {
  position: relative;
}

.forecast-chart {
  width: 100%;
  height: 500rpx;
  border-radius: var(--radius-lg);
}

/* 图表图例 */
.chart-legend {
  display: flex;
  justify-content: space-around;
  margin-top: var(--space-xl);
  padding-top: var(--space-xl);
  border-top: 1rpx solid var(--border-light);
}

.legend-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.legend-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
}

.legend-row {
  display: flex;
  gap: var(--space-lg);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: var(--radius-sm);
}

.legend-dot.historical.income {
  background: var(--success);
}

.legend-dot.historical.expense {
  background: var(--error);
}

.legend-dot.forecast.income {
  background: var(--success);
  opacity: 0.6;
  border: 2rpx dashed var(--success);
  background: transparent;
}

.legend-dot.forecast.expense {
  background: var(--error);
  opacity: 0.6;
  border: 2rpx dashed var(--error);
  background: transparent;
}

.legend-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* 详细预测数据 */
.forecast-details-section {
  margin: var(--space-xl);
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.detail-item {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.detail-item:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1rpx solid var(--border-light);
}

.detail-month {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.detail-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
}

.detail-status.up {
  background: var(--success-bg);
  color: var(--success);
}

.detail-status.down {
  background: var(--error-bg);
  color: var(--error);
}

.detail-status.stable {
  background: var(--info-bg);
  color: var(--info);
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-weight: 500;
}

/* 指标数据 */
.detail-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.metric-value {
  font-size: var(--text-base);
  font-weight: 700;
}

.metric-value.income {
  color: var(--success);
}

.metric-value.expense {
  color: var(--error);
}

.metric-value.profit {
  color: var(--info);
}

/* 可信度显示 */
.detail-confidence {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.confidence-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  min-width: 200rpx;
}

.confidence-bar {
  flex: 1;
  height: 8rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--warning) 0%, var(--success) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

/* 影响因子分析 */
.factors-section {
  margin: var(--space-xl);
}

.factors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
}

.factor-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
}

.factor-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.factor-icon {
  font-size: 32rpx;
}

.factor-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
}

.factor-impact {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

.impact-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  min-width: 100rpx;
}

.impact-bar {
  flex: 1;
  height: 8rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.impact-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.impact-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  min-width: 60rpx;
  text-align: right;
}

.factor-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* 预测建议 */
.recommendations-section {
  margin: var(--space-xl);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.recommendation-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.recommendation-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.recommendation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.rec-icon {
  font-size: 32rpx;
}

.rec-priority {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}

.rec-priority.high {
  background: var(--error-bg);
  color: var(--error);
}

.rec-priority.medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.rec-priority.low {
  background: var(--info-bg);
  color: var(--info);
}

.rec-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.rec-content {
  display: block;
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-lg);
}

.rec-timeline {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.timeline-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.timeline-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--primary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  padding-bottom: calc(var(--space-xl) + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-medium);
}

.action-btn.secondary:active {
  background: var(--bg-tertiary);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: var(--text-base);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-lg);
  }
  
  .forecast-controls {
    margin-left: 0;
    align-self: stretch;
  }
  
  .factors-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-metrics {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--space-md);
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}