<!--pages/workspace/finance/ai-comprehensive/ai-comprehensive.wxml-->
<view class="ai-comprehensive-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">数据加载中...</text>
  </view>

  <view wx:else class="content-container">

    <!-- 财务概览卡片 -->
    <view class="finance-overview-card">
      <view class="overview-header">
        <text class="overview-title">财务概览</text>
        <text class="date-range">{{dateRange}}</text>
      </view>
      
      <!-- 日期选择器 -->
      <view class="date-selector">
        <view class="date-item">
          <text class="date-label">开始日期</text>
          <picker mode="date" value="{{startDate}}" data-type="start" bindchange="onDateChange">
            <view class="date-picker">
              <text class="date-value">{{startDate}}</text>
              <text class="date-icon">📅</text>
            </view>
          </picker>
        </view>
        <view class="date-divider">至</view>
        <view class="date-item">
          <text class="date-label">结束日期</text>
          <picker mode="date" value="{{endDate}}" data-type="end" bindchange="onDateChange">
            <view class="date-picker">
              <text class="date-value">{{endDate}}</text>
              <text class="date-icon">📅</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="finance-stats-grid">
        <view class="stat-item">
          <text class="stat-label">总收入</text>
          <text class="stat-value income">¥{{totalIncome}}</text>
          <view class="stat-growth {{incomeGrowth >= 0 ? 'positive' : 'negative'}}">
            <text class="growth-icon">{{incomeGrowth >= 0 ? '📈' : '📉'}}</text>
            <text class="growth-text">{{incomeGrowth}}%</text>
          </view>
        </view>
        
        <view class="stat-item">
          <text class="stat-label">总支出</text>
          <text class="stat-value expense">¥{{totalExpense}}</text>
          <view class="stat-growth {{expenseGrowth >= 0 ? 'negative' : 'positive'}}">
            <text class="growth-icon">{{expenseGrowth >= 0 ? '📈' : '📉'}}</text>
            <text class="growth-text">{{expenseGrowth}}%</text>
          </view>
        </view>
        
        <view class="stat-item">
          <text class="stat-label">净利润</text>
          <text class="stat-value profit">¥{{netProfit}}</text>
          <view class="stat-growth {{profitGrowth >= 0 ? 'positive' : 'negative'}}">
            <text class="growth-icon">{{profitGrowth >= 0 ? '📈' : '📉'}}</text>
            <text class="growth-text">{{profitGrowth}}%</text>
          </view>
        </view>
        
        <view class="stat-item">
          <text class="stat-label">利润率</text>
          <text class="stat-value margin">{{profitMargin}}%</text>
          <view class="stat-status {{profitMargin > 20 ? 'excellent' : profitMargin > 10 ? 'good' : 'warning'}}">
            <text>{{profitMargin > 20 ? '优秀' : profitMargin > 10 ? '良好' : '待改善'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能模块切换导航 -->
    <view class="module-nav">
      <view class="nav-item {{activeModule === 'analysis' ? 'active' : ''}}" 
            data-module="analysis" bindtap="onModuleSwitch">
        <text class="nav-icon">📊</text>
        <text class="nav-text">财务分析</text>
      </view>
      <view class="nav-item {{activeModule === 'forecast' ? 'active' : ''}}" 
            data-module="forecast" bindtap="onModuleSwitch">
        <text class="nav-icon">🔮</text>
        <text class="nav-text">财务预测</text>
      </view>
      <view class="nav-item {{activeModule === 'suggestions' ? 'active' : ''}}" 
            data-module="suggestions" bindtap="onModuleSwitch">
        <text class="nav-icon">💡</text>
        <text class="nav-text">智能建议</text>
      </view>
      <view class="nav-item {{activeModule === 'export' ? 'active' : ''}}" 
            data-module="export" bindtap="onModuleSwitch">
        <text class="nav-icon">📋</text>
        <text class="nav-text">导出报表</text>
      </view>
    </view>

    <!-- 功能内容区域 -->
    <view class="module-content">
      
      <!-- 财务分析模块 -->
      <view wx:if="{{activeModule === 'analysis'}}" class="analysis-module">
        <view wx:if="{{analysis.loading}}" class="module-loading">
          <text class="loading-icon">🤖</text>
          <text class="loading-text">AI正在分析中...</text>
        </view>
        
        <view wx:else class="analysis-content">
          <!-- 财务健康度 -->
          <view class="health-score-card">
            <view class="score-header">
              <text class="score-title">财务健康度</text>
              <view class="score-circle {{analysis.healthScore >= 80 ? 'excellent' : analysis.healthScore >= 60 ? 'good' : 'warning'}}">
                <text class="score-value">{{analysis.healthScore}}</text>
                <text class="score-suffix">分</text>
              </view>
            </view>
            <text class="score-summary">{{analysis.summary}}</text>
          </view>

          <!-- 关键洞察 -->
          <view class="insights-section">
            <text class="section-title">关键洞察</text>
            <view class="insights-list">
              <view wx:for="{{analysis.keyInsights}}" wx:key="index" class="insight-item">
                <text class="insight-icon">{{item.icon}}</text>
                <text class="insight-text">{{item.text}}</text>
              </view>
            </view>
          </view>

          <!-- 支出分类分析 -->
          <view class="categories-section">
            <text class="section-title">支出分类分析</text>
            <view class="categories-list">
              <view wx:for="{{analysis.categories}}" wx:key="id" class="category-item">
                <view class="category-info">
                  <view class="category-header">
                    <text class="category-icon">{{item.icon}}</text>
                    <text class="category-name">{{item.name}}</text>
                  </view>
                  <view class="category-stats">
                    <text class="category-amount">¥{{item.amount}}</text>
                    <text class="category-percentage">{{item.percentage}}%</text>
                  </view>
                </view>
                <view class="category-bar">
                  <view class="bar-fill" style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
                </view>
              </view>
            </view>
          </view>

          <!-- AI建议 -->
          <view class="suggestions-section">
            <text class="section-title">AI智能建议</text>
            <view class="suggestions-list">
              <view wx:for="{{analysis.suggestions}}" wx:key="index" 
                    class="suggestion-item priority-{{item.priority}}">
                <view class="suggestion-header">
                  <text class="suggestion-icon">{{item.icon}}</text>
                  <view class="suggestion-info">
                    <text class="suggestion-title">{{item.title}}</text>
                    <text class="suggestion-priority">{{item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级'}}</text>
                  </view>
                </view>
                <text class="suggestion-description">{{item.description}}</text>
                <text class="suggestion-impact">预期效果: {{item.impact}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 财务预测模块 -->
      <view wx:if="{{activeModule === 'forecast'}}" class="forecast-module">
        <view wx:if="{{forecast.loading}}" class="module-loading">
          <text class="loading-icon">🔮</text>
          <text class="loading-text">AI正在预测中...</text>
        </view>
        
        <view wx:else class="forecast-content">
          <!-- 预测摘要 -->
          <view class="forecast-summary-card">
            <view class="summary-header">
              <text class="summary-title">预测摘要</text>
              <text class="summary-icon">🔮</text>
            </view>
            <text class="summary-content">{{forecast.summary}}</text>
          </view>

          <!-- 收入预测 -->
          <view class="predict-section">
            <text class="section-title">收入预测</text>
            <view class="predict-chart">
              <view wx:for="{{forecast.incomePredict}}" wx:key="month" class="chart-item">
                <view class="chart-bar income-bar" style="height: {{item.amount / 2000}}px;"></view>
                <text class="chart-label">{{item.month}}</text>
                <text class="chart-value">¥{{item.amount / 1000}}k</text>
                <text class="chart-growth {{item.growth >= 0 ? 'positive' : 'negative'}}">
                  {{item.growth >= 0 ? '+' : ''}}{{item.growth}}%
                </text>
              </view>
            </view>
          </view>

          <!-- 支出预测 -->
          <view class="predict-section">
            <text class="section-title">支出预测</text>
            <view class="predict-chart">
              <view wx:for="{{forecast.expensePredict}}" wx:key="month" class="chart-item">
                <view class="chart-bar expense-bar" style="height: {{item.amount / 2500}}px;"></view>
                <text class="chart-label">{{item.month}}</text>
                <text class="chart-value">¥{{item.amount / 1000}}k</text>
                <text class="chart-growth {{item.growth >= 0 ? 'negative' : 'positive'}}">
                  {{item.growth >= 0 ? '+' : ''}}{{item.growth}}%
                </text>
              </view>
            </view>
          </view>

          <!-- 现金流预测 -->
          <view class="cashflow-section">
            <text class="section-title">现金流预测</text>
            <view class="cashflow-list">
              <view wx:for="{{forecast.cashFlowPredict}}" wx:key="month" class="cashflow-item">
                <text class="cashflow-month">{{item.month}}</text>
                <text class="cashflow-amount">¥{{item.amount / 1000}}k</text>
                <view class="cashflow-status status-{{item.status === '优秀' ? 'excellent' : item.status === '良好' ? 'good' : 'warning'}}">
                  <text>{{item.status}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 风险情景分析 -->
          <view class="risk-scenarios-section">
            <text class="section-title">风险情景分析</text>
            <view class="scenarios-list">
              <view wx:for="{{forecast.riskScenarios}}" wx:key="scenario" class="scenario-item">
                <view class="scenario-header">
                  <text class="scenario-name">{{item.scenario}}</text>
                  <view class="scenario-probability">
                    <text class="probability-value">{{item.probability}}%</text>
                    <text class="probability-label">概率</text>
                  </view>
                </view>
                <text class="scenario-description">{{item.description}}</text>
                <text class="scenario-impact">{{item.impact}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 智能建议模块 -->
      <view wx:if="{{activeModule === 'suggestions'}}" class="suggestions-module">
        <view wx:if="{{suggestions.loading}}" class="module-loading">
          <text class="loading-icon">💡</text>
          <text class="loading-text">AI正在生成建议...</text>
        </view>
        
        <view wx:else class="suggestions-content">
          <!-- 短期建议 -->
          <view class="suggestions-section">
            <text class="section-title">短期建议 (1-3月)</text>
            <view class="suggestions-list">
              <view wx:for="{{suggestions.shortTerm}}" wx:key="index" class="suggestion-card">
                <view class="suggestion-header">
                  <text class="suggestion-title">{{item.title}}</text>
                  <text class="suggestion-timeframe">{{item.timeframe}}</text>
                </view>
                <text class="suggestion-description">{{item.description}}</text>
                <text class="suggestion-impact">预期效果: {{item.impact}}</text>
              </view>
            </view>
          </view>

          <!-- 中期建议 -->
          <view class="suggestions-section">
            <text class="section-title">中期建议 (3-12月)</text>
            <view class="suggestions-list">
              <view wx:for="{{suggestions.mediumTerm}}" wx:key="index" class="suggestion-card">
                <view class="suggestion-header">
                  <text class="suggestion-title">{{item.title}}</text>
                  <text class="suggestion-timeframe">{{item.timeframe}}</text>
                </view>
                <text class="suggestion-description">{{item.description}}</text>
                <text class="suggestion-impact">预期效果: {{item.impact}}</text>
              </view>
            </view>
          </view>

          <!-- 成本优化建议 -->
          <view class="optimization-section">
            <text class="section-title">成本优化方案</text>
            <view class="optimization-list">
              <view wx:for="{{suggestions.costOptimization}}" wx:key="category" class="optimization-item">
                <text class="optimization-category">{{item.category}}</text>
                <view class="optimization-comparison">
                  <view class="comparison-item">
                    <text class="comparison-label">当前</text>
                    <text class="comparison-value current">{{item.current}}</text>
                  </view>
                  <text class="comparison-arrow">→</text>
                  <view class="comparison-item">
                    <text class="comparison-label">目标</text>
                    <text class="comparison-value target">{{item.target}}</text>
                  </view>
                </view>
                <text class="optimization-method">方法: {{item.method}}</text>
              </view>
            </view>
          </view>

          <!-- 收入提升建议 -->
          <view class="revenue-section">
            <text class="section-title">收入提升方案</text>
            <view class="revenue-list">
              <view wx:for="{{suggestions.revenueBoost}}" wx:key="channel" class="revenue-item">
                <text class="revenue-channel">{{item.channel}}</text>
                <view class="revenue-comparison">
                  <view class="comparison-item">
                    <text class="comparison-label">当前</text>
                    <text class="comparison-value current">{{item.current}}</text>
                  </view>
                  <text class="comparison-arrow">→</text>
                  <view class="comparison-item">
                    <text class="comparison-label">潜力</text>
                    <text class="comparison-value potential">{{item.potential}}</text>
                  </view>
                </view>
                <text class="revenue-strategy">策略: {{item.strategy}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 导出报表模块 -->
      <view wx:if="{{activeModule === 'export'}}" class="export-module">
        <view class="export-content">
          <view class="export-header">
            <text class="export-title">报表导出中心</text>
            <text class="export-subtitle">生成专业的财务分析报告</text>
          </view>

          <view class="export-types">
            <view wx:for="{{export.reportTypes}}" wx:key="id" class="export-type-item" 
                  data-type="{{item.id}}" bindtap="onExportReport">
              <view class="export-type-icon">{{item.icon}}</view>
              <view class="export-type-info">
                <text class="export-type-name">{{item.name}}</text>
                <text class="export-type-desc">
                  {{item.id === 'summary' ? '包含关键财务指标和趋势分析' : 
                    item.id === 'detailed' ? '详细的收支明细和分类统计' :
                    item.id === 'analysis' ? 'AI分析结果和改进建议' :
                    '基于AI算法的财务预测报告'}}
                </text>
              </view>
              <view class="export-arrow">></view>
            </view>
          </view>

          <!-- 导出说明 -->
          <view class="export-notes">
            <text class="notes-title">导出说明</text>
            <view class="notes-list">
              <text class="note-item">• 支持PDF、Excel格式导出</text>
              <text class="note-item">• 报表内容基于当前选择的时间范围</text>
              <text class="note-item">• AI分析报告包含详细的建议和执行计划</text>
              <text class="note-item">• 导出后可通过微信分享或保存到相册</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 刷新按钮 -->
    <view class="refresh-button {{refreshing ? 'refreshing' : ''}}" bindtap="onRefresh">
      <text class="refresh-icon">🔄</text>
      <text class="refresh-text">{{refreshing ? '刷新中...' : '刷新数据'}}</text>
    </view>
  </view>
</view>