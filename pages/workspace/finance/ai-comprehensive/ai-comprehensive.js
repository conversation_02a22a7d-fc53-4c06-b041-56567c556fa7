// pages/workspace/finance/ai-comprehensive/ai-comprehensive.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { checkPageAccess, getCurrentUser } = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    refreshing: false,
    
    // 用户信息和权限状态
    currentUser: null,
    hasPermission: false,
    permissionChecked: false,
    
    // 当前激活的功能模块
    activeModule: 'analysis', // analysis, forecast, suggestions, export
    
    // 财务统计数据
    dateRange: '',
    startDate: '',
    endDate: '',
    totalIncome: '0.00',
    totalExpense: '0.00',
    netProfit: '0.00',
    profitMargin: '0.0',
    
    // 增长趋势
    incomeGrowth: 0,
    expenseGrowth: 0,
    profitGrowth: 0,
    
    // 分析结果
    analysis: {
      loading: false,
      healthScore: 0,
      summary: '',
      keyInsights: [],
      categories: [],
      suggestions: []
    },
    
    // 预测结果
    forecast: {
      loading: false,
      summary: '',
      incomePredict: [],
      expensePredict: [],
      cashFlowPredict: [],
      riskScenarios: []
    },
    
    // 智能建议
    suggestions: {
      loading: false,
      shortTerm: [],
      mediumTerm: [],
      costOptimization: [],
      revenueBoost: []
    },
    
    // 导出状态
    export: {
      loading: false,
      reportTypes: [
        { id: 'summary', name: '财务汇总报告', icon: '📊' },
        { id: 'detailed', name: '详细财务报告', icon: '📋' },
        { id: 'analysis', name: 'AI分析报告', icon: '🤖' },
        { id: 'forecast', name: '预测分析报告', icon: '🔮' }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('[AI财务分析] 页面加载开始，检查访问权限...');
    
    try {
      // 引入权限诊断工具
      const { PermissionDiagnostic } = require('../../../../utils/helpers/permission-diagnostic.js');
      const diagnostic = new PermissionDiagnostic();
      
      // 执行权限诊断
      const diagnosticReport = await diagnostic.performFullDiagnostic();
      
      // 获取当前用户信息
      const currentUser = getCurrentUser();
      console.log('[AI财务分析] 诊断完成，当前用户:', currentUser);
      
      // 检查页面访问权限
      const hasPermission = checkPageAccess('finance/ai-comprehensive');
      console.log('[AI财务分析] 权限检查结果:', hasPermission);
      
      // 更新权限状态
      this.setData({
        currentUser: currentUser,
        hasPermission: hasPermission,
        permissionChecked: true
      });
      
      if (!hasPermission) {
        console.warn('[AI财务分析] 用户无访问权限，立即拒绝访问');
        
        // 显示权限错误信息
        const roleDisplayNames = {
          'admin': '系统管理员',
          'manager': '经理',
          'finance': '财务人员',
          'employee': '普通员工'
        };
        
        const currentRoleDisplay = roleDisplayNames[currentUser?.role] || currentUser?.role || '未知角色';
        
        wx.showModal({
          title: '访问受限',
          content: `当前用户：${currentUser?.name || '未登录用户'}\n当前角色：${currentRoleDisplay}\n\n该功能仅限以下角色访问：\n• 系统管理员\n• 经理\n• 财务人员`,
          showCancel: false,
          confirmText: '返回',
          complete: () => {
            // 返回上一页或跳转到工作台
            const pages = getCurrentPages();
            if (pages.length > 1) {
              wx.navigateBack();
            } else {
              wx.switchTab({
                url: '/pages/workspace/workspace'
              });
            }
          }
        });
        return;
      }
      
      // 权限验证通过，初始化页面
      console.log('[AI财务分析] 权限验证通过，开始初始化页面');
      this.initializePage();
      
    } catch (error) {
      console.error('[AI财务分析] 权限检查出错:', error);
      
      // 出错时显示错误信息
      wx.showModal({
        title: '权限检查失败',
        content: '权限系统出现异常，请联系技术支持。\n\n错误信息：' + (error.message || '未知错误'),
        showCancel: false,
        confirmText: '返回工作台',
        success: () => {
          wx.switchTab({
            url: '/pages/workspace/workspace'
          });
        }
      });
    }
  },

  /**
   * 初始化页面
   */
  async initializePage() {
    this.initializeDates();
    await this.loadBasicFinanceData();
    // 默认加载分析数据
    await this.loadAnalysisData();
  },

  /**
   * 初始化日期范围
   */
  initializeDates() {
    const today = new Date();
    const endDate = this.formatDate(today);
    
    // 默认显示最近3个月的数据
    const startDateObj = new Date(today);
    startDateObj.setMonth(startDateObj.getMonth() - 3);
    const startDate = this.formatDate(startDateObj);
    
    this.setData({
      startDate,
      endDate,
      dateRange: `${startDate} 至 ${endDate}`
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 加载基础财务数据
   */
  async loadBasicFinanceData() {
    try {
      this.setData({ loading: true });
      
      // 这里可以调用实际的API
      // const response = await request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.BASIC_STATS, {
      //   startDate: this.data.startDate,
      //   endDate: this.data.endDate
      // });
      
      // 模拟数据
      const mockData = {
        totalIncome: '128,500.00',
        totalExpense: '95,200.00',
        netProfit: '33,300.00',
        profitMargin: '25.9',
        incomeGrowth: 12.5,
        expenseGrowth: 8.3,
        profitGrowth: 24.2
      };
      
      this.setData(mockData);
      
    } catch (error) {
      console.error('加载基础财务数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 模块切换
   */
  onModuleSwitch(e) {
    const { module } = e.currentTarget.dataset;
    
    if (module === this.data.activeModule) {
      return;
    }
    
    this.setData({ activeModule: module });
    
    // 根据切换的模块加载对应数据
    switch (module) {
      case 'analysis':
        this.loadAnalysisData();
        break;
      case 'forecast':
        this.loadForecastData();
        break;
      case 'suggestions':
        this.loadSuggestionsData();
        break;
      case 'export':
        // 导出模块不需要加载数据
        break;
    }
  },

  /**
   * 加载分析数据
   */
  async loadAnalysisData() {
    if (this.data.analysis.loading) return;
    
    try {
      this.setData({
        'analysis.loading': true
      });
      
      // 获取综合养鹅业务数据进行智能分析
      const gooseFarmingData = await this.getGooseFarmingData();
      const analysisData = this.performGooseFarmingAIAnalysis(gooseFarmingData);
      
      this.setData({
        'analysis.healthScore': analysisData.healthScore,
        'analysis.summary': analysisData.summary,
        'analysis.keyInsights': analysisData.keyInsights,
        'analysis.categories': analysisData.categories,
        'analysis.suggestions': analysisData.suggestions
      });
      
    } catch (error) {
      console.error('加载分析数据失败:', error);
      wx.showToast({
        title: '分析加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        'analysis.loading': false
      });
    }
  },

  /**
   * 获取养鹅业务综合数据
   */
  async getGooseFarmingData() {
    // 综合获取各类业务数据
    const farmingData = {
      // 财务数据
      finance: {
        totalIncome: parseFloat(this.data.totalIncome.replace(/,/g, '')),
        totalExpense: parseFloat(this.data.totalExpense.replace(/,/g, '')),
        netProfit: parseFloat(this.data.netProfit.replace(/,/g, '')),
        profitMargin: parseFloat(this.data.profitMargin),
        incomeGrowth: this.data.incomeGrowth,
        expenseGrowth: this.data.expenseGrowth
      },
      
      // 生产数据 (模拟从生产模块获取)
      production: {
        totalGeese: 1200, // 总鹅数
        batchCount: 3, // 批次数量
        avgWeight: 3.2, // 平均重量(kg)
        growthRate: 85, // 生长达标率(%)
        feedConversionRate: 2.8, // 料肉比
        slaughterRate: 92 // 出栏率(%)
      },
      
      // 健康数据 (模拟从健康模块获取)
      health: {
        survivalRate: 94.2, // 存活率(%)
        healthyCount: 1130,
        sickCount: 65,
        deathCount: 5,
        vaccinationRate: 98, // 疫苗接种率(%)
        diseaseIncidence: 5.4 // 疾病发生率(%)
      },
      
      // 市场价格数据 (从价格组件获取)
      marketPrice: {
        goslingPrice: 13.0, // 鹅苗均价(元/只)
        adultGoosePrice: 19.6, // 成鹅均价(元/斤)
        feedPrice: 3.2, // 饲料价格(元/斤)
        priceVolatility: 'medium' // 价格波动性: low/medium/high
      },
      
      // 成本结构数据
      costStructure: {
        feedCost: 52000, // 饲料成本
        laborCost: 18000, // 人工成本
        medicineCost: 8500, // 药品成本
        facilityCost: 12000, // 设施成本
        utilitiesCost: 4500, // 水电成本
        otherCost: 3000 // 其他成本
      },
      
      // 销售数据
      sales: {
        totalSoldCount: 800, // 累计出栏数量
        averageSellingPrice: 19.8, // 平均售价(元/斤)
        salesChannels: ['批发市场', '直销', '电商平台'],
        customerSatisfaction: 4.3 // 客户满意度(5分制)
      }
    };
    
    return farmingData;
  },

  /**
   * 执行养鹅业务AI智能分析
   */
  performGooseFarmingAIAnalysis(data) {
    // 计算综合健康评分
    const healthScore = this.calculateFarmHealthScore(data);
    
    // 生成智能摘要
    const summary = this.generateFarmingSummary(data, healthScore);
    
    // 分析关键洞察
    const keyInsights = this.analyzeKeyInsights(data);
    
    // 分析成本结构
    const categories = this.analyzeCostStructure(data);
    
    // 生成智能建议
    const suggestions = this.generateFarmingSuggestions(data);
    
    return {
      healthScore,
      summary,
      keyInsights,
      categories,
      suggestions
    };
  },

  /**
   * 计算养殖场健康评分
   */
  calculateFarmHealthScore(data) {
    let score = 0;
    let totalWeights = 0;
    
    // 财务健康度 (权重: 30%)
    const profitMargin = data.finance.profitMargin;
    let financialScore = 0;
    if (profitMargin > 25) financialScore = 100;
    else if (profitMargin > 15) financialScore = 80;
    else if (profitMargin > 5) financialScore = 60;
    else if (profitMargin > 0) financialScore = 40;
    else financialScore = 20;
    
    score += financialScore * 0.3;
    totalWeights += 0.3;
    
    // 生产效率 (权重: 25%)
    const productionScore = (data.production.growthRate + data.production.slaughterRate) / 2;
    score += productionScore * 0.25;
    totalWeights += 0.25;
    
    // 健康状况 (权重: 25%)
    const healthScore = Math.min(100, data.health.survivalRate + (100 - data.health.diseaseIncidence) * 0.5);
    score += healthScore * 0.25;
    totalWeights += 0.25;
    
    // 成本控制 (权重: 20%)
    const feedCostRatio = data.costStructure.feedCost / data.finance.totalIncome;
    let costScore = 0;
    if (feedCostRatio < 0.4) costScore = 100;
    else if (feedCostRatio < 0.5) costScore = 80;
    else if (feedCostRatio < 0.6) costScore = 60;
    else if (feedCostRatio < 0.7) costScore = 40;
    else costScore = 20;
    
    score += costScore * 0.2;
    totalWeights += 0.2;
    
    return Math.round(score);
  },

  /**
   * 生成养殖业务智能摘要
   */
  generateFarmingSummary(data, healthScore) {
    const profitMargin = data.finance.profitMargin;
    const survivalRate = data.health.survivalRate;
    const growthRate = data.production.growthRate;
    
    let summary = '';
    
    if (healthScore >= 85) {
      summary = `您的养鹅场运营状况优秀！当前存活率${survivalRate}%，利润率${profitMargin}%，生长达标率${growthRate}%。各项指标均表现良好，建议继续保持现有管理模式。`;
    } else if (healthScore >= 70) {
      summary = `养鹅场运营状况良好。利润率${profitMargin}%，存活率${survivalRate}%。建议重点关注成本控制和疾病预防，有进一步优化空间。`;
    } else if (healthScore >= 60) {
      summary = `养鹅场运营状况一般，需要改进。当前利润率${profitMargin}%，存活率${survivalRate}%。建议从饲料成本控制和健康管理入手进行优化。`;
    } else {
      summary = `养鹅场运营面临挑战，急需改进。利润率${profitMargin}%，存活率${survivalRate}%。建议立即采取措施优化成本结构，加强疾病防控。`;
    }
    
    return summary;
  },

  /**
   * 分析关键洞察
   */
  analyzeKeyInsights(data) {
    const insights = [];
    
    // 存活率分析
    if (data.health.survivalRate > 95) {
      insights.push({
        icon: '🎯',
        text: `存活率高达${data.health.survivalRate}%，健康管理优秀，疾病防控措施有效`
      });
    } else if (data.health.survivalRate < 90) {
      insights.push({
        icon: '⚠️',
        text: `存活率${data.health.survivalRate}%偏低，建议加强疫苗接种和疾病预防措施`
      });
    }
    
    // 饲料成本分析
    const feedCostRatio = (data.costStructure.feedCost / data.finance.totalIncome * 100).toFixed(1);
    if (parseFloat(feedCostRatio) > 60) {
      insights.push({
        icon: '📊',
        text: `饲料成本占收入${feedCostRatio}%，比例偏高，建议优化饲料配方或寻找更优质供应商`
      });
    } else if (parseFloat(feedCostRatio) < 40) {
      insights.push({
        icon: '💰',
        text: `饲料成本控制良好，仅占收入${feedCostRatio}%，为利润增长提供了空间`
      });
    }
    
    // 料肉比分析
    if (data.production.feedConversionRate < 2.5) {
      insights.push({
        icon: '🚀',
        text: `料肉比${data.production.feedConversionRate}表现优秀，饲料利用效率高，养殖技术成熟`
      });
    } else if (data.production.feedConversionRate > 3.2) {
      insights.push({
        icon: '📈',
        text: `料肉比${data.production.feedConversionRate}需要改善，建议调整饲料配方和饲养管理`
      });
    }
    
    // 市场价格分析
    if (data.marketPrice.adultGoosePrice > 20) {
      insights.push({
        icon: '📈',
        text: `成鹅市场价格${data.marketPrice.adultGoosePrice}元/斤处于高位，适合增加出栏量`
      });
    } else if (data.marketPrice.adultGoosePrice < 18) {
      insights.push({
        icon: '⏰',
        text: `当前鹅价${data.marketPrice.adultGoosePrice}元/斤相对较低，建议等待更好时机出栏`
      });
    }
    
    // 生长效率分析
    if (data.production.growthRate > 90) {
      insights.push({
        icon: '🌟',
        text: `生长达标率${data.production.growthRate}%表现优异，饲养管理和环境控制到位`
      });
    }
    
    return insights.slice(0, 4); // 最多显示4个洞察
  },

  /**
   * 分析成本结构
   */
  analyzeCostStructure(data) {
    const totalCost = Object.values(data.costStructure).reduce((sum, cost) => sum + cost, 0);
    
    const categories = [
      {
        id: 1,
        name: '饲料成本',
        amount: data.costStructure.feedCost.toLocaleString() + '.00',
        percentage: ((data.costStructure.feedCost / totalCost) * 100).toFixed(1),
        icon: '🌾',
        color: '#ff6b6b'
      },
      {
        id: 2,
        name: '人工成本',
        amount: data.costStructure.laborCost.toLocaleString() + '.00',
        percentage: ((data.costStructure.laborCost / totalCost) * 100).toFixed(1),
        icon: '👥',
        color: '#4ecdc4'
      },
      {
        id: 3,
        name: '设施成本',
        amount: data.costStructure.facilityCost.toLocaleString() + '.00',
        percentage: ((data.costStructure.facilityCost / totalCost) * 100).toFixed(1),
        icon: '🏭',
        color: '#45b7d1'
      },
      {
        id: 4,
        name: '药品成本',
        amount: data.costStructure.medicineCost.toLocaleString() + '.00',
        percentage: ((data.costStructure.medicineCost / totalCost) * 100).toFixed(1),
        icon: '💊',
        color: '#96ceb4'
      },
      {
        id: 5,
        name: '水电成本',
        amount: data.costStructure.utilitiesCost.toLocaleString() + '.00',
        percentage: ((data.costStructure.utilitiesCost / totalCost) * 100).toFixed(1),
        icon: '⚡',
        color: '#feca57'
      }
    ];
    
    // 按金额排序并返回前4项
    return categories.sort((a, b) => b.percentage - a.percentage).slice(0, 4);
  },

  /**
   * 生成养殖业务智能建议
   */
  generateFarmingSuggestions(data) {
    const suggestions = [];
    
    // 存活率优化建议
    if (data.health.survivalRate < 95) {
      suggestions.push({
        icon: '🛡️',
        priority: 'high',
        title: '提升鹅群存活率',
        description: `当前存活率${data.health.survivalRate}%，建议加强疫苗接种计划，改善饲养环境卫生，定期进行健康检查。`,
        impact: `预期存活率提升至96%以上，年增收${Math.round((96 - data.health.survivalRate) * data.production.totalGeese * 0.01 * data.marketPrice.adultGoosePrice * 3.2)}元`
      });
    }
    
    // 饲料成本优化建议
    const feedCostRatio = data.costStructure.feedCost / data.finance.totalIncome;
    if (feedCostRatio > 0.5) {
      suggestions.push({
        icon: '🌾',
        priority: 'high',
        title: '优化饲料成本',
        description: '饲料成本占比较高，建议优化饲料配方，寻找性价比更高的原料供应商，或考虑自配饲料。',
        impact: `预期饲料成本降低15%，年节省${Math.round(data.costStructure.feedCost * 0.15)}元`
      });
    }
    
    // 料肉比改善建议
    if (data.production.feedConversionRate > 3.0) {
      suggestions.push({
        icon: '📊',
        priority: 'medium',
        title: '改善料肉比',
        description: `当前料肉比${data.production.feedConversionRate}，建议调整饲料营养配方，优化饲喂时间和方式，提高饲料利用率。`,
        impact: '料肉比降至2.8以下，每只鹅可节省饲料成本8-12元'
      });
    }
    
    // 出栏时机建议
    if (data.marketPrice.adultGoosePrice > 20) {
      suggestions.push({
        icon: '💰',
        priority: 'medium',
        title: '把握出栏时机',
        description: `当前鹅价${data.marketPrice.adultGoosePrice}元/斤处于高位，建议达到出栏标准的鹅群尽快出栏，锁定利润。`,
        impact: '及时出栏可增加收益10-15%'
      });
    } else if (data.marketPrice.adultGoosePrice < 18) {
      suggestions.push({
        icon: '⏰',
        priority: 'low',
        title: '延缓出栏等待时机',
        description: `当前鹅价${data.marketPrice.adultGoosePrice}元/斤相对较低，如饲养条件允许，建议适当延长饲养周期等待更好价格。`,
        impact: '等待合适时机出栏可增加收益5-8%'
      });
    }
    
    // 规模化建议
    if (data.finance.profitMargin > 20 && data.health.survivalRate > 93) {
      suggestions.push({
        icon: '📈',
        priority: 'low',
        title: '考虑适度扩大规模',
        description: '当前各项指标良好，可考虑增加鹅群数量，扩大养殖规模，提高整体收益。',
        impact: '规模扩大20%预期增加年收入30%'
      });
    }
    
    // 疾病预防建议
    if (data.health.diseaseIncidence > 5) {
      suggestions.push({
        icon: '🔬',
        priority: 'high',
        title: '强化疾病预防',
        description: `疾病发生率${data.health.diseaseIncidence}%偏高，建议完善防疫制度，定期消毒，加强营养管理。`,
        impact: '疾病发生率降低50%，可减少损失约' + Math.round(data.health.sickCount * data.marketPrice.adultGoosePrice * 3.2 * 0.5) + '元'
      });
    }
    
    return suggestions.slice(0, 4); // 返回最多4个建议
  },

  /**
   * 加载预测数据
   */
  async loadForecastData() {
    if (this.data.forecast.loading) return;
    
    try {
      this.setData({
        'forecast.loading': true
      });
      
      // 获取养鹅业务数据并进行智能预测
      const gooseFarmingData = await this.getGooseFarmingData();
      const forecastData = this.performGooseFarmingForecast(gooseFarmingData);
      
      this.setData({
        'forecast.summary': forecastData.summary,
        'forecast.incomePredict': forecastData.incomePredict,
        'forecast.expensePredict': forecastData.expensePredict,
        'forecast.cashFlowPredict': forecastData.cashFlowPredict,
        'forecast.riskScenarios': forecastData.riskScenarios
      });
      
    } catch (error) {
      console.error('加载预测数据失败:', error);
      wx.showToast({
        title: '预测加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        'forecast.loading': false
      });
    }
  },

  /**
   * 执行养鹅业务智能预测
   */
  performGooseFarmingForecast(data) {
    // 生成预测摘要
    const summary = this.generateForecastSummary(data);
    
    // 预测收入趋势
    const incomePredict = this.predictIncomeByGooseCycle(data);
    
    // 预测支出趋势
    const expensePredict = this.predictExpenseByFarmingCost(data);
    
    // 预测现金流
    const cashFlowPredict = this.predictCashFlowBySeasonality(data);
    
    // 生成风险情景
    const riskScenarios = this.generateGooseFarmingRiskScenarios(data);
    
    return {
      summary,
      incomePredict,
      expensePredict,
      cashFlowPredict,
      riskScenarios
    };
  },

  /**
   * 生成养鹅业务预测摘要
   */
  generateForecastSummary(data) {
    const currentSurvivalRate = data.health.survivalRate;
    const currentProfitMargin = data.finance.profitMargin;
    const feedCostRatio = (data.costStructure.feedCost / data.finance.totalIncome) * 100;
    
    let summary = '';
    
    if (currentSurvivalRate > 94 && currentProfitMargin > 20) {
      summary = `基于当前优良的养殖指标分析，预测未来6个月收入将稳步增长18-25%。存活率${currentSurvivalRate}%和利润率${currentProfitMargin}%为持续盈利提供保障，建议适度扩大养殖规模。`;
    } else if (currentSurvivalRate > 90 && currentProfitMargin > 10) {
      summary = `根据当前养殖数据预测，未来6个月收入增长预期12-18%。需关注饲料成本控制（当前占比${feedCostRatio.toFixed(1)}%），加强疾病防控以维持稳定增长。`;
    } else {
      summary = `基于当前养殖状况分析，未来6个月增长可能放缓至5-12%。建议重点改善存活率和成本结构，预计通过管理优化可恢复15%以上增长。`;
    }
    
    return summary;
  },

  /**
   * 基于养鹅周期预测收入
   */
  predictIncomeByGooseCycle(data) {
    const baseIncome = data.finance.totalIncome;
    const currentPrice = data.marketPrice.adultGoosePrice;
    const totalGeese = data.production.totalGeese;
    const monthlySlaughterRate = 0.15; // 假设月出栏率15%
    
    const months = ['下月', '2个月后', '3个月后', '4个月后', '5个月后', '6个月后'];
    const incomePredict = [];
    
    // 考虑季节性价格波动
    const seasonalFactors = [1.05, 1.08, 1.12, 1.15, 1.10, 1.06]; // 夏秋季鹅价相对较高
    
    months.forEach((month, index) => {
      // 基础月收入 = 出栏数量 × 平均重量 × 市场价格
      const slaughterCount = Math.round(totalGeese * monthlySlaughterRate);
      const avgWeight = 3.2 + (index * 0.1); // 假设鹅群平均重量逐月增加
      const seasonalPrice = currentPrice * seasonalFactors[index];
      const monthlyIncome = slaughterCount * avgWeight * seasonalPrice;
      
      // 计算增长率（相比当期）
      const growthRate = ((monthlyIncome - baseIncome) / baseIncome * 100).toFixed(1);
      
      incomePredict.push({
        month: month,
        amount: Math.round(monthlyIncome),
        growth: parseFloat(growthRate)
      });
    });
    
    return incomePredict;
  },

  /**
   * 基于养殖成本预测支出
   */
  predictExpenseByFarmingCost(data) {
    const feedCostBase = data.costStructure.feedCost;
    const laborCost = data.costStructure.laborCost;
    const medicineCost = data.costStructure.medicineCost;
    
    const months = ['下月', '2个月后', '3个月后', '4个月后', '5个月后', '6个月后'];
    const expensePredict = [];
    
    // 考虑饲料价格季节性变化和鹅群成长带来的饲料需求增加
    const feedGrowthFactors = [1.02, 1.05, 1.08, 1.12, 1.15, 1.10]; // 夏季饲料需求增加
    const priceInflationRate = 0.015; // 月通胀率1.5%
    
    months.forEach((month, index) => {
      // 预测饲料成本（考虑需求增长和价格上涨）
      const feedCost = feedCostBase * feedGrowthFactors[index] * (1 + priceInflationRate * index);
      
      // 预测人工成本（相对稳定，考虑适度增长）
      const projectedLaborCost = laborCost * (1 + 0.01 * index);
      
      // 预测药品成本（夏季疾病高发期成本增加）
      const medicineGrowthFactor = index >= 2 && index <= 4 ? 1.3 : 1.1;
      const projectedMedicineCost = medicineCost * medicineGrowthFactor;
      
      // 其他成本（水电、设施等）
      const otherCosts = data.costStructure.utilitiesCost + data.costStructure.facilityCost + data.costStructure.otherCost;
      
      const totalExpense = feedCost + projectedLaborCost + projectedMedicineCost + otherCosts;
      const growthRate = ((totalExpense - data.finance.totalExpense) / data.finance.totalExpense * 100).toFixed(1);
      
      expensePredict.push({
        month: month,
        amount: Math.round(totalExpense),
        growth: parseFloat(growthRate)
      });
    });
    
    return expensePredict;
  },

  /**
   * 基于季节性预测现金流
   */
  predictCashFlowBySeasonality(data) {
    const months = ['下月', '2个月后', '3个月后', '4个月后', '5个月后', '6个月后'];
    const cashFlowPredict = [];
    
    // 获取预测的收入和支出数据
    const incomePredict = this.predictIncomeByGooseCycle(data);
    const expensePredict = this.predictExpenseByFarmingCost(data);
    
    months.forEach((month, index) => {
      const projectedIncome = incomePredict[index].amount;
      const projectedExpense = expensePredict[index].amount;
      const cashFlow = projectedIncome - projectedExpense;
      
      let status = '';
      if (cashFlow > 50000) status = '优秀';
      else if (cashFlow > 30000) status = '良好';
      else if (cashFlow > 10000) status = '一般';
      else status = '紧张';
      
      cashFlowPredict.push({
        month: month,
        amount: Math.round(cashFlow),
        status: status
      });
    });
    
    return cashFlowPredict;
  },

  /**
   * 生成养鹅业务风险情景
   */
  generateGooseFarmingRiskScenarios(data) {
    return [
      {
        scenario: '理想情况',
        probability: 25,
        description: '鹅价持续上涨，存活率保持95%以上，饲料成本稳定',
        impact: `预计6个月净利润可达${Math.round(data.finance.netProfit * 1.4).toLocaleString()}元，较当前增长40%`
      },
      {
        scenario: '正常情况',
        probability: 50,
        description: '市场价格波动在正常范围，存活率维持90-95%',
        impact: `预计6个月净利润${Math.round(data.finance.netProfit * 1.15).toLocaleString()}元，增长15%左右`
      },
      {
        scenario: '挑战情况',
        probability: 20,
        description: '饲料价格上涨15%以上，或遭遇疾病影响存活率',
        impact: `净利润可能下降至${Math.round(data.finance.netProfit * 0.8).toLocaleString()}元，需加强风险管控`
      },
      {
        scenario: '极端情况',
        probability: 5,
        description: '遭遇重大疫病或极端天气，存活率低于85%',
        impact: '可能出现亏损，需要启动应急预案和保险理赔'
      }
    ];
  },

  /**
   * 加载建议数据
   */
  async loadSuggestionsData() {
    if (this.data.suggestions.loading) return;
    
    try {
      this.setData({
        'suggestions.loading': true
      });
      
      // 获取养鹅业务数据并生成专业建议
      const gooseFarmingData = await this.getGooseFarmingData();
      const suggestionsData = this.generateGooseFarmingSuggestions(gooseFarmingData);
      
      this.setData({
        'suggestions.shortTerm': suggestionsData.shortTerm,
        'suggestions.mediumTerm': suggestionsData.mediumTerm,
        'suggestions.costOptimization': suggestionsData.costOptimization,
        'suggestions.revenueBoost': suggestionsData.revenueBoost
      });
      
    } catch (error) {
      console.error('加载建议数据失败:', error);
      wx.showToast({
        title: '建议加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        'suggestions.loading': false
      });
    }
  },

  /**
   * 生成养鹅业务专业建议
   */
  generateGooseFarmingSuggestions(data) {
    // 短期建议 (1-3月)
    const shortTerm = this.generateShortTermFarmingSuggestions(data);
    
    // 中期建议 (3-12月)
    const mediumTerm = this.generateMediumTermFarmingSuggestions(data);
    
    // 成本优化方案
    const costOptimization = this.generateCostOptimizationPlan(data);
    
    // 收入提升方案
    const revenueBoost = this.generateRevenueBoostPlan(data);
    
    return {
      shortTerm,
      mediumTerm,
      costOptimization,
      revenueBoost
    };
  },

  /**
   * 生成短期养殖建议
   */
  generateShortTermFarmingSuggestions(data) {
    const suggestions = [];
    
    // 存活率改善建议
    if (data.health.survivalRate < 95) {
      suggestions.push({
        title: '强化疫病防控措施',
        description: `当前存活率${data.health.survivalRate}%，立即完善疫苗接种计划，加强鹅舍消毒，建立疾病监测预警机制。`,
        impact: '预期存活率提升至95%以上，减少死亡损失',
        timeframe: '1-2个月见效'
      });
    }
    
    // 饲料成本控制建议
    const feedCostRatio = (data.costStructure.feedCost / data.finance.totalIncome) * 100;
    if (feedCostRatio > 55) {
      suggestions.push({
        title: '优化饲料采购策略',
        description: `饲料成本占比${feedCostRatio.toFixed(1)}%偏高，建议调整饲料配方，寻找优质替代原料，实行集中采购降低成本。`,
        impact: '饲料成本降低10-15%，月节省成本约' + Math.round(data.costStructure.feedCost * 0.125) + '元',
        timeframe: '即刻执行'
      });
    }
    
    // 出栏时机建议
    if (data.marketPrice.adultGoosePrice > 20) {
      suggestions.push({
        title: '把握当前高价出栏',
        description: `成鹅价格${data.marketPrice.adultGoosePrice}元/斤处于高位，建议加快符合标准鹅只的出栏速度，锁定高收益。`,
        impact: '及时出栏可增加收益12-18%',
        timeframe: '立即行动'
      });
    } else if (data.marketPrice.adultGoosePrice < 18) {
      suggestions.push({
        title: '延后出栏等待时机',
        description: `当前鹅价${data.marketPrice.adultGoosePrice}元/斤相对较低，如条件允许，建议延长饲养周期等待价格回升。`,
        impact: '等待合适时机可增加收益8-12%',
        timeframe: '1-2个月调整'
      });
    }
    
    // 料肉比改善建议
    if (data.production.feedConversionRate > 3.2) {
      suggestions.push({
        title: '改善饲料利用效率',
        description: `料肉比${data.production.feedConversionRate}需要优化，调整饲喂时间和次数，改善饲料质量和配方结构。`,
        impact: '料肉比降至3.0以下，每只鹅节省饲料成本6-10元',
        timeframe: '2-3个月改善'
      });
    }
    
    return suggestions.slice(0, 3);
  },

  /**
   * 生成中期养殖建议
   */
  generateMediumTermFarmingSuggestions(data) {
    const suggestions = [];
    
    // 规模化发展建议
    if (data.finance.profitMargin > 18 && data.health.survivalRate > 92) {
      suggestions.push({
        title: '考虑适度扩大养殖规模',
        description: '当前经营指标良好，可考虑增加鹅舍数量和饲养规模，实现规模效益。需做好资金规划和技术储备。',
        impact: '规模扩大30%预期年增收50%以上',
        timeframe: '6-12个月规划'
      });
    }
    
    // 品种优化建议
    if (data.production.growthRate < 85) {
      suggestions.push({
        title: '引进优良鹅种改善品质',
        description: '当前鹅群生长达标率需改善，建议逐步引进生长快、抗病强的优良品种，提升整体生产效率。',
        impact: '预期生长达标率提升至90%以上，缩短饲养周期',
        timeframe: '3-6个月实施'
      });
    }
    
    // 自动化设备建议
    suggestions.push({
      title: '投资自动化养殖设备',
      description: '引进自动饮水系统、温控设备和粪便处理系统，提高饲养效率，降低人工成本。',
      impact: '人工成本降低25%，提高管理效率',
      timeframe: '6-9个月建设'
    });
    
    // 产业链延伸建议
    if (data.sales.customerSatisfaction > 4.0) {
      suggestions.push({
        title: '发展鹅产品深加工',
        description: '基于良好的市场口碑，可考虑发展鹅肉制品、鹅绒制品等深加工业务，提升产品附加值。',
        impact: '产品附加值提升40-60%，增加新的收入来源',
        timeframe: '9-12个月启动'
      });
    }
    
    return suggestions.slice(0, 3);
  },

  /**
   * 生成成本优化方案
   */
  generateCostOptimizationPlan(data) {
    const optimizations = [];
    
    // 饲料成本优化
    const feedCostOptimization = Math.round(data.costStructure.feedCost * 0.85);
    optimizations.push({
      category: '饲料成本',
      current: '¥' + data.costStructure.feedCost.toLocaleString() + '/月',
      target: '¥' + feedCostOptimization.toLocaleString() + '/月',
      method: '自配饲料、集中采购、优化配方结构，提高饲料转化率'
    });
    
    // 人工成本优化
    const laborCostOptimization = Math.round(data.costStructure.laborCost * 0.8);
    optimizations.push({
      category: '人工成本',
      current: '¥' + data.costStructure.laborCost.toLocaleString() + '/月',
      target: '¥' + laborCostOptimization.toLocaleString() + '/月',
      method: '引入自动化设备，优化人员配置，提高单人管理鹅只数量'
    });
    
    // 药品成本优化
    const medicineCostOptimization = Math.round(data.costStructure.medicineCost * 0.7);
    optimizations.push({
      category: '药品成本',
      current: '¥' + data.costStructure.medicineCost.toLocaleString() + '/月',
      target: '¥' + medicineCostOptimization.toLocaleString() + '/月',
      method: '加强预防保健，减少治疗用药，建立药品库存管理制度'
    });
    
    // 设施成本优化
    const facilityCostOptimization = Math.round(data.costStructure.facilityCost * 0.9);
    optimizations.push({
      category: '设施维护',
      current: '¥' + data.costStructure.facilityCost.toLocaleString() + '/月',
      target: '¥' + facilityCostOptimization.toLocaleString() + '/月',
      method: '定期保养设备，延长使用寿命，合理规划设施投资'
    });
    
    return optimizations;
  },

  /**
   * 生成收入提升方案
   */
  generateRevenueBoostPlan(data) {
    const boostPlans = [];
    
    // 提高出栏重量
    const weightImprovement = Math.round(data.sales.totalSoldCount * 3.5 * data.marketPrice.adultGoosePrice);
    boostPlans.push({
      channel: '提高出栏重量',
      current: '¥' + Math.round(data.sales.totalSoldCount * 3.2 * data.marketPrice.adultGoosePrice).toLocaleString() + '/批',
      potential: '¥' + weightImprovement.toLocaleString() + '/批',
      strategy: '优化饲料配方，延长饲养周期，平均出栏重量提升至3.5公斤'
    });
    
    // 提高存活率增加出栏量
    const survivalImprovement = Math.round(data.production.totalGeese * 0.96 * 3.2 * data.marketPrice.adultGoosePrice);
    boostPlans.push({
      channel: '提高存活率',
      current: '¥' + Math.round(data.production.totalGeese * (data.health.survivalRate/100) * 3.2 * data.marketPrice.adultGoosePrice).toLocaleString() + '/批',
      potential: '¥' + survivalImprovement.toLocaleString() + '/批',
      strategy: '强化疾病防控，提高存活率至96%以上，增加可出栏数量'
    });
    
    // 发展直销渠道
    const directSalesRevenue = Math.round(data.finance.totalIncome * 1.25);
    boostPlans.push({
      channel: '直销渠道拓展',
      current: '¥' + Math.round(data.finance.totalIncome).toLocaleString() + '/月',
      potential: '¥' + directSalesRevenue.toLocaleString() + '/月',
      strategy: '发展线上销售、农场直供、会员制销售，减少中间环节获得更高价格'
    });
    
    // 副产品开发
    const byProductRevenue = Math.round(data.finance.totalIncome * 0.15);
    boostPlans.push({
      channel: '副产品收入',
      current: '¥0/月',
      potential: '¥' + byProductRevenue.toLocaleString() + '/月',
      strategy: '开发鹅毛、鹅绒、鹅蛋等副产品，增加额外收入来源'
    });
    
    return boostPlans.slice(0, 3);
  },

  /**
   * 导出报表
   */
  async onExportReport(e) {
    const { type } = e.currentTarget.dataset;
    
    try {
      this.setData({ 'export.loading': true });
      
      wx.showLoading({
        title: '生成报表中...'
      });
      
      // 模拟报表生成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      wx.hideLoading();
      
      const reportNames = {
        summary: '财务汇总报告',
        detailed: '详细财务报告',
        analysis: 'AI分析报告',
        forecast: '预测分析报告'
      };
      
      wx.showModal({
        title: '报表生成成功',
        content: `${reportNames[type]}已生成完成，是否立即查看？`,
        confirmText: '立即查看',
        cancelText: '稍后查看',
        success: (res) => {
          if (res.confirm) {
            // 这里可以跳转到报表查看页面
            wx.showToast({
              title: '正在打开报表...',
              icon: 'loading'
            });
          }
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    } finally {
      this.setData({ 'export.loading': false });
    }
  },

  /**
   * 刷新数据
   */
  async onRefresh() {
    if (this.data.refreshing) return;
    
    this.setData({ refreshing: true });
    
    try {
      // 重新加载基础数据
      await this.loadBasicFinanceData();
      
      // 重新加载当前模块的数据
      switch (this.data.activeModule) {
        case 'analysis':
          await this.loadAnalysisData();
          break;
        case 'forecast':
          await this.loadForecastData();
          break;
        case 'suggestions':
          await this.loadSuggestionsData();
          break;
      }
      
      wx.showToast({
        title: '数据已更新',
        icon: 'success'
      });
      
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 日期范围改变
   */
  onDateChange(e) {
    const { type } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    if (type === 'start') {
      this.setData({ startDate: value });
    } else {
      this.setData({ endDate: value });
    }
    
    // 验证日期范围
    if (this.validateDateRange()) {
      this.setData({
        dateRange: `${this.data.startDate} 至 ${this.data.endDate}`
      });
      
      // 重新加载数据
      this.onRefresh();
    }
  },

  /**
   * 验证日期范围
   */
  validateDateRange() {
    const { startDate, endDate } = this.data;
    
    if (new Date(startDate) >= new Date(endDate)) {
      wx.showToast({
        title: '开始日期不能大于等于结束日期',
        icon: 'none'
      });
      return false;
    }
    
    // 检查日期范围是否超过1年
    const diffTime = new Date(endDate) - new Date(startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      wx.showToast({
        title: '日期范围不能超过1年',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'AI财务分析中心',
      path: '/pages/workspace/finance/ai-comprehensive/ai-comprehensive'
    };
  }
})