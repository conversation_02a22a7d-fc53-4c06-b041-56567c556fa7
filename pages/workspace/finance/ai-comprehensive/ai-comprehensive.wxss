/* pages/workspace/finance/ai-comprehensive/ai-comprehensive.wxss */

/* 基础容器样式 */
.ai-comprehensive-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 0;
}

.content-container {
  padding: 0 0 120rpx 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #fff;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #fff;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}

.header-subtitle {
  opacity: 0.9;
  font-size: 24rpx;
}

/* 财务概览卡片 */
.finance-overview-card {
  background: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.date-range {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 日期选择器 */
.date-selector {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.date-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.date-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.date-picker {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.date-picker:active {
  background: #e9ecef;
  border-color: #0066CC;
}

.date-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.date-icon {
  font-size: 24rpx;
  opacity: 0.6;
}

.date-divider {
  font-size: 24rpx;
  color: #999;
  margin: 0 20rpx;
  align-self: flex-end;
  margin-bottom: 20rpx;
}

/* 财务统计网格 */
.finance-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-value.income {
  color: #52c41a;
}

.stat-value.expense {
  color: #ff4d4f;
}

.stat-value.profit {
  color: #1890ff;
}

.stat-value.margin {
  color: #722ed1;
}

.stat-growth {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.stat-growth.positive {
  color: #52c41a;
}

.stat-growth.negative {
  color: #ff4d4f;
}

.growth-icon {
  margin-right: 4rpx;
}

.stat-status {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.stat-status.excellent {
  background: #f6ffed;
  color: #52c41a;
}

.stat-status.good {
  background: #e6f7ff;
  color: #1890ff;
}

.stat-status.warning {
  background: #fff7e6;
  color: #fa8c16;
}

/* 模块导航 */
.module-nav {
  display: flex;
  background: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  transform: scale(1.02);
}

.nav-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 22rpx;
  font-weight: 500;
}

.nav-item.active .nav-text {
  color: #fff;
}

/* 模块内容区域 */
.module-content {
  margin: 0 20rpx;
}

/* 模块加载状态 */
.module-loading {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.module-loading .loading-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 20rpx;
  animation: pulse 2s ease-in-out infinite;
}

.module-loading .loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 分析模块样式 */
.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 健康度评分卡片 */
.health-score-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  color: #fff;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.health-score-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.score-title {
  font-size: 32rpx;
  font-weight: 600;
}

.score-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  backdrop-filter: blur(10rpx);
}

.score-circle.excellent {
  background: rgba(82, 196, 26, 0.2);
  border: 3rpx solid #52c41a;
}

.score-circle.good {
  background: rgba(24, 144, 255, 0.2);
  border: 3rpx solid #1890ff;
}

.score-circle.warning {
  background: rgba(250, 140, 22, 0.2);
  border: 3rpx solid #fa8c16;
}

.score-value {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1;
}

.score-suffix {
  font-size: 20rpx;
  opacity: 0.8;
}

.score-summary {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.95;
  position: relative;
  z-index: 1;
}

/* 通用区块样式 */
.insights-section,
.categories-section,
.suggestions-section,
.predict-section,
.cashflow-section,
.risk-scenarios-section,
.optimization-section,
.revenue-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 洞察列表 */
.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.insight-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.insight-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 分类分析 */
.categories-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.category-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-header {
  display: flex;
  align-items: center;
}

.category-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.category-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.category-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.category-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.category-percentage {
  font-size: 22rpx;
  color: #666;
}

.category-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 建议列表 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid;
}

.suggestion-item.priority-high {
  background: #fff2f0;
  border-left-color: #ff4d4f;
}

.suggestion-item.priority-medium {
  background: #fff7e6;
  border-left-color: #fa8c16;
}

.suggestion-item.priority-low {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.suggestion-info {
  flex: 1;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}

.suggestion-priority {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.suggestion-item.priority-high .suggestion-priority {
  background: #ffebee;
  color: #ff4d4f;
}

.suggestion-item.priority-medium .suggestion-priority {
  background: #fff3e0;
  color: #fa8c16;
}

.suggestion-item.priority-low .suggestion-priority {
  background: #f1f8e9;
  color: #52c41a;
}

.suggestion-description {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.suggestion-impact {
  display: block;
  font-size: 24rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 预测模块样式 */
.forecast-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.forecast-summary-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  color: #333;
  position: relative;
  overflow: hidden;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-icon {
  font-size: 36rpx;
}

.summary-content {
  font-size: 26rpx;
  line-height: 1.6;
  color: #555;
}

/* 预测图表 */
.predict-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20rpx 0;
  height: 300rpx;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 100rpx;
}

.chart-bar {
  width: 24rpx;
  margin-bottom: 12rpx;
  border-radius: 12rpx 12rpx 0 0;
  transition: height 0.8s ease;
  min-height: 20rpx;
}

.income-bar {
  background: linear-gradient(180deg, #52c41a 0%, #73d13d 100%);
}

.expense-bar {
  background: linear-gradient(180deg, #ff4d4f 0%, #ff7875 100%);
}

.chart-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.chart-value {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.chart-growth {
  font-size: 18rpx;
  font-weight: 500;
}

.chart-growth.positive {
  color: #52c41a;
}

.chart-growth.negative {
  color: #ff4d4f;
}

/* 现金流列表 */
.cashflow-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.cashflow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.cashflow-month {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.cashflow-amount {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.cashflow-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.cashflow-status.status-excellent {
  background: #f6ffed;
  color: #52c41a;
}

.cashflow-status.status-good {
  background: #e6f7ff;
  color: #1890ff;
}

.cashflow-status.status-warning {
  background: #fff7e6;
  color: #fa8c16;
}

/* 风险情景 */
.scenarios-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.scenario-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 4rpx solid #667eea;
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.scenario-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.scenario-probability {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.probability-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #667eea;
}

.probability-label {
  font-size: 20rpx;
  color: #999;
}

.scenario-description,
.scenario-impact {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.scenario-impact {
  color: #1890ff;
  font-weight: 500;
  margin-bottom: 0;
}

/* 建议模块样式 */
.suggestions-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-card {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.suggestion-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.suggestion-timeframe {
  font-size: 20rpx;
  color: #667eea;
  background: #f0f2ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.suggestion-description {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.suggestion-impact {
  display: block;
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

/* 优化建议对比 */
.optimization-list,
.revenue-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.optimization-item,
.revenue-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.optimization-category,
.revenue-channel {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.optimization-comparison,
.revenue-comparison {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.comparison-label {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.comparison-value {
  font-size: 24rpx;
  font-weight: 600;
}

.comparison-value.current {
  color: #ff4d4f;
}

.comparison-value.target,
.comparison-value.potential {
  color: #52c41a;
}

.comparison-arrow {
  font-size: 24rpx;
  color: #1890ff;
  margin: 0 20rpx;
}

.optimization-method,
.revenue-strategy {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 导出模块样式 */
.export-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.export-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.export-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.export-subtitle {
  font-size: 24rpx;
  color: #666;
}

.export-types {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.export-type-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.export-type-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.export-type-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.export-type-info {
  flex: 1;
}

.export-type-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.export-type-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.export-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.export-notes {
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.notes-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.note-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 刷新按钮 */
.refresh-button {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}

.refresh-button:active {
  transform: scale(0.95);
}

.refresh-button.refreshing {
  opacity: 0.7;
  pointer-events: none;
}

.refresh-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  animation: none;
}

.refresh-button.refreshing .refresh-icon {
  animation: spin 1s linear infinite;
}

.refresh-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .finance-stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .module-nav {
    flex-wrap: wrap;
  }
  
  .nav-item {
    min-width: 25%;
    margin-bottom: 8rpx;
  }
  
  .predict-chart {
    height: 250rpx;
  }
  
  .chart-item {
    max-width: 80rpx;
  }
  
  .chart-bar {
    width: 20rpx;
  }
}