/* pages/workspace/finance/suggestions/suggestions.wxss */

.suggestions-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f8ff 0%, #e6f3ff 100%);
  padding-bottom: var(--space-2xl);
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: var(--text-inverse);
  padding: var(--space-2xl) var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-lg);
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-sm);
  letter-spacing: 1rpx;
}

.page-subtitle {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
  font-weight: 400;
}

.ai-indicator {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-sm) var(--space-lg);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.ai-badge {
  font-size: var(--text-base);
  font-weight: 700;
  color: var(--text-inverse);
  letter-spacing: 2rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
  height: 60vh;
}

.ai-thinking {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.thinking-brain {
  font-size: 120rpx;
  animation: bounce 2s ease-in-out infinite;
  margin-bottom: var(--space-2xl);
}

.thinking-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-weight: 500;
}

.thinking-dots {
  display: flex;
  gap: var(--space-sm);
}

.dot {
  font-size: var(--text-base);
  color: var(--primary);
  animation: wave 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* 分析概览 */
.analysis-overview {
  background: var(--bg-primary);
  margin: var(--space-xl);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 2rpx solid rgba(79, 172, 254, 0.2);
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.overview-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.analysis-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-xs);
}

.score-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.score-value {
  font-size: var(--text-2xl);
  font-weight: 700;
}

/* 关键指标 */
.key-metrics {
  display: flex;
  gap: var(--space-lg);
}

.metric-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  padding: var(--space-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.metric-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.metric-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.metric-info {
  display: flex;
  flex-direction: column;
}

.metric-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 优先级筛选 */
.priority-filter {
  margin: var(--space-xl) var(--space-xl) 0;
}

.filter-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.filter-options {
  display: flex;
  gap: var(--space-md);
  overflow-x: auto;
  padding-bottom: var(--space-sm);
}

.filter-option {
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2rpx solid var(--border-light);
  white-space: nowrap;
  flex-shrink: 0;
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-option.active {
  background: var(--primary);
  color: var(--text-inverse);
  border-color: var(--primary);
  box-shadow: var(--shadow-primary);
}

.filter-option.high.active {
  background: var(--error);
  border-color: var(--error);
}

.filter-option.medium.active {
  background: var(--warning);
  border-color: var(--warning);
}

.filter-option.low.active {
  background: var(--info);
  border-color: var(--info);
}

/* 建议列表 */
.suggestions-list {
  padding: var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.suggestion-card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.suggestion-card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

/* 建议头部 */
.suggestion-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.suggestion-info {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
}

.suggestion-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.suggestion-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.suggestion-category {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.priority-badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  align-self: flex-start;
}

.priority-badge.high {
  background: var(--error-bg);
  color: var(--error);
}

.priority-badge.medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.priority-badge.low {
  background: var(--info-bg);
  color: var(--info);
}

.priority-text {
  letter-spacing: 1rpx;
}

/* 操作按钮 */
.suggestion-actions {
  display: flex;
  gap: var(--space-sm);
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2rpx solid var(--border-light);
  background: var(--bg-secondary);
}

.action-btn:active {
  transform: scale(0.9);
}

.favorite {
  background: var(--bg-secondary);
}

.favorite.active {
  background: var(--error-bg);
  border-color: var(--error);
}

.favorite-icon {
  font-size: 24rpx;
}

/* 建议内容 */
.suggestion-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: var(--leading-snug);
  margin-bottom: var(--space-lg);
  display: block;
}

.suggestion-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-2xl);
  display: block;
}

/* 预期效果 */
.expected-results {
  background: var(--success-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.results-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--success);
  margin-bottom: var(--space-lg);
  display: block;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.result-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.result-icon {
  font-size: 24rpx;
}

.result-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

/* 实施步骤 */
.implementation-steps {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.steps-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-lg);
  display: block;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary);
  color: var(--text-inverse);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: 700;
  flex-shrink: 0;
}

.step-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
  flex: 1;
  padding-top: 4rpx;
}

/* 建议详情 */
.suggestion-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  padding: var(--space-xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2xl);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  font-size: var(--text-sm);
}

.detail-label {
  color: var(--text-secondary);
  min-width: 160rpx;
}

.detail-value {
  color: var(--text-primary);
  font-weight: 500;
}

.detail-value.cost {
  color: var(--primary);
  font-weight: 600;
}

/* 难度指示器 */
.difficulty-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.difficulty-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: var(--radius-full);
  background: var(--border-medium);
  transition: all 0.3s ease;
}

.difficulty-dot.active {
  background: var(--warning);
}

.difficulty-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-left: var(--space-sm);
}

/* 建议按钮 */
.suggestion-buttons {
  display: flex;
  gap: var(--space-lg);
}

.suggestion-btn {
  flex: 1;
  height: 72rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: var(--text-sm);
}

.suggestion-btn:active {
  transform: scale(0.98);
}

.suggestion-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-medium);
}

.suggestion-btn.secondary:active {
  background: var(--bg-tertiary);
}

.suggestion-btn.primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: var(--text-sm);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-2xl);
}

.empty-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.empty-desc {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* 快速操作 */
.quick-actions {
  margin: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
}

.quick-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  display: block;
}

.quick-buttons {
  display: flex;
  gap: var(--space-lg);
}

.quick-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  text-align: center;
}

.quick-btn:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.quick-icon {
  font-size: 48rpx;
  margin-bottom: var(--space-lg);
}

.quick-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 底部统计 */
.bottom-stats {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl);
  background: var(--bg-primary);
  margin: var(--space-xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-sm);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .key-metrics {
    flex-direction: column;
  }
  
  .quick-buttons {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .suggestion-buttons {
    flex-direction: column;
    gap: var(--space-md);
  }
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-20rpx);
  }
}