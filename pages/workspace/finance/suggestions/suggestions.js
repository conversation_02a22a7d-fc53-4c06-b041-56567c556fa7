// pages/workspace/finance/suggestions/suggestions.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { checkPageAccess, getCurrentUser } = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    
    // 优化潜力和统计
    optimizationScore: 73,
    potentialSavings: '25,800',
    efficiencyImprovement: 28,
    actionableItems: 12,
    
    // 筛选条件
    selectedPriority: 'all',
    priorityMap: {
      high: '高优先级',
      medium: '中优先级', 
      low: '低优先级'
    },
    
    // 统计数据
    implementedCount: 8,
    inProgressCount: 3,
    plannedCount: 4,
    totalSaved: '18,500',
    
    // 所有建议数据
    allSuggestions: [
      {
        id: 1,
        category: '成本优化',
        priority: 'high',
        priorityText: '高优先级',
        icon: '💰',
        title: '优化办公用品采购策略',
        description: '通过集中采购和供应商谈判，预计可以降低15-20%的办公用品成本。建议建立采购审批制度，避免重复和不必要的采购。',
        expectedResults: [
          { icon: '💵', text: '每月节省约3,000-4,000元办公费用' },
          { icon: '📈', text: '采购效率提升30%，减少采购时间' },
          { icon: '📋', text: '建立完善的库存管理系统' }
        ],
        steps: [
          '分析过去6个月的办公用品采购记录',
          '筛选3-5家优质供应商进行比价',
          '与供应商协商年度采购框架协议',
          '建立采购审批和库存管理制度',
          '培训相关人员执行新的采购流程'
        ],
        timeline: '2-3周',
        difficulty: 2,
        difficultyText: '容易',
        estimatedCost: '无需额外投入',
        isFavorite: false
      },
      {
        id: 2,
        category: '现金流管理',
        priority: 'high',
        priorityText: '高优先级',
        icon: '💎',
        title: '优化应收账款回收周期',
        description: '当前应收账款回收周期偏长，建议通过改善催收流程和客户信用管理，将回收周期从45天缩短至30天以内。',
        expectedResults: [
          { icon: '⚡', text: '现金流周转效率提升33%' },
          { icon: '📊', text: '坏账风险降低至2%以下' },
          { icon: '💰', text: '减少资金占用约15万元' }
        ],
        steps: [
          '建立客户信用评估体系',
          '制定分层级的催收策略',
          '实施自动化账期提醒系统',
          '与主要客户重新协商付款条件',
          '建立应收账款周报制度'
        ],
        timeline: '1个月',
        difficulty: 3,
        difficultyText: '中等',
        estimatedCost: '5,000元（系统升级）',
        isFavorite: true
      },
      {
        id: 3,
        category: '投资理财',
        priority: 'medium',
        priorityText: '中优先级',
        icon: '📈',
        title: '闲置资金理财投资',
        description: '目前账户有约50万元闲置资金，建议配置安全性较高的理财产品，预期年化收益3-5%，可增加额外收入。',
        expectedResults: [
          { icon: '💰', text: '年增加收益15,000-25,000元' },
          { icon: '🛡️', text: '资金安全性得到保障' },
          { icon: '📊', text: '建立多元化投资组合' }
        ],
        steps: [
          '分析资金使用计划和流动性需求',
          '选择2-3种低风险理财产品',
          '分批投入，控制单一产品占比',
          '建立投资收益监控机制',
          '定期调整投资组合配置'
        ],
        timeline: '1-2周',
        difficulty: 2,
        difficultyText: '容易',
        estimatedCost: '无需额外投入',
        isFavorite: false
      },
      {
        id: 4,
        category: '税务筹划',
        priority: 'medium',
        priorityText: '中优先级',
        icon: '📋',
        title: '合理税务筹划优化',
        description: '通过合法的税务筹划手段，如研发费用加计扣除、小微企业优惠政策等，预计可以节省5-8%的税务成本。',
        expectedResults: [
          { icon: '💵', text: '年节省税费约12,000-20,000元' },
          { icon: '✅', text: '确保税务合规性' },
          { icon: '📚', text: '提升财务团队专业能力' }
        ],
        steps: [
          '梳理现有业务涉及的税种和优惠政策',
          '咨询税务专家制定筹划方案',
          '完善相关财务制度和流程',
          '培训财务人员掌握政策要点',
          '建立定期政策更新机制'
        ],
        timeline: '3-4周',
        difficulty: 4,
        difficultyText: '较难',
        estimatedCost: '8,000元（咨询费）',
        isFavorite: true
      },
      {
        id: 5,
        category: '成本控制',
        priority: 'low',
        priorityText: '低优先级',
        icon: '⚡',
        title: '能源费用节约计划',
        description: '通过优化用电时段、升级节能设备等措施，预计可以节省10-15%的能源费用，同时提升企业环保形象。',
        expectedResults: [
          { icon: '🌱', text: '每月节省电费约800-1,200元' },
          { icon: '♻️', text: '提升企业环保形象' },
          { icon: '🏆', text: '可申请绿色企业认证' }
        ],
        steps: [
          '进行能源使用情况审计',
          '识别高耗能设备和时段',
          '制定节能改造计划',
          '员工节能意识培训',
          '建立能源监控体系'
        ],
        timeline: '6-8周',
        difficulty: 3,
        difficultyText: '中等',
        estimatedCost: '15,000元（设备升级）',
        isFavorite: false
      },
      {
        id: 6,
        category: '流程优化',
        priority: 'low',
        priorityText: '低优先级',
        icon: '🚀',
        title: '财务报销流程数字化',
        description: '实施电子报销系统，减少纸质单据处理时间，提高报销审批效率，预计可以节省50%的处理时间。',
        expectedResults: [
          { icon: '⏰', text: '报销处理时间缩短50%' },
          { icon: '📱', text: '员工满意度提升' },
          { icon: '📊', text: '费用数据更加透明准确' }
        ],
        steps: [
          '选择合适的电子报销系统',
          '设计新的报销流程',
          '系统部署和测试',
          '员工培训和试运行',
          '正式上线和持续优化'
        ],
        timeline: '4-6周',
        difficulty: 3,
        difficultyText: '中等',
        estimatedCost: '12,000元（系统费用）',
        isFavorite: false
      }
    ],
    
    // 筛选后的建议
    filteredSuggestions: [],
    
    // 各优先级数量
    highPriorityCount: 0,
    mediumPriorityCount: 0,
    lowPriorityCount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查页面访问权限
    if (!checkPageAccess('finance/suggestions')) {
      wx.switchTab({
        url: '/pages/workspace/workspace'
      });
      return;
    }

    this.loadSuggestions();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新收藏状态
    this.refreshFavoriteStatus();
  },

  /**
   * 加载建议数据
   */
  async loadSuggestions() {
    try {
      this.setData({ loading: true });
      
      // 模拟AI分析过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 计算各优先级数量
      const suggestions = this.data.allSuggestions;
      const counts = this.calculatePriorityCounts(suggestions);
      
      this.setData({
        ...counts,
        filteredSuggestions: suggestions
      });
      
    } catch (error) {
      console.error('加载建议失败:', error);
      wx.showToast({
        title: '加载建议失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 计算各优先级数量
   */
  calculatePriorityCounts(suggestions) {
    const counts = {
      highPriorityCount: 0,
      mediumPriorityCount: 0,
      lowPriorityCount: 0
    };
    
    suggestions.forEach(item => {
      switch (item.priority) {
        case 'high':
          counts.highPriorityCount++;
          break;
        case 'medium':
          counts.mediumPriorityCount++;
          break;
        case 'low':
          counts.lowPriorityCount++;
          break;
      }
    });
    
    return counts;
  },

  /**
   * 优先级筛选
   */
  onPriorityFilter(e) {
    const { priority } = e.currentTarget.dataset;
    let filteredSuggestions = [];
    
    if (priority === 'all') {
      filteredSuggestions = this.data.allSuggestions;
    } else {
      filteredSuggestions = this.data.allSuggestions.filter(item => item.priority === priority);
    }
    
    this.setData({
      selectedPriority: priority,
      filteredSuggestions
    });
  },

  /**
   * 切换收藏状态
   */
  onToggleFavorite(e) {
    const { id } = e.currentTarget.dataset;
    const suggestions = this.data.allSuggestions.map(item => {
      if (item.id == id) {
        return { ...item, isFavorite: !item.isFavorite };
      }
      return item;
    });
    
    // 更新筛选后的数据
    const filteredSuggestions = this.filterSuggestions(suggestions, this.data.selectedPriority);
    
    this.setData({
      allSuggestions: suggestions,
      filteredSuggestions
    });
    
    // 保存到本地存储
    this.saveFavoriteStatus();
    
    wx.showToast({
      title: filteredSuggestions.find(item => item.id == id).isFavorite ? '已收藏' : '已取消收藏',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 筛选建议
   */
  filterSuggestions(suggestions, priority) {
    if (priority === 'all') {
      return suggestions;
    }
    return suggestions.filter(item => item.priority === priority);
  },

  /**
   * 保存收藏状态
   */
  saveFavoriteStatus() {
    try {
      const favorites = this.data.allSuggestions
        .filter(item => item.isFavorite)
        .map(item => item.id);
      wx.setStorageSync('favoriteSuggestions', favorites);
    } catch (error) {
      console.error('保存收藏状态失败:', error);
    }
  },

  /**
   * 刷新收藏状态
   */
  refreshFavoriteStatus() {
    try {
      const favorites = wx.getStorageSync('favoriteSuggestions') || [];
      const suggestions = this.data.allSuggestions.map(item => ({
        ...item,
        isFavorite: favorites.includes(item.id)
      }));
      
      const filteredSuggestions = this.filterSuggestions(suggestions, this.data.selectedPriority);
      
      this.setData({
        allSuggestions: suggestions,
        filteredSuggestions
      });
    } catch (error) {
      console.error('刷新收藏状态失败:', error);
    }
  },

  /**
   * 查看建议详情
   */
  onViewDetails(e) {
    const { id } = e.currentTarget.dataset;
    const suggestion = this.data.allSuggestions.find(item => item.id == id);
    
    if (suggestion) {
      wx.navigateTo({
        url: `/pages/workspace/finance/suggestion-detail/suggestion-detail?id=${id}`
      });
    }
  },

  /**
   * 开始实施建议
   */
  onImplementSuggestion(e) {
    const { id } = e.currentTarget.dataset;
    const suggestion = this.data.allSuggestions.find(item => item.id == id);
    
    if (!suggestion) return;
    
    wx.showModal({
      title: '开始实施建议',
      content: `确定要开始实施"${suggestion.title}"吗？系统将为您创建实施计划并跟踪进度。`,
      confirmText: '开始实施',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          this.createImplementationPlan(suggestion);
        }
      }
    });
  },

  /**
   * 创建实施计划
   */
  async createImplementationPlan(suggestion) {
    wx.showLoading({
      title: '创建计划中...'
    });
    
    try {
      // 模拟创建实施计划
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      wx.hideLoading();
      wx.showToast({
        title: '计划创建成功',
        icon: 'success',
        duration: 2000
      });
      
      // 跳转到实施计划页面
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/workspace/finance/implementation/implementation?suggestionId=${suggestion.id}`
        });
      }, 2000);
      
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '创建计划失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成建议报告
   */
  async onGenerateReport() {
    wx.showLoading({
      title: '生成报告中...'
    });
    
    try {
      // 模拟报告生成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      wx.hideLoading();
      wx.showModal({
        title: '报告生成成功',
        content: '智能建议报告已生成完成，包含详细的优化方案和实施指南。',
        confirmText: '立即查看',
        cancelText: '稍后查看',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/workspace/finance/reports/reports?type=suggestions'
            });
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 定期回顾提醒
   */
  onScheduleReview() {
    wx.showModal({
      title: '定期回顾提醒',
      content: '建议每月进行一次财务优化回顾，及时调整策略。是否设置提醒？',
      confirmText: '设置提醒',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以调用系统日历或提醒API
          wx.showToast({
            title: '提醒已设置',
            icon: 'success',
            duration: 1500
          });
        }
      }
    });
  },

  /**
   * 专家咨询
   */
  onExpertConsult() {
    wx.showModal({
      title: '专家咨询',
      content: '我们提供专业的财务咨询服务，专家将根据您的具体情况提供定制化建议。',
      confirmText: '联系专家',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到专家咨询页面或联系方式
          wx.navigateTo({
            url: '/pages/workspace/finance/expert/expert'
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadSuggestions().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '财务智能建议',
      path: '/pages/workspace/finance/suggestions/suggestions'
    };
  }
})