<!--pages/workspace/finance/suggestions/suggestions.wxml-->
<view class="suggestions-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">💡 智能建议</text>
      <text class="page-subtitle">基于数据分析的个性化财务优化方案</text>
    </view>
    <view class="ai-indicator">
      <text class="ai-badge">AI</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="ai-thinking">
      <view class="thinking-brain">🧠</view>
      <text class="thinking-text">AI正在分析您的财务状况...</text>
      <view class="thinking-dots">
        <text class="dot">●</text>
        <text class="dot">●</text>
        <text class="dot">●</text>
      </view>
    </view>
  </view>

  <view wx:else>
    <!-- AI分析概览 -->
    <view class="analysis-overview">
      <view class="overview-header">
        <text class="overview-title">🎯 分析概览</text>
        <view class="analysis-score">
          <text class="score-label">优化潜力</text>
          <text class="score-value" style="color: {{optimizationScore >= 70 ? '#52c41a' : optimizationScore >= 40 ? '#fa8c16' : '#ff4d4f'}}">
            {{optimizationScore}}%
          </text>
        </view>
      </view>
      
      <view class="key-metrics">
        <view class="metric-card">
          <text class="metric-icon">💰</text>
          <view class="metric-info">
            <text class="metric-value">¥{{potentialSavings}}</text>
            <text class="metric-label">潜在节省</text>
          </view>
        </view>
        <view class="metric-card">
          <text class="metric-icon">⚡</text>
          <view class="metric-info">
            <text class="metric-value">{{efficiencyImprovement}}%</text>
            <text class="metric-label">效率提升</text>
          </view>
        </view>
        <view class="metric-card">
          <text class="metric-icon">🎯</text>
          <view class="metric-info">
            <text class="metric-value">{{actionableItems}}</text>
            <text class="metric-label">可执行项</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 优先级筛选 -->
    <view class="priority-filter">
      <view class="filter-title">按优先级筛选:</view>
      <view class="filter-options">
        <view class="filter-option {{selectedPriority === 'all' ? 'active' : ''}}"
              bindtap="onPriorityFilter"
              data-priority="all">
          全部 ({{allSuggestions.length}})
        </view>
        <view class="filter-option high {{selectedPriority === 'high' ? 'active' : ''}}"
              bindtap="onPriorityFilter"
              data-priority="high">
          高优先级 ({{highPriorityCount}})
        </view>
        <view class="filter-option medium {{selectedPriority === 'medium' ? 'active' : ''}}"
              bindtap="onPriorityFilter"
              data-priority="medium">
          中优先级 ({{mediumPriorityCount}})
        </view>
        <view class="filter-option low {{selectedPriority === 'low' ? 'active' : ''}}"
              bindtap="onPriorityFilter"
              data-priority="low">
          低优先级 ({{lowPriorityCount}})
        </view>
      </view>
    </view>

    <!-- 建议列表 -->
    <view class="suggestions-list">
      <view wx:for="{{filteredSuggestions}}" wx:key="id" class="suggestion-card">
        <view class="suggestion-header">
          <view class="suggestion-info">
            <text class="suggestion-icon">{{item.icon}}</text>
            <view class="suggestion-meta">
              <text class="suggestion-category">{{item.category}}</text>
              <view class="priority-badge {{item.priority}}">
                <text class="priority-text">{{item.priorityText}}</text>
              </view>
            </view>
          </view>
          <view class="suggestion-actions">
            <view class="action-btn favorite {{item.isFavorite ? 'active' : ''}}"
                  bindtap="onToggleFavorite"
                  data-id="{{item.id}}">
              <text class="favorite-icon">{{item.isFavorite ? '❤️' : '🤍'}}</text>
            </view>
          </view>
        </view>

        <text class="suggestion-title">{{item.title}}</text>
        <text class="suggestion-description">{{item.description}}</text>

        <!-- 预期效果 -->
        <view class="expected-results">
          <text class="results-title">📊 预期效果:</text>
          <view class="results-list">
            <view wx:for="{{item.expectedResults}}" wx:for-item="result" wx:key="type" class="result-item">
              <text class="result-icon">{{result.icon}}</text>
              <text class="result-text">{{result.text}}</text>
            </view>
          </view>
        </view>

        <!-- 实施步骤 -->
        <view class="implementation-steps">
          <text class="steps-title">🚀 实施步骤:</text>
          <view class="steps-list">
            <view wx:for="{{item.steps}}" wx:for-item="step" wx:for-index="stepIndex" wx:key="stepIndex" class="step-item">
              <view class="step-number">{{stepIndex + 1}}</view>
              <text class="step-text">{{step}}</text>
            </view>
          </view>
        </view>

        <!-- 时间线和难度 -->
        <view class="suggestion-details">
          <view class="detail-item">
            <text class="detail-label">⏱️ 执行周期:</text>
            <text class="detail-value">{{item.timeline}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">🎯 实施难度:</text>
            <view class="difficulty-indicator">
              <view wx:for="{{[1,2,3,4,5]}}" wx:key="*this" 
                    class="difficulty-dot {{index < item.difficulty ? 'active' : ''}}"></view>
              <text class="difficulty-text">{{item.difficultyText}}</text>
            </view>
          </view>
          <view class="detail-item">
            <text class="detail-label">💰 预计投入:</text>
            <text class="detail-value cost">{{item.estimatedCost}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="suggestion-buttons">
          <view class="suggestion-btn secondary"
                bindtap="onViewDetails"
                data-id="{{item.id}}">
            <text class="btn-icon">📋</text>
            <text class="btn-text">查看详情</text>
          </view>
          <view class="suggestion-btn primary"
                bindtap="onImplementSuggestion"
                data-id="{{item.id}}">
            <text class="btn-icon">✅</text>
            <text class="btn-text">开始实施</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{filteredSuggestions.length === 0}}" class="empty-state">
      <text class="empty-icon">🎉</text>
      <text class="empty-title">暂无{{selectedPriority === 'all' ? '' : priorityMap[selectedPriority]}}建议</text>
      <text class="empty-desc">您的财务管理已经很棒了！</text>
    </view>

    <!-- 快速操作区域 -->
    <view class="quick-actions">
      <text class="quick-title">🚀 快速操作</text>
      <view class="quick-buttons">
        <view class="quick-btn" bindtap="onGenerateReport">
          <text class="quick-icon">📊</text>
          <text class="quick-text">生成建议报告</text>
        </view>
        <view class="quick-btn" bindtap="onScheduleReview">
          <text class="quick-icon">⏰</text>
          <text class="quick-text">定期回顾提醒</text>
        </view>
        <view class="quick-btn" bindtap="onExpertConsult">
          <text class="quick-icon">👨‍💼</text>
          <text class="quick-text">专家咨询</text>
        </view>
      </view>
    </view>

    <!-- 底部统计 -->
    <view class="bottom-stats">
      <view class="stat-item">
        <text class="stat-value">{{implementedCount}}</text>
        <text class="stat-label">已实施</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{inProgressCount}}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{plannedCount}}</text>
        <text class="stat-label">已计划</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">¥{{totalSaved}}</text>
        <text class="stat-label">累计节省</text>
      </view>
    </view>
  </view>
</view>