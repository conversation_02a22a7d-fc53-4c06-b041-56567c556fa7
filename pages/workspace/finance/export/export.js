// pages/workspace/finance/export/export.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { checkPageAccess, getCurrentUser } = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 导出状态
    exportStatus: 'ready', // ready, processing, completed
    statusText: '准备就绪',
    isExporting: false,
    exportProgress: 0,
    currentProgressStep: '',
    
    // 报表类型
    reportTypes: [
      {
        id: 'comprehensive',
        title: '综合财务报告',
        description: '包含收入、支出、利润等全面财务分析的完整报告',
        icon: '📊',
        features: ['收支分析', '趋势图表', 'AI建议', '预测分析']
      },
      {
        id: 'income_statement',
        title: '损益表',
        description: '标准的损益表格式，适合财务报表需求',
        icon: '💰',
        features: ['标准格式', '详细分类', '同比分析', '图表展示']
      },
      {
        id: 'cash_flow',
        title: '现金流量表',
        description: '现金流入流出明细分析，帮助掌握资金状况',
        icon: '💎',
        features: ['现金流向', '流动性分析', '预警提示', '月度对比']
      },
      {
        id: 'budget_analysis',
        title: '预算分析报告',
        description: '预算执行情况分析，包含偏差分析和改进建议',
        icon: '🎯',
        features: ['预算对比', '偏差分析', '执行率', '优化建议']
      }
    ],
    selectedReportType: 'comprehensive',
    selectedReportTitle: '综合财务报告',
    
    // 时间范围
    quickDateOptions: [
      { value: 'current_month', label: '本月' },
      { value: 'last_month', label: '上月' },
      { value: 'current_quarter', label: '本季度' },
      { value: 'last_quarter', label: '上季度' },
      { value: 'current_year', label: '今年' },
      { value: 'last_year', label: '去年' }
    ],
    selectedDateRange: 'current_month',
    customStartDate: '',
    customEndDate: '',
    previewDateRange: '2024年8月1日 - 2024年8月31日',
    
    // 导出格式
    exportFormats: [
      {
        id: 'pdf',
        name: 'PDF文档',
        description: '适合打印和分享的专业格式',
        icon: '📄',
        color: '#e74c3c',
        features: ['专业排版', '保护内容', '打印友好', '通用格式'],
        estimatedSize: '2-5MB'
      },
      {
        id: 'excel',
        name: 'Excel表格',
        description: '可编辑的电子表格格式，支持数据分析',
        icon: '📊',
        color: '#27ae60',
        features: ['数据可编辑', '公式计算', '图表制作', '数据透视'],
        estimatedSize: '1-3MB'
      },
      {
        id: 'word',
        name: 'Word文档',
        description: '可编辑的文档格式，便于添加说明',
        icon: '📝',
        color: '#3498db',
        features: ['内容可编辑', '格式灵活', '批注功能', '协作编辑'],
        estimatedSize: '3-8MB'
      },
      {
        id: 'html',
        name: '网页格式',
        description: '在线查看的网页格式，支持交互',
        icon: '🌐',
        color: '#f39c12',
        features: ['在线查看', '交互图表', '响应式', '无需软件'],
        estimatedSize: '500KB-1MB'
      }
    ],
    selectedFormat: 'pdf',
    
    // 高级选项
    includeCharts: true,
    includeDetails: true,
    includeAIAnalysis: true,
    includeCompanyInfo: true,
    showPreview: false,
    
    // 导出历史
    exportHistory: [
      {
        id: 1,
        title: '综合财务报告',
        dateRange: '2024年7月',
        format: 'PDF',
        createdTime: '2024-08-15 10:30',
        icon: '📊',
        color: '#e74c3c'
      },
      {
        id: 2,
        title: '现金流量表',
        dateRange: '2024年第二季度',
        format: 'Excel',
        createdTime: '2024-08-10 14:20',
        icon: '💎',
        color: '#27ae60'
      },
      {
        id: 3,
        title: '损益表',
        dateRange: '2024年上半年',
        format: 'PDF',
        createdTime: '2024-08-05 09:15',
        icon: '💰',
        color: '#e74c3c'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查页面访问权限
    if (!checkPageAccess('finance/export')) {
      wx.switchTab({
        url: '/pages/workspace/workspace'
      });
      return;
    }

    // 从参数中获取预设选项
    if (options.type) {
      this.setData({
        selectedReportType: options.type
      });
    }

    this.initializeDates();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 初始化日期
   */
  initializeDates() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    
    // 设置本月的开始和结束日期
    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0);
    
    this.setData({
      customStartDate: this.formatDate(startDate),
      customEndDate: this.formatDate(endDate),
      previewDateRange: `${this.formatDisplayDate(startDate)} - ${this.formatDisplayDate(endDate)}`
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化显示日期
   */
  formatDisplayDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  },

  /**
   * 选择报表类型
   */
  onSelectReportType(e) {
    const { type } = e.currentTarget.dataset;
    const reportType = this.data.reportTypes.find(item => item.id === type);
    
    this.setData({
      selectedReportType: type,
      selectedReportTitle: reportType ? reportType.title : '综合财务报告'
    });
  },

  /**
   * 选择快速日期
   */
  onSelectQuickDate(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({ selectedDateRange: value });
    
    // 根据选择更新自定义日期范围
    const dateRange = this.getDateRangeByType(value);
    this.setData({
      customStartDate: this.formatDate(dateRange.start),
      customEndDate: this.formatDate(dateRange.end),
      previewDateRange: `${this.formatDisplayDate(dateRange.start)} - ${this.formatDisplayDate(dateRange.end)}`
    });
  },

  /**
   * 根据类型获取日期范围
   */
  getDateRangeByType(type) {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    
    switch (type) {
      case 'current_month':
        return {
          start: new Date(year, month, 1),
          end: new Date(year, month + 1, 0)
        };
      case 'last_month':
        return {
          start: new Date(year, month - 1, 1),
          end: new Date(year, month, 0)
        };
      case 'current_quarter':
        const quarterStart = Math.floor(month / 3) * 3;
        return {
          start: new Date(year, quarterStart, 1),
          end: new Date(year, quarterStart + 3, 0)
        };
      case 'last_quarter':
        const lastQuarterStart = Math.floor(month / 3) * 3 - 3;
        const lastQuarterYear = lastQuarterStart < 0 ? year - 1 : year;
        const adjustedQuarterStart = lastQuarterStart < 0 ? 9 : lastQuarterStart;
        return {
          start: new Date(lastQuarterYear, adjustedQuarterStart, 1),
          end: new Date(lastQuarterYear, adjustedQuarterStart + 3, 0)
        };
      case 'current_year':
        return {
          start: new Date(year, 0, 1),
          end: new Date(year, 11, 31)
        };
      case 'last_year':
        return {
          start: new Date(year - 1, 0, 1),
          end: new Date(year - 1, 11, 31)
        };
      default:
        return {
          start: new Date(year, month, 1),
          end: new Date(year, month + 1, 0)
        };
    }
  },

  /**
   * 自定义开始日期改变
   */
  onStartDateChange(e) {
    const startDate = e.detail.value;
    this.setData({ 
      customStartDate: startDate,
      selectedDateRange: 'custom' 
    });
    this.updatePreviewDateRange();
  },

  /**
   * 自定义结束日期改变
   */
  onEndDateChange(e) {
    const endDate = e.detail.value;
    this.setData({ 
      customEndDate: endDate,
      selectedDateRange: 'custom' 
    });
    this.updatePreviewDateRange();
  },

  /**
   * 更新预览日期范围
   */
  updatePreviewDateRange() {
    const { customStartDate, customEndDate } = this.data;
    if (customStartDate && customEndDate) {
      const start = new Date(customStartDate);
      const end = new Date(customEndDate);
      this.setData({
        previewDateRange: `${this.formatDisplayDate(start)} - ${this.formatDisplayDate(end)}`
      });
    }
  },

  /**
   * 选择导出格式
   */
  onSelectFormat(e) {
    const { format } = e.currentTarget.dataset;
    this.setData({ selectedFormat: format });
  },

  /**
   * 切换图表选项
   */
  onToggleCharts(e) {
    this.setData({ includeCharts: e.detail.value });
  },

  /**
   * 切换详细数据选项
   */
  onToggleDetails(e) {
    this.setData({ includeDetails: e.detail.value });
  },

  /**
   * 切换AI分析选项
   */
  onToggleAIAnalysis(e) {
    this.setData({ includeAIAnalysis: e.detail.value });
  },

  /**
   * 切换公司信息选项
   */
  onToggleCompanyInfo(e) {
    this.setData({ includeCompanyInfo: e.detail.value });
  },

  /**
   * 预览报表
   */
  onPreview() {
    this.setData({ showPreview: !this.data.showPreview });
    
    wx.showToast({
      title: this.data.showPreview ? '显示预览' : '隐藏预览',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 开始导出
   */
  async onStartExport() {
    // 验证选项
    if (!this.validateExportOptions()) {
      return;
    }

    try {
      this.setData({ 
        isExporting: true,
        exportProgress: 0,
        exportStatus: 'processing',
        statusText: '处理中'
      });

      // 模拟导出过程
      await this.simulateExportProcess();

      // 导出成功
      this.setData({
        exportStatus: 'completed',
        statusText: '导出完成'
      });

      wx.showModal({
        title: '导出成功',
        content: '报表已成功生成并保存，您可以在导出历史中查看和下载。',
        confirmText: '查看文件',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm) {
            this.addToHistory();
          }
        }
      });

    } catch (error) {
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ 
        isExporting: false,
        exportProgress: 0 
      });
    }
  },

  /**
   * 验证导出选项
   */
  validateExportOptions() {
    if (!this.data.selectedReportType) {
      wx.showToast({
        title: '请选择报表类型',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.selectedFormat) {
      wx.showToast({
        title: '请选择导出格式',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.customStartDate || !this.data.customEndDate) {
      wx.showToast({
        title: '请选择日期范围',
        icon: 'none'
      });
      return false;
    }

    // 验证日期范围
    const startDate = new Date(this.data.customStartDate);
    const endDate = new Date(this.data.customEndDate);
    
    if (startDate >= endDate) {
      wx.showToast({
        title: '开始日期应早于结束日期',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  /**
   * 模拟导出过程
   */
  async simulateExportProcess() {
    const steps = [
      { progress: 20, message: '正在收集数据...' },
      { progress: 40, message: '正在分析财务指标...' },
      { progress: 60, message: '正在生成图表...' },
      { progress: 80, message: '正在应用格式...' },
      { progress: 100, message: '导出完成！' }
    ];

    for (let step of steps) {
      await new Promise(resolve => setTimeout(resolve, 800));
      this.setData({
        exportProgress: step.progress,
        currentProgressStep: step.message
      });
    }
  },

  /**
   * 添加到历史记录
   */
  addToHistory() {
    const reportType = this.data.reportTypes.find(item => item.id === this.data.selectedReportType);
    const format = this.data.exportFormats.find(item => item.id === this.data.selectedFormat);
    
    const newHistoryItem = {
      id: Date.now(),
      title: reportType ? reportType.title : '财务报告',
      dateRange: this.data.previewDateRange,
      format: format ? format.name : 'PDF',
      createdTime: this.formatTime(new Date()),
      icon: reportType ? reportType.icon : '📊',
      color: format ? format.color : '#e74c3c'
    };

    const updatedHistory = [newHistoryItem, ...this.data.exportHistory];
    this.setData({ exportHistory: updatedHistory });

    // 保存到本地存储
    try {
      wx.setStorageSync('exportHistory', updatedHistory);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 下载历史文件
   */
  onDownloadHistory(e) {
    const { id } = e.currentTarget.dataset;
    const historyItem = this.data.exportHistory.find(item => item.id == id);
    
    if (historyItem) {
      wx.showModal({
        title: '下载文件',
        content: `确定要下载"${historyItem.title}"吗？`,
        confirmText: '下载',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 实际项目中这里会调用下载API
            wx.showToast({
              title: '开始下载',
              icon: 'success',
              duration: 1500
            });
          }
        }
      });
    }
  },

  /**
   * 删除历史记录
   */
  onDeleteHistory(e) {
    const { id } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '删除确认',
      content: '确定要删除这个导出记录吗？',
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          const updatedHistory = this.data.exportHistory.filter(item => item.id != id);
          this.setData({ exportHistory: updatedHistory });
          
          // 更新本地存储
          try {
            wx.setStorageSync('exportHistory', updatedHistory);
          } catch (error) {
            console.error('更新历史记录失败:', error);
          }
          
          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1000
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新页面数据
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '财务报表导出',
      path: '/pages/workspace/finance/export/export'
    };
  }
})