/* pages/workspace/finance/export/export.wxss */

.export-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  color: var(--text-inverse);
  padding: var(--space-2xl) var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-lg);
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-sm);
  letter-spacing: 1rpx;
}

.page-subtitle {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
  font-weight: 400;
}

.export-status {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-sm) var(--space-lg);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-badge {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-inverse);
}

.status-badge.ready {
  color: #00b894;
}

.status-badge.processing {
  color: #fdcb6e;
}

.status-badge.completed {
  color: #00cec9;
}

/* 通用区块样式 */
.section-title {
  margin-bottom: var(--space-xl);
  padding: 0 var(--space-xl);
}

.title-text {
  display: block;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.title-desc {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 报表类型选择 */
.report-types-section {
  margin: var(--space-xl) 0;
}

.report-types-grid {
  padding: 0 var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.report-type-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.report-type-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.report-type-card.selected {
  border-color: var(--primary);
  background: var(--primary-bg);
  box-shadow: var(--shadow-primary);
}

.type-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.type-icon {
  font-size: 40rpx;
}

.type-info {
  flex: 1;
  min-width: 0;
}

.type-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.type-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-md);
  display: block;
}

.type-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.feature-tag {
  padding: var(--space-xs) var(--space-sm);
  background: var(--info-bg);
  color: var(--info);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}

.selection-indicator {
  flex-shrink: 0;
}

.check-icon {
  font-size: 32rpx;
}

/* 时间范围选择 */
.date-range-section {
  margin: var(--space-xl) 0;
}

.date-options {
  padding: 0 var(--space-xl);
}

.quick-dates {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  margin-bottom: var(--space-2xl);
}

.quick-date-btn {
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2rpx solid var(--border-light);
}

.quick-date-btn:active {
  transform: scale(0.95);
}

.quick-date-btn.active {
  background: var(--primary);
  color: var(--text-inverse);
  border-color: var(--primary);
  box-shadow: var(--shadow-primary);
}

.custom-date-range {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.date-input {
  flex: 1;
}

.input-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  display: block;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.picker-display:active {
  background: var(--bg-tertiary);
}

.date-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
}

.picker-icon {
  font-size: 24rpx;
  opacity: 0.6;
}

.date-separator {
  font-size: var(--text-lg);
  color: var(--text-tertiary);
  font-weight: 500;
}

/* 导出格式选择 */
.export-format-section {
  margin: var(--space-xl) 0;
}

.format-options {
  padding: 0 var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.format-option {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.format-option:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.format-option.selected {
  border-color: var(--primary);
  background: var(--primary-bg);
  box-shadow: var(--shadow-primary);
}

.format-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.format-icon {
  font-size: 40rpx;
}

.format-details {
  flex: 1;
  min-width: 0;
}

.format-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.format-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-md);
  display: block;
}

.format-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.format-feature {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: var(--leading-normal);
}

.format-size {
  text-align: right;
  flex-shrink: 0;
}

.size-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 高级选项 */
.advanced-options-section {
  margin: var(--space-xl) 0;
}

.options-list {
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  margin: 0 var(--space-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
}

.option-item:last-child {
  border-bottom: none;
}

.option-info {
  flex: 1;
  min-width: 0;
}

.option-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  display: block;
}

.option-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}

.option-switch {
  margin-left: var(--space-lg);
}

/* 预览区域 */
.preview-section {
  margin: var(--space-xl) 0;
}

.preview-container {
  margin: 0 var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 2rpx solid var(--border-light);
}

.preview-header {
  background: var(--bg-secondary);
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
  text-align: center;
}

.preview-title {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.preview-date {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.preview-content {
  padding: var(--space-xl);
}

.preview-chart {
  height: 200rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-xl);
  border: 2rpx dashed var(--border-medium);
}

.chart-placeholder {
  font-size: var(--text-base);
  color: var(--text-tertiary);
}

.preview-summary {
  margin-bottom: var(--space-xl);
}

.summary-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: block;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.item-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.item-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
}

.preview-ai {
  background: var(--info-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1rpx solid var(--info);
}

.ai-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--info);
  margin-bottom: var(--space-md);
  display: block;
}

.ai-content {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
}

/* 导出历史 */
.export-history-section {
  margin: var(--space-xl) 0;
}

.history-list {
  padding: 0 var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.history-item {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.history-item:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.history-info {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  flex: 1;
  min-width: 0;
}

.history-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-text {
  font-size: 28rpx;
}

.history-details {
  flex: 1;
  min-width: 0;
}

.history-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  display: block;
}

.history-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
  display: block;
}

.history-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.history-actions {
  display: flex;
  gap: var(--space-md);
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.action-btn.download {
  background: var(--success-bg);
  border: 1rpx solid var(--success);
}

.action-btn.delete {
  background: var(--error-bg);
  border: 1rpx solid var(--error);
}

.action-icon {
  font-size: 24rpx;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

/* 导出进度 */
.export-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin: var(--space-xl);
  box-shadow: var(--shadow-xl);
  min-width: 500rpx;
}

.progress-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.progress-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  display: block;
}

.progress-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.progress-bar {
  flex: 1;
  height: 16rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--primary);
  min-width: 80rpx;
  text-align: right;
}

.progress-steps {
  text-align: center;
}

.current-step {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  padding-bottom: calc(var(--space-xl) + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-medium);
}

.action-btn.secondary:active {
  background: var(--bg-tertiary);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary) 0%, #a29bfe 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: var(--text-base);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .date-input-group {
    flex-direction: column;
    gap: var(--space-lg);
  }
  
  .date-separator {
    transform: rotate(90deg);
    align-self: center;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .progress-card {
    margin: var(--space-lg);
    min-width: auto;
  }
}