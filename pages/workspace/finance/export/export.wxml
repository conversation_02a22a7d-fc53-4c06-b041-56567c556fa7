<!--pages/workspace/finance/export/export.wxml-->
<view class="export-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">📋 导出报表</text>
      <text class="page-subtitle">生成专业的财务分析报告，支持多种格式导出</text>
    </view>
    <view class="export-status">
      <text class="status-badge {{exportStatus}}">{{statusText}}</text>
    </view>
  </view>

  <!-- 报表类型选择 -->
  <view class="report-types-section">
    <view class="section-title">
      <text class="title-text">📊 选择报表类型</text>
      <text class="title-desc">选择需要生成的报表类型</text>
    </view>
    
    <view class="report-types-grid">
      <view wx:for="{{reportTypes}}" wx:key="id" 
            class="report-type-card {{selectedReportType === item.id ? 'selected' : ''}}"
            bindtap="onSelectReportType"
            data-type="{{item.id}}">
        <view class="type-icon-container">
          <text class="type-icon">{{item.icon}}</text>
        </view>
        <view class="type-info">
          <text class="type-title">{{item.title}}</text>
          <text class="type-desc">{{item.description}}</text>
          <view class="type-features">
            <text wx:for="{{item.features}}" wx:for-item="feature" wx:key="*this" class="feature-tag">
              {{feature}}
            </text>
          </view>
        </view>
        <view class="selection-indicator">
          <text class="check-icon">{{selectedReportType === item.id ? '✅' : '⭕'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 时间范围选择 -->
  <view class="date-range-section">
    <view class="section-title">
      <text class="title-text">📅 选择时间范围</text>
      <text class="title-desc">指定报表的数据时间范围</text>
    </view>
    
    <view class="date-options">
      <view class="quick-dates">
        <view wx:for="{{quickDateOptions}}" wx:key="value" 
              class="quick-date-btn {{selectedDateRange === item.value ? 'active' : ''}}"
              bindtap="onSelectQuickDate"
              data-value="{{item.value}}">
          <text class="date-text">{{item.label}}</text>
        </view>
      </view>
      
      <view class="custom-date-range">
        <view class="date-input-group">
          <view class="date-input">
            <text class="input-label">开始日期</text>
            <picker mode="date" 
                    value="{{customStartDate}}" 
                    bindchange="onStartDateChange"
                    class="date-picker">
              <view class="picker-display">
                <text class="date-value">{{customStartDate || '请选择'}}</text>
                <text class="picker-icon">📅</text>
              </view>
            </picker>
          </view>
          
          <view class="date-separator">~</view>
          
          <view class="date-input">
            <text class="input-label">结束日期</text>
            <picker mode="date" 
                    value="{{customEndDate}}" 
                    bindchange="onEndDateChange"
                    class="date-picker">
              <view class="picker-display">
                <text class="date-value">{{customEndDate || '请选择'}}</text>
                <text class="picker-icon">📅</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 导出格式选择 -->
  <view class="export-format-section">
    <view class="section-title">
      <text class="title-text">📄 选择导出格式</text>
      <text class="title-desc">选择适合您需要的文件格式</text>
    </view>
    
    <view class="format-options">
      <view wx:for="{{exportFormats}}" wx:key="id" 
            class="format-option {{selectedFormat === item.id ? 'selected' : ''}}"
            bindtap="onSelectFormat"
            data-format="{{item.id}}">
        <view class="format-icon-container" style="background-color: {{item.color}}20;">
          <text class="format-icon" style="color: {{item.color}};">{{item.icon}}</text>
        </view>
        <view class="format-details">
          <text class="format-name">{{item.name}}</text>
          <text class="format-desc">{{item.description}}</text>
          <view class="format-features">
            <text wx:for="{{item.features}}" wx:for-item="feature" wx:key="*this" class="format-feature">
              • {{feature}}
            </text>
          </view>
        </view>
        <view class="format-size">
          <text class="size-text">{{item.estimatedSize}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 高级选项 -->
  <view class="advanced-options-section">
    <view class="section-title">
      <text class="title-text">⚙️ 高级选项</text>
      <text class="title-desc">自定义报表内容和样式</text>
    </view>
    
    <view class="options-list">
      <view class="option-item">
        <view class="option-info">
          <text class="option-title">包含图表</text>
          <text class="option-desc">在报表中包含可视化图表</text>
        </view>
        <switch class="option-switch" 
               checked="{{includeCharts}}" 
               bindchange="onToggleCharts"/>
      </view>
      
      <view class="option-item">
        <view class="option-info">
          <text class="option-title">详细数据</text>
          <text class="option-desc">包含原始交易明细数据</text>
        </view>
        <switch class="option-switch" 
               checked="{{includeDetails}}" 
               bindchange="onToggleDetails"/>
      </view>
      
      <view class="option-item">
        <view class="option-info">
          <text class="option-title">AI分析</text>
          <text class="option-desc">包含AI智能分析和建议</text>
        </view>
        <switch class="option-switch" 
               checked="{{includeAIAnalysis}}" 
               bindchange="onToggleAIAnalysis"/>
      </view>
      
      <view class="option-item">
        <view class="option-info">
          <text class="option-title">公司信息</text>
          <text class="option-desc">在报表头部显示公司logo和信息</text>
        </view>
        <switch class="option-switch" 
               checked="{{includeCompanyInfo}}" 
               bindchange="onToggleCompanyInfo"/>
      </view>
    </view>
  </view>

  <!-- 预览区域 -->
  <view class="preview-section" wx:if="{{showPreview}}">
    <view class="section-title">
      <text class="title-text">👀 报表预览</text>
      <text class="title-desc">预览将要生成的报表内容</text>
    </view>
    
    <view class="preview-container">
      <view class="preview-header">
        <text class="preview-title">{{selectedReportTitle}}</text>
        <text class="preview-date">{{previewDateRange}}</text>
      </view>
      
      <view class="preview-content">
        <view wx:if="{{includeCharts}}" class="preview-chart">
          <text class="chart-placeholder">📊 图表预览区域</text>
        </view>
        
        <view class="preview-summary">
          <text class="summary-title">财务摘要</text>
          <view class="summary-items">
            <view class="summary-item">
              <text class="item-label">总收入</text>
              <text class="item-value">¥128,500</text>
            </view>
            <view class="summary-item">
              <text class="item-label">总支出</text>
              <text class="item-value">¥95,200</text>
            </view>
            <view class="summary-item">
              <text class="item-label">净利润</text>
              <text class="item-value">¥33,300</text>
            </view>
          </view>
        </view>
        
        <view wx:if="{{includeAIAnalysis}}" class="preview-ai">
          <text class="ai-title">🤖 AI分析</text>
          <text class="ai-content">基于数据分析，您的财务状况整体良好...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 导出历史 -->
  <view class="export-history-section">
    <view class="section-title">
      <text class="title-text">📋 导出历史</text>
      <text class="title-desc">查看和管理之前生成的报表</text>
    </view>
    
    <view class="history-list" wx:if="{{exportHistory.length > 0}}">
      <view wx:for="{{exportHistory}}" wx:key="id" class="history-item">
        <view class="history-info">
          <view class="history-icon" style="background-color: {{item.color}}20;">
            <text class="icon-text" style="color: {{item.color}};">{{item.icon}}</text>
          </view>
          <view class="history-details">
            <text class="history-title">{{item.title}}</text>
            <text class="history-desc">{{item.dateRange}} • {{item.format}}</text>
            <text class="history-time">{{item.createdTime}}</text>
          </view>
        </view>
        <view class="history-actions">
          <view class="action-btn download" 
                bindtap="onDownloadHistory" 
                data-id="{{item.id}}">
            <text class="action-icon">⬇️</text>
          </view>
          <view class="action-btn delete" 
                bindtap="onDeleteHistory" 
                data-id="{{item.id}}">
            <text class="action-icon">🗑️</text>
          </view>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-history">
      <text class="empty-icon">📂</text>
      <text class="empty-text">暂无导出历史</text>
    </view>
  </view>

  <!-- 导出进度 -->
  <view class="export-progress" wx:if="{{isExporting}}">
    <view class="progress-overlay">
      <view class="progress-card">
        <view class="progress-header">
          <text class="progress-title">🚀 正在生成报表</text>
          <text class="progress-desc">请稍候，正在处理您的数据...</text>
        </view>
        
        <view class="progress-bar-container">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{exportProgress}}%;"></view>
          </view>
          <text class="progress-text">{{exportProgress}}%</text>
        </view>
        
        <view class="progress-steps">
          <text class="current-step">{{currentProgressStep}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-btn secondary" bindtap="onPreview">
      <text class="btn-icon">👀</text>
      <text class="btn-text">预览报表</text>
    </view>
    <view class="action-btn primary" bindtap="onStartExport">
      <text class="btn-icon">📤</text>
      <text class="btn-text">开始导出</text>
    </view>
  </view>
</view>