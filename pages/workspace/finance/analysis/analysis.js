// pages/workspace/finance/analysis/analysis.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { checkPageAccess, getCurrentUser } = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    refreshing: false,
    
    // AI分析结果
    healthScore: 75,
    analysisSummary: '根据近期财务数据分析，您的财务状况整体良好。收入保持稳定增长，但部分支出类别需要关注和优化。',
    
    // 关键洞察
    keyInsights: [
      {
        icon: '📈',
        text: '本月收入较上月增长12.5%，主要来源于产品销售收入增加'
      },
      {
        icon: '⚠️',
        text: '办公费用支出占比过高，建议优化办公用品采购策略'
      },
      {
        icon: '💡',
        text: '现金流充足，可考虑适当增加研发投入提升竞争力'
      }
    ],
    
    // 财务统计数据
    dateRange: '2024年1月 - 2024年3月',
    totalIncome: '128,500.00',
    totalExpense: '95,200.00',
    netProfit: '33,300.00',
    profitMargin: '25.9',
    
    // 增长趋势
    incomeGrowth: 12.5,
    expenseGrowth: 8.3,
    profitGrowth: 24.2,
    marginChange: 2.1,
    
    // 支出分类数据
    expenseCategories: [
      {
        id: 1,
        name: '员工薪资',
        amount: '45,000.00',
        percentage: 47.3,
        icon: '👥',
        color: '#1890ff'
      },
      {
        id: 2,
        name: '办公费用',
        amount: '18,500.00',
        percentage: 19.4,
        icon: '🏢',
        color: '#52c41a'
      },
      {
        id: 3,
        name: '营销推广',
        amount: '12,300.00',
        percentage: 12.9,
        icon: '📢',
        color: '#fa8c16'
      },
      {
        id: 4,
        name: '设备采购',
        amount: '10,800.00',
        percentage: 11.3,
        icon: '💻',
        color: '#722ed1'
      },
      {
        id: 5,
        name: '其他费用',
        amount: '8,600.00',
        percentage: 9.0,
        icon: '📋',
        color: '#13c2c2'
      }
    ],
    
    // AI建议
    suggestions: [
      {
        icon: '💰',
        priority: 'high',
        title: '优化办公费用支出',
        description: '建议建立统一采购制度，与供应商建立长期合作关系，预计可节省15-20%的办公费用。',
        impact: '月节省约3000元'
      },
      {
        icon: '📊',
        priority: 'medium',
        title: '加强现金流管理',
        description: '建议建立现金流预测模型，提前规划资金使用，避免资金闲置或紧缺。',
        impact: '提高资金效率20%'
      },
      {
        icon: '🎯',
        priority: 'medium',
        title: '拓展收入渠道',
        description: '基于当前盈利能力，建议适度扩大业务规模或开发新产品线。',
        impact: '预期收入增长25%'
      },
      {
        icon: '📈',
        priority: 'low',
        title: '建立预算管控体系',
        description: '建议制定详细的预算计划，定期跟踪执行情况，提升财务管理效率。',
        impact: '降低成本波动'
      }
    ],
    
    // 图表数据
    chartData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查页面访问权限
    if (!checkPageAccess('finance/analysis')) {
      wx.switchTab({
        url: '/pages/workspace/workspace'
      });
      return;
    }

    this.loadAnalysisData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果需要实时更新数据，可以在这里重新加载
  },

  /**
   * 加载分析数据
   */
  async loadAnalysisData() {
    try {
      this.setData({ loading: true });
      
      // 并行加载所有数据
      const [
        financeStats,
        categoryData,
        trendData,
        aiInsights
      ] = await Promise.all([
        this.loadFinanceStats(),
        this.loadCategoryData(),
        this.loadTrendData(),
        this.loadAIInsights()
      ]);
      
      // 绘制趋势图表
      this.drawTrendChart();
      
    } catch (error) {
      console.error('加载分析数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载财务统计数据
   */
  async loadFinanceStats() {
    try {
      // 实际项目中这里调用API
      // const response = await request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.ANALYSIS_STATS);
      
      // 模拟API响应
      const mockData = {
        totalIncome: '128,500.00',
        totalExpense: '95,200.00',
        netProfit: '33,300.00',
        profitMargin: '25.9',
        incomeGrowth: 12.5,
        expenseGrowth: 8.3,
        profitGrowth: 24.2,
        marginChange: 2.1
      };
      
      this.setData(mockData);
      return mockData;
    } catch (error) {
      console.error('加载财务统计失败:', error);
      throw error;
    }
  },

  /**
   * 加载分类数据
   */
  async loadCategoryData() {
    try {
      // 实际项目中调用API
      // const response = await request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.CATEGORY_ANALYSIS);
      
      // 模拟数据已在data中定义
      return this.data.expenseCategories;
    } catch (error) {
      console.error('加载分类数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载趋势数据
   */
  async loadTrendData() {
    try {
      // 模拟趋势数据
      const trendData = {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        income: [95000, 102000, 98000, 115000, 128500, 135000],
        expense: [78000, 85000, 82000, 89000, 95200, 98000],
        profit: [17000, 17000, 16000, 26000, 33300, 37000]
      };
      
      this.setData({ chartData: trendData });
      return trendData;
    } catch (error) {
      console.error('加载趋势数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载AI洞察
   */
  async loadAIInsights() {
    try {
      // 实际项目中调用AI分析API
      // const response = await request.post(API.API_ENDPOINTS.AI.FINANCIAL_ANALYSIS, {
      //   dateRange: this.data.dateRange
      // });
      
      // 计算财务健康度分数
      const healthScore = this.calculateHealthScore();
      this.setData({ healthScore });
      
      return this.data.keyInsights;
    } catch (error) {
      console.error('加载AI洞察失败:', error);
      throw error;
    }
  },

  /**
   * 计算财务健康度分数
   */
  calculateHealthScore() {
    // 简化的健康度计算逻辑
    let score = 60; // 基础分
    
    // 利润率评分 (最多20分)
    const profitMargin = parseFloat(this.data.profitMargin);
    if (profitMargin > 25) score += 20;
    else if (profitMargin > 15) score += 15;
    else if (profitMargin > 5) score += 10;
    
    // 增长率评分 (最多20分)
    const incomeGrowth = this.data.incomeGrowth;
    if (incomeGrowth > 15) score += 20;
    else if (incomeGrowth > 5) score += 15;
    else if (incomeGrowth > 0) score += 10;
    
    return Math.min(score, 100);
  },

  /**
   * 绘制趋势图表
   */
  drawTrendChart() {
    const chartData = this.data.chartData;
    if (!chartData) return;
    
    const query = wx.createSelectorQuery();
    query.select('.trend-chart')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0]) return;
        
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;
        ctx.scale(dpr, dpr);
        
        // 绘制图表的简化实现
        this.drawSimpleLineChart(ctx, res[0].width, res[0].height, chartData);
      });
  },

  /**
   * 绘制简单折线图
   */
  drawSimpleLineChart(ctx, width, height, data) {
    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 获取数据范围
    const maxIncome = Math.max(...data.income);
    const maxExpense = Math.max(...data.expense);
    const maxValue = Math.max(maxIncome, maxExpense);
    
    // 绘制网格线
    ctx.strokeStyle = '#e5e5e5';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }
    
    // 绘制收入线
    ctx.strokeStyle = '#52c41a';
    ctx.lineWidth = 3;
    ctx.beginPath();
    data.income.forEach((value, index) => {
      const x = padding + (chartWidth / (data.income.length - 1)) * index;
      const y = padding + chartHeight - (value / maxValue) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
    
    // 绘制支出线
    ctx.strokeStyle = '#ff4d4f';
    ctx.lineWidth = 3;
    ctx.beginPath();
    data.expense.forEach((value, index) => {
      const x = padding + (chartWidth / (data.expense.length - 1)) * index;
      const y = padding + chartHeight - (value / maxValue) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.stroke();
  },

  /**
   * 刷新数据
   */
  async onRefresh() {
    if (this.data.refreshing) return;
    
    this.setData({ refreshing: true });
    
    try {
      await this.loadAnalysisData();
      wx.showToast({
        title: '数据已更新',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 1500
      });
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 导出报告
   */
  async onExportReport() {
    wx.showLoading({
      title: '生成报告中...'
    });
    
    try {
      // 模拟报告生成过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      wx.hideLoading();
      wx.showModal({
        title: '报告生成成功',
        content: '财务分析报告已生成完成，是否立即查看？',
        confirmText: '立即查看',
        cancelText: '稍后查看',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/workspace/finance/reports/reports'
            });
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看预测
   */
  onViewForecast() {
    wx.navigateTo({
      url: '/pages/workspace/finance/forecast/forecast'
    });
  },

  /**
   * 图表触摸事件处理
   */
  onChartTouchStart(e) {
    // 图表交互逻辑
  },

  onChartTouchMove(e) {
    // 图表交互逻辑
  },

  onChartTouchEnd(e) {
    // 图表交互逻辑
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果有更多数据可以在这里加载
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '财务分析报告',
      path: '/pages/workspace/finance/analysis/analysis'
    };
  }
})