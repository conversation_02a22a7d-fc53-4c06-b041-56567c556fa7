<!--pages/workspace/finance/analysis/analysis.wxml-->
<view class="analysis-container">
  <!-- 页面标题栏 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">📊 财务分析</text>
      <text class="page-subtitle">深度分析财务状况和趋势</text>
    </view>
    <view class="header-actions">
      <view class="refresh-btn" bindtap="onRefresh">
        <text class="refresh-icon">🔄</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
    <text class="loading-text">AI正在分析财务数据...</text>
  </view>

  <view wx:else>
    <!-- AI分析结果卡片 -->
    <view class="ai-result-card">
      <view class="result-header">
        <text class="result-title">🤖 AI分析结果</text>
        <view class="analysis-score">
          <text class="score-label">财务健康度</text>
          <text class="score-value" style="color: {{healthScore >= 80 ? '#4CAF50' : healthScore >= 60 ? '#FF9800' : '#F44336'}}">
            {{healthScore}}分
          </text>
        </view>
      </view>
      
      <view class="analysis-summary">
        <text class="summary-text">{{analysisSummary}}</text>
      </view>
      
      <view class="key-insights">
        <text class="insights-title">🔍 关键洞察</text>
        <view class="insights-list">
          <view wx:for="{{keyInsights}}" wx:key="index" class="insight-item">
            <text class="insight-icon">{{item.icon}}</text>
            <text class="insight-text">{{item.text}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 财务概览统计 -->
    <view class="finance-overview-section">
      <view class="section-title">
        <text class="title-text">📈 财务概览</text>
        <text class="title-desc">{{dateRange}}</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card income-card">
          <view class="stat-icon">💰</view>
          <view class="stat-content">
            <text class="stat-label">总收入</text>
            <text class="stat-value">¥{{totalIncome}}</text>
            <text class="stat-trend {{incomeGrowth >= 0 ? 'positive' : 'negative'}}">
              {{incomeGrowth >= 0 ? '↗️' : '↘️'}} {{Math.abs(incomeGrowth)}}%
            </text>
          </view>
        </view>
        
        <view class="stat-card expense-card">
          <view class="stat-icon">💸</view>
          <view class="stat-content">
            <text class="stat-label">总支出</text>
            <text class="stat-value">¥{{totalExpense}}</text>
            <text class="stat-trend {{expenseGrowth <= 0 ? 'positive' : 'negative'}}">
              {{expenseGrowth <= 0 ? '↘️' : '↗️'}} {{Math.abs(expenseGrowth)}}%
            </text>
          </view>
        </view>
        
        <view class="stat-card profit-card">
          <view class="stat-icon">📊</view>
          <view class="stat-content">
            <text class="stat-label">净利润</text>
            <text class="stat-value">¥{{netProfit}}</text>
            <text class="stat-trend {{profitGrowth >= 0 ? 'positive' : 'negative'}}">
              {{profitGrowth >= 0 ? '↗️' : '↘️'}} {{Math.abs(profitGrowth)}}%
            </text>
          </view>
        </view>
        
        <view class="stat-card margin-card">
          <view class="stat-icon">📋</view>
          <view class="stat-content">
            <text class="stat-label">利润率</text>
            <text class="stat-value">{{profitMargin}}%</text>
            <text class="stat-trend {{marginChange >= 0 ? 'positive' : 'negative'}}">
              {{marginChange >= 0 ? '↗️' : '↘️'}} {{Math.abs(marginChange)}}%
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势图表区域 -->
    <view class="charts-section">
      <view class="section-title">
        <text class="title-text">📊 趋势分析</text>
        <text class="title-desc">收入支出变化趋势</text>
      </view>
      
      <view class="chart-container">
        <canvas canvas-id="trendChart" 
                class="trend-chart"
                disable-scroll="true"
                bindtouchstart="onChartTouchStart"
                bindtouchmove="onChartTouchMove"
                bindtouchend="onChartTouchEnd">
        </canvas>
      </view>
      
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color income"></view>
          <text class="legend-text">收入</text>
        </view>
        <view class="legend-item">
          <view class="legend-color expense"></view>
          <text class="legend-text">支出</text>
        </view>
        <view class="legend-item">
          <view class="legend-color profit"></view>
          <text class="legend-text">利润</text>
        </view>
      </view>
    </view>

    <!-- 分类分析 -->
    <view class="category-analysis-section">
      <view class="section-title">
        <text class="title-text">🎯 支出分析</text>
        <text class="title-desc">支出类别分布</text>
      </view>
      
      <view class="category-list">
        <view wx:for="{{expenseCategories}}" wx:key="id" class="category-item">
          <view class="category-info">
            <text class="category-icon">{{item.icon}}</text>
            <view class="category-details">
              <text class="category-name">{{item.name}}</text>
              <text class="category-amount">¥{{item.amount}}</text>
            </view>
          </view>
          <view class="category-progress">
            <view class="progress-bar">
              <view class="progress-fill" 
                    style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
            </view>
            <text class="progress-text">{{item.percentage}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- AI建议区域 -->
    <view class="ai-suggestions-section">
      <view class="section-title">
        <text class="title-text">💡 AI优化建议</text>
        <text class="title-desc">基于数据分析的专业建议</text>
      </view>
      
      <view class="suggestions-list">
        <view wx:for="{{suggestions}}" wx:key="index" class="suggestion-card">
          <view class="suggestion-header">
            <text class="suggestion-icon">{{item.icon}}</text>
            <view class="suggestion-priority {{item.priority}}">
              {{item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级'}}
            </view>
          </view>
          <text class="suggestion-title">{{item.title}}</text>
          <text class="suggestion-desc">{{item.description}}</text>
          <view class="suggestion-impact">
            <text class="impact-label">预期效果:</text>
            <text class="impact-value">{{item.impact}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="onExportReport">
        <text class="btn-icon">📄</text>
        <text class="btn-text">导出报告</text>
      </view>
      <view class="action-btn primary" bindtap="onViewForecast">
        <text class="btn-icon">🔮</text>
        <text class="btn-text">查看预测</text>
      </view>
    </view>
  </view>
</view>