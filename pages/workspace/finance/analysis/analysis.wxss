/* pages/workspace/finance/analysis/analysis.wxss */

.analysis-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面标题栏 */
.page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  padding: var(--space-2xl) var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-lg);
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-sm);
  letter-spacing: 1rpx;
}

.page-subtitle {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
}

.refresh-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  transform: scale(0.9) rotate(180deg);
  background: rgba(255, 255, 255, 0.3);
}

.refresh-icon {
  font-size: 32rpx;
}

/* 加载状态 */
.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
  height: 60vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(0, 102, 204, 0.2);
  border-top: 4rpx solid var(--primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-xl);
}

.loading-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  text-align: center;
}

/* AI分析结果卡片 */
.ai-result-card {
  background: var(--bg-primary);
  margin: var(--space-xl);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 1rpx solid var(--border-light);
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.result-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.analysis-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.score-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.score-value {
  font-size: var(--text-2xl);
  font-weight: 700;
}

.analysis-summary {
  margin-bottom: var(--space-2xl);
}

.summary-text {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
}

.key-insights {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
}

.insights-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: block;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
}

.insight-icon {
  font-size: 32rpx;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.insight-text {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  flex: 1;
}

/* 财务概览统计 */
.finance-overview-section {
  margin: var(--space-xl);
}

.section-title {
  margin-bottom: var(--space-xl);
}

.title-text {
  display: block;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.title-desc {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  transition: all 0.3s ease;
}

.stat-card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.income-card .stat-icon {
  background: var(--success-bg);
}

.expense-card .stat-icon {
  background: var(--error-bg);
}

.profit-card .stat-icon {
  background: var(--info-bg);
}

.margin-card .stat-icon {
  background: var(--warning-bg);
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-trend {
  font-size: var(--text-sm);
  font-weight: 500;
}

.stat-trend.positive {
  color: var(--success);
}

.stat-trend.negative {
  color: var(--error);
}

/* 趋势图表区域 */
.charts-section {
  margin: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
}

.chart-container {
  margin: var(--space-xl) 0;
  height: 400rpx;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-lg);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-2xl);
  margin-top: var(--space-xl);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-sm);
}

.legend-color.income {
  background: var(--success);
}

.legend-color.expense {
  background: var(--error);
}

.legend-color.profit {
  background: var(--info);
}

.legend-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 分类分析 */
.category-analysis-section {
  margin: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.category-item {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex: 1;
  min-width: 0;
}

.category-icon {
  font-size: 32rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.category-details {
  flex: 1;
  min-width: 0;
}

.category-name {
  display: block;
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.category-amount {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.category-progress {
  width: 200rpx;
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  min-width: 60rpx;
  text-align: right;
}

/* AI建议区域 */
.ai-suggestions-section {
  margin: var(--space-xl);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.suggestion-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.suggestion-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.suggestion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.suggestion-icon {
  font-size: 32rpx;
}

.suggestion-priority {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}

.suggestion-priority.high {
  background: var(--error-bg);
  color: var(--error);
}

.suggestion-priority.medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.suggestion-priority.low {
  background: var(--info-bg);
  color: var(--info);
}

.suggestion-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.suggestion-desc {
  display: block;
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
}

.suggestion-impact {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.impact-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.impact-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--success);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-xl);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  padding-bottom: calc(var(--space-xl) + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-medium);
}

.action-btn.secondary:active {
  background: var(--bg-tertiary);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: var(--text-base);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .chart-legend {
    flex-wrap: wrap;
    gap: var(--space-lg);
  }
  
  .category-progress {
    width: 150rpx;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--space-md);
  }
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}