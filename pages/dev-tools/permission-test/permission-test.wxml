<!-- pages/dev-tools/permission-test/permission-test.wxml -->
<view class="permission-test-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🔐 权限系统测试工具</text>
    <text class="page-subtitle">用于测试AI财务分析页面的权限控制</text>
  </view>

  <!-- 当前用户信息 -->
  <view class="current-user-section">
    <view class="section-title">当前登录用户</view>
    <view class="current-user-card" wx:if="{{currentUser}}">
      <image class="user-avatar" src="{{currentUser.avatar}}" mode="aspectFill"></image>
      <view class="user-info">
        <view class="user-name">{{currentUser.name}}</view>
        <view class="user-details">
          <text class="role-badge role-{{currentUser.role}}">{{currentUser.role}}</text>
          <text class="user-dept">{{currentUser.department}} - {{currentUser.position}}</text>
        </view>
      </view>
      <view class="logout-btn" bindtap="onLogout">退出</view>
    </view>
    <view class="no-user-card" wx:else>
      <text class="no-user-text">❌ 当前未登录</text>
    </view>
  </view>

  <!-- 快速角色切换 -->
  <view class="quick-switch-section">
    <view class="section-title">快速角色切换</view>
    <view class="role-buttons">
      <button class="role-btn admin-btn" data-role="admin" bindtap="onQuickSwitchRole">管理员</button>
      <button class="role-btn manager-btn" data-role="manager" bindtap="onQuickSwitchRole">经理</button>
      <button class="role-btn finance-btn" data-role="finance" bindtap="onQuickSwitchRole">财务</button>
      <button class="role-btn employee-btn" data-role="employee" bindtap="onQuickSwitchRole">员工</button>
    </view>
  </view>

  <!-- 权限测试区域 -->
  <view class="test-section">
    <view class="section-title">权限测试</view>
    
    <!-- 页面选择 -->
    <view class="page-selector">
      <text class="selector-label">测试页面：</text>
      <picker range="{{testPages}}" bindchange="onPageSelect" value="{{selectedPage}}">
        <view class="picker-display">{{selectedPage}}</view>
      </picker>
    </view>

    <!-- 测试按钮组 -->
    <view class="test-buttons">
      <button class="test-btn primary" bindtap="onTestCurrentUser">测试当前用户</button>
      <button class="test-btn secondary" bindtap="onGenerateReport">生成完整报告</button>
      <button class="test-btn success" bindtap="onTestPageAccess">实际访问页面</button>
    </view>
  </view>

  <!-- 用户列表 -->
  <view class="users-section">
    <view class="section-title">所有测试用户</view>
    <scroll-view class="users-list" scroll-y="true" enhanced="true">
      <view class="user-item" wx:for="{{allUsers}}" wx:key="id" data-userid="{{item.id}}" bindtap="onSwitchUser">
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="user-info">
          <view class="user-name">{{item.name}}</view>
          <view class="user-details">
            <text class="role-badge role-{{item.role}}">{{item.role}}</text>
            <text class="user-dept">{{item.department}} - {{item.position}}</text>
          </view>
        </view>
        <view class="current-badge" wx:if="{{currentUser && currentUser.id === item.id}}">当前</view>
      </view>
    </scroll-view>
  </view>

  <!-- 权限报告 -->
  <view class="report-section" wx:if="{{permissionReport}}">
    <view class="section-title">权限测试报告</view>
    <view class="report-header">
      <text class="report-page">页面: {{permissionReport.pageName}}</text>
      <text class="report-time">生成时间: {{permissionReport.testTime}}</text>
    </view>
    <view class="report-results">
      <view class="result-item" wx:for="{{permissionReport.results}}" wx:key="userId">
        <view class="result-user">
          <text class="user-name">{{item.userName}}</text>
          <text class="role-badge role-{{item.role}}">{{item.roleDisplayName}}</text>
        </view>
        <text class="result-status {{item.hasAccess ? 'allowed' : 'denied'}}">{{item.status}}</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <text class="loading-text">生成报告中...</text>
    </view>
  </view>
</view>