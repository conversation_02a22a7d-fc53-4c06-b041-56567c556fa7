// pages/dev-tools/permission-test/permission-test.js
const { 
  getAllMockUsers, 
  setCurrentUser, 
  getCurrentMockUser, 
  generatePermissionReport,
  getRoleDisplayName,
  switchToRole
} = require('../../../utils/mock-users.js');

const { 
  checkPageAccess,
  ROLES
} = require('../../../utils/permission-checker.js');

Page({
  data: {
    currentUser: null,
    allUsers: [],
    testPages: [
      'finance/overview',
      'finance/ai-comprehensive',
      'finance/analysis',
      'finance/forecast',
      'finance/suggestions',
      'finance/export',
      'finance/reports'
    ],
    permissionReport: null,
    selectedPage: 'finance/ai-comprehensive',
    loading: false
  },

  onLoad() {
    this.loadData();
  },

  onShow() {
    this.refreshCurrentUser();
  },

  // 加载数据
  loadData() {
    const allUsers = getAllMockUsers();
    const currentUser = getCurrentMockUser();
    
    this.setData({
      allUsers,
      currentUser
    });
  },

  // 刷新当前用户信息
  refreshCurrentUser() {
    const currentUser = getCurrentMockUser();
    this.setData({
      currentUser
    });
  },

  // 切换用户
  onSwitchUser(e) {
    const userId = e.currentTarget.dataset.userid;
    
    wx.showModal({
      title: '切换用户',
      content: '确定要切换到这个用户吗？',
      success: (res) => {
        if (res.confirm) {
          const success = setCurrentUser(userId);
          if (success) {
            this.refreshCurrentUser();
            wx.showToast({
              title: '用户切换成功',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '用户切换失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 快速切换角色
  onQuickSwitchRole(e) {
    const role = e.currentTarget.dataset.role;
    
    const success = switchToRole(role);
    if (success) {
      this.refreshCurrentUser();
      wx.showToast({
        title: `已切换为${getRoleDisplayName(role)}`,
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '角色切换失败',
        icon: 'none'
      });
    }
  },

  // 选择测试页面
  onPageSelect(e) {
    const page = e.detail.value[0];
    this.setData({
      selectedPage: this.data.testPages[page]
    });
  },

  // 测试当前用户权限
  onTestCurrentUser() {
    const { currentUser, selectedPage } = this.data;
    
    if (!currentUser) {
      wx.showToast({
        title: '请先选择用户',
        icon: 'none'
      });
      return;
    }

    const hasAccess = checkPageAccess(selectedPage, currentUser.role);
    
    wx.showModal({
      title: '权限测试结果',
      content: `用户：${currentUser.name}\n角色：${getRoleDisplayName(currentUser.role)}\n页面：${selectedPage}\n\n${hasAccess ? '✅ 有访问权限' : '❌ 无访问权限'}`,
      showCancel: false
    });
  },

  // 生成完整权限报告
  onGenerateReport() {
    this.setData({ loading: true });
    
    setTimeout(() => {
      const report = generatePermissionReport(this.data.selectedPage);
      this.setData({
        permissionReport: report,
        loading: false
      });
      
      wx.showToast({
        title: '报告生成完成',
        icon: 'success'
      });
    }, 500);
  },

  // 测试实际页面访问
  onTestPageAccess() {
    const { selectedPage, currentUser } = this.data;
    
    if (!currentUser) {
      wx.showToast({
        title: '请先选择用户',
        icon: 'none'
      });
      return;
    }

    const hasAccess = checkPageAccess(selectedPage, currentUser.role);
    
    if (!hasAccess) {
      wx.showModal({
        title: '访问被拒绝',
        content: `当前用户 ${currentUser.name}（${getRoleDisplayName(currentUser.role)}）无权访问 ${selectedPage} 页面`,
        showCancel: false
      });
      return;
    }

    // 构造页面URL
    let pageUrl;
    switch(selectedPage) {
      case 'finance/overview':
        pageUrl = '/pages/workspace/finance/overview/overview';
        break;
      case 'finance/ai-comprehensive':
        pageUrl = '/pages/workspace/finance/ai-comprehensive/ai-comprehensive';
        break;
      default:
        wx.showToast({
          title: '页面路径未配置',
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: pageUrl,
      success: () => {
        wx.showToast({
          title: '页面访问成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        wx.showToast({
          title: '页面访问失败',
          icon: 'none'
        });
        console.error('页面访问失败:', err);
      }
    });
  },

  // 清空当前用户（退出登录）
  onLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要清空当前用户吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('userInfo');
          this.setData({
            currentUser: null
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
});