/* pages/dev-tools/permission-test/permission-test.wxss */
.permission-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 通用节标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #4CAF50;
}

/* 当前用户区域 */
.current-user-section {
  margin-bottom: 30rpx;
}

.current-user-card, .no-user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.no-user-card {
  justify-content: center;
  padding: 60rpx 30rpx;
}

.no-user-text {
  font-size: 32rpx;
  color: #ff4444;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  background: #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10rpx;
}

.user-dept {
  font-size: 24rpx;
  color: #666;
}

.logout-btn {
  background: #ff4444;
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
}

/* 角色标签 */
.role-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

.role-badge.role-admin {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.role-badge.role-manager {
  background: linear-gradient(45deg, #4ecdc4, #6edde6);
}

.role-badge.role-finance {
  background: linear-gradient(45deg, #45b7d1, #67c3dd);
}

.role-badge.role-employee {
  background: linear-gradient(45deg, #96ceb4, #a8d5c4);
}

/* 快速角色切换 */
.quick-switch-section {
  margin-bottom: 30rpx;
}

.role-buttons {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
}

.role-btn {
  flex: 1;
  padding: 25rpx 10rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  color: white;
}

.admin-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.manager-btn {
  background: linear-gradient(45deg, #4ecdc4, #6edde6);
}

.finance-btn {
  background: linear-gradient(45deg, #45b7d1, #67c3dd);
}

.employee-btn {
  background: linear-gradient(45deg, #96ceb4, #a8d5c4);
}

/* 权限测试区域 */
.test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.test-section .section-title {
  color: #333;
  border-left-color: #667eea;
  margin-bottom: 30rpx;
}

.page-selector {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.selector-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.picker-display {
  flex: 1;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #333;
  border: 2rpx solid #e9ecef;
}

.test-buttons {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
}

.test-btn {
  flex: 1;
  padding: 25rpx 10rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  color: white;
}

.test-btn.primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.test-btn.secondary {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.test-btn.success {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

/* 用户列表 */
.users-section {
  margin-bottom: 30rpx;
}

.users-list {
  max-height: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.user-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.user-item:active {
  background: #e9ecef;
  border-color: #667eea;
}

.user-item .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.user-item .user-info {
  margin-left: 20rpx;
}

.user-item .user-name {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.current-badge {
  background: #4CAF50;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  margin-left: auto;
}

/* 权限报告 */
.report-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.report-section .section-title {
  color: #333;
  border-left-color: #4CAF50;
}

.report-header {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.report-page, .report-time {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.report-results {
  max-height: 500rpx;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-user {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.result-user .user-name {
  font-size: 26rpx;
  color: #333;
}

.result-status {
  font-size: 26rpx;
  font-weight: bold;
}

.result-status.allowed {
  color: #4CAF50;
}

.result-status.denied {
  color: #ff4444;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 60rpx;
  border-radius: 20rpx;
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #333;
}