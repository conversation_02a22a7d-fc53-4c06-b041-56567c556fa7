-- 创建平台用户表
CREATE TABLE IF NOT EXISTS `platform_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `role` enum('admin','manager','support','user') DEFAULT 'user' COMMENT '角色',
  `status` enum('active','inactive','locked') DEFAULT 'active' COMMENT '状态',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台用户表';

-- 插入默认管理员用户
INSERT IGNORE INTO `platform_users` (`username`, `email`, `name`, `password`, `role`, `status`, `created_at`) VALUES
('admin', '<EMAIL>', '系统管理员', '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY', 'admin', 'active', NOW()),
('manager', '<EMAIL>', '平台管理员', '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY', 'manager', 'active', NOW()),
('support', '<EMAIL>', '客服人员', '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY', 'support', 'active', NOW());

-- 创建用户权限表
CREATE TABLE IF NOT EXISTS `platform_user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `module` varchar(50) NOT NULL COMMENT '模块名称',
  `permission` varchar(50) NOT NULL COMMENT '权限名称',
  `granted` tinyint(1) DEFAULT '0' COMMENT '是否授权',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_module_permission` (`user_id`, `module`, `permission`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  FOREIGN KEY (`user_id`) REFERENCES `platform_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台用户权限表';

-- 插入默认权限
INSERT IGNORE INTO `platform_user_permissions` (`user_id`, `module`, `permission`, `granted`) VALUES
(1, 'tenant_management', 'read', 1),
(1, 'tenant_management', 'write', 1),
(1, 'user_management', 'read', 1),
(1, 'user_management', 'write', 1),
(1, 'system_settings', 'read', 1),
(1, 'system_settings', 'write', 1),
(1, 'api_management', 'read', 1),
(1, 'api_management', 'write', 1),
(1, 'data_export', 'read', 1),
(1, 'data_export', 'write', 1);
