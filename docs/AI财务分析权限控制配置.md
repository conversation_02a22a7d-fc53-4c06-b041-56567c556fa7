# AI财务分析模块权限控制配置

## 权限控制总览

AI财务分析页面 (`finance/ai-comprehensive`) 现已配置完整的权限控制系统，只允许以下角色访问：

### 有权限访问的角色
- ✅ **系统管理员** (`admin`) - 完全访问权限
- ✅ **经理** (`manager`) - 完全访问权限  
- ✅ **财务人员** (`finance`) - 完全访问权限

### 无权限访问的角色
- ❌ **普通员工** (`employee`) - 禁止访问

## 权限系统架构

### 1. 权限配置文件
**文件路径**: `/utils/permission-checker.js`
```javascript
// AI综合财务分析页面（集成版）
'finance/ai-comprehensive': {
  [ROLES.EMPLOYEE]: false,    // 普通员工禁止访问
  [ROLES.FINANCE]: true,      // 财务人员可访问
  [ROLES.MANAGER]: true,      // 经理可访问
  [ROLES.ADMIN]: true         // 管理员可访问
}
```

### 2. 页面权限检查
**文件路径**: `/pages/workspace/finance/ai-comprehensive/ai-comprehensive.js`

页面在 `onLoad` 生命周期中进行权限验证：
- 获取当前用户信息
- 检查访问权限
- 权限不足时显示详细说明并跳转回工作台
- 权限验证通过后正常初始化页面功能

### 3. 权限验证流程

```mermaid
graph TD
    A[用户访问AI财务分析页面] --> B[获取当前用户信息]
    B --> C[调用权限检查函数]
    C --> D{权限验证}
    D -->|有权限| E[正常加载页面]
    D -->|无权限| F[显示权限不足提示]
    F --> G[跳转回工作台首页]
```

## 测试工具

### 权限测试页面
**文件路径**: `/pages/dev-tools/permission-test/`

提供完整的权限测试工具，包括：
- 模拟用户数据和角色管理
- 快速角色切换功能
- 权限测试和报告生成
- 实际页面访问测试

### 模拟用户数据
**文件路径**: `/utils/mock-users.js`

预设了6个测试用户，涵盖所有角色类型：
- 系统管理员：admin_001
- 总经理：manager_001  
- 财务主管：finance_001
- 出纳员：finance_002
- 养殖员：employee_001
- 技术员：employee_002

## 安全特性

### 1. 多层权限检查
- 页面级权限控制
- 实时用户状态验证
- 详细的权限拒绝提示

### 2. 用户体验优化
- 权限不足时提供明确的角色要求说明
- 静默跳转避免用户困惑
- 完整的日志记录便于调试

### 3. 开发调试支持
- 详细的控制台日志输出
- 权限测试工具和报告
- 角色快速切换功能

## 使用示例

### 开发阶段测试权限
1. 访问权限测试页面：`/pages/dev-tools/permission-test/permission-test`
2. 选择不同角色的测试用户
3. 点击"实际访问页面"测试AI财务分析页面权限
4. 查看权限验证结果和用户体验

### 生产环境部署
1. 确保用户登录时正确设置 `userInfo` 到本地存储
2. 包含正确的 `role` 字段（admin/manager/finance/employee）
3. AI财务分析页面会自动进行权限验证

## 权限扩展

如需修改权限配置，只需编辑 `/utils/permission-checker.js` 中的 `PAGE_PERMISSIONS` 配置：

```javascript
'finance/ai-comprehensive': {
  [ROLES.EMPLOYEE]: false,    // 修改为 true 可允许员工访问
  [ROLES.FINANCE]: true,      
  [ROLES.MANAGER]: true,      
  [ROLES.ADMIN]: true         
}
```

## 注意事项

1. **用户信息存储**: 权限系统依赖微信小程序的 `wx.getStorageSync('userInfo')` 获取用户信息
2. **角色字段**: 用户信息必须包含 `role` 字段，值为预定义的角色常量
3. **页面标识**: 权限检查使用 `'finance/ai-comprehensive'` 作为页面标识符
4. **兼容性**: 系统向下兼容，未配置权限的页面默认允许所有用户访问

---

**配置完成时间**: 2024年当前时间  
**权限状态**: ✅ 已启用并测试通过