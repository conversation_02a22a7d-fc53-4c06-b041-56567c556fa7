#!/bin/bash

# 智慧养鹅项目结构清理脚本
# Project Structure Cleanup Script

echo "🧹 开始清理项目结构..."

# 清理临时文件和缓存
echo "清理临时文件..."
find . -name ".DS_Store" -type f -delete 2>/dev/null || true
find . -name "*.tmp" -type f -delete 2>/dev/null || true  
find . -name "*.log" -type f -delete 2>/dev/null || true
find . -name "node_modules/.cache" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理空目录
echo "清理空目录..."
find . -type d -empty -delete 2>/dev/null || true

# 检查重复文件
echo "检查可能的重复文件..."
find . -name "*.bak" -type f -ls 2>/dev/null || true
find . -name "*-copy*" -type f -ls 2>/dev/null || true
find . -name "*backup*" -type f -ls 2>/dev/null || true

# 检查未使用的资源文件
echo "检查可能未使用的资源文件..."
find ./assets -name "*.png" -o -name "*.jpg" -o -name "*.gif" 2>/dev/null | wc -l | xargs echo "图片文件总数:"
find ./assets -name "*.mp3" -o -name "*.mp4" 2>/dev/null | wc -l | xargs echo "媒体文件总数:"

# 检查代码文件统计
echo "项目代码统计..."
find . -name "*.js" -not -path "./node_modules/*" | wc -l | xargs echo "JavaScript文件数:"
find . -name "*.json" -not -path "./node_modules/*" | wc -l | xargs echo "JSON文件数:"
find . -name "*.wxml" | wc -l | xargs echo "WXML文件数:"
find . -name "*.wxss" | wc -l | xargs echo "WXSS文件数:"

# 检查package.json依赖
echo "检查依赖包..."
if [ -f "package.json" ]; then
    echo "package.json存在"
    npm list --depth=0 2>/dev/null | grep "UNMET DEPENDENCY" && echo "⚠️  发现未满足的依赖" || echo "✅ 依赖检查通过"
fi

if [ -f "backend/package.json" ]; then
    echo "backend/package.json存在"
    cd backend && npm list --depth=0 2>/dev/null | grep "UNMET DEPENDENCY" && echo "⚠️  后端发现未满足的依赖" || echo "✅ 后端依赖检查通过"
    cd ..
fi

# 生成清理报告
echo "📊 生成清理报告..."
cat > cleanup-report.md << 'EOF'
# 项目清理报告

## 清理内容
- ✅ 删除临时文件 (.DS_Store, *.tmp, *.log)
- ✅ 清理空目录
- ✅ 检查重复文件
- ✅ 统计项目文件

## 后续建议
1. 定期执行此清理脚本
2. 使用.gitignore忽略临时文件
3. 考虑使用压缩工具优化资源文件
4. 定期更新依赖包

## 注意事项  
- 请在执行前备份重要文件
- 检查重复文件报告，确认是否需要保留
EOF

echo "✅ 项目结构清理完成！"
echo "📄 清理报告已生成: cleanup-report.md"