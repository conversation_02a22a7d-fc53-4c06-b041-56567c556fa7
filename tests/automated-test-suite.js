/**
 * 智慧养鹅SAAS管理后台自动化测试套件
 * 包含单元测试、集成测试和E2E测试
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class AutomatedTestSuite {
    constructor() {
        this.baseURL = 'http://localhost:3003';
        this.session = null;
        this.testResults = {
            unitTests: { passed: 0, failed: 0, tests: [] },
            integrationTests: { passed: 0, failed: 0, tests: [] },
            e2eTests: { passed: 0, failed: 0, tests: [] },
            summary: {}
        };
    }

    // 登录获取会话
    async login() {
        try {
            const response = await axios.post(`${this.baseURL}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            }, {
                withCredentials: true,
                headers: { 'Content-Type': 'application/json' }
            });
            
            this.session = response.headers['set-cookie'];
            return true;
        } catch (error) {
            console.error('登录失败:', error.message);
            return false;
        }
    }

    // 单元测试 - 测试单个API端点
    async runUnitTests() {
        console.log('🧪 运行单元测试...');
        
        const unitTests = [
            {
                name: '用户API - 获取用户列表',
                test: async () => {
                    const response = await axios.get(`${this.baseURL}/api/users`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    return response.status === 200 && response.data.success === true;
                }
            },
            {
                name: '生产API - 获取生产记录',
                test: async () => {
                    const response = await axios.get(`${this.baseURL}/api/production`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    return response.status === 200 && response.data.success === true;
                }
            },
            {
                name: '财务API - 获取财务记录',
                test: async () => {
                    const response = await axios.get(`${this.baseURL}/api/finance`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    return response.status === 200 && response.data.success === true;
                }
            },
            {
                name: '库存API - 获取库存列表',
                test: async () => {
                    const response = await axios.get(`${this.baseURL}/api/inventory`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    return response.status === 200 && response.data.success === true;
                }
            },
            {
                name: '报表API - 获取报表数据',
                test: async () => {
                    const response = await axios.get(`${this.baseURL}/api/reports`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    return response.status === 200 && response.data.success === true;
                }
            }
        ];

        for (const unitTest of unitTests) {
            try {
                const result = await unitTest.test();
                if (result) {
                    this.testResults.unitTests.passed++;
                    this.testResults.unitTests.tests.push({
                        name: unitTest.name,
                        status: 'PASSED',
                        message: '测试通过'
                    });
                    console.log(`✅ ${unitTest.name}`);
                } else {
                    throw new Error('测试条件不满足');
                }
            } catch (error) {
                this.testResults.unitTests.failed++;
                this.testResults.unitTests.tests.push({
                    name: unitTest.name,
                    status: 'FAILED',
                    message: error.message
                });
                console.log(`❌ ${unitTest.name}: ${error.message}`);
            }
        }
    }

    // 集成测试 - 测试多个组件的交互
    async runIntegrationTests() {
        console.log('\n🔗 运行集成测试...');
        
        const integrationTests = [
            {
                name: '用户认证流程测试',
                test: async () => {
                    // 测试登录 -> 获取用户信息 -> 登出流程
                    const loginResponse = await axios.post(`${this.baseURL}/auth/login`, {
                        username: 'admin',
                        password: 'admin123'
                    });
                    
                    if (loginResponse.status !== 200) return false;
                    
                    const userResponse = await axios.get(`${this.baseURL}/api/users`, {
                        headers: { 'Cookie': loginResponse.headers['set-cookie'].join('; ') }
                    });
                    
                    return userResponse.status === 200 && userResponse.data.success === true;
                }
            },
            {
                name: '数据流测试 - 生产到财务',
                test: async () => {
                    // 测试生产数据 -> 财务记录的数据流
                    const productionResponse = await axios.get(`${this.baseURL}/api/production`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    
                    if (productionResponse.status !== 200) return false;
                    
                    const financeResponse = await axios.get(`${this.baseURL}/api/finance`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    
                    return financeResponse.status === 200 && financeResponse.data.success === true;
                }
            },
            {
                name: '报表数据完整性测试',
                test: async () => {
                    // 测试报表数据是否包含所有必要字段
                    const response = await axios.get(`${this.baseURL}/api/reports?type=summary`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    
                    if (response.status !== 200) return false;
                    
                    const data = response.data.data;
                    const requiredFields = ['totalFlocks', 'totalGeese', 'totalEggsThisMonth', 'totalRevenueThisMonth'];
                    
                    return requiredFields.every(field => data.hasOwnProperty(field));
                }
            },
            {
                name: '页面导航完整性测试',
                test: async () => {
                    // 测试主要页面是否都能正常访问
                    const pages = ['/dashboard', '/users', '/flocks', '/production', '/finance'];
                    
                    for (const page of pages) {
                        const response = await axios.get(`${this.baseURL}${page}`, {
                            headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                        });
                        
                        if (response.status !== 200) return false;
                    }
                    
                    return true;
                }
            }
        ];

        for (const integrationTest of integrationTests) {
            try {
                const result = await integrationTest.test();
                if (result) {
                    this.testResults.integrationTests.passed++;
                    this.testResults.integrationTests.tests.push({
                        name: integrationTest.name,
                        status: 'PASSED',
                        message: '测试通过'
                    });
                    console.log(`✅ ${integrationTest.name}`);
                } else {
                    throw new Error('测试条件不满足');
                }
            } catch (error) {
                this.testResults.integrationTests.failed++;
                this.testResults.integrationTests.tests.push({
                    name: integrationTest.name,
                    status: 'FAILED',
                    message: error.message
                });
                console.log(`❌ ${integrationTest.name}: ${error.message}`);
            }
        }
    }

    // E2E测试 - 端到端用户场景测试
    async runE2ETests() {
        console.log('\n🎭 运行E2E测试...');
        
        const e2eTests = [
            {
                name: '完整用户工作流程测试',
                test: async () => {
                    // 模拟用户完整操作流程：登录 -> 查看仪表板 -> 管理鹅群 -> 查看报表
                    
                    // 1. 访问登录页面
                    const loginPageResponse = await axios.get(`${this.baseURL}/auth/login`);
                    if (loginPageResponse.status !== 200) return false;
                    
                    // 2. 登录
                    const loginResponse = await axios.post(`${this.baseURL}/auth/login`, {
                        username: 'admin',
                        password: 'admin123'
                    });
                    if (loginResponse.status !== 200) return false;
                    
                    const sessionCookie = loginResponse.headers['set-cookie'].join('; ');
                    
                    // 3. 访问仪表板
                    const dashboardResponse = await axios.get(`${this.baseURL}/dashboard`, {
                        headers: { 'Cookie': sessionCookie }
                    });
                    if (dashboardResponse.status !== 200) return false;
                    
                    // 4. 访问鹅群管理
                    const flocksResponse = await axios.get(`${this.baseURL}/flocks`, {
                        headers: { 'Cookie': sessionCookie }
                    });
                    if (flocksResponse.status !== 200) return false;
                    
                    // 5. 访问报表
                    const reportsResponse = await axios.get(`${this.baseURL}/reports`, {
                        headers: { 'Cookie': sessionCookie }
                    });
                    if (reportsResponse.status !== 200) return false;
                    
                    return true;
                }
            },
            {
                name: '数据管理完整流程测试',
                test: async () => {
                    // 测试数据的查看 -> 筛选 -> 导出流程
                    
                    // 1. 获取用户列表
                    const usersResponse = await axios.get(`${this.baseURL}/api/users`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    if (usersResponse.status !== 200) return false;
                    
                    // 2. 带筛选条件获取用户列表
                    const filteredUsersResponse = await axios.get(`${this.baseURL}/api/users?page=1&limit=10`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    if (filteredUsersResponse.status !== 200) return false;
                    
                    // 3. 获取生产数据
                    const productionResponse = await axios.get(`${this.baseURL}/api/production`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    if (productionResponse.status !== 200) return false;
                    
                    return true;
                }
            },
            {
                name: '系统健康检查测试',
                test: async () => {
                    // 测试系统各个组件的健康状态
                    
                    // 1. API健康检查
                    const healthResponse = await axios.get(`${this.baseURL}/api/health`);
                    if (healthResponse.status !== 200) return false;
                    
                    // 2. 数据库连接测试（通过用户API）
                    const dbTestResponse = await axios.get(`${this.baseURL}/api/users`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    if (dbTestResponse.status !== 200) return false;
                    
                    // 3. 会话管理测试
                    const sessionTestResponse = await axios.get(`${this.baseURL}/dashboard`, {
                        headers: { 'Cookie': this.session ? this.session.join('; ') : '' }
                    });
                    if (sessionTestResponse.status !== 200) return false;
                    
                    return true;
                }
            }
        ];

        for (const e2eTest of e2eTests) {
            try {
                const result = await e2eTest.test();
                if (result) {
                    this.testResults.e2eTests.passed++;
                    this.testResults.e2eTests.tests.push({
                        name: e2eTest.name,
                        status: 'PASSED',
                        message: '测试通过'
                    });
                    console.log(`✅ ${e2eTest.name}`);
                } else {
                    throw new Error('测试条件不满足');
                }
            } catch (error) {
                this.testResults.e2eTests.failed++;
                this.testResults.e2eTests.tests.push({
                    name: e2eTest.name,
                    status: 'FAILED',
                    message: error.message
                });
                console.log(`❌ ${e2eTest.name}: ${error.message}`);
            }
        }
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🚀 开始运行自动化测试套件...\n');
        
        // 登录
        const loginSuccess = await this.login();
        if (!loginSuccess) {
            console.log('❌ 登录失败，无法运行测试');
            return this.testResults;
        }

        // 运行各类测试
        await this.runUnitTests();
        await this.runIntegrationTests();
        await this.runE2ETests();

        // 生成测试摘要
        this.generateSummary();
        
        return this.testResults;
    }

    // 生成测试摘要
    generateSummary() {
        const totalPassed = this.testResults.unitTests.passed + 
                           this.testResults.integrationTests.passed + 
                           this.testResults.e2eTests.passed;
        
        const totalFailed = this.testResults.unitTests.failed + 
                           this.testResults.integrationTests.failed + 
                           this.testResults.e2eTests.failed;
        
        const totalTests = totalPassed + totalFailed;
        const successRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(2) : 0;
        
        this.testResults.summary = {
            totalTests,
            totalPassed,
            totalFailed,
            successRate: `${successRate}%`,
            unitTests: {
                passed: this.testResults.unitTests.passed,
                failed: this.testResults.unitTests.failed
            },
            integrationTests: {
                passed: this.testResults.integrationTests.passed,
                failed: this.testResults.integrationTests.failed
            },
            e2eTests: {
                passed: this.testResults.e2eTests.passed,
                failed: this.testResults.e2eTests.failed
            }
        };
    }

    // 打印测试报告
    printReport() {
        const summary = this.testResults.summary;
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 自动化测试套件结果报告');
        console.log('='.repeat(60));
        console.log(`总测试数: ${summary.totalTests}`);
        console.log(`通过数: ${summary.totalPassed}`);
        console.log(`失败数: ${summary.totalFailed}`);
        console.log(`成功率: ${summary.successRate}`);
        
        console.log('\n📋 分类测试结果:');
        console.log(`单元测试: ${summary.unitTests.passed}/${summary.unitTests.passed + summary.unitTests.failed} 通过`);
        console.log(`集成测试: ${summary.integrationTests.passed}/${summary.integrationTests.passed + summary.integrationTests.failed} 通过`);
        console.log(`E2E测试: ${summary.e2eTests.passed}/${summary.e2eTests.passed + summary.e2eTests.failed} 通过`);
        
        // 显示失败的测试
        const allFailedTests = [
            ...this.testResults.unitTests.tests.filter(t => t.status === 'FAILED'),
            ...this.testResults.integrationTests.tests.filter(t => t.status === 'FAILED'),
            ...this.testResults.e2eTests.tests.filter(t => t.status === 'FAILED')
        ];
        
        if (allFailedTests.length > 0) {
            console.log('\n❌ 失败的测试:');
            allFailedTests.forEach(test => {
                console.log(`  - ${test.name}: ${test.message}`);
            });
        }
        
        console.log('\n💡 建议:');
        if (summary.totalFailed === 0) {
            console.log('- 所有测试都通过了！系统运行良好');
            console.log('- 建议定期运行此测试套件以确保系统稳定性');
        } else {
            console.log('- 修复失败的测试以提高系统稳定性');
            console.log('- 考虑添加更多测试用例覆盖边缘情况');
        }
    }

    // 保存测试报告
    async saveReport() {
        const reportPath = path.join(__dirname, 'automated-test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
        console.log(`\n📊 自动化测试报告已保存到: ${reportPath}`);
    }
}

// 运行自动化测试套件
async function runAutomatedTests() {
    const testSuite = new AutomatedTestSuite();
    
    try {
        const results = await testSuite.runAllTests();
        testSuite.printReport();
        await testSuite.saveReport();
        
        return results;
        
    } catch (error) {
        console.error('自动化测试套件执行失败:', error);
    }
}

if (require.main === module) {
    runAutomatedTests();
}

module.exports = AutomatedTestSuite;
