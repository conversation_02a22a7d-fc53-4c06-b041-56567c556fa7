const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 交互功能测试
 * 测试表单提交、按钮点击、导航链接跳转、数据表格操作等
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  apiURL: 'http://localhost:3000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  }
};

// 测试结果收集
let testResults = {
  workingFeatures: [],
  brokenFeatures: [],
  issues: []
};

test.describe('交互功能测试', () => {
  
  // 登录辅助函数
  async function login(page) {
    await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
    await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
    await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  }

  test('1. 登录表单交互测试', async ({ page }) => {
    await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
    
    // 测试表单验证
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);
    
    // 测试正确登录
    await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
    await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待重定向
    await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
    
    testResults.workingFeatures.push('登录表单提交');
    console.log('✅ 登录表单交互功能正常');
  });

  test('2. 导航菜单交互测试', async ({ page }) => {
    await login(page);
    
    // 测试主要导航链接
    const navigationTests = [
      { name: '仪表板', selector: 'a[href="/dashboard"], a:has-text("仪表板"), a:has-text("首页")' },
      { name: '用户管理', selector: 'a[href="/users"], a:has-text("用户管理")' },
      { name: '租户管理', selector: 'a[href="/tenants"], a:has-text("租户管理")' },
      { name: '健康管理', selector: 'a[href="/health"], a:has-text("健康管理")' },
      { name: '生产管理', selector: 'a[href="/production"], a:has-text("生产管理")' }
    ];
    
    for (const nav of navigationTests) {
      try {
        const navLink = page.locator(nav.selector).first();
        
        if (await navLink.count() > 0) {
          await navLink.click();
          await page.waitForTimeout(1000);
          
          // 检查页面是否正常加载
          await expect(page.locator('body')).toBeVisible();
          
          testResults.workingFeatures.push(`导航-${nav.name}`);
          console.log(`✅ ${nav.name} 导航功能正常`);
        } else {
          console.log(`⚠️ ${nav.name} 导航链接未找到`);
        }
      } catch (error) {
        testResults.brokenFeatures.push(`导航-${nav.name}`);
        testResults.issues.push(`${nav.name}导航失败: ${error.message}`);
        console.log(`❌ ${nav.name} 导航功能异常: ${error.message}`);
      }
    }
  });

  test('3. 用户管理页面交互测试', async ({ page }) => {
    await login(page);
    await page.goto(`${TEST_CONFIG.baseURL}/users`);
    
    try {
      // 等待页面加载
      await page.waitForTimeout(2000);
      
      // 查找搜索框
      const searchBox = page.locator('input[type="search"], input[placeholder*="搜索"], .search-input').first();
      if (await searchBox.count() > 0) {
        await searchBox.fill('admin');
        await page.waitForTimeout(1000);
        testResults.workingFeatures.push('用户搜索功能');
        console.log('✅ 用户搜索功能正常');
      }
      
      // 查找添加用户按钮
      const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-primary').first();
      if (await addButton.count() > 0) {
        testResults.workingFeatures.push('添加用户按钮');
        console.log('✅ 添加用户按钮存在');
      }
      
      // 查找数据表格
      const dataTable = page.locator('table, .table, .data-table').first();
      if (await dataTable.count() > 0) {
        testResults.workingFeatures.push('用户数据表格');
        console.log('✅ 用户数据表格显示正常');
      }
      
    } catch (error) {
      testResults.brokenFeatures.push('用户管理交互');
      testResults.issues.push(`用户管理页面交互失败: ${error.message}`);
      console.log(`❌ 用户管理页面交互异常: ${error.message}`);
    }
  });

  test('4. 仪表板交互功能测试', async ({ page }) => {
    await login(page);
    
    try {
      // 等待页面加载
      await page.waitForTimeout(2000);
      
      // 查找刷新按钮
      const refreshButton = page.locator('button:has-text("刷新"), .btn-tool, [onclick*="reload"]').first();
      if (await refreshButton.count() > 0) {
        await refreshButton.click();
        await page.waitForTimeout(1000);
        testResults.workingFeatures.push('仪表板刷新功能');
        console.log('✅ 仪表板刷新功能正常');
      }
      
      // 查找统计卡片链接
      const statLinks = page.locator('.small-box-footer, .card a, .info-box a');
      const linkCount = await statLinks.count();
      if (linkCount > 0) {
        // 测试第一个链接
        await statLinks.first().click();
        await page.waitForTimeout(1000);
        testResults.workingFeatures.push('统计卡片链接');
        console.log('✅ 统计卡片链接功能正常');
      }
      
    } catch (error) {
      testResults.brokenFeatures.push('仪表板交互');
      testResults.issues.push(`仪表板交互失败: ${error.message}`);
      console.log(`❌ 仪表板交互异常: ${error.message}`);
    }
  });

  test('5. 响应式交互测试', async ({ page }) => {
    await login(page);
    
    // 测试不同屏幕尺寸下的交互
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 768, height: 1024, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      try {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.waitForTimeout(500);
        
        // 检查页面是否仍然可交互
        const isVisible = await page.locator('body').isVisible();
        expect(isVisible).toBe(true);
        
        // 在移动端测试侧边栏切换
        if (viewport.width < 768) {
          const menuToggle = page.locator('[data-widget="pushmenu"], .navbar-toggler, .menu-toggle').first();
          if (await menuToggle.count() > 0) {
            await menuToggle.click();
            await page.waitForTimeout(500);
          }
        }
        
        testResults.workingFeatures.push(`响应式交互-${viewport.name}`);
        console.log(`✅ ${viewport.name} 响应式交互正常`);
      } catch (error) {
        testResults.brokenFeatures.push(`响应式交互-${viewport.name}`);
        testResults.issues.push(`${viewport.name}响应式交互失败: ${error.message}`);
        console.log(`❌ ${viewport.name} 响应式交互异常: ${error.message}`);
      }
    }
  });

  test('6. 用户下拉菜单交互测试', async ({ page }) => {
    await login(page);
    
    try {
      // 查找用户下拉菜单
      const userDropdown = page.locator('#navbarDropdown, .dropdown-toggle, .user-menu').first();
      
      if (await userDropdown.count() > 0) {
        // 点击展开下拉菜单
        await userDropdown.click();
        await page.waitForTimeout(500);
        
        // 检查下拉菜单项
        const dropdownItems = page.locator('.dropdown-item, .dropdown-menu a');
        const itemCount = await dropdownItems.count();
        
        if (itemCount > 0) {
          testResults.workingFeatures.push('用户下拉菜单');
          console.log(`✅ 用户下拉菜单功能正常，包含 ${itemCount} 个菜单项`);
        }
      } else {
        console.log('⚠️ 用户下拉菜单未找到');
      }
      
    } catch (error) {
      testResults.brokenFeatures.push('用户下拉菜单');
      testResults.issues.push(`用户下拉菜单交互失败: ${error.message}`);
      console.log(`❌ 用户下拉菜单交互异常: ${error.message}`);
    }
  });

  test('7. 表单验证功能测试', async ({ page }) => {
    await login(page);
    
    // 测试登录表单验证
    await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
    
    try {
      // 清空表单并尝试提交
      await page.fill('input[name="username"]', '');
      await page.fill('input[name="password"]', '');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(1000);
      
      // 检查是否有验证提示
      const validationMessage = page.locator('.alert, .error, .invalid-feedback, .text-danger');
      const hasValidation = await validationMessage.count() > 0;
      
      if (hasValidation) {
        testResults.workingFeatures.push('表单验证');
        console.log('✅ 表单验证功能正常');
      } else {
        console.log('⚠️ 表单验证功能可能需要改进');
      }
      
    } catch (error) {
      testResults.brokenFeatures.push('表单验证');
      testResults.issues.push(`表单验证测试失败: ${error.message}`);
      console.log(`❌ 表单验证测试异常: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 交互功能测试结果汇总:');
    console.log(`✅ 正常功能: ${testResults.workingFeatures.length}`);
    console.log(`❌ 异常功能: ${testResults.brokenFeatures.length}`);
    
    if (testResults.workingFeatures.length > 0) {
      console.log('\n✅ 正常工作的功能:');
      testResults.workingFeatures.forEach(feature => {
        console.log(`  - ${feature}`);
      });
    }
    
    if (testResults.brokenFeatures.length > 0) {
      console.log('\n❌ 需要修复的功能:');
      testResults.brokenFeatures.forEach(feature => {
        console.log(`  - ${feature}`);
      });
    }
    
    if (testResults.issues.length > 0) {
      console.log('\n🐛 发现的问题:');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

});
