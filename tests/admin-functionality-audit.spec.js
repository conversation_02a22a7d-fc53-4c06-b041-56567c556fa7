const { test, expect } = require('@playwright/test');

/**
 * 后台管理中心功能审查测试套件
 * 基于Context7最佳实践，对现有功能进行全面审查
 */

// 测试配置
const ADMIN_BASE_URL = 'http://localhost:4001';
const STATIC_ADMIN_URL = 'http://localhost:4001'; // 静态管理页面
const TEST_CREDENTIALS = {
  username: 'demo',
  password: 'demo123'
};

// 功能审查报告存储
let auditReport = {
  timestamp: new Date().toISOString(),
  baseUrl: ADMIN_BASE_URL,
  testResults: [],
  functionalityGaps: [],
  recommendations: []
};

test.describe('后台管理中心功能审查', () => {
  
  test.beforeEach(async ({ page }) => {
    // 设置页面超时
    page.setDefaultTimeout(10000);
    
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console Error:', msg.text());
        auditReport.testResults.push({
          type: 'console_error',
          message: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });
    
    // 监听网络错误
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log('Network Error:', response.url(), response.status());
        auditReport.testResults.push({
          type: 'network_error',
          url: response.url(),
          status: response.status(),
          timestamp: new Date().toISOString()
        });
      }
    });
  });

  test('1. 访问静态管理页面', async ({ page }) => {
    console.log('🔍 测试静态管理页面访问...');
    
    try {
      await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
      await page.waitForLoadState('networkidle');
      
      // 检查页面标题
      const title = await page.title();
      expect(title).toContain('智慧养鹅平台');
      
      // 检查主要元素是否存在
      await expect(page.locator('.admin-container')).toBeVisible();
      await expect(page.locator('.sidebar')).toBeVisible();
      await expect(page.locator('.main-content')).toBeVisible();
      
      auditReport.testResults.push({
        test: '静态管理页面访问',
        status: 'PASS',
        details: '页面成功加载，主要布局元素正常显示'
      });
      
    } catch (error) {
      auditReport.testResults.push({
        test: '静态管理页面访问',
        status: 'FAIL',
        error: error.message
      });
      auditReport.functionalityGaps.push('静态管理页面无法正常访问');
    }
  });

  test('2. 检查侧边栏菜单功能', async ({ page }) => {
    console.log('🔍 测试侧边栏菜单功能...');
    
    await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
    await page.waitForLoadState('networkidle');
    
    // 检查菜单项
    const menuItems = [
      { selector: '[index="/dashboard"]', name: '仪表板' },
      { selector: '[index="/approval/pending"]', name: '待审批' },
      { selector: '[index="/approval/history"]', name: '审批历史' },
      { selector: '[index="/tenant/list"]', name: '租户列表' },
      { selector: '[index="/tenant/statistics"]', name: '数据统计' },
      { selector: '[index="/user/list"]', name: '用户列表' },
      { selector: '[index="/user/roles"]', name: '角色权限' },
      { selector: '[index="/system"]', name: '系统设置' },
      { selector: '[index="/logs"]', name: '操作日志' }
    ];
    
    for (const item of menuItems) {
      try {
        const menuElement = page.locator(item.selector);
        await expect(menuElement).toBeVisible();
        
        // 尝试点击菜单项
        await menuElement.click();
        await page.waitForTimeout(1000);
        
        // 检查是否显示"页面开发中"
        const developmentMessage = page.locator('text=页面开发中');
        const isDevelopmentPage = await developmentMessage.isVisible();
        
        if (isDevelopmentPage) {
          auditReport.functionalityGaps.push(`${item.name}功能未实现，显示开发中页面`);
        }
        
        auditReport.testResults.push({
          test: `菜单项-${item.name}`,
          status: 'PASS',
          implemented: !isDevelopmentPage,
          details: isDevelopmentPage ? '功能未实现' : '菜单可点击'
        });
        
      } catch (error) {
        auditReport.testResults.push({
          test: `菜单项-${item.name}`,
          status: 'FAIL',
          error: error.message
        });
      }
    }
  });

  test('3. 检查仪表板数据展示', async ({ page }) => {
    console.log('🔍 测试仪表板数据展示...');
    
    await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
    await page.waitForLoadState('networkidle');
    
    // 检查统计卡片
    const statCards = await page.locator('.stat-card').count();
    expect(statCards).toBeGreaterThan(0);
    
    // 检查数据是否为模拟数据
    const pendingCount = await page.locator('.stat-card').first().locator('.stat-number').textContent();
    
    auditReport.testResults.push({
      test: '仪表板数据展示',
      status: 'PASS',
      details: `显示${statCards}个统计卡片，待审批数量：${pendingCount}`,
      dataSource: '前端模拟数据'
    });
    
    auditReport.functionalityGaps.push('仪表板数据为前端模拟数据，未连接真实后端API');
  });

  test('4. 检查表格交互功能', async ({ page }) => {
    console.log('🔍 测试表格交互功能...');
    
    await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
    await page.waitForLoadState('networkidle');
    
    // 检查最近申请表格
    const table = page.locator('.el-table');
    await expect(table).toBeVisible();
    
    // 检查操作按钮
    const approveButtons = page.locator('button:has-text("通过")');
    const rejectButtons = page.locator('button:has-text("拒绝")');
    
    const approveCount = await approveButtons.count();
    const rejectCount = await rejectButtons.count();
    
    if (approveCount > 0) {
      // 尝试点击第一个通过按钮
      await approveButtons.first().click();
      await page.waitForTimeout(1000);
      
      // 检查是否弹出确认对话框
      const dialog = page.locator('.el-dialog');
      const hasDialog = await dialog.isVisible();
      
      auditReport.testResults.push({
        test: '审批按钮交互',
        status: 'PASS',
        details: `找到${approveCount}个通过按钮，${rejectCount}个拒绝按钮`,
        dialogShown: hasDialog
      });
      
      if (!hasDialog) {
        auditReport.functionalityGaps.push('审批按钮点击后未显示确认对话框');
      }
    } else {
      auditReport.functionalityGaps.push('表格中没有审批操作按钮');
    }
  });

  test('5. 检查用户下拉菜单', async ({ page }) => {
    console.log('🔍 测试用户下拉菜单...');
    
    await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
    await page.waitForLoadState('networkidle');
    
    // 查找用户头像或下拉菜单触发器
    const userDropdown = page.locator('.el-dropdown');
    
    if (await userDropdown.isVisible()) {
      await userDropdown.click();
      await page.waitForTimeout(500);
      
      // 检查下拉菜单项
      const menuItems = ['个人信息', '修改密码', '退出登录'];
      let foundItems = 0;
      
      for (const item of menuItems) {
        const menuItem = page.locator(`text=${item}`);
        if (await menuItem.isVisible()) {
          foundItems++;
        }
      }
      
      auditReport.testResults.push({
        test: '用户下拉菜单',
        status: 'PASS',
        details: `找到${foundItems}个菜单项`
      });
      
      if (foundItems < menuItems.length) {
        auditReport.functionalityGaps.push('用户下拉菜单项不完整');
      }
    } else {
      auditReport.functionalityGaps.push('未找到用户下拉菜单');
    }
  });

  test('6. 检查响应式设计', async ({ page }) => {
    console.log('🔍 测试响应式设计...');
    
    await page.goto(`${STATIC_ADMIN_URL}/admin/dashboard.html`);
    await page.waitForLoadState('networkidle');
    
    // 测试不同屏幕尺寸
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 1366, height: 768, name: '桌面标准' },
      { width: 768, height: 1024, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      const sidebar = page.locator('.sidebar');
      const isVisible = await sidebar.isVisible();
      
      auditReport.testResults.push({
        test: `响应式设计-${viewport.name}`,
        status: 'PASS',
        viewport: `${viewport.width}x${viewport.height}`,
        sidebarVisible: isVisible
      });
    }
  });

  test.afterAll(async () => {
    // 生成审查报告
    console.log('\n📊 生成功能审查报告...');
    
    // 统计结果
    const totalTests = auditReport.testResults.length;
    const passedTests = auditReport.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = auditReport.testResults.filter(r => r.status === 'FAIL').length;
    
    auditReport.summary = {
      totalTests,
      passedTests,
      failedTests,
      successRate: `${((passedTests / totalTests) * 100).toFixed(2)}%`,
      totalGaps: auditReport.functionalityGaps.length
    };
    
    // 基于Context7最佳实践的建议
    auditReport.recommendations = [
      '实现模块化架构设计，将各功能模块独立开发',
      '建立统一的API规范和数据模型',
      '实现完整的CRUD操作功能',
      '添加表单验证和错误处理',
      '实现权限控制和用户管理',
      '添加数据可视化组件',
      '优化响应式设计和用户体验',
      '建立完整的测试覆盖'
    ];
    
    // 保存报告到文件
    const fs = require('fs');
    const reportPath = 'test-results/admin-functionality-audit-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(auditReport, null, 2));
    
    console.log(`📋 审查报告已保存到: ${reportPath}`);
    console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
    console.log(`❌ 失败测试: ${failedTests}/${totalTests}`);
    console.log(`🔍 发现功能缺失: ${auditReport.functionalityGaps.length}项`);
  });
});
