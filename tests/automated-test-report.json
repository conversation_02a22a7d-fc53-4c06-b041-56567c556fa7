{"unitTests": {"passed": 5, "failed": 0, "tests": [{"name": "用户API - 获取用户列表", "status": "PASSED", "message": "测试通过"}, {"name": "生产API - 获取生产记录", "status": "PASSED", "message": "测试通过"}, {"name": "财务API - 获取财务记录", "status": "PASSED", "message": "测试通过"}, {"name": "库存API - 获取库存列表", "status": "PASSED", "message": "测试通过"}, {"name": "报表API - 获取报表数据", "status": "PASSED", "message": "测试通过"}]}, "integrationTests": {"passed": 4, "failed": 0, "tests": [{"name": "用户认证流程测试", "status": "PASSED", "message": "测试通过"}, {"name": "数据流测试 - 生产到财务", "status": "PASSED", "message": "测试通过"}, {"name": "报表数据完整性测试", "status": "PASSED", "message": "测试通过"}, {"name": "页面导航完整性测试", "status": "PASSED", "message": "测试通过"}]}, "e2eTests": {"passed": 3, "failed": 0, "tests": [{"name": "完整用户工作流程测试", "status": "PASSED", "message": "测试通过"}, {"name": "数据管理完整流程测试", "status": "PASSED", "message": "测试通过"}, {"name": "系统健康检查测试", "status": "PASSED", "message": "测试通过"}]}, "summary": {"totalTests": 12, "totalPassed": 12, "totalFailed": 0, "successRate": "100.00%", "unitTests": {"passed": 5, "failed": 0}, "integrationTests": {"passed": 4, "failed": 0}, "e2eTests": {"passed": 3, "failed": 0}}}