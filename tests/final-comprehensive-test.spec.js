const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 最终综合测试
 * 基于实际页面结构的全面功能验证
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  }
};

// 测试结果记录
let testResults = {
  modules: {},
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  issues: [],
  screenshots: []
};

// 登录辅助函数
async function loginToSystem(page) {
  await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
  await page.waitForLoadState('networkidle');
  
  await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
  await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
  await page.click('button[type="submit"]');
  await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  
  return true;
}

// 截图辅助函数
async function takeScreenshot(page, name, description) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    
    await page.screenshot({ 
      path: `test-results/screenshots/${filename}`,
      fullPage: true 
    });
    
    testResults.screenshots.push({
      name: filename,
      description: description,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📸 截图已保存: ${filename}`);
  } catch (error) {
    console.log(`⚠️ 截图失败: ${error.message}`);
  }
}

// 记录测试结果
function recordTestResult(module, testName, passed, details = '') {
  if (!testResults.modules[module]) {
    testResults.modules[module] = { passed: 0, failed: 0, tests: [] };
  }
  
  testResults.modules[module].tests.push({
    name: testName,
    passed: passed,
    details: details
  });
  
  if (passed) {
    testResults.modules[module].passed++;
    testResults.passedTests++;
    console.log(`✅ ${module} - ${testName}: ${details}`);
  } else {
    testResults.modules[module].failed++;
    testResults.failedTests++;
    testResults.issues.push(`${module} - ${testName}: ${details}`);
    console.log(`❌ ${module} - ${testName}: ${details}`);
  }
  
  testResults.totalTests++;
}

test.describe('智慧养鹅后台管理中心 - 最终综合测试', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始智慧养鹅后台管理中心最终综合测试');
    
    // 创建截图目录
    const fs = require('fs');
    const path = require('path');
    const screenshotDir = path.join(process.cwd(), 'test-results', 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  });

  // 1. 健康管理模块最终验证
  test('健康管理模块 - 最终功能验证', async ({ page }) => {
    console.log('\n🏥 开始健康管理模块最终功能验证...');
    
    try {
      await loginToSystem(page);
      await page.goto(`${TEST_CONFIG.baseURL}/health`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      await takeScreenshot(page, 'health-final', '健康管理页面最终截图');
      
      // 1. 验证页面加载成功
      const pageLoaded = await page.locator('body').isVisible();
      recordTestResult('健康管理', '页面加载', pageLoaded, '页面成功加载');
      
      // 2. 验证页面标题（使用更通用的选择器）
      const titleElements = page.locator('h1, h2, h3, .page-title, .content-header h1');
      const titleCount = await titleElements.count();
      recordTestResult('健康管理', '页面标题元素', titleCount > 0, `找到${titleCount}个标题元素`);
      
      // 3. 验证核心功能按钮
      const actionButtons = page.locator('button[data-bs-target*="Modal"], .btn-primary, .btn-success, .btn-warning');
      const buttonCount = await actionButtons.count();
      recordTestResult('健康管理', '功能按钮', buttonCount > 0, `找到${buttonCount}个功能按钮`);
      
      // 4. 验证统计卡片
      const statCards = page.locator('.card, .info-box, .small-box');
      const cardCount = await statCards.count();
      recordTestResult('健康管理', '统计卡片', cardCount > 0, `找到${cardCount}个统计卡片`);
      
      // 5. 验证数据表格
      const dataTable = page.locator('table, .table, .data-table');
      const tableExists = await dataTable.count() > 0;
      recordTestResult('健康管理', '数据表格', tableExists, '数据表格存在');
      
      // 6. 验证筛选功能
      const filterElements = page.locator('select, input[type="date"], .form-select, .form-control');
      const filterCount = await filterElements.count();
      recordTestResult('健康管理', '筛选功能', filterCount > 0, `找到${filterCount}个筛选元素`);
      
      // 7. 测试按钮交互
      if (buttonCount > 0) {
        try {
          await actionButtons.first().click();
          await page.waitForTimeout(1000);
          
          const modal = page.locator('.modal.show, .modal:visible');
          const modalVisible = await modal.count() > 0;
          recordTestResult('健康管理', '按钮交互', modalVisible, '按钮点击后模态框正常显示');
          
          if (modalVisible) {
            await takeScreenshot(page, 'health-modal', '健康管理模态框');
            
            // 关闭模态框
            const closeBtn = page.locator('.modal .btn-close, .modal .close');
            if (await closeBtn.count() > 0) {
              await closeBtn.first().click();
              await page.waitForTimeout(500);
            }
          }
        } catch (clickError) {
          recordTestResult('健康管理', '按钮交互', false, `按钮点击失败: ${clickError.message}`);
        }
      }
      
    } catch (error) {
      recordTestResult('健康管理', '模块测试', false, `测试异常: ${error.message}`);
      await takeScreenshot(page, 'health-error', '健康管理模块错误');
    }
  });

  // 2. 生产管理模块最终验证
  test('生产管理模块 - 最终功能验证', async ({ page }) => {
    console.log('\n🏭 开始生产管理模块最终功能验证...');
    
    try {
      await loginToSystem(page);
      await page.goto(`${TEST_CONFIG.baseURL}/production`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      await takeScreenshot(page, 'production-final', '生产管理页面最终截图');
      
      // 1. 验证页面加载成功
      const pageLoaded = await page.locator('body').isVisible();
      recordTestResult('生产管理', '页面加载', pageLoaded, '页面成功加载');
      
      // 2. 验证页面内容
      const contentElements = page.locator('.container-fluid, .content, .main-content');
      const contentExists = await contentElements.count() > 0;
      recordTestResult('生产管理', '页面内容', contentExists, '页面内容容器存在');
      
      // 3. 验证功能按钮
      const actionButtons = page.locator('button, .btn');
      const buttonCount = await actionButtons.count();
      recordTestResult('生产管理', '功能按钮', buttonCount > 0, `找到${buttonCount}个按钮`);
      
      // 4. 验证统计卡片
      const statCards = page.locator('.card, .info-box, .small-box');
      const cardCount = await statCards.count();
      recordTestResult('生产管理', '统计卡片', cardCount > 0, `找到${cardCount}个统计卡片`);
      
      // 5. 验证数据表格
      const dataTable = page.locator('table, .table');
      const tableExists = await dataTable.count() > 0;
      recordTestResult('生产管理', '数据表格', tableExists, '数据表格存在');
      
      // 6. 测试按钮交互
      const primaryButtons = page.locator('.btn-primary, .btn-info');
      const primaryButtonCount = await primaryButtons.count();
      
      if (primaryButtonCount > 0) {
        try {
          await primaryButtons.first().click();
          await page.waitForTimeout(1000);
          
          const modal = page.locator('.modal.show, .modal:visible');
          const modalVisible = await modal.count() > 0;
          recordTestResult('生产管理', '按钮交互', modalVisible, '按钮点击后模态框正常显示');
          
          if (modalVisible) {
            await takeScreenshot(page, 'production-modal', '生产管理模态框');
            
            // 验证表单字段
            const formFields = page.locator('.modal input, .modal select, .modal textarea');
            const fieldCount = await formFields.count();
            recordTestResult('生产管理', '表单字段', fieldCount > 0, `模态框包含${fieldCount}个表单字段`);
            
            // 关闭模态框
            const closeBtn = page.locator('.modal .btn-close, .modal .close');
            if (await closeBtn.count() > 0) {
              await closeBtn.first().click();
              await page.waitForTimeout(500);
            }
          }
        } catch (clickError) {
          recordTestResult('生产管理', '按钮交互', false, `按钮点击失败: ${clickError.message}`);
        }
      }
      
    } catch (error) {
      recordTestResult('生产管理', '模块测试', false, `测试异常: ${error.message}`);
      await takeScreenshot(page, 'production-error', '生产管理模块错误');
    }
  });

  // 3. 系统整体功能验证
  test('系统整体功能 - 综合验证', async ({ page }) => {
    console.log('\n🔧 开始系统整体功能综合验证...');
    
    try {
      await loginToSystem(page);
      
      // 测试主要页面导航
      const testPages = [
        { url: '/dashboard', name: '仪表板' },
        { url: '/users', name: '用户管理' },
        { url: '/tenants', name: '租户管理' },
        { url: '/settings', name: '系统设置' }
      ];
      
      for (const testPage of testPages) {
        try {
          await page.goto(`${TEST_CONFIG.baseURL}${testPage.url}`);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
          const pageLoaded = await page.locator('body').isVisible();
          recordTestResult('系统导航', `${testPage.name}页面`, pageLoaded, `${testPage.name}页面正常加载`);
          
          await takeScreenshot(page, `system-${testPage.name.toLowerCase()}`, `${testPage.name}页面截图`);
        } catch (navError) {
          recordTestResult('系统导航', `${testPage.name}页面`, false, `导航失败: ${navError.message}`);
        }
      }
      
      // 测试响应式设计
      const viewports = [
        { width: 1920, height: 1080, name: '桌面大屏' },
        { width: 768, height: 1024, name: '平板' },
        { width: 375, height: 667, name: '手机' }
      ];
      
      await page.goto(`${TEST_CONFIG.baseURL}/dashboard`);
      
      for (const viewport of viewports) {
        try {
          await page.setViewportSize({ width: viewport.width, height: viewport.height });
          await page.waitForTimeout(500);
          
          const isVisible = await page.locator('body').isVisible();
          recordTestResult('响应式设计', viewport.name, isVisible, `${viewport.name}显示正常`);
          
          await takeScreenshot(page, `responsive-${viewport.name}`, `${viewport.name}响应式截图`);
        } catch (viewportError) {
          recordTestResult('响应式设计', viewport.name, false, `响应式测试失败: ${viewportError.message}`);
        }
      }
      
    } catch (error) {
      recordTestResult('系统整体', '综合测试', false, `测试异常: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 最终综合测试结果汇总:');
    
    const totalTests = testResults.totalTests;
    const passedTests = testResults.passedTests;
    const failedTests = testResults.failedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过测试: ${passedTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📈 成功率: ${successRate}%`);
    console.log(`📸 截图数量: ${testResults.screenshots.length}`);
    
    console.log('\n=== 各模块详细结果 ===');
    Object.keys(testResults.modules).forEach(module => {
      const moduleResult = testResults.modules[module];
      const moduleTotal = moduleResult.passed + moduleResult.failed;
      const moduleRate = moduleTotal > 0 ? ((moduleResult.passed / moduleTotal) * 100).toFixed(1) : 0;
      console.log(`\n${module}: ${moduleResult.passed}/${moduleTotal} (${moduleRate}%)`);
      
      moduleResult.tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${status} ${test.name}: ${test.details}`);
      });
    });
    
    if (testResults.issues.length > 0) {
      console.log('\n=== 需要关注的问题 ===');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    console.log('\n🎉 智慧养鹅后台管理中心测试完成！');
  });

});
