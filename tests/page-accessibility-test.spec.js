const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 页面可用性测试
 * 专注于测试页面是否可以正常访问，无404/500错误
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  apiURL: 'http://localhost:3000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  }
};

// 测试结果收集
let testResults = {
  accessiblePages: [],
  brokenPages: [],
  issues: []
};

test.describe('页面可用性测试', () => {
  
  // 登录辅助函数
  async function login(page) {
    await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
    await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
    await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  }

  test('1. 登录页面访问测试', async ({ page }) => {
    const response = await page.goto(TEST_CONFIG.baseURL);
    expect(response.status()).toBeLessThan(400);
    
    // 应该重定向到登录页面
    await expect(page).toHaveURL(/.*\/auth\/login/);
    await expect(page.locator('h3')).toContainText('智慧养鹅SAAS');
    
    testResults.accessiblePages.push('/auth/login');
    console.log('✅ 登录页面访问正常');
  });

  test('2. 仪表板页面访问测试', async ({ page }) => {
    await login(page);
    
    const response = await page.goto(`${TEST_CONFIG.baseURL}/dashboard`);
    expect(response.status()).toBeLessThan(400);
    
    // 检查页面是否正常加载
    await expect(page.locator('body')).toBeVisible();
    
    testResults.accessiblePages.push('/dashboard');
    console.log('✅ 仪表板页面访问正常');
  });

  test('3. 用户管理页面访问测试', async ({ page }) => {
    await login(page);
    
    try {
      const response = await page.goto(`${TEST_CONFIG.baseURL}/users`);
      expect(response.status()).toBeLessThan(400);
      
      await expect(page.locator('body')).toBeVisible();
      testResults.accessiblePages.push('/users');
      console.log('✅ 用户管理页面访问正常');
    } catch (error) {
      testResults.brokenPages.push('/users');
      testResults.issues.push(`用户管理页面访问失败: ${error.message}`);
      console.log('⚠️ 用户管理页面可能需要开发');
    }
  });

  test('4. 租户管理页面访问测试', async ({ page }) => {
    await login(page);
    
    try {
      const response = await page.goto(`${TEST_CONFIG.baseURL}/tenants`);
      expect(response.status()).toBeLessThan(400);
      
      await expect(page.locator('body')).toBeVisible();
      testResults.accessiblePages.push('/tenants');
      console.log('✅ 租户管理页面访问正常');
    } catch (error) {
      testResults.brokenPages.push('/tenants');
      testResults.issues.push(`租户管理页面访问失败: ${error.message}`);
      console.log('⚠️ 租户管理页面可能需要开发');
    }
  });

  test('5. 健康管理页面访问测试', async ({ page }) => {
    await login(page);
    
    try {
      const response = await page.goto(`${TEST_CONFIG.baseURL}/health`);
      expect(response.status()).toBeLessThan(400);
      
      await expect(page.locator('body')).toBeVisible();
      testResults.accessiblePages.push('/health');
      console.log('✅ 健康管理页面访问正常');
    } catch (error) {
      testResults.brokenPages.push('/health');
      testResults.issues.push(`健康管理页面访问失败: ${error.message}`);
      console.log('⚠️ 健康管理页面可能需要开发');
    }
  });

  test('6. 生产管理页面访问测试', async ({ page }) => {
    await login(page);
    
    try {
      const response = await page.goto(`${TEST_CONFIG.baseURL}/production`);
      expect(response.status()).toBeLessThan(400);
      
      await expect(page.locator('body')).toBeVisible();
      testResults.accessiblePages.push('/production');
      console.log('✅ 生产管理页面访问正常');
    } catch (error) {
      testResults.brokenPages.push('/production');
      testResults.issues.push(`生产管理页面访问失败: ${error.message}`);
      console.log('⚠️ 生产管理页面可能需要开发');
    }
  });

  test('7. 系统设置页面访问测试', async ({ page }) => {
    await login(page);
    
    try {
      const response = await page.goto(`${TEST_CONFIG.baseURL}/settings`);
      expect(response.status()).toBeLessThan(400);
      
      await expect(page.locator('body')).toBeVisible();
      testResults.accessiblePages.push('/settings');
      console.log('✅ 系统设置页面访问正常');
    } catch (error) {
      testResults.brokenPages.push('/settings');
      testResults.issues.push(`系统设置页面访问失败: ${error.message}`);
      console.log('⚠️ 系统设置页面可能需要开发');
    }
  });

  test('8. API健康检查测试', async ({ page }) => {
    const response = await page.request.get(`${TEST_CONFIG.apiURL}/api/health`);
    expect(response.status()).toBe(200);
    
    const healthData = await response.json();
    expect(healthData.success).toBe(true);
    expect(healthData.service).toBe('Smart Goose API');
    
    console.log('✅ API服务健康检查通过');
  });

  test('9. 仪表板API数据测试', async ({ page }) => {
    await login(page);
    
    try {
      // 测试仪表板统计API
      const response = await page.request.get(`${TEST_CONFIG.baseURL}/api/dashboard/stats`);
      
      if (response.status() === 200) {
        const statsData = await response.json();
        console.log('✅ 仪表板API数据获取成功');
        console.log('📊 统计数据:', JSON.stringify(statsData, null, 2));
      } else {
        console.log(`⚠️ 仪表板API返回状态: ${response.status()}`);
      }
    } catch (error) {
      console.log(`⚠️ 仪表板API测试失败: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 页面可用性测试结果汇总:');
    console.log(`✅ 可访问页面: ${testResults.accessiblePages.length}`);
    console.log(`❌ 问题页面: ${testResults.brokenPages.length}`);
    
    if (testResults.accessiblePages.length > 0) {
      console.log('\n✅ 可访问的页面:');
      testResults.accessiblePages.forEach(page => {
        console.log(`  - ${page}`);
      });
    }
    
    if (testResults.brokenPages.length > 0) {
      console.log('\n❌ 需要修复的页面:');
      testResults.brokenPages.forEach(page => {
        console.log(`  - ${page}`);
      });
    }
    
    if (testResults.issues.length > 0) {
      console.log('\n🐛 发现的问题:');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

});
