{"summary": {"totalTests": 31, "passed": 31, "failed": 0, "successRate": "100.00%"}, "missingPages": [], "brokenFeatures": [], "moduleResults": {"auth": {"name": "认证系统", "pages": {"/auth/login": {"success": true, "status": 200}}, "features": [], "issues": []}, "dashboard": {"name": "仪表板", "pages": {"/dashboard": {"success": true, "status": 200}}, "features": [], "issues": []}, "users": {"name": "用户管理", "pages": {"/users": {"success": true, "status": 200}}, "features": [], "issues": []}, "tenants": {"name": "租户管理", "pages": {"/tenants": {"success": true, "status": 200}, "/tenants/create": {"success": true, "status": 200}, "/tenants/subscriptions": {"success": true, "status": 200}}, "features": [], "issues": []}, "flocks": {"name": "鹅群管理", "pages": {"/flocks": {"success": true, "status": 200}}, "features": [], "issues": []}, "production": {"name": "生产管理", "pages": {"/production": {"success": true, "status": 200}}, "features": [], "issues": []}, "health": {"name": "健康管理", "pages": {"/health": {"success": true, "status": 200}}, "features": [], "issues": []}, "finance": {"name": "财务管理", "pages": {"/finance": {"success": true, "status": 200}}, "features": [], "issues": []}, "inventory": {"name": "库存管理", "pages": {"/inventory": {"success": true, "status": 200}}, "features": [], "issues": []}, "reports": {"name": "报表管理", "pages": {"/reports": {"success": true, "status": 200}}, "features": [], "issues": []}, "system": {"name": "系统管理", "pages": {"/system": {"success": true, "status": 200}, "/system/backup": {"success": true, "status": 200}, "/system/logs": {"success": true, "status": 200}}, "features": [], "issues": []}, "goosePrices": {"name": "鹅价管理", "pages": {"/goose-prices": {"success": true, "status": 200}, "/goose-prices/trends": {"success": true, "status": 200}}, "features": [], "issues": []}, "mall": {"name": "商城管理", "pages": {"/mall": {"success": true, "status": 200}, "/mall/products": {"success": true, "status": 200}, "/mall/orders": {"success": true, "status": 200}, "/mall/categories": {"success": true, "status": 200}}, "features": [], "issues": []}, "knowledge": {"name": "知识库管理", "pages": {"/knowledge": {"success": true, "status": 200}, "/knowledge/categories": {"success": true, "status": 200}, "/knowledge/create": {"success": true, "status": 200}}, "features": [], "issues": []}, "announcements": {"name": "公告管理", "pages": {"/announcements": {"success": true, "status": 200}, "/announcements/create": {"success": true, "status": 200}}, "features": [], "issues": []}, "apiManagement": {"name": "API管理", "pages": {"/api-management": {"success": true, "status": 200}}, "features": [], "issues": []}, "platformUsers": {"name": "平台用户管理", "pages": {"/platform-users": {"success": true, "status": 200}}, "features": [], "issues": []}, "aiConfig": {"name": "AI配置", "pages": {"/ai-config": {"success": true, "status": 200}}, "features": [], "issues": []}, "tenantStats": {"name": "租户统计", "pages": {"/tenant-stats": {"success": true, "status": 200}}, "features": [], "issues": []}}, "errors": []}