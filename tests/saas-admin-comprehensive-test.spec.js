// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅SAAS后台管理系统全面功能测试
 * 基于Context7最佳实践和Tabler UI规范
 */

// 测试数据
const ADMIN_USER = {
  username: 'admin',
  password: 'admin123'
};

const BASE_URL = 'http://localhost:4000';

test.describe('SAAS后台管理系统全面测试', () => {
  
  // 全局设置
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
  });

  test('1. 登录功能测试', async ({ page }) => {
    // 访问首页，应该重定向到登录页
    await expect(page).toHaveURL(/.*\/auth\/login/);
    
    // 检查登录页面元素
    await expect(page.locator('h1, h2, .card-title')).toContainText(['登录', 'Sign in', '智慧养鹅']);
    
    // 输入登录信息
    await page.fill('input[name="username"]', ADMIN_USER.username);
    await page.fill('input[name="password"]', ADMIN_USER.password);
    
    // 点击登录按钮
    await page.click('button[type="submit"], .btn-primary');
    
    // 验证登录成功，重定向到仪表板
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('h1, .page-title, .dashboard-title')).toBeVisible();
  });

  test('2. 仪表板功能测试', async ({ page }) => {
    // 先登录
    await login(page);
    
    // 检查仪表板主要元素
    await expect(page.locator('.page-title, h1')).toContainText(['仪表板', 'Dashboard', '概览']);
    
    // 检查统计卡片
    const statCards = page.locator('.card, .stat-card, .dashboard-card');
    await expect(statCards).toHaveCount(4); // 预期至少4个统计卡片
    
    // 检查图表存在
    await expect(page.locator('canvas, .chart-container, #chart')).toBeVisible();
    
    // 检查导航菜单
    const navLinks = page.locator('.nav-link, .sidebar-item');
    await expect(navLinks).toHaveCountGreaterThan(5);
  });

  test('3. 租户管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到租户管理页面
    await page.click('text="租户管理", text="Tenants", a[href*="tenants"]');
    await expect(page).toHaveURL(/.*\/tenants/);
    
    // 检查页面标题
    await expect(page.locator('h1, .page-title')).toContainText(['租户', 'Tenant']);
    
    // 检查租户列表表格
    await expect(page.locator('table, .table')).toBeVisible();
    
    // 检查添加租户按钮
    await expect(page.locator('text="添加租户", text="Add Tenant", .btn-primary')).toBeVisible();
    
    // 测试租户创建
    await page.click('text="添加租户", text="Add Tenant", .btn-primary');
    await expect(page).toHaveURL(/.*\/tenants\/create/);
    
    // 填写租户信息（如果表单存在）
    const nameInput = page.locator('input[name="name"], input[name="tenant_name"]');
    if (await nameInput.count() > 0) {
      await nameInput.fill('测试租户');
      
      const emailInput = page.locator('input[name="email"], input[name="contact_email"]');
      if (await emailInput.count() > 0) {
        await emailInput.fill('<EMAIL>');
      }
      
      // 提交表单
      await page.click('button[type="submit"], .btn-primary');
    }
  });

  test('4. 用户管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到用户管理
    await page.click('text="用户管理", text="Users", a[href*="users"]');
    await expect(page).toHaveURL(/.*\/users/);
    
    // 检查用户列表
    await expect(page.locator('table, .table, .user-list')).toBeVisible();
    
    // 检查搜索功能
    const searchInput = page.locator('input[type="search"], input[name="search"], .search-input');
    if (await searchInput.count() > 0) {
      await searchInput.fill('admin');
    }
    
    // 检查分页控件
    const pagination = page.locator('.pagination, .page-numbers');
    if (await pagination.count() > 0) {
      await expect(pagination).toBeVisible();
    }
  });

  test('5. 鹅群管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到鹅群管理
    await page.click('text="鹅群管理", text="Flocks", a[href*="flocks"]');
    await expect(page).toHaveURL(/.*\/flocks/);
    
    // 检查页面基本元素
    await expect(page.locator('h1, .page-title')).toContainText(['鹅群', 'Flock']);
    
    // 检查统计信息
    const statsCards = page.locator('.stat-card, .card, .flock-stats');
    await expect(statsCards.first()).toBeVisible();
    
    // 检查鹅群列表
    await expect(page.locator('table, .table, .flock-list')).toBeVisible();
  });

  test('6. 生产记录功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到生产记录
    await page.click('text="生产记录", text="Production", a[href*="production"]');
    await expect(page).toHaveURL(/.*\/production/);
    
    // 检查页面标题
    await expect(page.locator('h1, .page-title')).toContainText(['生产', 'Production']);
    
    // 检查记录表格
    await expect(page.locator('table, .table, .production-table')).toBeVisible();
    
    // 检查添加记录按钮
    const addButton = page.locator('text="添加记录", text="Add Record", .btn-primary');
    if (await addButton.count() > 0) {
      await expect(addButton).toBeVisible();
    }
  });

  test('7. 健康管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到健康管理
    await page.click('text="健康管理", text="Health", a[href*="health"]');
    await expect(page).toHaveURL(/.*\/health/);
    
    // 检查健康统计
    const healthStats = page.locator('.health-stats, .card, .stat-card');
    await expect(healthStats.first()).toBeVisible();
    
    // 检查健康记录列表
    await expect(page.locator('table, .table, .health-records')).toBeVisible();
  });

  test('8. 财务管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到财务管理
    await page.click('text="财务管理", text="Finance", a[href*="finance"]');
    await expect(page).toHaveURL(/.*\/finance/);
    
    // 检查财务概览
    await expect(page.locator('h1, .page-title')).toContainText(['财务', 'Finance']);
    
    // 检查财务统计卡片
    const financeCards = page.locator('.finance-card, .card, .stat-card');
    await expect(financeCards.first()).toBeVisible();
    
    // 检查财务图表
    const charts = page.locator('canvas, .chart-container, .finance-chart');
    if (await charts.count() > 0) {
      await expect(charts.first()).toBeVisible();
    }
  });

  test('9. 库存管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到库存管理
    await page.click('text="库存管理", text="Inventory", a[href*="inventory"]');
    await expect(page).toHaveURL(/.*\/inventory/);
    
    // 检查库存统计
    const inventoryStats = page.locator('.inventory-stats, .card, .stat-card');
    await expect(inventoryStats.first()).toBeVisible();
    
    // 检查库存列表
    await expect(page.locator('table, .table, .inventory-table')).toBeVisible();
  });

  test('10. 商城管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到商城管理
    await page.click('text="商城管理", text="Mall", a[href*="mall"]');
    await expect(page).toHaveURL(/.*\/mall/);
    
    // 检查商城概览
    await expect(page.locator('h1, .page-title')).toContainText(['商城', 'Mall']);
    
    // 测试各个商城子功能
    const mallSubMenus = ['products', 'orders', 'categories', 'inventory'];
    
    for (const menu of mallSubMenus) {
      const menuLink = page.locator(`a[href*="${menu}"], text="${menu}"`);
      if (await menuLink.count() > 0) {
        await menuLink.click();
        await page.waitForLoadState('networkidle');
        // 检查页面加载成功
        await expect(page.locator('body')).toBeVisible();
      }
    }
  });

  test('11. 鹅价管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到鹅价管理
    await page.click('text="鹅价管理", text="Goose Prices", a[href*="goose-prices"]');
    await expect(page).toHaveURL(/.*\/goose-prices/);
    
    // 检查价格列表
    await expect(page.locator('table, .table, .price-table')).toBeVisible();
    
    // 测试价格趋势页面
    const trendsLink = page.locator('text="价格趋势", text="Trends", a[href*="trends"]');
    if (await trendsLink.count() > 0) {
      await trendsLink.click();
      await expect(page).toHaveURL(/.*\/trends/);
      
      // 检查图表
      const chartCanvas = page.locator('canvas, .chart-container');
      if (await chartCanvas.count() > 0) {
        await expect(chartCanvas.first()).toBeVisible();
      }
    }
  });

  test('12. 系统设置功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到系统设置
    await page.click('text="系统设置", text="System", a[href*="system"]');
    await expect(page).toHaveURL(/.*\/system/);
    
    // 检查设置页面
    await expect(page.locator('h1, .page-title')).toContainText(['系统', 'System']);
    
    // 检查设置表单或选项
    const settingsForms = page.locator('form, .settings-form, .system-config');
    if (await settingsForms.count() > 0) {
      await expect(settingsForms.first()).toBeVisible();
    }
  });

  test('13. 公告管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到公告管理
    await page.click('text="公告管理", text="Announcements", a[href*="announcements"]');
    await expect(page).toHaveURL(/.*\/announcements/);
    
    // 检查公告列表
    await expect(page.locator('table, .table, .announcements-table')).toBeVisible();
    
    // 检查添加公告按钮
    const addButton = page.locator('text="添加公告", text="Add Announcement", .btn-primary');
    if (await addButton.count() > 0) {
      await expect(addButton).toBeVisible();
    }
  });

  test('14. 知识库管理功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到知识库管理
    await page.click('text="知识库", text="Knowledge", a[href*="knowledge"]');
    await expect(page).toHaveURL(/.*\/knowledge/);
    
    // 检查知识库内容
    await expect(page.locator('h1, .page-title')).toContainText(['知识', 'Knowledge']);
    
    // 检查知识库列表
    const knowledgeList = page.locator('table, .table, .knowledge-list, .article-list');
    if (await knowledgeList.count() > 0) {
      await expect(knowledgeList).toBeVisible();
    }
  });

  test('15. 报表功能测试', async ({ page }) => {
    await login(page);
    
    // 导航到报表页面
    await page.click('text="报表", text="Reports", a[href*="reports"]');
    await expect(page).toHaveURL(/.*\/reports/);
    
    // 检查报表页面
    await expect(page.locator('h1, .page-title')).toContainText(['报表', 'Report']);
    
    // 检查报表选项
    const reportOptions = page.locator('.report-option, .report-card, .card');
    if (await reportOptions.count() > 0) {
      await expect(reportOptions.first()).toBeVisible();
    }
    
    // 检查图表
    const charts = page.locator('canvas, .chart-container');
    if (await charts.count() > 0) {
      await expect(charts.first()).toBeVisible();
    }
  });

  test('16. 响应式设计测试', async ({ page }) => {
    await login(page);
    
    // 桌面视口
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.sidebar, .nav-sidebar')).toBeVisible();
    
    // 平板视口
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    // 手机视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // 检查移动端菜单按钮
    const mobileMenuBtn = page.locator('.navbar-toggler, .mobile-menu-btn, .sidebar-toggle');
    if (await mobileMenuBtn.count() > 0) {
      await expect(mobileMenuBtn).toBeVisible();
    }
  });

  test('17. 搜索和筛选功能测试', async ({ page }) => {
    await login(page);
    
    // 在各个页面测试搜索功能
    const pagesWithSearch = ['users', 'tenants', 'flocks', 'production'];
    
    for (const pageName of pagesWithSearch) {
      await page.goto(`${BASE_URL}/${pageName}`);
      await page.waitForLoadState('networkidle');
      
      const searchInput = page.locator('input[type="search"], input[name="search"], .search-input');
      if (await searchInput.count() > 0) {
        await searchInput.fill('test');
        await page.waitForTimeout(1000);
        
        // 检查搜索结果
        await expect(page.locator('table, .table, .search-results')).toBeVisible();
      }
    }
  });

  test('18. 数据导出功能测试', async ({ page }) => {
    await login(page);
    
    // 测试可能存在导出功能的页面
    const pagesWithExport = ['reports', 'users', 'tenants', 'production'];
    
    for (const pageName of pagesWithExport) {
      await page.goto(`${BASE_URL}/${pageName}`);
      await page.waitForLoadState('networkidle');
      
      const exportBtn = page.locator('text="导出", text="Export", .btn-export, button[title*="导出"]');
      if (await exportBtn.count() > 0) {
        // 点击导出按钮但不下载文件
        await exportBtn.click();
        await page.waitForTimeout(1000);
      }
    }
  });

  test('19. 权限和安全测试', async ({ page }) => {
    await login(page);
    
    // 检查管理员权限页面访问
    const adminPages = [
      '/system',
      '/platform-users',
      '/api-management'
    ];
    
    for (const adminPage of adminPages) {
      await page.goto(`${BASE_URL}${adminPage}`);
      await page.waitForLoadState('networkidle');
      
      // 检查页面是否可访问（不是403或404）
      const pageContent = page.locator('body');
      await expect(pageContent).toBeVisible();
    }
    
    // 测试登出功能
    const logoutBtn = page.locator('text="退出", text="Logout", .logout-btn, a[href*="logout"]');
    if (await logoutBtn.count() > 0) {
      await logoutBtn.click();
      await expect(page).toHaveURL(/.*\/auth\/login/);
    }
  });

  test('20. 性能和可用性测试', async ({ page }) => {
    await login(page);
    
    // 测试页面加载性能
    const startTime = Date.now();
    await page.goto(`${BASE_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // 页面加载时间应该少于5秒
    expect(loadTime).toBeLessThan(5000);
    
    // 检查是否有JavaScript错误
    const errors = [];
    page.on('pageerror', error => errors.push(error));
    
    // 导航到各个主要页面
    const mainPages = ['users', 'tenants', 'flocks', 'production', 'health', 'finance'];
    
    for (const pageName of mainPages) {
      await page.goto(`${BASE_URL}/${pageName}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
    }
    
    // 验证没有JavaScript错误
    expect(errors.length).toBe(0);
  });
});

// 辅助函数
async function login(page) {
  await page.goto(`${BASE_URL}/auth/login`);
  await page.fill('input[name="username"]', ADMIN_USER.username);
  await page.fill('input[name="password"]', ADMIN_USER.password);
  await page.click('button[type="submit"], .btn-primary');
  await page.waitForURL(/.*\/dashboard/);
}