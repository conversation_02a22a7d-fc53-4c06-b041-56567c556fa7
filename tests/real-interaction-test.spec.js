const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 真实交互测试
 * 真正测试每个菜单模块和按钮的功能
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  },
  timeout: 30000
};

// 测试结果记录
let testResults = {
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  menuTests: [],
  buttonTests: [],
  issues: []
};

// 登录辅助函数
async function performLogin(page) {
  console.log('🔐 执行登录操作...');
  
  // 导航到登录页面
  await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
  await page.waitForLoadState('networkidle');
  
  // 等待登录表单加载
  await page.waitForSelector('input[name="username"]', { timeout: 10000 });
  await page.waitForSelector('input[name="password"]', { timeout: 10000 });
  await page.waitForSelector('button[type="submit"]', { timeout: 10000 });
  
  // 填写登录信息
  await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
  await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
  
  // 点击登录按钮
  await page.click('button[type="submit"]');
  
  // 等待登录成功并跳转到仪表板
  await page.waitForURL(/.*\/dashboard/, { timeout: 15000 });
  
  console.log('✅ 登录成功');
  return true;
}

// 截图辅助函数
async function takeScreenshot(page, name) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    
    await page.screenshot({ 
      path: `test-results/screenshots/${filename}`,
      fullPage: true 
    });
    
    console.log(`📸 截图保存: ${filename}`);
    return filename;
  } catch (error) {
    console.log(`⚠️ 截图失败: ${error.message}`);
    return null;
  }
}

// 记录测试结果
function recordTest(category, name, passed, details = '', screenshot = null) {
  const result = {
    category,
    name,
    passed,
    details,
    screenshot,
    timestamp: new Date().toISOString()
  };
  
  if (category === 'menu') {
    testResults.menuTests.push(result);
  } else if (category === 'button') {
    testResults.buttonTests.push(result);
  }
  
  testResults.totalTests++;
  if (passed) {
    testResults.passedTests++;
    console.log(`✅ ${category} - ${name}: ${details}`);
  } else {
    testResults.failedTests++;
    testResults.issues.push(`${category} - ${name}: ${details}`);
    console.log(`❌ ${category} - ${name}: ${details}`);
  }
}

test.describe('智慧养鹅后台管理中心 - 真实交互测试', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始真实交互测试');
    
    // 创建截图目录
    const fs = require('fs');
    const path = require('path');
    const screenshotDir = path.join(process.cwd(), 'test-results', 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  });

  // 1. 登录功能测试
  test('登录功能真实测试', async ({ page }) => {
    console.log('\n🔐 开始登录功能真实测试...');
    
    try {
      // 访问首页，应该重定向到登录页面
      await page.goto(TEST_CONFIG.baseURL);
      await page.waitForLoadState('networkidle');
      
      // 验证是否重定向到登录页面
      const currentURL = page.url();
      const isLoginPage = currentURL.includes('/auth/login');
      recordTest('system', '登录页面重定向', isLoginPage, `当前URL: ${currentURL}`);
      
      if (isLoginPage) {
        await takeScreenshot(page, 'login-page');
        
        // 执行登录
        await performLogin(page);
        
        // 验证登录后的页面
        const dashboardURL = page.url();
        const isDashboard = dashboardURL.includes('/dashboard');
        recordTest('system', '登录成功跳转', isDashboard, `登录后URL: ${dashboardURL}`);
        
        if (isDashboard) {
          await takeScreenshot(page, 'dashboard-after-login');
        }
      }
      
    } catch (error) {
      recordTest('system', '登录功能', false, `登录测试失败: ${error.message}`);
    }
  });

  // 2. 主导航菜单测试
  test('主导航菜单真实测试', async ({ page }) => {
    console.log('\n📋 开始主导航菜单真实测试...');
    
    try {
      await performLogin(page);
      await page.waitForTimeout(2000); // 等待页面完全加载
      
      // 定义要测试的菜单项
      const menuItems = [
        { name: '仪表板', url: '/dashboard', selector: 'a[href="/dashboard"], a:has-text("仪表板"), a:has-text("首页")' },
        { name: '用户管理', url: '/users', selector: 'a[href="/users"], a:has-text("用户管理")' },
        { name: '租户管理', url: '/tenants', selector: 'a[href="/tenants"], a:has-text("租户管理")' },
        { name: '鹅群管理', url: '/flocks', selector: 'a[href="/flocks"], a:has-text("鹅群管理")' },
        { name: '健康管理', url: '/health', selector: 'a[href="/health"], a:has-text("健康管理")' },
        { name: '生产管理', url: '/production', selector: 'a[href="/production"], a:has-text("生产管理")' },
        { name: '财务管理', url: '/finance', selector: 'a[href="/finance"], a:has-text("财务管理")' },
        { name: '系统设置', url: '/system', selector: 'a[href="/system"], a:has-text("系统设置"), a[href="/settings"]' }
      ];
      
      for (const menuItem of menuItems) {
        try {
          console.log(`🔍 测试菜单项: ${menuItem.name}`);
          
          // 查找菜单链接
          const menuLink = page.locator(menuItem.selector).first();
          const linkExists = await menuLink.count() > 0;
          
          if (linkExists) {
            // 点击菜单链接
            await menuLink.click();
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(1000);
            
            // 验证页面是否正确跳转
            const currentURL = page.url();
            const correctNavigation = currentURL.includes(menuItem.url);
            
            recordTest('menu', menuItem.name, correctNavigation, 
              `菜单链接存在且可点击，跳转到: ${currentURL}`);
            
            if (correctNavigation) {
              await takeScreenshot(page, `menu-${menuItem.name.toLowerCase()}`);
              
              // 检查页面是否有内容加载
              const pageContent = page.locator('body');
              const hasContent = await pageContent.isVisible();
              
              if (hasContent) {
                console.log(`✅ ${menuItem.name} 页面内容正常加载`);
              }
            }
            
          } else {
            recordTest('menu', menuItem.name, false, '菜单链接不存在');
          }
          
        } catch (menuError) {
          recordTest('menu', menuItem.name, false, `菜单测试失败: ${menuError.message}`);
        }
      }
      
    } catch (error) {
      recordTest('menu', '导航菜单', false, `导航菜单测试失败: ${error.message}`);
    }
  });

  // 3. 用户管理页面按钮测试
  test('用户管理页面按钮真实测试', async ({ page }) => {
    console.log('\n👥 开始用户管理页面按钮真实测试...');
    
    try {
      await performLogin(page);
      
      // 导航到用户管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      await takeScreenshot(page, 'users-page-buttons');
      
      // 测试搜索功能
      const searchInput = page.locator('input[type="search"], input[placeholder*="搜索"], .search-input').first();
      const searchExists = await searchInput.count() > 0;
      
      if (searchExists) {
        await searchInput.fill('admin');
        await page.waitForTimeout(1000);
        recordTest('button', '用户搜索输入', true, '搜索输入框存在且可输入');
      } else {
        recordTest('button', '用户搜索输入', false, '搜索输入框不存在');
      }
      
      // 测试添加用户按钮
      const addButtons = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-primary');
      const addButtonCount = await addButtons.count();
      
      if (addButtonCount > 0) {
        try {
          await addButtons.first().click();
          await page.waitForTimeout(1500);
          
          // 检查是否打开了模态框或跳转到新页面
          const modal = page.locator('.modal.show, .modal:visible');
          const modalVisible = await modal.count() > 0;
          const currentURL = page.url();
          const isCreatePage = currentURL.includes('create') || currentURL.includes('add');
          
          if (modalVisible || isCreatePage) {
            recordTest('button', '添加用户按钮', true, '添加用户按钮点击成功，打开了模态框或跳转页面');
            await takeScreenshot(page, 'users-add-interface');
            
            // 如果是模态框，尝试关闭
            if (modalVisible) {
              const closeBtn = page.locator('.modal .btn-close, .modal .close, button:has-text("取消")').first();
              if (await closeBtn.count() > 0) {
                await closeBtn.click();
                await page.waitForTimeout(500);
              }
            }
          } else {
            recordTest('button', '添加用户按钮', false, '添加用户按钮点击后没有响应');
          }
          
        } catch (clickError) {
          recordTest('button', '添加用户按钮', false, `添加用户按钮点击失败: ${clickError.message}`);
        }
      } else {
        recordTest('button', '添加用户按钮', false, '添加用户按钮不存在');
      }
      
      // 测试操作按钮（编辑、删除等）
      const actionButtons = page.locator('button:has-text("编辑"), button:has-text("删除"), .btn-edit, .btn-delete, .action-btn');
      const actionCount = await actionButtons.count();
      
      recordTest('button', '用户操作按钮', actionCount > 0, `找到 ${actionCount} 个操作按钮`);
      
    } catch (error) {
      recordTest('button', '用户管理按钮', false, `用户管理按钮测试失败: ${error.message}`);
    }
  });

  // 4. 健康管理页面按钮测试
  test('健康管理页面按钮真实测试', async ({ page }) => {
    console.log('\n🏥 开始健康管理页面按钮真实测试...');
    
    try {
      await performLogin(page);
      
      // 导航到健康管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/health`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // 等待页面完全加载
      
      await takeScreenshot(page, 'health-page-buttons');
      
      // 测试健康管理的主要按钮
      const healthButtons = [
        { name: '健康检查', selector: 'button[data-bs-target="#inspectionModal"], button:has-text("健康检查")' },
        { name: '疫苗接种', selector: 'button[data-bs-target="#vaccinationModal"], button:has-text("疫苗接种")' },
        { name: '疾病治疗', selector: 'button[data-bs-target="#treatmentModal"], button:has-text("疾病治疗")' }
      ];
      
      for (const button of healthButtons) {
        try {
          const buttonElement = page.locator(button.selector).first();
          const buttonExists = await buttonElement.count() > 0;
          
          if (buttonExists) {
            // 点击按钮
            await buttonElement.click();
            await page.waitForTimeout(1500);
            
            // 检查模态框是否打开
            const modal = page.locator('.modal.show, .modal:visible');
            const modalVisible = await modal.count() > 0;
            
            recordTest('button', button.name, modalVisible, 
              modalVisible ? '按钮点击成功，模态框正常打开' : '按钮点击后模态框未打开');
            
            if (modalVisible) {
              await takeScreenshot(page, `health-${button.name.toLowerCase()}-modal`);
              
              // 关闭模态框
              const closeBtn = page.locator('.modal .btn-close, .modal .close').first();
              if (await closeBtn.count() > 0) {
                await closeBtn.click();
                await page.waitForTimeout(500);
              }
            }
          } else {
            recordTest('button', button.name, false, '按钮不存在');
          }
          
        } catch (buttonError) {
          recordTest('button', button.name, false, `按钮测试失败: ${buttonError.message}`);
        }
      }
      
    } catch (error) {
      recordTest('button', '健康管理按钮', false, `健康管理按钮测试失败: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 真实交互测试结果汇总:');
    
    const totalTests = testResults.totalTests;
    const passedTests = testResults.passedTests;
    const failedTests = testResults.failedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过测试: ${passedTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    console.log('\n=== 菜单测试结果 ===');
    testResults.menuTests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });
    
    console.log('\n=== 按钮测试结果 ===');
    testResults.buttonTests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });
    
    if (testResults.issues.length > 0) {
      console.log('\n=== 发现的问题 ===');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

});
