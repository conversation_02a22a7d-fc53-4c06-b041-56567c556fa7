/**
 * 审批功能测试
 * 专门测试审批操作和审批历史功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4001';
const API_BASE = `${BASE_URL}/api/admin`;

async function testApprovalFunctions() {
  console.log('🚀 开始审批功能测试...\n');
  
  try {
    // 1. 获取待审批列表
    console.log('📋 获取待审批列表...');
    const pendingResponse = await axios.get(`${API_BASE}/pending`);
    console.log(`✅ 找到 ${pendingResponse.data.data.total} 个待审批项目`);
    
    if (pendingResponse.data.data.list.length > 0) {
      const firstPending = pendingResponse.data.data.list[0];
      console.log(`   - 待审批项目: ${firstPending.farm_name || firstPending.real_name} (ID: ${firstPending.approval_id})`);
      
      // 2. 测试审批通过
      console.log('\n✅ 测试审批通过...');
      const approveResponse = await axios.post(`${API_BASE}/approve/${firstPending.approval_id}`, {
        action: 'approve',
        application_type: firstPending.application_type,
        comment: '自动化测试审批通过'
      });
      
      if (approveResponse.data.success) {
        console.log('✅ 审批通过操作成功');
      } else {
        console.log('❌ 审批通过操作失败');
      }
    }
    
    // 3. 创建一个新的待审批项目用于拒绝测试
    console.log('\n📝 创建新的待审批项目...');
    const createResponse = await axios.post(`${API_BASE}/tenants`, {
      name: '测试拒绝农场',
      tenant_code: 'test_reject_farm',
      contact_name: '测试拒绝联系人',
      contact_phone: '13900139999',
      contact_email: '<EMAIL>',
      subscription_plan: 'basic'
    });
    
    if (createResponse.data.success) {
      const newTenantId = createResponse.data.data.id;
      console.log(`✅ 创建成功，ID: ${newTenantId}`);
      
      // 设置为待审批状态
      await axios.put(`${API_BASE}/tenants/${newTenantId}`, {
        name: '测试拒绝农场',
        contact_name: '测试拒绝联系人',
        contact_phone: '13900139999',
        contact_email: '<EMAIL>',
        subscription_plan: 'basic',
        status: 'pending'
      });
      
      // 4. 测试审批拒绝
      console.log('\n❌ 测试审批拒绝...');
      const rejectResponse = await axios.post(`${API_BASE}/approve/${newTenantId}`, {
        action: 'reject',
        application_type: 'tenant',
        comment: '自动化测试审批拒绝'
      });
      
      if (rejectResponse.data.success) {
        console.log('✅ 审批拒绝操作成功');
      } else {
        console.log('❌ 审批拒绝操作失败');
      }
      
      // 清理测试数据
      await axios.delete(`${API_BASE}/tenants/${newTenantId}`);
    }
    
    // 5. 查看审批历史
    console.log('\n📚 查看审批历史...');
    const historyResponse = await axios.get(`${API_BASE}/approval-history`);
    console.log(`✅ 查询到 ${historyResponse.data.data.list.length} 条审批记录`);
    
    historyResponse.data.data.list.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.application_type} - ${record.action} - ${record.comment || '无备注'}`);
    });
    
    console.log('\n🎉 审批功能测试完成！');
    
  } catch (error) {
    console.error('❌ 审批功能测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testApprovalFunctions().catch(console.error);
}

module.exports = { testApprovalFunctions };
