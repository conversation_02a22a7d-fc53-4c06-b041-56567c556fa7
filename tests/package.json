{"name": "saas-admin-tests", "version": "1.0.0", "description": "智慧养鹅SAAS管理后台自动化测试套件", "main": "automated-test-suite.js", "scripts": {"test": "node automated-test-suite.js", "test:unit": "node automated-test-suite.js --unit", "test:integration": "node automated-test-suite.js --integration", "test:e2e": "node automated-test-suite.js --e2e", "test:api": "node api-endpoints-verification.js", "test:basic": "node admin-backend-comprehensive-test.js", "test:interactive": "node interactive-elements-test.js", "test:cleanup": "node code-cleanup-analyzer.js", "test:all": "./run-tests.sh", "report": "node comprehensive-test-report.js", "start:server": "cd ../backend/saas-admin && PORT=3003 npm start", "health:check": "curl -s http://localhost:3003/api/health || echo 'Server not running'"}, "keywords": ["testing", "automation", "saas", "admin", "backend", "api", "e2e", "integration", "unit"], "author": "智慧养鹅团队", "license": "MIT", "dependencies": {"axios": "^1.6.0"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "playwright": "^1.40.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "智慧养鹅全栈项目"}, "bugs": {"url": "项目问题追踪"}, "homepage": "项目主页"}