/**
 * 智能重复文件检测工具
 * 基于文件内容而不是文件名检测真正的重复文件
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SmartDuplicateDetector {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.adminPath = path.join(this.projectRoot, 'backend/saas-admin');
        this.results = {
            trueDuplicates: [],
            similarFiles: [],
            uniqueFiles: [],
            analysis: {}
        };
    }

    // 计算文件的MD5哈希值
    calculateFileHash(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            return crypto.createHash('md5').update(content).digest('hex');
        } catch (error) {
            console.warn(`无法读取文件: ${filePath}`);
            return null;
        }
    }

    // 计算文件内容相似度（简化版）
    calculateSimilarity(content1, content2) {
        // 移除空白字符和注释进行比较
        const normalize = (content) => {
            return content
                .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
                .replace(/\/\/.*$/gm, '') // 移除行注释
                .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
                .replace(/\s+/g, ' ') // 标准化空白字符
                .trim()
                .toLowerCase();
        };
        
        const norm1 = normalize(content1);
        const norm2 = normalize(content2);
        
        if (norm1 === norm2) return 100; // 完全相同
        
        // 简单的相似度计算
        const longer = norm1.length > norm2.length ? norm1 : norm2;
        const shorter = norm1.length > norm2.length ? norm2 : norm1;
        
        if (longer.length === 0) return 100;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return ((longer.length - editDistance) / longer.length) * 100;
    }

    // 计算编辑距离（简化版，仅用于小文件）
    levenshteinDistance(str1, str2) {
        // 对于大文件，使用采样比较
        if (str1.length > 1000 || str2.length > 1000) {
            const sample1 = str1.substring(0, 500) + str1.substring(str1.length - 500);
            const sample2 = str2.substring(0, 500) + str2.substring(str2.length - 500);
            return this.levenshteinDistance(sample1, sample2);
        }
        
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    getAllFiles(dir, extensions = []) {
        const files = [];
        
        if (!fs.existsSync(dir)) {
            return files;
        }

        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                files.push(...this.getAllFiles(fullPath, extensions));
            } else if (stat.isFile()) {
                const ext = path.extname(item).toLowerCase();
                if (extensions.length === 0 || extensions.includes(ext)) {
                    files.push({
                        path: fullPath,
                        relativePath: path.relative(this.projectRoot, fullPath),
                        name: item,
                        size: stat.size,
                        extension: ext
                    });
                }
            }
        }
        
        return files;
    }

    // 检测真正的重复文件
    async detectTrueDuplicates() {
        console.log('🔍 检测真正的重复文件...');
        
        const allFiles = this.getAllFiles(this.adminPath, ['.js', '.ejs', '.html', '.css']);
        const hashGroups = {};
        const contentCache = {};
        
        // 按哈希值分组
        allFiles.forEach(file => {
            const hash = this.calculateFileHash(file.path);
            if (hash) {
                if (!hashGroups[hash]) {
                    hashGroups[hash] = [];
                }
                hashGroups[hash].push(file);
                contentCache[file.path] = fs.readFileSync(file.path, 'utf8');
            }
        });
        
        // 找出真正的重复文件（完全相同的内容）
        Object.entries(hashGroups).forEach(([hash, files]) => {
            if (files.length > 1) {
                // 验证这些文件是否真的应该被认为是重复的
                const firstFile = files[0];
                const shouldMerge = this.shouldMergeFiles(files, contentCache);
                
                if (shouldMerge.canMerge) {
                    this.results.trueDuplicates.push({
                        hash,
                        files: files.map(f => f.relativePath),
                        reason: shouldMerge.reason,
                        recommendation: shouldMerge.recommendation
                    });
                } else {
                    // 虽然内容相同，但不建议合并
                    this.results.similarFiles.push({
                        hash,
                        files: files.map(f => f.relativePath),
                        reason: shouldMerge.reason,
                        similarity: 100
                    });
                }
            }
        });
        
        // 检测高度相似的文件
        await this.detectSimilarFiles(allFiles, contentCache);
        
        console.log(`找到 ${this.results.trueDuplicates.length} 组真正的重复文件`);
        console.log(`找到 ${this.results.similarFiles.length} 组相似文件`);
    }

    // 判断文件是否应该合并
    shouldMergeFiles(files, contentCache) {
        const firstFile = files[0];
        
        // 检查文件类型和用途
        const isSystemFile = files.some(f => 
            f.relativePath.includes('middleware/') ||
            f.relativePath.includes('routes/') ||
            f.relativePath.includes('config/')
        );
        
        const isTemplateFile = files.some(f => f.extension === '.ejs' || f.extension === '.html');
        
        if (isSystemFile) {
            return {
                canMerge: false,
                reason: '系统文件，功能不同，不应合并',
                recommendation: '保持现状'
            };
        }
        
        if (isTemplateFile) {
            // 检查是否是不同模块的模板
            const modules = files.map(f => {
                const parts = f.relativePath.split('/');
                return parts[parts.length - 2]; // 获取父目录名
            });
            
            const uniqueModules = [...new Set(modules)];
            if (uniqueModules.length > 1) {
                return {
                    canMerge: false,
                    reason: '不同模块的模板文件，虽然内容相同但用途不同',
                    recommendation: '考虑创建通用模板组件'
                };
            }
        }
        
        // 检查文件大小
        if (firstFile.size < 100) {
            return {
                canMerge: false,
                reason: '文件太小，合并意义不大',
                recommendation: '保持现状'
            };
        }
        
        return {
            canMerge: true,
            reason: '真正的重复文件，可以安全合并',
            recommendation: '删除重复文件，保留一个'
        };
    }

    // 检测相似文件
    async detectSimilarFiles(allFiles, contentCache) {
        console.log('🔍 检测相似文件...');
        
        const similarityThreshold = 80; // 80%以上相似度
        const processed = new Set();
        
        for (let i = 0; i < allFiles.length; i++) {
            for (let j = i + 1; j < allFiles.length; j++) {
                const file1 = allFiles[i];
                const file2 = allFiles[j];
                
                // 跳过已处理的文件对
                const pair = `${file1.path}:${file2.path}`;
                if (processed.has(pair)) continue;
                processed.add(pair);
                
                // 只比较相同类型的文件
                if (file1.extension !== file2.extension) continue;
                
                // 跳过大小差异太大的文件
                const sizeDiff = Math.abs(file1.size - file2.size) / Math.max(file1.size, file2.size);
                if (sizeDiff > 0.5) continue;
                
                const content1 = contentCache[file1.path];
                const content2 = contentCache[file2.path];
                
                if (content1 && content2) {
                    const similarity = this.calculateSimilarity(content1, content2);
                    
                    if (similarity >= similarityThreshold && similarity < 100) {
                        this.results.similarFiles.push({
                            files: [file1.relativePath, file2.relativePath],
                            similarity: Math.round(similarity),
                            reason: `${similarity.toFixed(1)}% 相似度`,
                            recommendation: similarity > 90 ? '考虑重构为通用组件' : '检查是否可以复用代码'
                        });
                    }
                }
            }
        }
    }

    // 生成报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                trueDuplicates: this.results.trueDuplicates.length,
                similarFiles: this.results.similarFiles.length,
                totalAnalyzed: this.results.trueDuplicates.length + this.results.similarFiles.length
            },
            trueDuplicates: this.results.trueDuplicates,
            similarFiles: this.results.similarFiles
        };
        
        return report;
    }

    // 打印报告
    printReport() {
        const report = this.generateReport();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 智能重复文件检测结果');
        console.log('='.repeat(60));
        console.log(`真正的重复文件组: ${report.summary.trueDuplicates}`);
        console.log(`相似文件组: ${report.summary.similarFiles}`);
        
        if (report.trueDuplicates.length > 0) {
            console.log('\n🔴 真正的重复文件:');
            report.trueDuplicates.forEach((group, index) => {
                console.log(`  ${index + 1}. ${group.files.join(' | ')}`);
                console.log(`     理由: ${group.reason}`);
                console.log(`     建议: ${group.recommendation}`);
            });
        }
        
        if (report.similarFiles.length > 0) {
            console.log('\n🟡 相似文件:');
            report.similarFiles.forEach((group, index) => {
                console.log(`  ${index + 1}. ${group.files.join(' | ')}`);
                console.log(`     相似度: ${group.similarity}%`);
                console.log(`     建议: ${group.recommendation}`);
            });
        }
        
        if (report.summary.trueDuplicates === 0 && report.summary.similarFiles === 0) {
            console.log('\n✅ 没有发现需要处理的重复文件');
        }
    }

    // 保存报告
    async saveReport(report) {
        const reportPath = path.join(__dirname, 'smart-duplicate-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📊 智能重复检测报告已保存到: ${reportPath}`);
    }
}

// 运行智能重复检测
async function runSmartDuplicateDetection() {
    const detector = new SmartDuplicateDetector();
    
    try {
        await detector.detectTrueDuplicates();
        const report = detector.generateReport();
        
        detector.printReport();
        await detector.saveReport(report);
        
        return report;
        
    } catch (error) {
        console.error('智能重复检测失败:', error);
    }
}

if (require.main === module) {
    runSmartDuplicateDetection();
}

module.exports = SmartDuplicateDetector;
