/**
 * 交互元素深度测试
 * 测试页面内的按钮、表单、模态框、表格等交互功能
 */

const axios = require('axios');
const fs = require('fs');

class InteractiveElementsTester {
    constructor() {
        this.baseURL = 'http://localhost:3003';
        this.session = null;
        this.testResults = {
            pageAnalysis: {},
            interactionTests: {},
            missingElements: [],
            brokenInteractions: [],
            uiIssues: []
        };
    }

    async login() {
        try {
            const loginResponse = await axios.post(`${this.baseURL}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            }, {
                withCredentials: true,
                headers: { 'Content-Type': 'application/json' }
            });
            
            this.session = loginResponse.headers['set-cookie'];
            return true;
        } catch (error) {
            console.error('登录失败:', error.message);
            return false;
        }
    }

    async analyzePage(url, pageName) {
        try {
            console.log(`\n🔍 分析页面: ${pageName} (${url})`);

            const response = await axios.get(`${this.baseURL}${url}`, {
                headers: {
                    'Cookie': this.session ? this.session.join('; ') : ''
                }
            });

            const html = response.data;

            const analysis = {
                title: this.extractTitle(html),
                buttons: this.extractButtons(html),
                forms: this.extractForms(html),
                tables: this.extractTables(html),
                modals: this.extractModals(html),
                links: this.extractLinks(html),
                scripts: this.extractScripts(html),
                stylesheets: this.extractStylesheets(html),
                issues: []
            };

            // 检查常见问题
            if (analysis.buttons.length === 0) {
                analysis.issues.push('页面没有找到任何按钮');
            }

            if (html.includes('alert') || html.includes('error') || html.includes('warning')) {
                analysis.issues.push('页面可能显示了警告或错误信息');
            }

            if (html.includes('src=""')) {
                analysis.issues.push('发现空的图片源');
            }

            this.testResults.pageAnalysis[url] = analysis;

            console.log(`  📊 按钮数量: ${analysis.buttons.length}`);
            console.log(`  📝 表单数量: ${analysis.forms.length}`);
            console.log(`  📋 表格数量: ${analysis.tables.length}`);
            console.log(`  🔗 内部链接数量: ${analysis.links.length}`);

            if (analysis.issues.length > 0) {
                console.log(`  ⚠️  发现问题: ${analysis.issues.length}个`);
                analysis.issues.forEach(issue => console.log(`    - ${issue}`));
            }

            return analysis;

        } catch (error) {
            console.error(`❌ 分析页面失败 ${url}:`, error.message);
            return null;
        }
    }

    extractTitle(html) {
        const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
        return titleMatch ? titleMatch[1].trim() : '';
    }

    extractButtons(html) {
        const buttonRegex = /<(button|input)[^>]*(?:type=["'](?:button|submit)["'])?[^>]*>([^<]*)<\/?\1>/gi;
        const buttons = [];
        let match;

        while ((match = buttonRegex.exec(html)) !== null) {
            const buttonHtml = match[0];
            const text = match[2] || '';

            buttons.push({
                text: text.trim(),
                html: buttonHtml,
                type: this.extractAttribute(buttonHtml, 'type'),
                class: this.extractAttribute(buttonHtml, 'class'),
                id: this.extractAttribute(buttonHtml, 'id')
            });
        }

        // 也查找带有btn类的元素
        const btnClassRegex = /<[^>]*class=["'][^"']*btn[^"']*["'][^>]*>([^<]*)</gi;
        while ((match = btnClassRegex.exec(html)) !== null) {
            buttons.push({
                text: match[1].trim(),
                html: match[0],
                type: 'btn-class',
                class: this.extractAttribute(match[0], 'class')
            });
        }

        return buttons;
    }

    extractForms(html) {
        const formRegex = /<form[^>]*>[\s\S]*?<\/form>/gi;
        const forms = [];
        let match;

        while ((match = formRegex.exec(html)) !== null) {
            const formHtml = match[0];
            forms.push({
                action: this.extractAttribute(formHtml, 'action'),
                method: this.extractAttribute(formHtml, 'method'),
                id: this.extractAttribute(formHtml, 'id'),
                class: this.extractAttribute(formHtml, 'class'),
                inputCount: (formHtml.match(/<input[^>]*>/gi) || []).length
            });
        }

        return forms;
    }

    extractTables(html) {
        const tableRegex = /<table[^>]*>[\s\S]*?<\/table>/gi;
        const tables = [];
        let match;

        while ((match = tableRegex.exec(html)) !== null) {
            const tableHtml = match[0];
            const headerMatches = tableHtml.match(/<th[^>]*>([^<]*)<\/th>/gi) || [];

            tables.push({
                id: this.extractAttribute(tableHtml, 'id'),
                class: this.extractAttribute(tableHtml, 'class'),
                headerCount: headerMatches.length,
                rowCount: (tableHtml.match(/<tr[^>]*>/gi) || []).length
            });
        }

        return tables;
    }

    extractModals(html) {
        const modalRegex = /<div[^>]*class=["'][^"']*modal[^"']*["'][^>]*>[\s\S]*?<\/div>/gi;
        const modals = [];
        let match;

        while ((match = modalRegex.exec(html)) !== null) {
            const modalHtml = match[0];
            modals.push({
                id: this.extractAttribute(modalHtml, 'id'),
                class: this.extractAttribute(modalHtml, 'class'),
                hasForm: modalHtml.includes('<form')
            });
        }

        return modals;
    }

    extractLinks(html) {
        const linkRegex = /<a[^>]*href=["']([^"']*)["'][^>]*>([^<]*)<\/a>/gi;
        const links = [];
        let match;

        while ((match = linkRegex.exec(html)) !== null) {
            const href = match[1];
            if (href && !href.startsWith('http') && !href.startsWith('#')) {
                links.push({
                    text: match[2].trim(),
                    href: href,
                    class: this.extractAttribute(match[0], 'class')
                });
            }
        }

        return links;
    }

    extractScripts(html) {
        const scriptRegex = /<script[^>]*src=["']([^"']*\.js)["'][^>]*>/gi;
        const scripts = [];
        let match;

        while ((match = scriptRegex.exec(html)) !== null) {
            const src = match[1];
            if (src && !src.startsWith('http')) {
                scripts.push(src);
            }
        }

        return scripts;
    }

    extractStylesheets(html) {
        const cssRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']*\.css)["'][^>]*>/gi;
        const stylesheets = [];
        let match;

        while ((match = cssRegex.exec(html)) !== null) {
            const href = match[1];
            if (href && !href.startsWith('http')) {
                stylesheets.push(href);
            }
        }

        return stylesheets;
    }

    extractAttribute(html, attrName) {
        const regex = new RegExp(`${attrName}=["']([^"']*)["']`, 'i');
        const match = html.match(regex);
        return match ? match[1] : '';
    }

    async testAPIEndpoints() {
        console.log('\n🔌 测试API端点...');
        
        const apiEndpoints = [
            '/api/dashboard/stats',
            '/api/users',
            '/api/tenants',
            '/api/flocks',
            '/api/production',
            '/api/health',
            '/api/finance',
            '/api/inventory',
            '/api/reports',
            '/api/goose-prices',
            '/api/mall/products',
            '/api/knowledge',
            '/api/announcements'
        ];

        const apiResults = {};
        
        for (const endpoint of apiEndpoints) {
            try {
                const response = await axios.get(`${this.baseURL}${endpoint}`, {
                    headers: {
                        'Cookie': this.session ? this.session.join('; ') : ''
                    },
                    timeout: 5000
                });
                
                apiResults[endpoint] = {
                    status: response.status,
                    success: true,
                    dataType: typeof response.data,
                    hasData: response.data && Object.keys(response.data).length > 0
                };
                
                console.log(`✅ ${endpoint} - 状态: ${response.status}`);
                
            } catch (error) {
                apiResults[endpoint] = {
                    status: error.response?.status || 'ERROR',
                    success: false,
                    error: error.message
                };
                
                if (error.response?.status === 404) {
                    console.log(`❌ ${endpoint} - 404 未找到`);
                    this.testResults.missingElements.push(`API端点: ${endpoint}`);
                } else {
                    console.log(`⚠️  ${endpoint} - 错误: ${error.message}`);
                    this.testResults.brokenInteractions.push(`API端点: ${endpoint} - ${error.message}`);
                }
            }
        }
        
        this.testResults.interactionTests.apiEndpoints = apiResults;
        return apiResults;
    }

    async runComprehensiveTest() {
        console.log('🚀 开始交互元素深度测试...');
        
        const loginSuccess = await this.login();
        if (!loginSuccess) {
            console.log('❌ 登录失败，无法继续测试');
            return;
        }

        // 测试主要页面的交互元素
        const pagesToTest = [
            { url: '/dashboard', name: '仪表板' },
            { url: '/users', name: '用户管理' },
            { url: '/tenants', name: '租户管理' },
            { url: '/flocks', name: '鹅群管理' },
            { url: '/production', name: '生产管理' },
            { url: '/health', name: '健康管理' },
            { url: '/finance', name: '财务管理' },
            { url: '/inventory', name: '库存管理' },
            { url: '/reports', name: '报表管理' },
            { url: '/system', name: '系统管理' },
            { url: '/mall', name: '商城管理' },
            { url: '/knowledge', name: '知识库管理' }
        ];

        for (const page of pagesToTest) {
            await this.analyzePage(page.url, page.name);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 测试API端点
        await this.testAPIEndpoints();

        return this.testResults;
    }

    generateDetailedReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                pagesAnalyzed: Object.keys(this.testResults.pageAnalysis).length,
                totalButtons: 0,
                totalForms: 0,
                totalTables: 0,
                totalIssues: this.testResults.missingElements.length + this.testResults.brokenInteractions.length
            },
            pageAnalysis: this.testResults.pageAnalysis,
            interactionTests: this.testResults.interactionTests,
            issues: {
                missingElements: this.testResults.missingElements,
                brokenInteractions: this.testResults.brokenInteractions,
                uiIssues: this.testResults.uiIssues
            },
            recommendations: []
        };

        // 计算统计数据
        Object.values(this.testResults.pageAnalysis).forEach(analysis => {
            if (analysis) {
                report.summary.totalButtons += analysis.buttons.length;
                report.summary.totalForms += analysis.forms.length;
                report.summary.totalTables += analysis.tables.length;
            }
        });

        // 生成建议
        if (report.summary.totalIssues > 0) {
            report.recommendations.push('修复发现的功能问题和缺失元素');
        }
        
        if (report.summary.totalButtons === 0) {
            report.recommendations.push('检查页面交互元素是否正常加载');
        }

        return report;
    }

    async saveDetailedReport(report) {
        const reportPath = './tests/interactive-elements-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📊 详细测试报告已保存到: ${reportPath}`);
    }
}

// 运行测试
async function runInteractiveTest() {
    const tester = new InteractiveElementsTester();
    
    try {
        const results = await tester.runComprehensiveTest();
        const report = tester.generateDetailedReport();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 交互元素测试结果汇总');
        console.log('='.repeat(60));
        console.log(`分析页面数: ${report.summary.pagesAnalyzed}`);
        console.log(`总按钮数: ${report.summary.totalButtons}`);
        console.log(`总表单数: ${report.summary.totalForms}`);
        console.log(`总表格数: ${report.summary.totalTables}`);
        console.log(`发现问题数: ${report.summary.totalIssues}`);
        
        if (report.issues.missingElements.length > 0) {
            console.log('\n❌ 缺失元素:');
            report.issues.missingElements.forEach(item => console.log(`  - ${item}`));
        }
        
        if (report.issues.brokenInteractions.length > 0) {
            console.log('\n⚠️  交互问题:');
            report.issues.brokenInteractions.forEach(item => console.log(`  - ${item}`));
        }
        
        await tester.saveDetailedReport(report);
        
    } catch (error) {
        console.error('交互测试执行失败:', error);
    }
}

if (require.main === module) {
    runInteractiveTest();
}

module.exports = InteractiveElementsTester;
