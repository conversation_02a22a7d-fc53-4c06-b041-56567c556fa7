/**
 * 精确文件使用情况分析工具
 * 更准确地分析哪些文件真正未被使用
 */

const fs = require('fs');
const path = require('path');

class PreciseFileUsageAnalyzer {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.adminPath = path.join(this.projectRoot, 'backend/saas-admin');
        this.results = {
            safeToDelete: [],
            potentiallyUnused: [],
            inUse: [],
            analysis: {}
        };
    }

    // 获取所有路由文件内容
    getAllRouteContent() {
        const routeFiles = [
            path.join(this.adminPath, 'server.js'),
            ...this.getAllFiles(path.join(this.adminPath, 'routes'), ['.js'])
        ];
        
        let allContent = '';
        routeFiles.forEach(file => {
            try {
                const content = fs.readFileSync(file.path || file, 'utf8');
                allContent += content + '\n';
            } catch (error) {
                console.warn(`无法读取路由文件: ${file.path || file}`);
            }
        });
        
        return allContent;
    }

    // 获取所有HTML/EJS文件内容
    getAllTemplateContent() {
        const templateFiles = this.getAllFiles(this.adminPath, ['.html', '.ejs']);
        let allContent = '';
        
        templateFiles.forEach(file => {
            try {
                const content = fs.readFileSync(file.path, 'utf8');
                allContent += content + '\n';
            } catch (error) {
                console.warn(`无法读取模板文件: ${file.relativePath}`);
            }
        });
        
        return allContent;
    }

    getAllFiles(dir, extensions = []) {
        const files = [];
        
        if (!fs.existsSync(dir)) {
            return files;
        }

        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                files.push(...this.getAllFiles(fullPath, extensions));
            } else if (stat.isFile()) {
                const ext = path.extname(item).toLowerCase();
                if (extensions.length === 0 || extensions.includes(ext)) {
                    files.push({
                        path: fullPath,
                        relativePath: path.relative(this.projectRoot, fullPath),
                        name: item,
                        size: stat.size,
                        extension: ext
                    });
                }
            }
        }
        
        return files;
    }

    // 分析JavaScript文件使用情况
    analyzeJavaScriptFiles() {
        console.log('📜 精确分析JavaScript文件使用情况...');
        
        const jsFiles = this.getAllFiles(this.adminPath, ['.js']);
        const templateContent = this.getAllTemplateContent();
        const routeContent = this.getAllRouteContent();
        
        jsFiles.forEach(jsFile => {
            const fileName = path.basename(jsFile.name, '.js');
            const relativePath = path.relative(this.adminPath, jsFile.path);
            
            let isUsed = false;
            let usageReasons = [];
            
            // 检查是否在HTML/EJS中被引用
            if (templateContent.includes(jsFile.name) || 
                templateContent.includes(fileName) ||
                templateContent.includes(relativePath)) {
                isUsed = true;
                usageReasons.push('在模板中被引用');
            }
            
            // 检查特殊用途文件
            if (jsFile.name === 'server.js' || 
                jsFile.relativePath.includes('routes/') ||
                jsFile.relativePath.includes('middleware/') ||
                jsFile.relativePath.includes('config/')) {
                isUsed = true;
                usageReasons.push('系统核心文件');
            }
            
            // 检查工具脚本
            if (jsFile.name.includes('setup') || 
                jsFile.name.includes('test') ||
                jsFile.name.includes('create-')) {
                // 这些是工具脚本，可能可以删除
                this.results.potentiallyUnused.push({
                    ...jsFile,
                    type: 'JavaScript工具脚本',
                    reason: '工具脚本，可能不需要保留'
                });
            } else if (!isUsed) {
                this.results.safeToDelete.push({
                    ...jsFile,
                    type: 'JavaScript',
                    reason: '未找到任何引用'
                });
            } else {
                this.results.inUse.push({
                    ...jsFile,
                    type: 'JavaScript',
                    usageReasons
                });
            }
        });
    }

    // 分析HTML/EJS模板文件使用情况
    analyzeTemplateFiles() {
        console.log('📄 精确分析模板文件使用情况...');
        
        const templateFiles = this.getAllFiles(this.adminPath, ['.html', '.ejs']);
        const routeContent = this.getAllRouteContent();
        
        templateFiles.forEach(templateFile => {
            const templateName = path.basename(templateFile.name, path.extname(templateFile.name));
            const relativePath = path.relative(path.join(this.adminPath, 'views'), templateFile.path);
            const templatePath = relativePath.replace(/\\/g, '/').replace(/\.(html|ejs)$/, '');
            
            let isUsed = false;
            let usageReasons = [];
            
            // 检查在路由中的引用
            if (routeContent.includes(`'${templateName}'`) ||
                routeContent.includes(`"${templateName}"`) ||
                routeContent.includes(`'${templatePath}'`) ||
                routeContent.includes(`"${templatePath}"`)) {
                isUsed = true;
                usageReasons.push('在路由中被引用');
            }
            
            // 检查布局文件
            if (templateName === 'main' || 
                templateName === 'layout' || 
                templateFile.relativePath.includes('layouts/')) {
                isUsed = true;
                usageReasons.push('布局文件');
            }
            
            // 检查错误页面
            if (templateName === 'error' || templateName === '404') {
                isUsed = true;
                usageReasons.push('错误页面');
            }
            
            // 检查测试文件
            if (templateFile.name.includes('test') || 
                templateFile.name.includes('integration')) {
                this.results.potentiallyUnused.push({
                    ...templateFile,
                    type: 'HTML测试文件',
                    templatePath,
                    reason: '测试文件，可能不需要保留'
                });
            } else if (!isUsed) {
                this.results.safeToDelete.push({
                    ...templateFile,
                    type: 'HTML模板',
                    templatePath,
                    reason: '未在路由中找到引用'
                });
            } else {
                this.results.inUse.push({
                    ...templateFile,
                    type: 'HTML模板',
                    templatePath,
                    usageReasons
                });
            }
        });
    }

    // 运行精确分析
    async runPreciseAnalysis() {
        console.log('🚀 开始精确文件使用情况分析...\n');
        
        this.analyzeJavaScriptFiles();
        this.analyzeTemplateFiles();
        
        return this.results;
    }

    // 生成报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                safeToDelete: this.results.safeToDelete.length,
                potentiallyUnused: this.results.potentiallyUnused.length,
                inUse: this.results.inUse.length,
                totalAnalyzed: this.results.safeToDelete.length + this.results.potentiallyUnused.length + this.results.inUse.length
            },
            safeToDelete: this.results.safeToDelete,
            potentiallyUnused: this.results.potentiallyUnused,
            inUse: this.results.inUse
        };
        
        return report;
    }

    // 打印报告
    printReport() {
        const report = this.generateReport();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 精确文件使用情况分析结果');
        console.log('='.repeat(60));
        console.log(`总分析文件数: ${report.summary.totalAnalyzed}`);
        console.log(`安全删除: ${report.summary.safeToDelete}`);
        console.log(`可能未使用: ${report.summary.potentiallyUnused}`);
        console.log(`正在使用: ${report.summary.inUse}`);
        
        if (report.safeToDelete.length > 0) {
            console.log('\n🗑️  安全删除的文件:');
            report.safeToDelete.forEach(file => {
                console.log(`  - ${file.relativePath} (${file.type})`);
                console.log(`    理由: ${file.reason}`);
            });
        }
        
        if (report.potentiallyUnused.length > 0) {
            console.log('\n⚠️  可能未使用的文件:');
            report.potentiallyUnused.forEach(file => {
                console.log(`  - ${file.relativePath} (${file.type})`);
                console.log(`    理由: ${file.reason}`);
            });
        }
        
        console.log('\n💡 建议:');
        if (report.summary.safeToDelete > 0) {
            console.log('- 可以安全删除标记为"安全删除"的文件');
        }
        if (report.summary.potentiallyUnused > 0) {
            console.log('- 仔细检查"可能未使用"的文件后再决定是否删除');
        }
        if (report.summary.safeToDelete === 0 && report.summary.potentiallyUnused === 0) {
            console.log('- 所有文件都在使用中，无需清理');
        }
    }

    // 保存报告
    async saveReport(report) {
        const reportPath = path.join(__dirname, 'precise-file-usage-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📊 精确分析报告已保存到: ${reportPath}`);
    }
}

// 运行精确分析
async function runPreciseAnalysis() {
    const analyzer = new PreciseFileUsageAnalyzer();
    
    try {
        const results = await analyzer.runPreciseAnalysis();
        const report = analyzer.generateReport();
        
        analyzer.printReport();
        await analyzer.saveReport(report);
        
        return report;
        
    } catch (error) {
        console.error('精确分析失败:', error);
    }
}

if (require.main === module) {
    runPreciseAnalysis();
}

module.exports = PreciseFileUsageAnalyzer;
