const { test, expect } = require('@playwright/test');

/**
 * 深度三端联调测试 - 测试每个按键每个页面
 * 全面验证SAAS后台管理系统功能
 */

const BASE_URL = 'http://localhost:4000';
const LOGIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

// 测试报告数据
let testResults = {
  totalTests: 0,
  passedTests: 0,
  failedTests: [],
  missingPages: [],
  brokenButtons: [],
  serverErrors: []
};

test.describe('SAAS后台管理系统 - 深度功能测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每次测试前都需要登录
    await page.goto(`${BASE_URL}/auth/login`);
    await page.fill('input[name="username"]', LOGIN_CREDENTIALS.username);
    await page.fill('input[name="password"]', LOGIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(`${BASE_URL}/dashboard`);
  });

  test('1. 深度测试 - 仪表板页面所有功能', async ({ page }) => {
    console.log('🏠 测试仪表板页面...');
    
    testResults.totalTests++;
    
    try {
      // 验证页面加载
      await expect(page.locator('h1, h2')).toContainText(['仪表盘', '平台仪表盘']);
      
      // 测试所有统计卡片
      const statCards = page.locator('.card');
      const cardCount = await statCards.count();
      console.log(`发现 ${cardCount} 个统计卡片`);
      
      // 测试图表渲染
      const charts = page.locator('canvas, .chart-container, #chart');
      const chartCount = await charts.count();
      console.log(`发现 ${chartCount} 个图表元素`);
      
      // 测试刷新按钮
      const refreshBtns = page.locator('button:has-text("刷新"), .btn-refresh, [data-action="refresh"]');
      for (let i = 0; i < await refreshBtns.count(); i++) {
        try {
          await refreshBtns.nth(i).click();
          await page.waitForTimeout(500);
        } catch (e) {
          console.warn(`刷新按钮 ${i} 点击失败:`, e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '仪表板页面测试',
        error: error.message
      });
    }
  });

  test('2. 深度测试 - 用户管理所有功能', async ({ page }) => {
    console.log('👥 测试用户管理页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/users`);
      await page.waitForLoadState('networkidle');
      
      // 测试搜索功能
      const searchInput = page.locator('input[type="search"], .search-input, [placeholder*="搜索"]');
      if (await searchInput.count() > 0) {
        await searchInput.fill('admin');
        await page.waitForTimeout(1000);
        await searchInput.clear();
      }
      
      // 测试筛选按钮
      const filterBtns = page.locator('.btn-filter, [data-action="filter"], select');
      for (let i = 0; i < await filterBtns.count(); i++) {
        try {
          await filterBtns.nth(i).click();
          await page.waitForTimeout(300);
        } catch (e) {
          console.warn(`筛选按钮 ${i} 失败:`, e.message);
        }
      }
      
      // 测试新建用户按钮
      const createBtns = page.locator('a:has-text("新建"), .btn-create, [href*="create"]');
      for (let i = 0; i < await createBtns.count(); i++) {
        try {
          await createBtns.nth(i).click();
          await page.waitForTimeout(1000);
          await page.goBack();
        } catch (e) {
          console.warn(`新建按钮 ${i} 失败:`, e.message);
          testResults.brokenButtons.push(`用户管理-新建按钮${i}`);
        }
      }
      
      // 测试表格操作按钮
      const actionBtns = page.locator('.btn-edit, .btn-delete, .btn-view, [data-action]');
      const actionCount = await actionBtns.count();
      console.log(`发现 ${actionCount} 个操作按钮`);
      
      // 只测试前几个操作按钮，避免数据变更
      for (let i = 0; i < Math.min(actionCount, 3); i++) {
        try {
          const btnText = await actionBtns.nth(i).textContent();
          console.log(`测试按钮: ${btnText}`);
          
          if (btnText?.includes('查看') || btnText?.includes('详情')) {
            await actionBtns.nth(i).click();
            await page.waitForTimeout(1000);
            await page.goBack();
          }
        } catch (e) {
          console.warn(`操作按钮 ${i} 失败:`, e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '用户管理页面测试',
        error: error.message
      });
    }
  });

  test('3. 深度测试 - 租户管理所有功能', async ({ page }) => {
    console.log('🏢 测试租户管理页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/tenants`);
      await page.waitForLoadState('networkidle');
      
      // 测试子页面导航
      const subPages = [
        '/tenants/create',
        '/tenants/subscriptions', 
        '/tenants/usage'
      ];
      
      for (const subPath of subPages) {
        try {
          console.log(`测试子页面: ${subPath}`);
          await page.goto(`${BASE_URL}${subPath}`);
          await page.waitForLoadState('networkidle');
          
          // 测试页面特定的按钮
          const pageButtons = page.locator('button, .btn, a.btn');
          for (let i = 0; i < Math.min(await pageButtons.count(), 5); i++) {
            try {
              const btn = pageButtons.nth(i);
              const btnText = await btn.textContent();
              const btnHref = await btn.getAttribute('href');
              
              console.log(`测试按钮: ${btnText} (href: ${btnHref})`);
              
              if (btnHref && !btnHref.includes('delete') && !btnHref.includes('remove')) {
                await btn.click();
                await page.waitForTimeout(500);
                await page.goBack();
              } else if (!btnText?.includes('删除') && !btnText?.includes('提交')) {
                await btn.click({ timeout: 3000 });
                await page.waitForTimeout(500);
              }
            } catch (e) {
              console.warn(`按钮测试失败:`, e.message);
            }
          }
          
        } catch (error) {
          testResults.missingPages.push(subPath);
          console.error(`子页面 ${subPath} 测试失败:`, error.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '租户管理页面测试',
        error: error.message
      });
    }
  });

  test('4. 深度测试 - 鹅群管理所有功能', async ({ page }) => {
    console.log('🦢 测试鹅群管理页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/flocks`);
      await page.waitForLoadState('networkidle');
      
      // 测试页面加载
      await page.waitForSelector('h1, h2, .page-title', { timeout: 10000 });
      
      // 测试所有可见按钮
      const allButtons = page.locator('button:visible, .btn:visible, a.btn:visible');
      const buttonCount = await allButtons.count();
      console.log(`发现 ${buttonCount} 个可见按钮`);
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        try {
          const btn = allButtons.nth(i);
          const btnText = await btn.textContent();
          console.log(`点击按钮: ${btnText}`);
          
          await btn.click();
          await page.waitForTimeout(1000);
          
          // 如果弹出模态框，尝试关闭
          const modal = page.locator('.modal, .dialog');
          if (await modal.isVisible()) {
            const closeBtn = page.locator('.modal .btn-close, .modal .close, .modal button:has-text("取消")');
            if (await closeBtn.count() > 0) {
              await closeBtn.first().click();
            } else {
              await page.keyboard.press('Escape');
            }
          }
        } catch (e) {
          console.warn(`鹅群管理按钮 ${i} 失败:`, e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '鹅群管理页面测试',
        error: error.message
      });
    }
  });

  test('5. 深度测试 - 商城管理所有功能', async ({ page }) => {
    console.log('🛍️ 测试商城管理页面...');
    
    testResults.totalTests++;
    
    try {
      // 测试主商城页面
      await page.goto(`${BASE_URL}/mall`);
      await page.waitForLoadState('networkidle');
      
      // 测试商城子页面
      const mallSubPages = [
        '/mall/products',
        '/mall/orders', 
        '/mall/categories',
        '/mall/inventory'
      ];
      
      for (const subPath of mallSubPages) {
        try {
          console.log(`测试商城子页面: ${subPath}`);
          await page.goto(`${BASE_URL}${subPath}`);
          await page.waitForLoadState('networkidle');
          
          // 检查页面是否正确加载
          const hasContent = await page.locator('body').isVisible();
          if (!hasContent) {
            testResults.missingPages.push(subPath);
          }
          
          // 测试页面内所有链接
          const links = page.locator('a:visible');
          for (let i = 0; i < Math.min(await links.count(), 5); i++) {
            try {
              const link = links.nth(i);
              const href = await link.getAttribute('href');
              if (href && href.startsWith('/') && !href.includes('#')) {
                console.log(`测试链接: ${href}`);
                await link.click();
                await page.waitForTimeout(1000);
                await page.goBack();
              }
            } catch (e) {
              console.warn(`链接测试失败:`, e.message);
            }
          }
          
        } catch (error) {
          testResults.missingPages.push(subPath);
          console.error(`商城子页面失败: ${subPath}`, error.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '商城管理页面测试',
        error: error.message
      });
    }
  });

  test('6. 深度测试 - 鹅价管理所有功能', async ({ page }) => {
    console.log('💰 测试鹅价管理页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/goose-prices`);
      await page.waitForLoadState('networkidle');
      
      // 测试价格趋势页面
      await page.goto(`${BASE_URL}/goose-prices/trends`);
      await page.waitForLoadState('networkidle');
      
      // 测试图表交互
      const chartElements = page.locator('canvas, .chart-container, svg');
      if (await chartElements.count() > 0) {
        console.log('发现价格趋势图表');
        // 测试图表点击
        try {
          await chartElements.first().click();
        } catch (e) {
          console.warn('图表交互失败:', e.message);
        }
      }
      
      // 测试筛选功能
      const filters = page.locator('select, input[type="date"]');
      for (let i = 0; i < await filters.count(); i++) {
        try {
          const filter = filters.nth(i);
          const tagName = await filter.evaluate(el => el.tagName);
          
          if (tagName === 'SELECT') {
            await filter.selectOption({ index: 1 });
          } else if (tagName === 'INPUT') {
            await filter.fill('2025-08-26');
          }
          
          await page.waitForTimeout(1000);
        } catch (e) {
          console.warn(`筛选器 ${i} 失败:`, e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '鹅价管理页面测试',
        error: error.message
      });
    }
  });

  test('7. 深度测试 - 公告管理所有功能', async ({ page }) => {
    console.log('📢 测试公告管理页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/announcements`);
      await page.waitForLoadState('networkidle');
      
      // 测试新建公告页面
      await page.goto(`${BASE_URL}/announcements/create`);
      await page.waitForLoadState('networkidle');
      
      // 测试表单交互
      const formInputs = page.locator('input, textarea, select');
      for (let i = 0; i < await formInputs.count(); i++) {
        try {
          const input = formInputs.nth(i);
          const inputType = await input.getAttribute('type');
          const tagName = await input.evaluate(el => el.tagName);
          
          console.log(`测试表单元素: ${tagName} (type: ${inputType})`);
          
          if (tagName === 'INPUT' && inputType === 'text') {
            await input.fill('测试标题');
          } else if (tagName === 'TEXTAREA') {
            await input.fill('测试内容');
          } else if (tagName === 'SELECT') {
            await input.selectOption({ index: 1 });
          }
          
          await page.waitForTimeout(300);
        } catch (e) {
          console.warn(`表单元素 ${i} 失败:`, e.message);
        }
      }
      
      // 测试预览功能
      const previewBtn = page.locator('button:has-text("预览")');
      if (await previewBtn.count() > 0) {
        try {
          await previewBtn.click();
          await page.waitForTimeout(1000);
        } catch (e) {
          console.warn('预览功能失败:', e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '公告管理页面测试',
        error: error.message
      });
    }
  });

  test('8. 深度测试 - 系统设置所有功能', async ({ page }) => {
    console.log('⚙️ 测试系统设置页面...');
    
    testResults.totalTests++;
    
    try {
      await page.goto(`${BASE_URL}/system`);
      await page.waitForLoadState('networkidle');
      
      // 测试设置标签页
      const tabs = page.locator('.nav-tab, .tab, [role="tab"]');
      for (let i = 0; i < await tabs.count(); i++) {
        try {
          await tabs.nth(i).click();
          await page.waitForTimeout(1000);
        } catch (e) {
          console.warn(`设置标签 ${i} 失败:`, e.message);
        }
      }
      
      // 测试设置表单
      const settingInputs = page.locator('.settings input, .config input');
      for (let i = 0; i < Math.min(await settingInputs.count(), 5); i++) {
        try {
          const input = settingInputs.nth(i);
          const originalValue = await input.inputValue();
          
          await input.clear();
          await input.fill('test-value');
          await page.waitForTimeout(500);
          
          // 恢复原值
          await input.clear();
          await input.fill(originalValue);
        } catch (e) {
          console.warn(`设置输入框 ${i} 失败:`, e.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '系统设置页面测试',
        error: error.message
      });
    }
  });

  test('9. 深度测试 - 所有导航菜单', async ({ page }) => {
    console.log('🧭 测试所有导航菜单...');
    
    testResults.totalTests++;
    
    try {
      // 回到仪表板
      await page.goto(`${BASE_URL}/dashboard`);
      await page.waitForLoadState('networkidle');
      
      // 查找所有导航链接
      const navLinks = page.locator('.nav-link, .menu-item, .sidebar a, .navbar a');
      const navCount = await navLinks.count();
      console.log(`发现 ${navCount} 个导航链接`);
      
      // 收集所有href链接
      const hrefs = [];
      for (let i = 0; i < navCount; i++) {
        try {
          const href = await navLinks.nth(i).getAttribute('href');
          if (href && href.startsWith('/') && !href.includes('#') && !hrefs.includes(href)) {
            hrefs.push(href);
          }
        } catch (e) {
          console.warn(`获取链接 ${i} 失败:`, e.message);
        }
      }
      
      console.log('测试导航链接:', hrefs);
      
      // 测试每个导航链接
      for (const href of hrefs.slice(0, 15)) { // 限制测试数量
        try {
          console.log(`测试导航: ${href}`);
          await page.goto(`${BASE_URL}${href}`);
          await page.waitForLoadState('networkidle');
          
          // 检查页面是否正确加载
          const hasError = await page.locator('.error, .alert-danger').count() > 0;
          if (hasError) {
            const errorText = await page.locator('.error, .alert-danger').first().textContent();
            testResults.serverErrors.push({
              page: href,
              error: errorText
            });
          }
        } catch (error) {
          testResults.missingPages.push(href);
          console.error(`导航链接失败: ${href}`, error.message);
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: '导航菜单测试',
        error: error.message
      });
    }
  });

  test('10. 深度测试 - API端点检查', async ({ page }) => {
    console.log('🔌 测试API端点...');
    
    testResults.totalTests++;
    
    try {
      const apiEndpoints = [
        '/api/dashboard/stats',
        '/api/users',
        '/api/tenants',
        '/api/flocks',
        '/api/mall/products',
        '/api/announcements'
      ];
      
      for (const endpoint of apiEndpoints) {
        try {
          console.log(`测试API: ${endpoint}`);
          const response = await page.request.get(`${BASE_URL}${endpoint}`);
          
          if (response.status() === 200) {
            console.log(`✅ API ${endpoint} 正常`);
          } else if (response.status() === 302 || response.status() === 401) {
            console.log(`⚠️ API ${endpoint} 需要认证 (${response.status()})`);
          } else {
            testResults.serverErrors.push({
              page: endpoint,
              error: `HTTP ${response.status()}`
            });
          }
        } catch (error) {
          testResults.serverErrors.push({
            page: endpoint,
            error: error.message
          });
        }
      }
      
      testResults.passedTests++;
    } catch (error) {
      testResults.failedTests.push({
        test: 'API端点测试',
        error: error.message
      });
    }
  });

  test.afterAll(async () => {
    // 生成测试报告
    console.log('\n' + '='.repeat(60));
    console.log('🎯 深度三端联调测试报告');
    console.log('='.repeat(60));
    console.log(`📊 总测试数: ${testResults.totalTests}`);
    console.log(`✅ 通过测试: ${testResults.passedTests}`);
    console.log(`❌ 失败测试: ${testResults.failedTests.length}`);
    console.log(`📄 缺失页面: ${testResults.missingPages.length}`);
    console.log(`🔨 故障按钮: ${testResults.brokenButtons.length}`);
    console.log(`⚠️ 服务器错误: ${testResults.serverErrors.length}`);
    
    const successRate = ((testResults.passedTests / testResults.totalTests) * 100).toFixed(1);
    console.log(`📈 成功率: ${successRate}%`);
    
    if (testResults.failedTests.length > 0) {
      console.log('\n❌ 失败测试详情:');
      testResults.failedTests.forEach((failure, index) => {
        console.log(`  ${index + 1}. ${failure.test}: ${failure.error}`);
      });
    }
    
    if (testResults.missingPages.length > 0) {
      console.log('\n📄 缺失页面:');
      testResults.missingPages.forEach((page, index) => {
        console.log(`  ${index + 1}. ${page}`);
      });
    }
    
    if (testResults.brokenButtons.length > 0) {
      console.log('\n🔨 故障按钮:');
      testResults.brokenButtons.forEach((button, index) => {
        console.log(`  ${index + 1}. ${button}`);
      });
    }
    
    if (testResults.serverErrors.length > 0) {
      console.log('\n⚠️ 服务器错误:');
      testResults.serverErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.page}: ${error.error}`);
      });
    }
    
    console.log('\n🕒 测试完成时间:', new Date().toLocaleString());
    console.log('='.repeat(60));
  });

});