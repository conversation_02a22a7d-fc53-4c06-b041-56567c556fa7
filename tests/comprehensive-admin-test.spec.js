const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心全面测试套件
 * 测试范围：核心功能模块、页面可用性、交互功能、数据操作
 */

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  apiURL: 'http://localhost:3000',
  timeout: 30000,
  credentials: {
    admin: { username: 'admin', password: 'admin123' },
    demo: { username: 'demo', password: 'demo123' },
    manager: { username: 'manager', password: 'manager123' }
  }
};

// 测试结果收集器
let testResults = {
  passed: 0,
  failed: 0,
  issues: [],
  coverage: {
    pages: [],
    apis: [],
    features: []
  }
};

test.describe('智慧养鹅后台管理中心 - 全面功能测试', () => {
  
  test.beforeAll(async () => {
    console.log('🚀 开始智慧养鹅后台管理中心全面测试');
    console.log(`📍 管理后台地址: ${TEST_CONFIG.baseURL}`);
    console.log(`🔗 API服务地址: ${TEST_CONFIG.apiURL}`);
  });

  test.afterAll(async () => {
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${testResults.passed}`);
    console.log(`❌ 失败: ${testResults.failed}`);
    console.log(`📄 页面覆盖: ${testResults.coverage.pages.length}`);
    console.log(`🔌 API覆盖: ${testResults.coverage.apis.length}`);
    
    if (testResults.issues.length > 0) {
      console.log('\n🐛 发现的问题:');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

  // 1. 系统健康检查
  test('系统健康检查', async ({ page }) => {
    try {
      // 检查API服务健康状态
      const apiResponse = await page.request.get(`${TEST_CONFIG.apiURL}/api/health`);
      expect(apiResponse.status()).toBe(200);
      
      const healthData = await apiResponse.json();
      expect(healthData.success).toBe(true);
      expect(healthData.service).toBe('Smart Goose API');
      
      testResults.coverage.apis.push('/api/health');
      testResults.passed++;
      
      console.log('✅ API服务健康检查通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`API健康检查失败: ${error.message}`);
      throw error;
    }
  });

  // 2. 管理后台首页访问测试
  test('管理后台首页访问', async ({ page }) => {
    try {
      await page.goto(TEST_CONFIG.baseURL);
      
      // 检查是否重定向到登录页面
      await expect(page).toHaveURL(/.*\/auth\/login/);
      
      // 检查登录页面元素
      await expect(page.locator('h3')).toContainText('智慧养鹅SAAS');
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('button[type="submit"]')).toBeVisible();
      
      testResults.coverage.pages.push('/auth/login');
      testResults.passed++;
      
      console.log('✅ 管理后台首页访问正常，正确重定向到登录页面');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`首页访问失败: ${error.message}`);
      throw error;
    }
  });

  // 3. 用户认证模块测试
  test('用户登录功能测试', async ({ page }) => {
    try {
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      
      // 测试无效登录
      await page.fill('input[name="username"]', 'invalid');
      await page.fill('input[name="password"]', 'invalid');
      await page.click('button[type="submit"]');
      
      // 等待错误消息
      await page.waitForTimeout(1000);
      
      // 测试有效登录
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      
      // 等待重定向到仪表板
      await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
      
      // 验证仪表板页面元素
      await expect(page.locator('h1, h2, .page-title')).toBeVisible();
      
      testResults.coverage.pages.push('/dashboard');
      testResults.coverage.features.push('用户登录');
      testResults.passed++;
      
      console.log('✅ 用户登录功能测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`用户登录测试失败: ${error.message}`);
      throw error;
    }
  });

  // 4. 仪表板数据加载测试
  test('仪表板数据统计测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 检查仪表板统计数据
      await page.waitForTimeout(2000); // 等待数据加载
      
      // 查找统计卡片或数据显示元素
      const statsElements = await page.locator('.card, .stat-card, .dashboard-stat, .metric-card').count();
      expect(statsElements).toBeGreaterThan(0);
      
      testResults.coverage.features.push('仪表板统计');
      testResults.passed++;
      
      console.log('✅ 仪表板数据统计测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`仪表板数据测试失败: ${error.message}`);
      throw error;
    }
  });

  // 5. 导航菜单测试
  test('导航菜单功能测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 测试主要导航链接
      const navigationLinks = [
        { text: '用户管理', url: '/users' },
        { text: '健康管理', url: '/health' },
        { text: '生产管理', url: '/production' },
        { text: '系统设置', url: '/settings' }
      ];
      
      for (const link of navigationLinks) {
        try {
          // 查找并点击导航链接
          const navLink = page.locator(`a:has-text("${link.text}"), .nav-link:has-text("${link.text}"), .menu-item:has-text("${link.text}")`).first();
          
          if (await navLink.count() > 0) {
            await navLink.click();
            await page.waitForTimeout(1000);
            
            // 检查URL是否包含预期路径
            const currentURL = page.url();
            if (currentURL.includes(link.url)) {
              testResults.coverage.pages.push(link.url);
              console.log(`✅ 导航到 ${link.text} 成功`);
            }
          }
        } catch (navError) {
          console.log(`⚠️ 导航链接 ${link.text} 可能不存在或不可点击`);
        }
      }
      
      testResults.coverage.features.push('导航菜单');
      testResults.passed++;
      
      console.log('✅ 导航菜单功能测试完成');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`导航菜单测试失败: ${error.message}`);
      throw error;
    }
  });

  // 6. 用户管理模块测试
  test('用户管理模块测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 尝试访问用户管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForTimeout(2000);
      
      // 检查页面是否正常加载
      const pageTitle = await page.locator('h1, h2, .page-title').first();
      if (await pageTitle.count() > 0) {
        testResults.coverage.pages.push('/users');
        testResults.coverage.features.push('用户管理');
        console.log('✅ 用户管理页面访问成功');
      }
      
      testResults.passed++;
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`用户管理模块测试失败: ${error.message}`);
      console.log(`⚠️ 用户管理模块可能需要开发或修复: ${error.message}`);
    }
  });

  // 7. 响应式设计测试
  test('响应式设计测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 测试不同屏幕尺寸
      const viewports = [
        { width: 1920, height: 1080, name: '桌面大屏' },
        { width: 1366, height: 768, name: '桌面标准' },
        { width: 768, height: 1024, name: '平板' },
        { width: 375, height: 667, name: '手机' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.waitForTimeout(500);
        
        // 检查页面是否仍然可用
        const isVisible = await page.locator('body').isVisible();
        expect(isVisible).toBe(true);
        
        console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) 显示正常`);
      }
      
      testResults.coverage.features.push('响应式设计');
      testResults.passed++;
      
      console.log('✅ 响应式设计测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`响应式设计测试失败: ${error.message}`);
      throw error;
    }
  });

  // 8. 退出登录测试
  test('退出登录功能测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);

      // 查找用户下拉菜单按钮
      const userDropdown = page.locator('#navbarDropdown, .dropdown-toggle').first();

      if (await userDropdown.count() > 0) {
        // 点击用户头像展开下拉菜单
        await userDropdown.click();
        await page.waitForTimeout(500);

        // 查找退出登录链接
        const logoutLink = page.locator('#logoutBtn, a:has-text("退出登录")').first();

        if (await logoutLink.count() > 0) {
          await logoutLink.click();
          await page.waitForTimeout(2000);

          // 验证已重定向到登录页面
          await expect(page.locator('input[name="username"]')).toBeVisible();

          testResults.coverage.features.push('退出登录');
          console.log('✅ 退出登录功能正常');
        } else {
          console.log('⚠️ 未找到退出登录按钮，尝试直接访问退出URL');
          await page.goto(`${TEST_CONFIG.baseURL}/auth/logout`);
          await page.waitForTimeout(1000);
        }
      } else {
        // 尝试直接访问退出URL
        await page.goto(`${TEST_CONFIG.baseURL}/auth/logout`);
        await page.waitForTimeout(1000);
      }

      testResults.passed++;
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`退出登录测试失败: ${error.message}`);
      throw error;
    }
  });

});
