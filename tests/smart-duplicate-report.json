{"timestamp": "2025-08-27T01:37:49.477Z", "summary": {"trueDuplicates": 0, "similarFiles": 630, "totalAnalyzed": 630}, "trueDuplicates": [], "similarFiles": [{"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/middleware/auth.js"], "similarity": 82, "reason": "81.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/public/js/admin-common.js"], "similarity": 86, "reason": "85.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/api-management.js"], "similarity": 85, "reason": "84.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/auth.js"], "similarity": 82, "reason": "81.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/dashboard.js"], "similarity": 91, "reason": "90.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/goose-prices.js"], "similarity": 88, "reason": "87.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 87, "reason": "86.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/reports.js"], "similarity": 82, "reason": "81.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/routes/system.js"], "similarity": 82, "reason": "81.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/config/database.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "89.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/css/admin-custom.css", "backend/saas-admin/public/css/responsive.css"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/announcements.js"], "similarity": 89, "reason": "89.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/api-management.js"], "similarity": 85, "reason": "85.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/dashboard.js"], "similarity": 89, "reason": "88.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/goose-prices.js"], "similarity": 86, "reason": "85.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/knowledge.js"], "similarity": 90, "reason": "90.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/mall.js"], "similarity": 90, "reason": "90.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 86, "reason": "86.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/reports.js"], "similarity": 85, "reason": "85.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/system.js"], "similarity": 86, "reason": "85.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/routes/tenants.js"], "similarity": 92, "reason": "92.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/public/js/admin-common.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "88.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/api-management.js"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/dashboard.js"], "similarity": 91, "reason": "90.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/goose-prices.js"], "similarity": 91, "reason": "90.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/knowledge.js"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/mall.js"], "similarity": 91, "reason": "91.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/system.js"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/routes/tenants.js"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/announcements.js", "backend/saas-admin/server.js"], "similarity": 91, "reason": "90.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/dashboard.js"], "similarity": 90, "reason": "90.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/goose-prices.js"], "similarity": 87, "reason": "86.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/knowledge.js"], "similarity": 92, "reason": "92.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/mall.js"], "similarity": 91, "reason": "91.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 89, "reason": "89.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/reports.js"], "similarity": 88, "reason": "88.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/system.js"], "similarity": 87, "reason": "87.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/routes/tenants.js"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/api-management.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "89.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/auth.js", "backend/saas-admin/routes/reports.js"], "similarity": 80, "reason": "80.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/auth.js", "backend/saas-admin/routes/system.js"], "similarity": 82, "reason": "81.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/goose-prices.js"], "similarity": 94, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/knowledge.js"], "similarity": 92, "reason": "92.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/mall.js"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 90, "reason": "90.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/reports.js"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/system.js"], "similarity": 90, "reason": "89.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/routes/tenants.js"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/dashboard.js", "backend/saas-admin/server.js"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/finance.js", "backend/saas-admin/routes/flocks.js"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/finance.js", "backend/saas-admin/routes/health.js"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/finance.js", "backend/saas-admin/routes/inventory.js"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/finance.js", "backend/saas-admin/routes/production.js"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/flocks.js", "backend/saas-admin/routes/health.js"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/flocks.js", "backend/saas-admin/routes/inventory.js"], "similarity": 92, "reason": "92.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/flocks.js", "backend/saas-admin/routes/production.js"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/routes/knowledge.js"], "similarity": 92, "reason": "91.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/routes/mall.js"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 88, "reason": "88.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/routes/reports.js"], "similarity": 87, "reason": "87.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/routes/system.js"], "similarity": 88, "reason": "87.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/goose-prices.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "89.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/health.js", "backend/saas-admin/routes/inventory.js"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/health.js", "backend/saas-admin/routes/production.js"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/inventory.js", "backend/saas-admin/routes/production.js"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/knowledge.js", "backend/saas-admin/routes/mall.js"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/knowledge.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/knowledge.js", "backend/saas-admin/routes/tenants.js"], "similarity": 93, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/knowledge.js", "backend/saas-admin/server.js"], "similarity": 92, "reason": "91.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/mall.js", "backend/saas-admin/routes/platform-users.js"], "similarity": 92, "reason": "91.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/mall.js", "backend/saas-admin/routes/system.js"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/mall.js", "backend/saas-admin/routes/tenants.js"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/mall.js", "backend/saas-admin/server.js"], "similarity": 91, "reason": "90.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/platform-users.js", "backend/saas-admin/routes/reports.js"], "similarity": 91, "reason": "91.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/platform-users.js", "backend/saas-admin/routes/system.js"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/platform-users.js", "backend/saas-admin/routes/tenants.js"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/routes/platform-users.js", "backend/saas-admin/server.js"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/reports.js", "backend/saas-admin/routes/system.js"], "similarity": 86, "reason": "85.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/reports.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "89.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/system.js", "backend/saas-admin/server.js"], "similarity": 89, "reason": "89.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/routes/tenants.js", "backend/saas-admin/server.js"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/announcements/create.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/announcements/edit.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/announcements/index.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 91, "reason": "91.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/dashboard/index.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/flocks/index.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 93, "reason": "92.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 92, "reason": "92.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 93, "reason": "92.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 92, "reason": "91.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/create.ejs"], "similarity": 90, "reason": "90.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/edit.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/index.ejs"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 88, "reason": "88.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/dashboard/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 90, "reason": "90.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 91, "reason": "90.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 91, "reason": "91.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 95, "reason": "94.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 91, "reason": "91.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 95, "reason": "94.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 89, "reason": "89.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "93.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "92.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/announcements/index.ejs"], "similarity": 89, "reason": "88.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/api-management/index.ejs"], "similarity": 88, "reason": "87.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 85, "reason": "85.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 92, "reason": "91.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 89, "reason": "89.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 89, "reason": "89.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/mall/index.ejs"], "similarity": 89, "reason": "89.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "91.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 87, "reason": "87.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 88, "reason": "87.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 87, "reason": "87.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 87, "reason": "87.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 88, "reason": "87.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/announcements/index.ejs"], "similarity": 97, "reason": "96.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 93, "reason": "92.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/dashboard/index.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/flocks/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 99, "reason": "99.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 85, "reason": "85.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 89, "reason": "88.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 95, "reason": "94.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "91.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 93, "reason": "92.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 91, "reason": "91.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "90.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 88, "reason": "87.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 89, "reason": "88.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 88, "reason": "87.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "87.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 87, "reason": "87.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/auth/login.ejs"], "similarity": 84, "reason": "83.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/mall/index.ejs"], "similarity": 81, "reason": "81.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 88, "reason": "87.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 83, "reason": "82.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 90, "reason": "90.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 84, "reason": "84.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/goose-prices/create.ejs"], "similarity": 85, "reason": "85.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 86, "reason": "85.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 89, "reason": "89.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 90, "reason": "89.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 85, "reason": "85.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 91, "reason": "90.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/mall/index.ejs"], "similarity": 84, "reason": "83.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 89, "reason": "89.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 90, "reason": "90.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 89, "reason": "89.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 84, "reason": "84.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 87, "reason": "86.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 84, "reason": "84.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 86, "reason": "86.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 84, "reason": "84.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "92.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/auth/login.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/flocks/index.ejs"], "similarity": 96, "reason": "96.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 95, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/health/index.ejs"], "similarity": 98, "reason": "97.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 96, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 97, "reason": "96.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 97, "reason": "96.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/inventory/index.ejs"], "similarity": 98, "reason": "98.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/reports/index.ejs"], "similarity": 98, "reason": "98.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/system/index.ejs"], "similarity": 98, "reason": "98.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/health/index.ejs"], "similarity": 100, "reason": "99.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 99, "reason": "99.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/goose-prices/index.ejs"], "similarity": 90, "reason": "89.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 89, "reason": "89.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 87, "reason": "87.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 88, "reason": "87.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 87, "reason": "87.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "87.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 87, "reason": "87.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 93, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/goose-prices/trends.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 93, "reason": "92.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 92, "reason": "91.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 96, "reason": "96.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 96, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 89, "reason": "88.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 89, "reason": "88.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 89, "reason": "88.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "87.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 88, "reason": "87.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 97, "reason": "96.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/knowledge/categories.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 92, "reason": "92.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 95, "reason": "94.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "91.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 92, "reason": "91.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 90, "reason": "90.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 90, "reason": "89.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 90, "reason": "89.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 91, "reason": "91.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/goose-prices/trends.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/health/index.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 100, "reason": "99.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/health/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 97, "reason": "97.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/inventory/index.ejs", "backend/saas-admin/views/reports/index.ejs"], "similarity": 98, "reason": "98.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/inventory/index.ejs", "backend/saas-admin/views/system/index.ejs"], "similarity": 98, "reason": "97.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/knowledge/create.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 97, "reason": "96.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 93, "reason": "93.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 93, "reason": "92.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 93, "reason": "93.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 95, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/knowledge/edit.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/knowledge/index.ejs"], "similarity": 93, "reason": "92.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 91, "reason": "90.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 90, "reason": "90.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/layouts/main.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 92, "reason": "92.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 93, "reason": "92.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/mall/index.ejs"], "similarity": 83, "reason": "82.6% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 83, "reason": "83.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 88, "reason": "88.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 84, "reason": "83.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "87.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 85, "reason": "85.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/logs/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/layouts/main.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/categories.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 92, "reason": "91.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 92, "reason": "91.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 93, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "94.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "93.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/mall/inventory.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 97, "reason": "97.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/categories.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 97, "reason": "96.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 83, "reason": "83.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 88, "reason": "87.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 82, "reason": "82.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 87, "reason": "87.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 86, "reason": "86.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/mall/orders.ejs"], "similarity": 97, "reason": "97.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 96, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "95.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/inventory.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 97, "reason": "97.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/mall/products.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 94, "reason": "93.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 97, "reason": "96.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/orders.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 97, "reason": "97.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/monitoring/index.ejs"], "similarity": 92, "reason": "91.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 91, "reason": "91.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/platform-users/index.ejs"], "similarity": 90, "reason": "90.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 90, "reason": "90.0% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 91, "reason": "91.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 90, "reason": "90.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 90, "reason": "90.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "93.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/mall/products.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 96, "reason": "95.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/production/index.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 91, "reason": "91.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 91, "reason": "90.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "92.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/pricing/index.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 91, "reason": "90.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 91, "reason": "90.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 90, "reason": "89.9% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 83, "reason": "83.2% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 91, "reason": "90.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 85, "reason": "84.7% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/system/backup.ejs"], "similarity": 93, "reason": "93.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 93, "reason": "92.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 96, "reason": "96.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "93.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 93, "reason": "93.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "95.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/reports/index.ejs", "backend/saas-admin/views/system/index.ejs"], "similarity": 98, "reason": "98.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/system/logs.ejs"], "similarity": 89, "reason": "88.8% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "87.5% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 88, "reason": "88.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/tenants/create.ejs"], "similarity": 92, "reason": "92.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 93, "reason": "93.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 92, "reason": "92.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/system/backup.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 92, "reason": "91.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/system/logs.ejs", "backend/saas-admin/views/system/monitoring.ejs"], "similarity": 88, "reason": "88.4% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/logs.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 85, "reason": "85.1% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/monitoring.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"], "similarity": 87, "reason": "87.3% 相似度", "recommendation": "检查是否可以复用代码"}, {"files": ["backend/saas-admin/views/system/monitoring.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 92, "reason": "92.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenant-stats/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 93, "reason": "92.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/tenants/details.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/create.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "similarity": 95, "reason": "95.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "94.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "93.9% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/details.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "94.5% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/edit.ejs", "backend/saas-admin/views/tenants/index.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/edit.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 96, "reason": "95.8% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/edit.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 96, "reason": "95.7% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/edit.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "95.4% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/edit.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/tenants/subscriptions.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "93.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "94.6% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 94, "reason": "94.2% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/subscriptions.ejs", "backend/saas-admin/views/tenants/usage.ejs"], "similarity": 94, "reason": "94.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/subscriptions.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 95, "reason": "95.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/subscriptions.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 95, "reason": "95.0% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/usage.ejs", "backend/saas-admin/views/users/index.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/tenants/usage.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "96.3% 相似度", "recommendation": "考虑重构为通用组件"}, {"files": ["backend/saas-admin/views/users/index.ejs", "backend/saas-admin/views/users/profile.ejs"], "similarity": 96, "reason": "96.1% 相似度", "recommendation": "考虑重构为通用组件"}]}