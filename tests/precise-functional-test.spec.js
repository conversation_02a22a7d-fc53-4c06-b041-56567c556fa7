const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 精确功能测试
 * 基于实际页面结构的精确测试
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  },
  timeout: 30000
};

// 测试结果记录
let testResults = {
  modules: {},
  issues: [],
  screenshots: [],
  totalTests: 0,
  passedTests: 0,
  failedTests: 0
};

// 登录辅助函数
async function loginToSystem(page) {
  console.log('🔐 开始登录系统...');
  
  await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
  await page.waitForLoadState('networkidle');
  
  await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
  await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
  await page.click('button[type="submit"]');
  await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  
  console.log('✅ 登录成功');
  return true;
}

// 截图辅助函数
async function takeScreenshot(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${name}-${timestamp}.png`;
  
  await page.screenshot({ 
    path: `test-results/screenshots/${filename}`,
    fullPage: true 
  });
  
  testResults.screenshots.push({
    name: filename,
    description: description,
    timestamp: new Date().toISOString()
  });
  
  console.log(`📸 截图已保存: ${filename}`);
}

// 记录测试结果
function recordTestResult(module, testName, passed, details = '') {
  if (!testResults.modules[module]) {
    testResults.modules[module] = { passed: 0, failed: 0, tests: [] };
  }
  
  testResults.modules[module].tests.push({
    name: testName,
    passed: passed,
    details: details,
    timestamp: new Date().toISOString()
  });
  
  if (passed) {
    testResults.modules[module].passed++;
    testResults.passedTests++;
  } else {
    testResults.modules[module].failed++;
    testResults.failedTests++;
    testResults.issues.push(`${module} - ${testName}: ${details}`);
  }
  
  testResults.totalTests++;
}

test.describe('智慧养鹅后台管理中心 - 精确功能测试', () => {
  
  test.beforeAll(async () => {
    console.log('🚀 开始精确功能测试');
    
    // 创建截图目录
    const fs = require('fs');
    const path = require('path');
    const screenshotDir = path.join(process.cwd(), 'test-results', 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  });

  // 1. 健康管理模块精确测试
  test('健康管理模块 - 精确功能验证', async ({ page }) => {
    console.log('\n🏥 开始健康管理模块精确测试...');
    
    try {
      await loginToSystem(page);
      
      // 导航到健康管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/health`);
      await page.waitForLoadState('networkidle');
      await takeScreenshot(page, 'health-precise', '健康管理页面精确截图');
      
      // 等待页面完全加载
      await page.waitForTimeout(2000);
      
      // 检查页面标题
      const pageTitle = await page.locator('h1, h2, .content-header h1, .page-title').first().textContent();
      console.log(`📋 页面标题: ${pageTitle}`);
      
      // 检查健康记录表格 - 基于实际EJS模板结构
      console.log('📊 检查健康记录表格...');
      const healthTable = page.locator('#healthRecordsTable, .health-records-table, table');
      const tableExists = await healthTable.count() > 0;
      
      if (tableExists) {
        recordTestResult('健康管理', '健康记录表格', true, '健康记录表格存在');
        console.log('✅ 健康记录表格存在');
        
        // 检查表格内容
        const tableRows = await healthTable.locator('tbody tr').count();
        console.log(`📊 表格行数: ${tableRows}`);
        
        if (tableRows > 0) {
          recordTestResult('健康管理', '健康记录数据', true, `找到${tableRows}条健康记录`);
          console.log(`✅ 找到${tableRows}条健康记录`);
        } else {
          recordTestResult('健康管理', '健康记录数据', true, '健康记录表格为空（正常状态）');
          console.log('ℹ️ 健康记录表格为空（可能是正常状态）');
        }
      } else {
        recordTestResult('健康管理', '健康记录表格', false, '未找到健康记录表格');
        console.log('❌ 未找到健康记录表格');
      }
      
      // 检查添加按钮 - 基于实际页面结构
      console.log('➕ 检查添加健康记录按钮...');
      const addButtons = page.locator('button:has-text("添加"), button:has-text("新增"), button:has-text("记录"), .btn-primary, #addInspectionBtn, #addVaccinationBtn, #addTreatmentBtn');
      const addButtonCount = await addButtons.count();
      
      if (addButtonCount > 0) {
        recordTestResult('健康管理', '添加功能按钮', true, `找到${addButtonCount}个添加按钮`);
        console.log(`✅ 找到${addButtonCount}个添加按钮`);
        
        // 测试点击第一个添加按钮
        try {
          await addButtons.first().click();
          await page.waitForTimeout(1000);
          
          // 检查是否打开了模态框
          const modal = page.locator('.modal.show, .modal.fade.show, .modal:visible');
          const modalVisible = await modal.count() > 0;
          
          if (modalVisible) {
            recordTestResult('健康管理', '添加记录模态框', true, '添加记录模态框正常打开');
            console.log('✅ 添加记录模态框正常打开');
            await takeScreenshot(page, 'health-add-modal', '健康记录添加模态框');
            
            // 关闭模态框
            const closeBtn = page.locator('.modal .btn-close, .modal .close, button:has-text("取消")');
            if (await closeBtn.count() > 0) {
              await closeBtn.first().click();
              await page.waitForTimeout(500);
            }
          } else {
            recordTestResult('健康管理', '添加记录模态框', false, '添加记录模态框未正常打开');
            console.log('❌ 添加记录模态框未正常打开');
          }
        } catch (clickError) {
          recordTestResult('健康管理', '添加按钮点击', false, `添加按钮点击失败: ${clickError.message}`);
          console.log(`❌ 添加按钮点击失败: ${clickError.message}`);
        }
      } else {
        recordTestResult('健康管理', '添加功能按钮', false, '未找到添加按钮');
        console.log('❌ 未找到添加按钮');
      }
      
      // 检查筛选功能
      console.log('🔍 检查筛选功能...');
      const filterElements = page.locator('#recordTypeFilter, #flockFilter, #statusFilter, select, input[type="search"]');
      const filterCount = await filterElements.count();
      
      if (filterCount > 0) {
        recordTestResult('健康管理', '筛选功能', true, `找到${filterCount}个筛选元素`);
        console.log(`✅ 找到${filterCount}个筛选元素`);
      } else {
        recordTestResult('健康管理', '筛选功能', false, '未找到筛选元素');
        console.log('⚠️ 未找到筛选元素');
      }
      
    } catch (error) {
      recordTestResult('健康管理', '模块测试', false, `测试异常: ${error.message}`);
      console.log(`❌ 健康管理模块测试异常: ${error.message}`);
      await takeScreenshot(page, 'health-error', '健康管理模块错误');
    }
  });

  // 2. 生产管理模块精确测试
  test('生产管理模块 - 精确功能验证', async ({ page }) => {
    console.log('\n🏭 开始生产管理模块精确测试...');
    
    try {
      await loginToSystem(page);
      
      // 导航到生产管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/production`);
      await page.waitForLoadState('networkidle');
      await takeScreenshot(page, 'production-precise', '生产管理页面精确截图');
      
      // 等待页面完全加载
      await page.waitForTimeout(2000);
      
      // 检查页面标题
      const pageTitle = await page.locator('h1, h2, .content-header h1, .page-title').first().textContent();
      console.log(`📋 页面标题: ${pageTitle}`);
      
      // 检查生产记录表格
      console.log('📊 检查生产记录表格...');
      const productionTable = page.locator('#productionTable, .production-table, table');
      const tableExists = await productionTable.count() > 0;
      
      if (tableExists) {
        recordTestResult('生产管理', '生产记录表格', true, '生产记录表格存在');
        console.log('✅ 生产记录表格存在');
        
        // 检查表格内容
        const tableRows = await productionTable.locator('tbody tr').count();
        console.log(`📊 表格行数: ${tableRows}`);
        
        if (tableRows > 0) {
          recordTestResult('生产管理', '生产记录数据', true, `找到${tableRows}条生产记录`);
          console.log(`✅ 找到${tableRows}条生产记录`);
        } else {
          recordTestResult('生产管理', '生产记录数据', true, '生产记录表格为空（正常状态）');
          console.log('ℹ️ 生产记录表格为空（可能是正常状态）');
        }
      } else {
        recordTestResult('生产管理', '生产记录表格', false, '未找到生产记录表格');
        console.log('❌ 未找到生产记录表格');
      }
      
      // 检查添加生产记录按钮
      console.log('➕ 检查添加生产记录按钮...');
      const addButtons = page.locator('button:has-text("添加"), button:has-text("新增"), button:has-text("录入"), .btn-primary, a[href*="create"]');
      const addButtonCount = await addButtons.count();
      
      if (addButtonCount > 0) {
        recordTestResult('生产管理', '添加功能按钮', true, `找到${addButtonCount}个添加按钮`);
        console.log(`✅ 找到${addButtonCount}个添加按钮`);
        
        // 测试点击添加按钮
        try {
          await addButtons.first().click();
          await page.waitForTimeout(1000);
          
          // 检查是否跳转到创建页面或打开模态框
          const currentUrl = page.url();
          const isCreatePage = currentUrl.includes('create') || currentUrl.includes('add');
          const modal = page.locator('.modal.show, .modal.fade.show, .modal:visible');
          const modalVisible = await modal.count() > 0;
          
          if (isCreatePage || modalVisible) {
            recordTestResult('生产管理', '添加记录界面', true, '添加记录界面正常打开');
            console.log('✅ 添加记录界面正常打开');
            await takeScreenshot(page, 'production-add', '生产记录添加界面');
          } else {
            recordTestResult('生产管理', '添加记录界面', false, '添加记录界面未正常打开');
            console.log('❌ 添加记录界面未正常打开');
          }
        } catch (clickError) {
          recordTestResult('生产管理', '添加按钮点击', false, `添加按钮点击失败: ${clickError.message}`);
          console.log(`❌ 添加按钮点击失败: ${clickError.message}`);
        }
      } else {
        recordTestResult('生产管理', '添加功能按钮', false, '未找到添加按钮');
        console.log('❌ 未找到添加按钮');
      }
      
    } catch (error) {
      recordTestResult('生产管理', '模块测试', false, `测试异常: ${error.message}`);
      console.log(`❌ 生产管理模块测试异常: ${error.message}`);
      await takeScreenshot(page, 'production-error', '生产管理模块错误');
    }
  });

  // 3. 用户管理模块修复测试
  test('用户管理模块 - 修复后验证', async ({ page }) => {
    console.log('\n👥 开始用户管理模块修复验证...');
    
    try {
      await loginToSystem(page);
      
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForLoadState('networkidle');
      await takeScreenshot(page, 'users-fixed', '用户管理页面修复后');
      
      // 检查用户表格
      const userTable = page.locator('table, .table, .user-table');
      if (await userTable.count() > 0) {
        recordTestResult('用户管理', '用户表格显示', true, '用户表格正常显示');
        console.log('✅ 用户表格正常显示');
        
        // 检查操作按钮
        const actionButtons = page.locator('button:has-text("编辑"), button:has-text("删除"), .btn-edit, .btn-delete');
        const actionCount = await actionButtons.count();
        
        if (actionCount > 0) {
          recordTestResult('用户管理', '操作按钮', true, `找到${actionCount}个操作按钮`);
          console.log(`✅ 找到${actionCount}个操作按钮`);
        } else {
          recordTestResult('用户管理', '操作按钮', false, '未找到操作按钮');
          console.log('⚠️ 未找到操作按钮');
        }
      } else {
        recordTestResult('用户管理', '用户表格显示', false, '未找到用户表格');
        console.log('❌ 未找到用户表格');
      }
      
    } catch (error) {
      recordTestResult('用户管理', '修复验证', false, `验证异常: ${error.message}`);
      console.log(`❌ 用户管理修复验证异常: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 生成精确测试报告...');
    
    const totalTests = testResults.totalTests;
    const passedTests = testResults.passedTests;
    const failedTests = testResults.failedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log('\n=== 精确功能测试结果汇总 ===');
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过测试: ${passedTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    console.log('\n=== 各模块测试结果 ===');
    Object.keys(testResults.modules).forEach(module => {
      const moduleResult = testResults.modules[module];
      const moduleTotal = moduleResult.passed + moduleResult.failed;
      const moduleRate = moduleTotal > 0 ? ((moduleResult.passed / moduleTotal) * 100).toFixed(1) : 0;
      console.log(`${module}: ${moduleResult.passed}/${moduleTotal} (${moduleRate}%)`);
    });
    
    if (testResults.issues.length > 0) {
      console.log('\n=== 发现的问题 ===');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    console.log(`\n📸 截图数量: ${testResults.screenshots.length}`);
  });

});
