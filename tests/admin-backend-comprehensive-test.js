/**
 * 智慧养鹅SAAS管理后台全面功能测试
 * 使用HTTP请求模拟用户操作，测试所有功能模块
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class AdminBackendTester {
    constructor() {
        this.baseURL = 'http://localhost:3003';
        this.session = null;
        this.testResults = {
            passed: 0,
            failed: 0,
            errors: [],
            moduleResults: {},
            missingPages: [],
            brokenFeatures: [],
            unusedAssets: []
        };
        
        // 定义所有需要测试的模块和页面
        this.testModules = {
            auth: {
                name: '认证系统',
                pages: ['/auth/login'],
                features: ['登录', '登出', '会话管理']
            },
            dashboard: {
                name: '仪表板',
                pages: ['/dashboard'],
                features: ['数据统计', '图表展示', '快速操作']
            },
            users: {
                name: '用户管理',
                pages: ['/users'],
                features: ['用户列表', '添加用户', '编辑用户', '删除用户', '搜索筛选']
            },
            tenants: {
                name: '租户管理',
                pages: ['/tenants', '/tenants/create', '/tenants/subscriptions'],
                features: ['租户列表', '创建租户', '编辑租户', '订阅管理']
            },
            flocks: {
                name: '鹅群管理',
                pages: ['/flocks'],
                features: ['鹅群列表', '添加鹅群', '编辑鹅群', '健康状态']
            },
            production: {
                name: '生产管理',
                pages: ['/production'],
                features: ['生产记录', '数据统计', '趋势分析']
            },
            health: {
                name: '健康管理',
                pages: ['/health'],
                features: ['健康记录', '疫苗管理', '疾病监控']
            },
            finance: {
                name: '财务管理',
                pages: ['/finance'],
                features: ['收支记录', '财务报表', '成本分析']
            },
            inventory: {
                name: '库存管理',
                pages: ['/inventory'],
                features: ['库存列表', '入库出库', '库存预警']
            },
            reports: {
                name: '报表管理',
                pages: ['/reports'],
                features: ['数据报表', '导出功能', '图表分析']
            },
            system: {
                name: '系统管理',
                pages: ['/system', '/system/backup', '/system/logs'],
                features: ['系统设置', '数据备份', '日志管理']
            },
            goosePrices: {
                name: '鹅价管理',
                pages: ['/goose-prices', '/goose-prices/trends'],
                features: ['价格管理', '趋势分析', '市场数据']
            },
            mall: {
                name: '商城管理',
                pages: ['/mall', '/mall/products', '/mall/orders', '/mall/categories'],
                features: ['商品管理', '订单管理', '分类管理']
            },
            knowledge: {
                name: '知识库管理',
                pages: ['/knowledge', '/knowledge/categories', '/knowledge/create'],
                features: ['知识管理', '分类管理', '内容编辑']
            },
            announcements: {
                name: '公告管理',
                pages: ['/announcements', '/announcements/create'],
                features: ['公告列表', '发布公告', '编辑公告']
            },
            apiManagement: {
                name: 'API管理',
                pages: ['/api-management'],
                features: ['API监控', '接口管理', '访问控制']
            },
            platformUsers: {
                name: '平台用户管理',
                pages: ['/platform-users'],
                features: ['平台用户', '权限管理', '角色分配']
            },
            aiConfig: {
                name: 'AI配置',
                pages: ['/ai-config'],
                features: ['AI设置', '模型配置', '智能分析']
            },
            tenantStats: {
                name: '租户统计',
                pages: ['/tenant-stats'],
                features: ['租户统计', '使用分析', '数据报告']
            }
        };
    }

    async login() {
        try {
            console.log('🔐 开始登录测试...');
            
            // 首先获取登录页面
            const loginPageResponse = await axios.get(`${this.baseURL}/auth/login`);
            if (loginPageResponse.status !== 200) {
                throw new Error('登录页面无法访问');
            }
            
            // 尝试登录
            const loginResponse = await axios.post(`${this.baseURL}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            }, {
                withCredentials: true,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (loginResponse.status === 200 || loginResponse.status === 302) {
                console.log('✅ 登录成功');
                this.session = loginResponse.headers['set-cookie'];
                this.testResults.passed++;
                return true;
            } else {
                throw new Error(`登录失败: ${loginResponse.status}`);
            }
        } catch (error) {
            console.error('❌ 登录失败:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push({
                module: 'auth',
                error: `登录失败: ${error.message}`
            });
            return false;
        }
    }

    async testPageAccess(url, moduleName) {
        try {
            const response = await axios.get(`${this.baseURL}${url}`, {
                headers: {
                    'Cookie': this.session ? this.session.join('; ') : ''
                },
                timeout: 10000
            });
            
            if (response.status === 200) {
                console.log(`✅ ${moduleName} - ${url} 页面正常访问`);
                return { success: true, status: response.status };
            } else {
                console.log(`⚠️  ${moduleName} - ${url} 返回状态码: ${response.status}`);
                return { success: false, status: response.status };
            }
        } catch (error) {
            console.error(`❌ ${moduleName} - ${url} 访问失败:`, error.message);
            return { success: false, error: error.message };
        }
    }

    async testModule(moduleKey, moduleConfig) {
        console.log(`\n📋 测试模块: ${moduleConfig.name}`);
        const moduleResult = {
            name: moduleConfig.name,
            pages: {},
            features: [],
            issues: []
        };

        // 测试所有页面
        for (const page of moduleConfig.pages) {
            const result = await this.testPageAccess(page, moduleConfig.name);
            moduleResult.pages[page] = result;
            
            if (result.success) {
                this.testResults.passed++;
            } else {
                this.testResults.failed++;
                if (result.status === 404) {
                    this.testResults.missingPages.push(`${moduleConfig.name}: ${page}`);
                } else {
                    this.testResults.brokenFeatures.push(`${moduleConfig.name}: ${page} - ${result.error || result.status}`);
                }
                moduleResult.issues.push(`页面 ${page} 无法访问: ${result.error || result.status}`);
            }
        }

        this.testResults.moduleResults[moduleKey] = moduleResult;
        return moduleResult;
    }

    async runAllTests() {
        console.log('🚀 开始全面功能测试...\n');
        
        // 首先登录
        const loginSuccess = await this.login();
        if (!loginSuccess) {
            console.log('❌ 登录失败，无法继续测试');
            return this.testResults;
        }

        // 测试所有模块
        for (const [moduleKey, moduleConfig] of Object.entries(this.testModules)) {
            await this.testModule(moduleKey, moduleConfig);
            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        return this.testResults;
    }

    generateReport() {
        const report = {
            summary: {
                totalTests: this.testResults.passed + this.testResults.failed,
                passed: this.testResults.passed,
                failed: this.testResults.failed,
                successRate: ((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(2) + '%'
            },
            missingPages: this.testResults.missingPages,
            brokenFeatures: this.testResults.brokenFeatures,
            moduleResults: this.testResults.moduleResults,
            errors: this.testResults.errors
        };

        return report;
    }

    async saveReport(report) {
        const reportPath = path.join(__dirname, 'admin-backend-test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📊 测试报告已保存到: ${reportPath}`);
    }
}

// 运行测试
async function runTests() {
    const tester = new AdminBackendTester();
    
    try {
        const results = await tester.runAllTests();
        const report = tester.generateReport();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 测试结果汇总');
        console.log('='.repeat(60));
        console.log(`总测试数: ${report.summary.totalTests}`);
        console.log(`通过: ${report.summary.passed}`);
        console.log(`失败: ${report.summary.failed}`);
        console.log(`成功率: ${report.summary.successRate}`);
        
        if (report.missingPages.length > 0) {
            console.log('\n❌ 缺失的页面:');
            report.missingPages.forEach(page => console.log(`  - ${page}`));
        }
        
        if (report.brokenFeatures.length > 0) {
            console.log('\n⚠️  功能问题:');
            report.brokenFeatures.forEach(feature => console.log(`  - ${feature}`));
        }
        
        await tester.saveReport(report);
        
    } catch (error) {
        console.error('测试执行失败:', error);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTests();
}

module.exports = AdminBackendTester;
