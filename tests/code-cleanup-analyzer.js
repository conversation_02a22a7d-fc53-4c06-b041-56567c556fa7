/**
 * 代码清理分析工具
 * 分析并识别未使用的CSS、JavaScript、HTML模板文件
 */

const fs = require('fs');
const path = require('path');

class CodeCleanupAnalyzer {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.adminPath = path.join(this.projectRoot, 'backend/saas-admin');
        this.results = {
            cssFiles: [],
            jsFiles: [],
            htmlFiles: [],
            unusedCss: [],
            unusedJs: [],
            unusedHtml: [],
            duplicateFiles: [],
            largeFiles: [],
            recommendations: []
        };
    }

    // 递归获取指定目录下的所有文件
    getAllFiles(dir, extensions = []) {
        const files = [];
        
        if (!fs.existsSync(dir)) {
            return files;
        }

        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                files.push(...this.getAllFiles(fullPath, extensions));
            } else if (stat.isFile()) {
                const ext = path.extname(item).toLowerCase();
                if (extensions.length === 0 || extensions.includes(ext)) {
                    files.push({
                        path: fullPath,
                        relativePath: path.relative(this.projectRoot, fullPath),
                        name: item,
                        size: stat.size,
                        extension: ext
                    });
                }
            }
        }
        
        return files;
    }

    // 分析CSS文件
    analyzeCssFiles() {
        console.log('🎨 分析CSS文件...');
        
        const cssFiles = this.getAllFiles(this.adminPath, ['.css']);
        this.results.cssFiles = cssFiles;
        
        console.log(`找到 ${cssFiles.length} 个CSS文件`);
        
        // 获取所有HTML/EJS文件内容
        const htmlFiles = this.getAllFiles(this.adminPath, ['.html', '.ejs']);
        let allHtmlContent = '';
        
        htmlFiles.forEach(file => {
            try {
                allHtmlContent += fs.readFileSync(file.path, 'utf8') + '\n';
            } catch (error) {
                console.warn(`无法读取文件: ${file.relativePath}`);
            }
        });

        // 检查CSS文件是否被引用
        cssFiles.forEach(cssFile => {
            const fileName = path.basename(cssFile.name, '.css');
            const isReferenced = allHtmlContent.includes(cssFile.name) || 
                               allHtmlContent.includes(fileName) ||
                               allHtmlContent.includes(cssFile.relativePath);
            
            if (!isReferenced) {
                this.results.unusedCss.push({
                    ...cssFile,
                    reason: '未在HTML/EJS文件中找到引用'
                });
            }

            // 检查大文件
            if (cssFile.size > 100000) { // 100KB
                this.results.largeFiles.push({
                    ...cssFile,
                    type: 'CSS',
                    sizeKB: Math.round(cssFile.size / 1024)
                });
            }
        });
    }

    // 分析JavaScript文件
    analyzeJsFiles() {
        console.log('📜 分析JavaScript文件...');
        
        const jsFiles = this.getAllFiles(this.adminPath, ['.js']);
        this.results.jsFiles = jsFiles;
        
        console.log(`找到 ${jsFiles.length} 个JavaScript文件`);
        
        // 获取所有HTML/EJS文件内容
        const htmlFiles = this.getAllFiles(this.adminPath, ['.html', '.ejs']);
        let allHtmlContent = '';
        
        htmlFiles.forEach(file => {
            try {
                allHtmlContent += fs.readFileSync(file.path, 'utf8') + '\n';
            } catch (error) {
                console.warn(`无法读取文件: ${file.relativePath}`);
            }
        });

        // 检查JS文件是否被引用
        jsFiles.forEach(jsFile => {
            const fileName = path.basename(jsFile.name, '.js');
            const isReferenced = allHtmlContent.includes(jsFile.name) || 
                               allHtmlContent.includes(fileName) ||
                               allHtmlContent.includes(jsFile.relativePath);
            
            if (!isReferenced && !jsFile.relativePath.includes('server.js') && 
                !jsFile.relativePath.includes('config') && 
                !jsFile.relativePath.includes('routes')) {
                this.results.unusedJs.push({
                    ...jsFile,
                    reason: '未在HTML/EJS文件中找到引用'
                });
            }

            // 检查大文件
            if (jsFile.size > 200000) { // 200KB
                this.results.largeFiles.push({
                    ...jsFile,
                    type: 'JavaScript',
                    sizeKB: Math.round(jsFile.size / 1024)
                });
            }
        });
    }

    // 分析HTML/EJS模板文件
    analyzeHtmlFiles() {
        console.log('📄 分析HTML/EJS模板文件...');
        
        const htmlFiles = this.getAllFiles(this.adminPath, ['.html', '.ejs']);
        this.results.htmlFiles = htmlFiles;
        
        console.log(`找到 ${htmlFiles.length} 个模板文件`);
        
        // 读取路由文件来检查哪些模板被使用
        const routeFiles = this.getAllFiles(path.join(this.adminPath, 'routes'), ['.js']);
        let allRouteContent = '';
        
        routeFiles.forEach(file => {
            try {
                allRouteContent += fs.readFileSync(file.path, 'utf8') + '\n';
            } catch (error) {
                console.warn(`无法读取路由文件: ${file.relativePath}`);
            }
        });

        // 检查模板文件是否被路由引用
        htmlFiles.forEach(htmlFile => {
            const templateName = path.basename(htmlFile.name, path.extname(htmlFile.name));
            const relativePath = path.relative(path.join(this.adminPath, 'views'), htmlFile.path);
            const templatePath = relativePath.replace(/\\/g, '/').replace(/\.(html|ejs)$/, '');
            
            const isReferenced = allRouteContent.includes(`'${templateName}'`) ||
                               allRouteContent.includes(`"${templateName}"`) ||
                               allRouteContent.includes(`'${templatePath}'`) ||
                               allRouteContent.includes(`"${templatePath}"`) ||
                               templateName === 'main' || // 主布局文件
                               templateName === 'layout' || // 布局文件
                               htmlFile.relativePath.includes('layouts'); // 布局目录
            
            if (!isReferenced) {
                this.results.unusedHtml.push({
                    ...htmlFile,
                    templateName,
                    templatePath,
                    reason: '未在路由文件中找到引用'
                });
            }
        });
    }

    // 查找重复文件
    findDuplicateFiles() {
        console.log('🔍 查找重复文件...');
        
        const allFiles = [
            ...this.results.cssFiles,
            ...this.results.jsFiles,
            ...this.results.htmlFiles
        ];
        
        const filesByName = {};
        
        allFiles.forEach(file => {
            const name = file.name.toLowerCase();
            if (!filesByName[name]) {
                filesByName[name] = [];
            }
            filesByName[name].push(file);
        });
        
        Object.entries(filesByName).forEach(([name, files]) => {
            if (files.length > 1) {
                this.results.duplicateFiles.push({
                    name,
                    files: files.map(f => f.relativePath),
                    count: files.length
                });
            }
        });
    }

    // 生成清理建议
    generateRecommendations() {
        console.log('💡 生成清理建议...');
        
        if (this.results.unusedCss.length > 0) {
            this.results.recommendations.push({
                type: 'CSS清理',
                priority: 'medium',
                description: `发现 ${this.results.unusedCss.length} 个未使用的CSS文件，可以安全删除`,
                files: this.results.unusedCss.map(f => f.relativePath)
            });
        }
        
        if (this.results.unusedJs.length > 0) {
            this.results.recommendations.push({
                type: 'JavaScript清理',
                priority: 'medium',
                description: `发现 ${this.results.unusedJs.length} 个未使用的JavaScript文件，建议检查后删除`,
                files: this.results.unusedJs.map(f => f.relativePath)
            });
        }
        
        if (this.results.unusedHtml.length > 0) {
            this.results.recommendations.push({
                type: 'HTML模板清理',
                priority: 'low',
                description: `发现 ${this.results.unusedHtml.length} 个未使用的HTML模板，建议检查后删除`,
                files: this.results.unusedHtml.map(f => f.relativePath)
            });
        }
        
        if (this.results.duplicateFiles.length > 0) {
            this.results.recommendations.push({
                type: '重复文件清理',
                priority: 'high',
                description: `发现 ${this.results.duplicateFiles.length} 组重复文件，建议合并或删除`,
                files: this.results.duplicateFiles
            });
        }
        
        if (this.results.largeFiles.length > 0) {
            this.results.recommendations.push({
                type: '大文件优化',
                priority: 'medium',
                description: `发现 ${this.results.largeFiles.length} 个大文件，建议优化或压缩`,
                files: this.results.largeFiles.map(f => `${f.relativePath} (${f.sizeKB}KB)`)
            });
        }
    }

    // 运行完整分析
    async runAnalysis() {
        console.log('🚀 开始代码清理分析...\n');
        
        this.analyzeCssFiles();
        this.analyzeJsFiles();
        this.analyzeHtmlFiles();
        this.findDuplicateFiles();
        this.generateRecommendations();
        
        return this.results;
    }

    // 生成报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalCssFiles: this.results.cssFiles.length,
                totalJsFiles: this.results.jsFiles.length,
                totalHtmlFiles: this.results.htmlFiles.length,
                unusedCssFiles: this.results.unusedCss.length,
                unusedJsFiles: this.results.unusedJs.length,
                unusedHtmlFiles: this.results.unusedHtml.length,
                duplicateFileGroups: this.results.duplicateFiles.length,
                largeFiles: this.results.largeFiles.length,
                totalRecommendations: this.results.recommendations.length
            },
            details: this.results,
            cleanupPlan: {
                highPriority: this.results.recommendations.filter(r => r.priority === 'high'),
                mediumPriority: this.results.recommendations.filter(r => r.priority === 'medium'),
                lowPriority: this.results.recommendations.filter(r => r.priority === 'low')
            }
        };
        
        return report;
    }

    // 保存报告
    async saveReport(report) {
        const reportPath = path.join(__dirname, 'code-cleanup-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📊 代码清理报告已保存到: ${reportPath}`);
    }
}

// 运行分析
async function runCleanupAnalysis() {
    const analyzer = new CodeCleanupAnalyzer();
    
    try {
        const results = await analyzer.runAnalysis();
        const report = analyzer.generateReport();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 代码清理分析结果');
        console.log('='.repeat(60));
        console.log(`CSS文件总数: ${report.summary.totalCssFiles}`);
        console.log(`JavaScript文件总数: ${report.summary.totalJsFiles}`);
        console.log(`HTML模板文件总数: ${report.summary.totalHtmlFiles}`);
        console.log(`未使用的CSS文件: ${report.summary.unusedCssFiles}`);
        console.log(`未使用的JavaScript文件: ${report.summary.unusedJsFiles}`);
        console.log(`未使用的HTML模板: ${report.summary.unusedHtmlFiles}`);
        console.log(`重复文件组: ${report.summary.duplicateFileGroups}`);
        console.log(`大文件数量: ${report.summary.largeFiles}`);
        
        console.log('\n🎯 清理建议:');
        report.details.recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.description}`);
        });
        
        await analyzer.saveReport(report);
        
    } catch (error) {
        console.error('代码清理分析失败:', error);
    }
}

if (require.main === module) {
    runCleanupAnalysis();
}

module.exports = CodeCleanupAnalyzer;
