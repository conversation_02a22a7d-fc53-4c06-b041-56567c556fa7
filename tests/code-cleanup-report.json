{"timestamp": "2025-08-27T01:25:04.167Z", "summary": {"totalCssFiles": 2, "totalJsFiles": 26, "totalHtmlFiles": 48, "unusedCssFiles": 0, "unusedJsFiles": 4, "unusedHtmlFiles": 8, "duplicateFileGroups": 5, "largeFiles": 0, "totalRecommendations": 3}, "details": {"cssFiles": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/css/admin-custom.css", "relativePath": "backend/saas-admin/public/css/admin-custom.css", "name": "admin-custom.css", "size": 10962, "extension": ".css"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/css/responsive.css", "relativePath": "backend/saas-admin/public/css/responsive.css", "name": "responsive.css", "size": 6695, "extension": ".css"}], "jsFiles": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/config/database.js", "relativePath": "backend/saas-admin/config/database.js", "name": "database.js", "size": 5921, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/create-test-user.js", "relativePath": "backend/saas-admin/create-test-user.js", "name": "create-test-user.js", "size": 1630, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/middleware/auth.js", "relativePath": "backend/saas-admin/middleware/auth.js", "name": "auth.js", "size": 3153, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/admin-common.js", "relativePath": "backend/saas-admin/public/js/admin-common.js", "name": "admin-common.js", "size": 9974, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/error-handler.js", "relativePath": "backend/saas-admin/public/js/error-handler.js", "name": "error-handler.js", "size": 15108, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/ui-feedback.js", "relativePath": "backend/saas-admin/public/js/ui-feedback.js", "name": "ui-feedback.js", "size": 13202, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/announcements.js", "relativePath": "backend/saas-admin/routes/announcements.js", "name": "announcements.js", "size": 13378, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/api-management.js", "relativePath": "backend/saas-admin/routes/api-management.js", "name": "api-management.js", "size": 9555, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/api.js", "relativePath": "backend/saas-admin/routes/api.js", "name": "api.js", "size": 11523, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/auth.js", "relativePath": "backend/saas-admin/routes/auth.js", "name": "auth.js", "size": 4078, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/dashboard.js", "relativePath": "backend/saas-admin/routes/dashboard.js", "name": "dashboard.js", "size": 11111, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/finance.js", "relativePath": "backend/saas-admin/routes/finance.js", "name": "finance.js", "size": 247, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/flocks.js", "relativePath": "backend/saas-admin/routes/flocks.js", "name": "flocks.js", "size": 245, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/goose-prices.js", "relativePath": "backend/saas-admin/routes/goose-prices.js", "name": "goose-prices.js", "size": 9173, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/health.js", "relativePath": "backend/saas-admin/routes/health.js", "name": "health.js", "size": 245, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/inventory.js", "relativePath": "backend/saas-admin/routes/inventory.js", "name": "inventory.js", "size": 251, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/knowledge.js", "relativePath": "backend/saas-admin/routes/knowledge.js", "name": "knowledge.js", "size": 15150, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/mall.js", "relativePath": "backend/saas-admin/routes/mall.js", "name": "mall.js", "size": 13077, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/platform-users.js", "relativePath": "backend/saas-admin/routes/platform-users.js", "name": "platform-users.js", "size": 10703, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/production.js", "relativePath": "backend/saas-admin/routes/production.js", "name": "production.js", "size": 253, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/reports.js", "relativePath": "backend/saas-admin/routes/reports.js", "name": "reports.js", "size": 6455, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/system.js", "relativePath": "backend/saas-admin/routes/system.js", "name": "system.js", "size": 6803, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/tenants.js", "relativePath": "backend/saas-admin/routes/tenants.js", "name": "tenants.js", "size": 18353, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/users.js", "relativePath": "backend/saas-admin/routes/users.js", "name": "users.js", "size": 2681, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/server.js", "relativePath": "backend/saas-admin/server.js", "name": "server.js", "size": 9227, "extension": ".js"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/setup-admin.js", "relativePath": "backend/saas-admin/setup-admin.js", "name": "setup-admin.js", "size": 2914, "extension": ".js"}], "htmlFiles": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/test-integration.html", "relativePath": "backend/saas-admin/test-integration.html", "name": "test-integration.html", "size": 1497, "extension": ".html"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai/index.ejs", "relativePath": "backend/saas-admin/views/ai/index.ejs", "name": "index.ejs", "size": 16275, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai-config/index.ejs", "relativePath": "backend/saas-admin/views/ai-config/index.ejs", "name": "index.ejs", "size": 13771, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/create.ejs", "relativePath": "backend/saas-admin/views/announcements/create.ejs", "name": "create.ejs", "size": 8322, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/edit.ejs", "relativePath": "backend/saas-admin/views/announcements/edit.ejs", "name": "edit.ejs", "size": 19390, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/index.ejs", "relativePath": "backend/saas-admin/views/announcements/index.ejs", "name": "index.ejs", "size": 11271, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/api-management/index.ejs", "relativePath": "backend/saas-admin/views/api-management/index.ejs", "name": "index.ejs", "size": 5025, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/auth/login.ejs", "relativePath": "backend/saas-admin/views/auth/login.ejs", "name": "login.ejs", "size": 9851, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/dashboard/index.ejs", "relativePath": "backend/saas-admin/views/dashboard/index.ejs", "name": "index.ejs", "size": 24164, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/error.ejs", "relativePath": "backend/saas-admin/views/error.ejs", "name": "error.ejs", "size": 1861, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/finance/index.ejs", "relativePath": "backend/saas-admin/views/finance/index.ejs", "name": "index.ejs", "size": 640, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/flocks/index.ejs", "relativePath": "backend/saas-admin/views/flocks/index.ejs", "name": "index.ejs", "size": 28999, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/create.ejs", "relativePath": "backend/saas-admin/views/goose-prices/create.ejs", "name": "create.ejs", "size": 10838, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/index.ejs", "relativePath": "backend/saas-admin/views/goose-prices/index.ejs", "name": "index.ejs", "size": 12118, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/trends.ejs", "relativePath": "backend/saas-admin/views/goose-prices/trends.ejs", "name": "trends.ejs", "size": 13918, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/health/index.ejs", "relativePath": "backend/saas-admin/views/health/index.ejs", "name": "index.ejs", "size": 47727, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/inventory/index.ejs", "relativePath": "backend/saas-admin/views/inventory/index.ejs", "name": "index.ejs", "size": 642, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/categories.ejs", "relativePath": "backend/saas-admin/views/knowledge/categories.ejs", "name": "categories.ejs", "size": 17904, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/create.ejs", "relativePath": "backend/saas-admin/views/knowledge/create.ejs", "name": "create.ejs", "size": 14024, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/edit.ejs", "relativePath": "backend/saas-admin/views/knowledge/edit.ejs", "name": "edit.ejs", "size": 17411, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/index.ejs", "relativePath": "backend/saas-admin/views/knowledge/index.ejs", "name": "index.ejs", "size": 7347, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/footer.ejs", "relativePath": "backend/saas-admin/views/layouts/footer.ejs", "name": "footer.ejs", "size": 103, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs", "relativePath": "backend/saas-admin/views/layouts/main.ejs", "name": "main.ejs", "size": 23140, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/logs/index.ejs", "relativePath": "backend/saas-admin/views/logs/index.ejs", "name": "index.ejs", "size": 16176, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/categories.ejs", "relativePath": "backend/saas-admin/views/mall/categories.ejs", "name": "categories.ejs", "size": 22029, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/index.ejs", "relativePath": "backend/saas-admin/views/mall/index.ejs", "name": "index.ejs", "size": 5302, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/inventory.ejs", "relativePath": "backend/saas-admin/views/mall/inventory.ejs", "name": "inventory.ejs", "size": 22523, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/orders.ejs", "relativePath": "backend/saas-admin/views/mall/orders.ejs", "name": "orders.ejs", "size": 21710, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/products.ejs", "relativePath": "backend/saas-admin/views/mall/products.ejs", "name": "products.ejs", "size": 12107, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/monitoring/index.ejs", "relativePath": "backend/saas-admin/views/monitoring/index.ejs", "name": "index.ejs", "size": 16778, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/plans/index.ejs", "relativePath": "backend/saas-admin/views/plans/index.ejs", "name": "index.ejs", "size": 15251, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/platform-users/index.ejs", "relativePath": "backend/saas-admin/views/platform-users/index.ejs", "name": "index.ejs", "size": 7205, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/pricing/index.ejs", "relativePath": "backend/saas-admin/views/pricing/index.ejs", "name": "index.ejs", "size": 16322, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/production/index.ejs", "relativePath": "backend/saas-admin/views/production/index.ejs", "name": "index.ejs", "size": 32647, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/reports/index.ejs", "relativePath": "backend/saas-admin/views/reports/index.ejs", "name": "index.ejs", "size": 640, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/backup.ejs", "relativePath": "backend/saas-admin/views/system/backup.ejs", "name": "backup.ejs", "size": 10022, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/index.ejs", "relativePath": "backend/saas-admin/views/system/index.ejs", "name": "index.ejs", "size": 639, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/logs.ejs", "relativePath": "backend/saas-admin/views/system/logs.ejs", "name": "logs.ejs", "size": 6099, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/monitoring.ejs", "relativePath": "backend/saas-admin/views/system/monitoring.ejs", "name": "monitoring.ejs", "size": 9133, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenant-stats/index.ejs", "relativePath": "backend/saas-admin/views/tenant-stats/index.ejs", "name": "index.ejs", "size": 9137, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/create.ejs", "relativePath": "backend/saas-admin/views/tenants/create.ejs", "name": "create.ejs", "size": 19494, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/details.ejs", "relativePath": "backend/saas-admin/views/tenants/details.ejs", "name": "details.ejs", "size": 30544, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/edit.ejs", "relativePath": "backend/saas-admin/views/tenants/edit.ejs", "name": "edit.ejs", "size": 23063, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/index.ejs", "relativePath": "backend/saas-admin/views/tenants/index.ejs", "name": "index.ejs", "size": 18687, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/subscriptions.ejs", "relativePath": "backend/saas-admin/views/tenants/subscriptions.ejs", "name": "subscriptions.ejs", "size": 16236, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/usage.ejs", "relativePath": "backend/saas-admin/views/tenants/usage.ejs", "name": "usage.ejs", "size": 18541, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/users/index.ejs", "relativePath": "backend/saas-admin/views/users/index.ejs", "name": "index.ejs", "size": 21978, "extension": ".ejs"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/users/profile.ejs", "relativePath": "backend/saas-admin/views/users/profile.ejs", "name": "profile.ejs", "size": 23567, "extension": ".ejs"}], "unusedCss": [], "unusedJs": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/create-test-user.js", "relativePath": "backend/saas-admin/create-test-user.js", "name": "create-test-user.js", "size": 1630, "extension": ".js", "reason": "未在HTML/EJS文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/error-handler.js", "relativePath": "backend/saas-admin/public/js/error-handler.js", "name": "error-handler.js", "size": 15108, "extension": ".js", "reason": "未在HTML/EJS文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/ui-feedback.js", "relativePath": "backend/saas-admin/public/js/ui-feedback.js", "name": "ui-feedback.js", "size": 13202, "extension": ".js", "reason": "未在HTML/EJS文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/setup-admin.js", "relativePath": "backend/saas-admin/setup-admin.js", "name": "setup-admin.js", "size": 2914, "extension": ".js", "reason": "未在HTML/EJS文件中找到引用"}], "unusedHtml": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/test-integration.html", "relativePath": "backend/saas-admin/test-integration.html", "name": "test-integration.html", "size": 1497, "extension": ".html", "templateName": "test-integration", "templatePath": "../test-integration", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai/index.ejs", "relativePath": "backend/saas-admin/views/ai/index.ejs", "name": "index.ejs", "size": 16275, "extension": ".ejs", "templateName": "index", "templatePath": "ai/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai-config/index.ejs", "relativePath": "backend/saas-admin/views/ai-config/index.ejs", "name": "index.ejs", "size": 13771, "extension": ".ejs", "templateName": "index", "templatePath": "ai-config/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/logs/index.ejs", "relativePath": "backend/saas-admin/views/logs/index.ejs", "name": "index.ejs", "size": 16176, "extension": ".ejs", "templateName": "index", "templatePath": "logs/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/monitoring/index.ejs", "relativePath": "backend/saas-admin/views/monitoring/index.ejs", "name": "index.ejs", "size": 16778, "extension": ".ejs", "templateName": "index", "templatePath": "monitoring/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/plans/index.ejs", "relativePath": "backend/saas-admin/views/plans/index.ejs", "name": "index.ejs", "size": 15251, "extension": ".ejs", "templateName": "index", "templatePath": "plans/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/pricing/index.ejs", "relativePath": "backend/saas-admin/views/pricing/index.ejs", "name": "index.ejs", "size": 16322, "extension": ".ejs", "templateName": "index", "templatePath": "pricing/index", "reason": "未在路由文件中找到引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenant-stats/index.ejs", "relativePath": "backend/saas-admin/views/tenant-stats/index.ejs", "name": "index.ejs", "size": 9137, "extension": ".ejs", "templateName": "index", "templatePath": "tenant-stats/index", "reason": "未在路由文件中找到引用"}], "duplicateFiles": [{"name": "auth.js", "files": ["backend/saas-admin/middleware/auth.js", "backend/saas-admin/routes/auth.js"], "count": 2}, {"name": "index.ejs", "files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/health/index.ejs", "backend/saas-admin/views/inventory/index.ejs", "backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/reports/index.ejs", "backend/saas-admin/views/system/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs", "backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/users/index.ejs"], "count": 23}, {"name": "create.ejs", "files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/create.ejs"], "count": 4}, {"name": "edit.ejs", "files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "count": 3}, {"name": "categories.ejs", "files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/categories.ejs"], "count": 2}], "largeFiles": [], "recommendations": [{"type": "JavaScript清理", "priority": "medium", "description": "发现 4 个未使用的JavaScript文件，建议检查后删除", "files": ["backend/saas-admin/create-test-user.js", "backend/saas-admin/public/js/error-handler.js", "backend/saas-admin/public/js/ui-feedback.js", "backend/saas-admin/setup-admin.js"]}, {"type": "HTML模板清理", "priority": "low", "description": "发现 8 个未使用的HTML模板，建议检查后删除", "files": ["backend/saas-admin/test-integration.html", "backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"]}, {"type": "重复文件清理", "priority": "high", "description": "发现 5 组重复文件，建议合并或删除", "files": [{"name": "auth.js", "files": ["backend/saas-admin/middleware/auth.js", "backend/saas-admin/routes/auth.js"], "count": 2}, {"name": "index.ejs", "files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/health/index.ejs", "backend/saas-admin/views/inventory/index.ejs", "backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/reports/index.ejs", "backend/saas-admin/views/system/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs", "backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/users/index.ejs"], "count": 23}, {"name": "create.ejs", "files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/create.ejs"], "count": 4}, {"name": "edit.ejs", "files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "count": 3}, {"name": "categories.ejs", "files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/categories.ejs"], "count": 2}]}]}, "cleanupPlan": {"highPriority": [{"type": "重复文件清理", "priority": "high", "description": "发现 5 组重复文件，建议合并或删除", "files": [{"name": "auth.js", "files": ["backend/saas-admin/middleware/auth.js", "backend/saas-admin/routes/auth.js"], "count": 2}, {"name": "index.ejs", "files": ["backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/announcements/index.ejs", "backend/saas-admin/views/api-management/index.ejs", "backend/saas-admin/views/dashboard/index.ejs", "backend/saas-admin/views/finance/index.ejs", "backend/saas-admin/views/flocks/index.ejs", "backend/saas-admin/views/goose-prices/index.ejs", "backend/saas-admin/views/health/index.ejs", "backend/saas-admin/views/inventory/index.ejs", "backend/saas-admin/views/knowledge/index.ejs", "backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/mall/index.ejs", "backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/platform-users/index.ejs", "backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/production/index.ejs", "backend/saas-admin/views/reports/index.ejs", "backend/saas-admin/views/system/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs", "backend/saas-admin/views/tenants/index.ejs", "backend/saas-admin/views/users/index.ejs"], "count": 23}, {"name": "create.ejs", "files": ["backend/saas-admin/views/announcements/create.ejs", "backend/saas-admin/views/goose-prices/create.ejs", "backend/saas-admin/views/knowledge/create.ejs", "backend/saas-admin/views/tenants/create.ejs"], "count": 4}, {"name": "edit.ejs", "files": ["backend/saas-admin/views/announcements/edit.ejs", "backend/saas-admin/views/knowledge/edit.ejs", "backend/saas-admin/views/tenants/edit.ejs"], "count": 3}, {"name": "categories.ejs", "files": ["backend/saas-admin/views/knowledge/categories.ejs", "backend/saas-admin/views/mall/categories.ejs"], "count": 2}]}], "mediumPriority": [{"type": "JavaScript清理", "priority": "medium", "description": "发现 4 个未使用的JavaScript文件，建议检查后删除", "files": ["backend/saas-admin/create-test-user.js", "backend/saas-admin/public/js/error-handler.js", "backend/saas-admin/public/js/ui-feedback.js", "backend/saas-admin/setup-admin.js"]}], "lowPriority": [{"type": "HTML模板清理", "priority": "low", "description": "发现 8 个未使用的HTML模板，建议检查后删除", "files": ["backend/saas-admin/test-integration.html", "backend/saas-admin/views/ai/index.ejs", "backend/saas-admin/views/ai-config/index.ejs", "backend/saas-admin/views/logs/index.ejs", "backend/saas-admin/views/monitoring/index.ejs", "backend/saas-admin/views/plans/index.ejs", "backend/saas-admin/views/pricing/index.ejs", "backend/saas-admin/views/tenant-stats/index.ejs"]}]}}