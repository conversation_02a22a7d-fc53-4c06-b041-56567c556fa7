{"timestamp": "2025-08-27T01:35:33.056Z", "summary": {"safeToDelete": 2, "potentiallyUnused": 3, "inUse": 69, "totalAnalyzed": 74}, "safeToDelete": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/error-handler.js", "relativePath": "backend/saas-admin/public/js/error-handler.js", "name": "error-handler.js", "size": 15108, "extension": ".js", "type": "JavaScript", "reason": "未找到任何引用"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/ui-feedback.js", "relativePath": "backend/saas-admin/public/js/ui-feedback.js", "name": "ui-feedback.js", "size": 13202, "extension": ".js", "type": "JavaScript", "reason": "未找到任何引用"}], "potentiallyUnused": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/create-test-user.js", "relativePath": "backend/saas-admin/create-test-user.js", "name": "create-test-user.js", "size": 1630, "extension": ".js", "type": "JavaScript工具脚本", "reason": "工具脚本，可能不需要保留"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/setup-admin.js", "relativePath": "backend/saas-admin/setup-admin.js", "name": "setup-admin.js", "size": 2914, "extension": ".js", "type": "JavaScript工具脚本", "reason": "工具脚本，可能不需要保留"}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/test-integration.html", "relativePath": "backend/saas-admin/test-integration.html", "name": "test-integration.html", "size": 1497, "extension": ".html", "type": "HTML测试文件", "templatePath": "../test-integration", "reason": "测试文件，可能不需要保留"}], "inUse": [{"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/config/database.js", "relativePath": "backend/saas-admin/config/database.js", "name": "database.js", "size": 5921, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/middleware/auth.js", "relativePath": "backend/saas-admin/middleware/auth.js", "name": "auth.js", "size": 3153, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/public/js/admin-common.js", "relativePath": "backend/saas-admin/public/js/admin-common.js", "name": "admin-common.js", "size": 9974, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/announcements.js", "relativePath": "backend/saas-admin/routes/announcements.js", "name": "announcements.js", "size": 13378, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/api-management.js", "relativePath": "backend/saas-admin/routes/api-management.js", "name": "api-management.js", "size": 9555, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/api.js", "relativePath": "backend/saas-admin/routes/api.js", "name": "api.js", "size": 40337, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/auth.js", "relativePath": "backend/saas-admin/routes/auth.js", "name": "auth.js", "size": 4078, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/dashboard.js", "relativePath": "backend/saas-admin/routes/dashboard.js", "name": "dashboard.js", "size": 11111, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/finance.js", "relativePath": "backend/saas-admin/routes/finance.js", "name": "finance.js", "size": 247, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/flocks.js", "relativePath": "backend/saas-admin/routes/flocks.js", "name": "flocks.js", "size": 245, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/goose-prices.js", "relativePath": "backend/saas-admin/routes/goose-prices.js", "name": "goose-prices.js", "size": 9173, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/health.js", "relativePath": "backend/saas-admin/routes/health.js", "name": "health.js", "size": 245, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/inventory.js", "relativePath": "backend/saas-admin/routes/inventory.js", "name": "inventory.js", "size": 251, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/knowledge.js", "relativePath": "backend/saas-admin/routes/knowledge.js", "name": "knowledge.js", "size": 15150, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/mall.js", "relativePath": "backend/saas-admin/routes/mall.js", "name": "mall.js", "size": 13077, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/platform-users.js", "relativePath": "backend/saas-admin/routes/platform-users.js", "name": "platform-users.js", "size": 10703, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/production.js", "relativePath": "backend/saas-admin/routes/production.js", "name": "production.js", "size": 253, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/reports.js", "relativePath": "backend/saas-admin/routes/reports.js", "name": "reports.js", "size": 6455, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/system.js", "relativePath": "backend/saas-admin/routes/system.js", "name": "system.js", "size": 6803, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/tenants.js", "relativePath": "backend/saas-admin/routes/tenants.js", "name": "tenants.js", "size": 18353, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/routes/users.js", "relativePath": "backend/saas-admin/routes/users.js", "name": "users.js", "size": 2681, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/server.js", "relativePath": "backend/saas-admin/server.js", "name": "server.js", "size": 9227, "extension": ".js", "type": "JavaScript", "usageReasons": ["在模板中被引用", "系统核心文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai/index.ejs", "relativePath": "backend/saas-admin/views/ai/index.ejs", "name": "index.ejs", "size": 16275, "extension": ".ejs", "type": "HTML模板", "templatePath": "ai/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/ai-config/index.ejs", "relativePath": "backend/saas-admin/views/ai-config/index.ejs", "name": "index.ejs", "size": 13771, "extension": ".ejs", "type": "HTML模板", "templatePath": "ai-config/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/create.ejs", "relativePath": "backend/saas-admin/views/announcements/create.ejs", "name": "create.ejs", "size": 8322, "extension": ".ejs", "type": "HTML模板", "templatePath": "announcements/create", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/edit.ejs", "relativePath": "backend/saas-admin/views/announcements/edit.ejs", "name": "edit.ejs", "size": 19390, "extension": ".ejs", "type": "HTML模板", "templatePath": "announcements/edit", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/announcements/index.ejs", "relativePath": "backend/saas-admin/views/announcements/index.ejs", "name": "index.ejs", "size": 11271, "extension": ".ejs", "type": "HTML模板", "templatePath": "announcements/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/api-management/index.ejs", "relativePath": "backend/saas-admin/views/api-management/index.ejs", "name": "index.ejs", "size": 5025, "extension": ".ejs", "type": "HTML模板", "templatePath": "api-management/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/auth/login.ejs", "relativePath": "backend/saas-admin/views/auth/login.ejs", "name": "login.ejs", "size": 9851, "extension": ".ejs", "type": "HTML模板", "templatePath": "auth/login", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/dashboard/index.ejs", "relativePath": "backend/saas-admin/views/dashboard/index.ejs", "name": "index.ejs", "size": 24164, "extension": ".ejs", "type": "HTML模板", "templatePath": "dashboard/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/error.ejs", "relativePath": "backend/saas-admin/views/error.ejs", "name": "error.ejs", "size": 1861, "extension": ".ejs", "type": "HTML模板", "templatePath": "error", "usageReasons": ["在路由中被引用", "错误页面"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/finance/index.ejs", "relativePath": "backend/saas-admin/views/finance/index.ejs", "name": "index.ejs", "size": 640, "extension": ".ejs", "type": "HTML模板", "templatePath": "finance/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/flocks/index.ejs", "relativePath": "backend/saas-admin/views/flocks/index.ejs", "name": "index.ejs", "size": 28999, "extension": ".ejs", "type": "HTML模板", "templatePath": "flocks/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/create.ejs", "relativePath": "backend/saas-admin/views/goose-prices/create.ejs", "name": "create.ejs", "size": 10838, "extension": ".ejs", "type": "HTML模板", "templatePath": "goose-prices/create", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/index.ejs", "relativePath": "backend/saas-admin/views/goose-prices/index.ejs", "name": "index.ejs", "size": 12118, "extension": ".ejs", "type": "HTML模板", "templatePath": "goose-prices/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/goose-prices/trends.ejs", "relativePath": "backend/saas-admin/views/goose-prices/trends.ejs", "name": "trends.ejs", "size": 13918, "extension": ".ejs", "type": "HTML模板", "templatePath": "goose-prices/trends", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/health/index.ejs", "relativePath": "backend/saas-admin/views/health/index.ejs", "name": "index.ejs", "size": 47727, "extension": ".ejs", "type": "HTML模板", "templatePath": "health/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/inventory/index.ejs", "relativePath": "backend/saas-admin/views/inventory/index.ejs", "name": "index.ejs", "size": 642, "extension": ".ejs", "type": "HTML模板", "templatePath": "inventory/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/categories.ejs", "relativePath": "backend/saas-admin/views/knowledge/categories.ejs", "name": "categories.ejs", "size": 17904, "extension": ".ejs", "type": "HTML模板", "templatePath": "knowledge/categories", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/create.ejs", "relativePath": "backend/saas-admin/views/knowledge/create.ejs", "name": "create.ejs", "size": 14024, "extension": ".ejs", "type": "HTML模板", "templatePath": "knowledge/create", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/edit.ejs", "relativePath": "backend/saas-admin/views/knowledge/edit.ejs", "name": "edit.ejs", "size": 17411, "extension": ".ejs", "type": "HTML模板", "templatePath": "knowledge/edit", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/knowledge/index.ejs", "relativePath": "backend/saas-admin/views/knowledge/index.ejs", "name": "index.ejs", "size": 7347, "extension": ".ejs", "type": "HTML模板", "templatePath": "knowledge/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/footer.ejs", "relativePath": "backend/saas-admin/views/layouts/footer.ejs", "name": "footer.ejs", "size": 103, "extension": ".ejs", "type": "HTML模板", "templatePath": "layouts/footer", "usageReasons": ["布局文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs", "relativePath": "backend/saas-admin/views/layouts/main.ejs", "name": "main.ejs", "size": 23140, "extension": ".ejs", "type": "HTML模板", "templatePath": "layouts/main", "usageReasons": ["在路由中被引用", "布局文件"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/logs/index.ejs", "relativePath": "backend/saas-admin/views/logs/index.ejs", "name": "index.ejs", "size": 16176, "extension": ".ejs", "type": "HTML模板", "templatePath": "logs/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/categories.ejs", "relativePath": "backend/saas-admin/views/mall/categories.ejs", "name": "categories.ejs", "size": 22029, "extension": ".ejs", "type": "HTML模板", "templatePath": "mall/categories", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/index.ejs", "relativePath": "backend/saas-admin/views/mall/index.ejs", "name": "index.ejs", "size": 5302, "extension": ".ejs", "type": "HTML模板", "templatePath": "mall/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/inventory.ejs", "relativePath": "backend/saas-admin/views/mall/inventory.ejs", "name": "inventory.ejs", "size": 22523, "extension": ".ejs", "type": "HTML模板", "templatePath": "mall/inventory", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/orders.ejs", "relativePath": "backend/saas-admin/views/mall/orders.ejs", "name": "orders.ejs", "size": 21710, "extension": ".ejs", "type": "HTML模板", "templatePath": "mall/orders", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/mall/products.ejs", "relativePath": "backend/saas-admin/views/mall/products.ejs", "name": "products.ejs", "size": 12107, "extension": ".ejs", "type": "HTML模板", "templatePath": "mall/products", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/monitoring/index.ejs", "relativePath": "backend/saas-admin/views/monitoring/index.ejs", "name": "index.ejs", "size": 16778, "extension": ".ejs", "type": "HTML模板", "templatePath": "monitoring/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/plans/index.ejs", "relativePath": "backend/saas-admin/views/plans/index.ejs", "name": "index.ejs", "size": 15251, "extension": ".ejs", "type": "HTML模板", "templatePath": "plans/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/platform-users/index.ejs", "relativePath": "backend/saas-admin/views/platform-users/index.ejs", "name": "index.ejs", "size": 7205, "extension": ".ejs", "type": "HTML模板", "templatePath": "platform-users/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/pricing/index.ejs", "relativePath": "backend/saas-admin/views/pricing/index.ejs", "name": "index.ejs", "size": 16322, "extension": ".ejs", "type": "HTML模板", "templatePath": "pricing/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/production/index.ejs", "relativePath": "backend/saas-admin/views/production/index.ejs", "name": "index.ejs", "size": 32647, "extension": ".ejs", "type": "HTML模板", "templatePath": "production/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/reports/index.ejs", "relativePath": "backend/saas-admin/views/reports/index.ejs", "name": "index.ejs", "size": 640, "extension": ".ejs", "type": "HTML模板", "templatePath": "reports/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/backup.ejs", "relativePath": "backend/saas-admin/views/system/backup.ejs", "name": "backup.ejs", "size": 10022, "extension": ".ejs", "type": "HTML模板", "templatePath": "system/backup", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/index.ejs", "relativePath": "backend/saas-admin/views/system/index.ejs", "name": "index.ejs", "size": 639, "extension": ".ejs", "type": "HTML模板", "templatePath": "system/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/logs.ejs", "relativePath": "backend/saas-admin/views/system/logs.ejs", "name": "logs.ejs", "size": 6099, "extension": ".ejs", "type": "HTML模板", "templatePath": "system/logs", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/system/monitoring.ejs", "relativePath": "backend/saas-admin/views/system/monitoring.ejs", "name": "monitoring.ejs", "size": 9133, "extension": ".ejs", "type": "HTML模板", "templatePath": "system/monitoring", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenant-stats/index.ejs", "relativePath": "backend/saas-admin/views/tenant-stats/index.ejs", "name": "index.ejs", "size": 9137, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenant-stats/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/create.ejs", "relativePath": "backend/saas-admin/views/tenants/create.ejs", "name": "create.ejs", "size": 19494, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/create", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/details.ejs", "relativePath": "backend/saas-admin/views/tenants/details.ejs", "name": "details.ejs", "size": 30544, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/details", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/edit.ejs", "relativePath": "backend/saas-admin/views/tenants/edit.ejs", "name": "edit.ejs", "size": 23063, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/edit", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/index.ejs", "relativePath": "backend/saas-admin/views/tenants/index.ejs", "name": "index.ejs", "size": 18687, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/subscriptions.ejs", "relativePath": "backend/saas-admin/views/tenants/subscriptions.ejs", "name": "subscriptions.ejs", "size": 16236, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/subscriptions", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/tenants/usage.ejs", "relativePath": "backend/saas-admin/views/tenants/usage.ejs", "name": "usage.ejs", "size": 18541, "extension": ".ejs", "type": "HTML模板", "templatePath": "tenants/usage", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/users/index.ejs", "relativePath": "backend/saas-admin/views/users/index.ejs", "name": "index.ejs", "size": 21978, "extension": ".ejs", "type": "HTML模板", "templatePath": "users/index", "usageReasons": ["在路由中被引用"]}, {"path": "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/users/profile.ejs", "relativePath": "backend/saas-admin/views/users/profile.ejs", "name": "profile.ejs", "size": 23567, "extension": ".ejs", "type": "HTML模板", "templatePath": "users/profile", "usageReasons": ["在路由中被引用"]}]}