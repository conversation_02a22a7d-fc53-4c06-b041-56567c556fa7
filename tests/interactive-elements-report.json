{"timestamp": "2025-08-27T01:31:15.350Z", "summary": {"pagesAnalyzed": 12, "totalButtons": 111, "totalForms": 15, "totalTables": 5, "totalIssues": 11}, "pageAnalysis": {"/dashboard": {"title": "平台仪表盘", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>", "type": "button", "class": "btn-close", "id": ""}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-tool\" onclick=\"location.reload()\">\n            <", "type": "btn-class", "class": "btn btn-tool"}, {"text": "", "html": "<a href=\"/tenants\" class=\"btn btn-tool\">\n            <", "type": "btn-class", "class": "btn btn-tool"}, {"text": "", "html": "<a href=\"/mall/orders\" class=\"btn btn-tool\">\n            <", "type": "btn-class", "class": "btn btn-tool"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"><", "type": "btn-class", "class": "btn-close"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [{"id": "", "class": "table table-striped", "headerCount": 5, "rowCount": 4}, {"id": "", "class": "table table-striped", "headerCount": 5, "rowCount": 3}], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/users": {"title": "用户管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>", "type": "button", "class": "btn-close", "id": ""}, {"text": "取消", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">取消</button>", "type": "button", "class": "btn btn-secondary", "id": ""}, {"text": "保存", "html": "<button type=\"submit\" class=\"btn btn-primary\">保存</button>", "type": "submit", "class": "btn btn-primary", "id": ""}, {"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>", "type": "button", "class": "btn-close", "id": ""}, {"text": "关闭", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">关闭</button>", "type": "button", "class": "btn btn-secondary", "id": ""}, {"text": "导出报告", "html": "<button type=\"button\" class=\"btn btn-primary\" onclick=\"exportUserStats()\">导出报告</button>", "type": "button", "class": "btn btn-primary", "id": ""}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button class=\"btn btn-info me-2\" onclick=\"showUserStats()\">\n            <", "type": "btn-class", "class": "btn btn-info me-2"}, {"text": "", "html": "<button class=\"btn btn-success\" data-bs-toggle=\"modal\" data-bs-target=\"#addUserModal\">\n            <", "type": "btn-class", "class": "btn btn-success"}, {"text": "", "html": "<button type=\"submit\" class=\"btn btn-primary\">\n                <", "type": "btn-class", "class": "btn btn-primary"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-success\" data-bs-toggle=\"modal\" data-bs-target=\"#addUserModal\">\n                <", "type": "btn-class", "class": "btn btn-success"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                          <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"><", "type": "btn-class", "class": "btn-close"}, {"text": "取消", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">取消<", "type": "btn-class", "class": "btn btn-secondary"}, {"text": "保存", "html": "<button type=\"submit\" class=\"btn btn-primary\">保存<", "type": "btn-class", "class": "btn btn-primary"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"><", "type": "btn-class", "class": "btn-close"}, {"text": "关闭", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">关闭<", "type": "btn-class", "class": "btn btn-secondary"}, {"text": "导出报告", "html": "<button type=\"button\" class=\"btn btn-primary\" onclick=\"exportUserStats()\">导出报告<", "type": "btn-class", "class": "btn btn-primary"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}, {"action": "", "method": "GET", "id": "", "class": "row g-3 mb-4", "inputCount": 1}, {"action": "", "method": "", "id": "addUserForm", "class": "modal-body", "inputCount": 5}], "tables": [{"id": "", "class": "table table-hover", "headerCount": 10, "rowCount": 2}], "modals": [{"id": "addUserModal", "class": "modal fade", "hasForm": false}, {"id": "", "class": "modal-body", "hasForm": false}, {"id": "", "class": "modal-footer", "hasForm": false}, {"id": "", "class": "modal-dialog modal-lg", "hasForm": false}, {"id": "", "class": "modal-body", "hasForm": false}, {"id": "", "class": "modal-footer", "hasForm": false}], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/tenants": {"title": "租户管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>", "type": "button", "class": "btn-close", "id": ""}, {"text": "取消", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">取消</button>", "type": "button", "class": "btn btn-secondary", "id": ""}, {"text": "", "html": "<button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\"></button>", "type": "button", "class": "btn-close btn-close-white me-2 m-auto", "id": ""}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<div class=\"btn-group\">\n            <", "type": "btn-class", "class": "btn-group"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-outline-primary\" onclick=\"selectAllTenants()\">\n              <", "type": "btn-class", "class": "btn btn-outline-primary"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-outline-warning\" onclick=\"batchUpdateTenantStatus('suspended')\">\n              <", "type": "btn-class", "class": "btn btn-outline-warning"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-outline-success\" onclick=\"batchUpdateTenantStatus('active')\">\n              <", "type": "btn-class", "class": "btn btn-outline-success"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-success\" onclick=\"exportTenants()\">\n              <", "type": "btn-class", "class": "btn btn-success"}, {"text": "", "html": "<a href=\"/tenants/subscriptions\" class=\"btn btn-warning\">\n              <", "type": "btn-class", "class": "btn btn-warning"}, {"text": "", "html": "<a href=\"/tenants/usage\" class=\"btn btn-info\">\n              <", "type": "btn-class", "class": "btn btn-info"}, {"text": "", "html": "<div class=\"btn-group\" role=\"group\">\n                        <", "type": "btn-class", "class": "btn-group"}, {"text": "", "html": "<a href=\"/tenants/1/edit\" class=\"btn btn-sm btn-warning\" title=\"编辑\">\n                          <", "type": "btn-class", "class": "btn btn-sm btn-warning"}, {"text": "", "html": "<div class=\"btn-group\" role=\"group\">\n                        <", "type": "btn-class", "class": "btn-group"}, {"text": "", "html": "<a href=\"/tenants/2/edit\" class=\"btn btn-sm btn-warning\" title=\"编辑\">\n                          <", "type": "btn-class", "class": "btn btn-sm btn-warning"}, {"text": "", "html": "<div class=\"btn-group\" role=\"group\">\n                        <", "type": "btn-class", "class": "btn-group"}, {"text": "", "html": "<a href=\"/tenants/3/edit\" class=\"btn btn-sm btn-warning\" title=\"编辑\">\n                          <", "type": "btn-class", "class": "btn btn-sm btn-warning"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"><", "type": "btn-class", "class": "btn-close"}, {"text": "取消", "html": "<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">取消<", "type": "btn-class", "class": "btn btn-secondary"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-success\" onclick=\"performExport()\">\n            <", "type": "btn-class", "class": "btn btn-success"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\"><", "type": "btn-class", "class": "btn-close btn-close-white me-2 m-auto"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}, {"action": "", "method": "", "id": "exportForm", "class": "mb-3", "inputCount": 5}], "tables": [{"id": "selectAllCheckbox", "class": "table table-striped table-hover", "headerCount": 9, "rowCount": 4}], "modals": [{"id": "", "class": "modal-dialog", "hasForm": false}, {"id": "exportForm", "class": "modal-body", "hasForm": true}, {"id": "", "class": "modal-footer", "hasForm": false}], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/flocks": {"title": "鹅群管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/production": {"title": "生产管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/health": {"title": "健康管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/finance": {"title": "finance管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/inventory": {"title": "inventory管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/reports": {"title": "统计报告", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/system": {"title": "系统设置", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/mall": {"title": "商城管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "进入管理", "html": "<a href=\"/mall/products\" class=\"btn btn-primary\">进入管理<", "type": "btn-class", "class": "btn btn-primary"}, {"text": "进入管理", "html": "<a href=\"/mall/orders\" class=\"btn btn-success\">进入管理<", "type": "btn-class", "class": "btn btn-success"}, {"text": "进入管理", "html": "<a href=\"/mall/categories\" class=\"btn btn-info\">进入管理<", "type": "btn-class", "class": "btn btn-info"}, {"text": "进入管理", "html": "<a href=\"/mall/inventory\" class=\"btn btn-warning\">进入管理<", "type": "btn-class", "class": "btn btn-warning"}, {"text": "查看报表", "html": "<a href=\"/reports?type=sales\" class=\"btn btn-secondary\">查看报表<", "type": "btn-class", "class": "btn btn-secondary"}, {"text": "进入设置", "html": "<a href=\"/system?tab=mall\" class=\"btn btn-dark\">进入设置<", "type": "btn-class", "class": "btn btn-dark"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}, {"text": "进入管理", "href": "/mall/products", "class": "btn btn-primary"}, {"text": "进入管理", "href": "/mall/orders", "class": "btn btn-success"}, {"text": "进入管理", "href": "/mall/categories", "class": "btn btn-info"}, {"text": "进入管理", "href": "/mall/inventory", "class": "btn btn-warning"}, {"text": "查看报表", "href": "/reports?type=sales", "class": "btn btn-secondary"}, {"text": "进入设置", "href": "/system?tab=mall", "class": "btn btn-dark"}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}, "/knowledge": {"title": "知识库管理", "buttons": [{"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button>", "type": "button", "class": "btn-close me-2 m-auto", "id": ""}, {"text": "", "html": "<button class=\"btn btn-navbar\" type=\"submit\">\n                    <", "type": "btn-class", "class": "btn btn-navbar"}, {"text": "", "html": "<a href=\"/knowledge/create\" class=\"btn btn-primary\">\n                        <", "type": "btn-class", "class": "btn btn-primary"}, {"text": "", "html": "<a href=\"/knowledge/categories\" class=\"btn btn-outline-secondary\">\n                        <", "type": "btn-class", "class": "btn btn-outline-secondary"}, {"text": "", "html": "<button type=\"submit\" class=\"btn btn-default\">\n                                    <", "type": "btn-class", "class": "btn btn-default"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(10)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(11)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(12)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(7)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(8)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(9)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(4)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(5)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(6)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(1)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(2)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<div class=\"btn-group btn-group-sm\">\n                                        <", "type": "btn-class", "class": "btn-group btn-group-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"deleteArticle(3)\">\n                                            <", "type": "btn-class", "class": "btn btn-danger btn-sm"}, {"text": "", "html": "<button type=\"button\" class=\"btn-close me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"><", "type": "btn-class", "class": "btn-close me-2 m-auto"}], "forms": [{"action": "", "method": "", "id": "", "class": "form-inline", "inputCount": 1}], "tables": [{"id": "", "class": "table table-hover text-nowrap", "headerCount": 8, "rowCount": 13}], "modals": [], "links": [{"text": "首页", "href": "/dashboard", "class": ""}, {"text": "鹅类养殖基础知识", "href": "/knowledge/10/edit", "class": "text-decoration-none"}, {"text": "饲料配比指南", "href": "/knowledge/11/edit", "class": "text-decoration-none"}, {"text": "疾病预防手册", "href": "/knowledge/12/edit", "class": "text-decoration-none"}, {"text": "鹅类养殖基础知识", "href": "/knowledge/7/edit", "class": "text-decoration-none"}, {"text": "饲料配比指南", "href": "/knowledge/8/edit", "class": "text-decoration-none"}, {"text": "疾病预防手册", "href": "/knowledge/9/edit", "class": "text-decoration-none"}, {"text": "鹅类养殖基础知识", "href": "/knowledge/4/edit", "class": "text-decoration-none"}, {"text": "饲料配比指南", "href": "/knowledge/5/edit", "class": "text-decoration-none"}, {"text": "疾病预防手册", "href": "/knowledge/6/edit", "class": "text-decoration-none"}, {"text": "鹅类养殖基础知识", "href": "/knowledge/1/edit", "class": "text-decoration-none"}, {"text": "饲料配比指南", "href": "/knowledge/2/edit", "class": "text-decoration-none"}, {"text": "疾病预防手册", "href": "/knowledge/3/edit", "class": "text-decoration-none"}], "scripts": ["/js/admin-common.js"], "stylesheets": ["/css/admin-custom.css"], "issues": ["页面可能显示了警告或错误信息"]}}, "interactionTests": {"apiEndpoints": {"/api/dashboard/stats": {"status": 200, "success": true, "dataType": "object", "hasData": true}, "/api/users": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/tenants": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/flocks": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/production": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/health": {"status": 200, "success": true, "dataType": "object", "hasData": true}, "/api/finance": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/inventory": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/reports": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/goose-prices": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/mall/products": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/knowledge": {"status": 404, "success": false, "error": "Request failed with status code 404"}, "/api/announcements": {"status": 404, "success": false, "error": "Request failed with status code 404"}}}, "issues": {"missingElements": ["API端点: /api/users", "API端点: /api/tenants", "API端点: /api/flocks", "API端点: /api/production", "API端点: /api/finance", "API端点: /api/inventory", "API端点: /api/reports", "API端点: /api/goose-prices", "API端点: /api/mall/products", "API端点: /api/knowledge", "API端点: /api/announcements"], "brokenInteractions": [], "uiIssues": []}, "recommendations": ["修复发现的功能问题和缺失元素"]}