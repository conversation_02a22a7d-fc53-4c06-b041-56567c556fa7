/**
 * 综合测试报告生成器
 * 汇总所有测试结果，生成详细的问题清单和修复建议
 */

const fs = require('fs');
const path = require('path');

class ComprehensiveReportGenerator {
    constructor() {
        this.testResults = {};
        this.finalReport = {
            timestamp: new Date().toISOString(),
            testSummary: {},
            findings: {
                criticalIssues: [],
                majorIssues: [],
                minorIssues: [],
                recommendations: []
            },
            codeQuality: {},
            performance: {},
            security: {},
            cleanup: {}
        };
    }

    // 加载所有测试报告
    loadTestReports() {
        const reportFiles = [
            'admin-backend-test-report.json',
            'interactive-elements-report.json',
            'code-cleanup-report.json'
        ];

        reportFiles.forEach(fileName => {
            const filePath = path.join(__dirname, fileName);
            if (fs.existsSync(filePath)) {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const reportName = fileName.replace('-report.json', '').replace('.json', '');
                    this.testResults[reportName] = JSON.parse(content);
                    console.log(`✅ 加载报告: ${fileName}`);
                } catch (error) {
                    console.error(`❌ 加载报告失败 ${fileName}:`, error.message);
                }
            } else {
                console.warn(`⚠️  报告文件不存在: ${fileName}`);
            }
        });
    }

    // 分析基础功能测试结果
    analyzeBasicFunctionality() {
        const basicTest = this.testResults['admin-backend-test'];
        if (!basicTest) return;

        this.finalReport.testSummary.basicFunctionality = {
            totalTests: basicTest.summary.totalTests,
            passed: basicTest.summary.passed,
            failed: basicTest.summary.failed,
            successRate: basicTest.summary.successRate
        };

        // 分析缺失页面
        if (basicTest.missingPages && basicTest.missingPages.length > 0) {
            this.finalReport.findings.criticalIssues.push({
                category: '缺失页面',
                severity: 'critical',
                count: basicTest.missingPages.length,
                description: '发现缺失的页面，影响系统完整性',
                items: basicTest.missingPages
            });
        }

        // 分析功能问题
        if (basicTest.brokenFeatures && basicTest.brokenFeatures.length > 0) {
            this.finalReport.findings.majorIssues.push({
                category: '功能故障',
                severity: 'major',
                count: basicTest.brokenFeatures.length,
                description: '发现功能故障，需要修复',
                items: basicTest.brokenFeatures
            });
        }
    }

    // 分析交互元素测试结果
    analyzeInteractiveElements() {
        const interactiveTest = this.testResults['interactive-elements'];
        if (!interactiveTest) return;

        this.finalReport.testSummary.interactiveElements = {
            pagesAnalyzed: interactiveTest.summary.pagesAnalyzed,
            totalButtons: interactiveTest.summary.totalButtons,
            totalForms: interactiveTest.summary.totalForms,
            totalTables: interactiveTest.summary.totalTables,
            totalIssues: interactiveTest.summary.totalIssues
        };

        // 分析缺失的API端点
        if (interactiveTest.issues.missingElements && interactiveTest.issues.missingElements.length > 0) {
            this.finalReport.findings.majorIssues.push({
                category: '缺失API端点',
                severity: 'major',
                count: interactiveTest.issues.missingElements.length,
                description: '发现多个API端点返回404，影响前端功能',
                items: interactiveTest.issues.missingElements
            });
        }

        // 分析交互问题
        if (interactiveTest.issues.brokenInteractions && interactiveTest.issues.brokenInteractions.length > 0) {
            this.finalReport.findings.majorIssues.push({
                category: '交互功能问题',
                severity: 'major',
                count: interactiveTest.issues.brokenInteractions.length,
                description: '发现交互功能问题，影响用户体验',
                items: interactiveTest.issues.brokenInteractions
            });
        }
    }

    // 分析代码清理结果
    analyzeCodeCleanup() {
        const cleanupTest = this.testResults['code-cleanup'];
        if (!cleanupTest) return;

        this.finalReport.cleanup = {
            totalFiles: cleanupTest.summary.totalCssFiles + cleanupTest.summary.totalJsFiles + cleanupTest.summary.totalHtmlFiles,
            unusedFiles: cleanupTest.summary.unusedCssFiles + cleanupTest.summary.unusedJsFiles + cleanupTest.summary.unusedHtmlFiles,
            duplicateGroups: cleanupTest.summary.duplicateFileGroups,
            largeFiles: cleanupTest.summary.largeFiles
        };

        // 处理清理建议
        if (cleanupTest.details.recommendations) {
            cleanupTest.details.recommendations.forEach(rec => {
                const severity = rec.priority === 'high' ? 'major' : 'minor';
                const targetArray = severity === 'major' ? 
                    this.finalReport.findings.majorIssues : 
                    this.finalReport.findings.minorIssues;

                targetArray.push({
                    category: '代码清理',
                    severity: severity,
                    type: rec.type,
                    description: rec.description,
                    files: rec.files
                });
            });
        }
    }

    // 生成修复建议
    generateRecommendations() {
        const recommendations = [];

        // 基于发现的问题生成建议
        if (this.finalReport.findings.criticalIssues.length > 0) {
            recommendations.push({
                priority: 1,
                category: '紧急修复',
                title: '修复关键功能问题',
                description: '立即修复缺失的页面和关键功能故障',
                estimatedTime: '1-2天',
                impact: 'high'
            });
        }

        if (this.finalReport.findings.majorIssues.some(issue => issue.category === '缺失API端点')) {
            recommendations.push({
                priority: 2,
                category: 'API开发',
                title: '实现缺失的API端点',
                description: '开发并实现所有缺失的API端点，确保前后端功能完整',
                estimatedTime: '3-5天',
                impact: 'high'
            });
        }

        if (this.finalReport.cleanup.unusedFiles > 0) {
            recommendations.push({
                priority: 3,
                category: '代码优化',
                title: '清理未使用的文件',
                description: '删除未使用的CSS、JavaScript和HTML文件，优化项目结构',
                estimatedTime: '1天',
                impact: 'medium'
            });
        }

        if (this.finalReport.cleanup.duplicateGroups > 0) {
            recommendations.push({
                priority: 4,
                category: '代码重构',
                title: '合并重复文件',
                description: '识别并合并重复的文件，减少代码冗余',
                estimatedTime: '2天',
                impact: 'medium'
            });
        }

        recommendations.push({
            priority: 5,
            category: '测试完善',
            title: '建立自动化测试',
            description: '建立完整的自动化测试体系，包括单元测试、集成测试和E2E测试',
            estimatedTime: '1周',
            impact: 'high'
        });

        this.finalReport.findings.recommendations = recommendations;
    }

    // 计算代码质量评分
    calculateQualityScore() {
        let score = 100;
        
        // 基于发现的问题扣分
        score -= this.finalReport.findings.criticalIssues.length * 20;
        score -= this.finalReport.findings.majorIssues.length * 10;
        score -= this.finalReport.findings.minorIssues.length * 5;
        
        // 基于代码清理情况调整
        if (this.finalReport.cleanup.unusedFiles > 0) {
            score -= Math.min(this.finalReport.cleanup.unusedFiles * 2, 10);
        }
        
        score = Math.max(score, 0);
        
        this.finalReport.codeQuality = {
            overallScore: score,
            grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F',
            criticalIssues: this.finalReport.findings.criticalIssues.length,
            majorIssues: this.finalReport.findings.majorIssues.length,
            minorIssues: this.finalReport.findings.minorIssues.length
        };
    }

    // 生成综合报告
    generateComprehensiveReport() {
        console.log('📊 生成综合测试报告...');
        
        this.loadTestReports();
        this.analyzeBasicFunctionality();
        this.analyzeInteractiveElements();
        this.analyzeCodeCleanup();
        this.generateRecommendations();
        this.calculateQualityScore();
        
        return this.finalReport;
    }

    // 保存报告
    saveReport(report) {
        const reportPath = path.join(__dirname, 'comprehensive-final-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 同时生成可读的Markdown报告
        this.generateMarkdownReport(report);
        
        console.log(`\n📊 综合测试报告已保存到: ${reportPath}`);
    }

    // 生成Markdown格式报告
    generateMarkdownReport(report) {
        const mdContent = `# 智慧养鹅SAAS管理后台 - 综合测试报告

## 📊 测试概览

**测试时间**: ${new Date(report.timestamp).toLocaleString('zh-CN')}
**代码质量评分**: ${report.codeQuality.overallScore}/100 (${report.codeQuality.grade}级)

## 🎯 测试结果汇总

### 基础功能测试
- **总测试数**: ${report.testSummary.basicFunctionality?.totalTests || 0}
- **通过数**: ${report.testSummary.basicFunctionality?.passed || 0}
- **失败数**: ${report.testSummary.basicFunctionality?.failed || 0}
- **成功率**: ${report.testSummary.basicFunctionality?.successRate || '0%'}

### 交互元素测试
- **分析页面数**: ${report.testSummary.interactiveElements?.pagesAnalyzed || 0}
- **总按钮数**: ${report.testSummary.interactiveElements?.totalButtons || 0}
- **总表单数**: ${report.testSummary.interactiveElements?.totalForms || 0}
- **总表格数**: ${report.testSummary.interactiveElements?.totalTables || 0}

### 代码清理分析
- **总文件数**: ${report.cleanup.totalFiles || 0}
- **未使用文件数**: ${report.cleanup.unusedFiles || 0}
- **重复文件组数**: ${report.cleanup.duplicateGroups || 0}

## ⚠️ 发现的问题

### 🔴 关键问题 (${report.findings.criticalIssues.length})
${report.findings.criticalIssues.map(issue => `- **${issue.category}**: ${issue.description} (${issue.count}个)`).join('\n')}

### 🟡 主要问题 (${report.findings.majorIssues.length})
${report.findings.majorIssues.map(issue => `- **${issue.category}**: ${issue.description} (${issue.count || issue.files?.length || 0}个)`).join('\n')}

### 🟢 次要问题 (${report.findings.minorIssues.length})
${report.findings.minorIssues.map(issue => `- **${issue.type || issue.category}**: ${issue.description}`).join('\n')}

## 🎯 修复建议

${report.findings.recommendations.map(rec => 
`### ${rec.priority}. ${rec.title}
- **类别**: ${rec.category}
- **描述**: ${rec.description}
- **预估时间**: ${rec.estimatedTime}
- **影响程度**: ${rec.impact}
`).join('\n')}

## 📈 总结

本次测试发现了 **${report.findings.criticalIssues.length}** 个关键问题，**${report.findings.majorIssues.length}** 个主要问题，和 **${report.findings.minorIssues.length}** 个次要问题。

建议按照优先级顺序进行修复，重点关注关键问题和API端点的实现。

---
*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`;

        const mdPath = path.join(__dirname, 'comprehensive-final-report.md');
        fs.writeFileSync(mdPath, mdContent);
        console.log(`📄 Markdown报告已保存到: ${mdPath}`);
    }
}

// 运行报告生成
async function generateFinalReport() {
    const generator = new ComprehensiveReportGenerator();
    
    try {
        const report = generator.generateComprehensiveReport();
        
        console.log('\n' + '='.repeat(80));
        console.log('📊 智慧养鹅SAAS管理后台 - 综合测试报告');
        console.log('='.repeat(80));
        console.log(`代码质量评分: ${report.codeQuality.overallScore}/100 (${report.codeQuality.grade}级)`);
        console.log(`关键问题: ${report.findings.criticalIssues.length}个`);
        console.log(`主要问题: ${report.findings.majorIssues.length}个`);
        console.log(`次要问题: ${report.findings.minorIssues.length}个`);
        console.log(`修复建议: ${report.findings.recommendations.length}条`);
        
        generator.saveReport(report);
        
        return report;
        
    } catch (error) {
        console.error('生成综合报告失败:', error);
    }
}

if (require.main === module) {
    generateFinalReport();
}

module.exports = ComprehensiveReportGenerator;
