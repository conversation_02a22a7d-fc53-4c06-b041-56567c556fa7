#!/bin/bash

# 智慧养鹅SAAS管理后台测试运行脚本
# 用于自动化运行所有测试并生成报告

echo "🚀 智慧养鹅SAAS管理后台测试套件"
echo "=================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 设置测试目录
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 测试目录: $TEST_DIR"

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if curl -s http://localhost:3003/api/health > /dev/null; then
    echo "✅ 服务器正在运行"
else
    echo "⚠️  服务器未运行，尝试启动服务器..."
    
    # 检查是否在正确的目录
    if [ -f "$PROJECT_ROOT/backend/saas-admin/server.js" ]; then
        echo "📂 切换到服务器目录..."
        cd "$PROJECT_ROOT/backend/saas-admin"
        
        # 启动服务器（后台运行）
        echo "🚀 启动服务器..."
        PORT=3003 npm start > /dev/null 2>&1 &
        SERVER_PID=$!
        
        # 等待服务器启动
        echo "⏳ 等待服务器启动..."
        for i in {1..30}; do
            if curl -s http://localhost:3003/api/health > /dev/null; then
                echo "✅ 服务器启动成功 (PID: $SERVER_PID)"
                break
            fi
            sleep 1
        done
        
        # 检查服务器是否成功启动
        if ! curl -s http://localhost:3003/api/health > /dev/null; then
            echo "❌ 服务器启动失败"
            exit 1
        fi
    else
        echo "❌ 找不到服务器文件，请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
fi

# 回到测试目录
cd "$TEST_DIR"

# 创建测试报告目录
REPORT_DIR="$TEST_DIR/reports"
mkdir -p "$REPORT_DIR"

echo ""
echo "🧪 开始运行测试套件..."
echo "=================================="

# 运行基础功能测试
echo "1️⃣ 运行基础功能测试..."
if node admin-backend-comprehensive-test.js; then
    echo "✅ 基础功能测试完成"
    mv admin-backend-test-report.json "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ 基础功能测试失败"
fi

echo ""

# 运行交互元素测试
echo "2️⃣ 运行交互元素测试..."
if node interactive-elements-test.js; then
    echo "✅ 交互元素测试完成"
    mv interactive-elements-report.json "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ 交互元素测试失败"
fi

echo ""

# 运行API端点验证
echo "3️⃣ 运行API端点验证..."
if node api-endpoints-verification.js; then
    echo "✅ API端点验证完成"
    mv api-verification-report.json "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ API端点验证失败"
fi

echo ""

# 运行自动化测试套件
echo "4️⃣ 运行自动化测试套件..."
if node automated-test-suite.js; then
    echo "✅ 自动化测试套件完成"
    mv automated-test-report.json "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ 自动化测试套件失败"
fi

echo ""

# 运行代码清理分析
echo "5️⃣ 运行代码清理分析..."
if node code-cleanup-analyzer.js; then
    echo "✅ 代码清理分析完成"
    mv code-cleanup-report.json "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ 代码清理分析失败"
fi

echo ""

# 生成综合报告
echo "6️⃣ 生成综合测试报告..."
if node comprehensive-test-report.js; then
    echo "✅ 综合测试报告生成完成"
    mv comprehensive-final-report.json "$REPORT_DIR/" 2>/dev/null || true
    mv comprehensive-final-report.md "$REPORT_DIR/" 2>/dev/null || true
else
    echo "❌ 综合测试报告生成失败"
fi

echo ""
echo "📊 测试完成！"
echo "=================================="

# 显示报告文件
echo "📁 测试报告文件:"
if [ -d "$REPORT_DIR" ]; then
    ls -la "$REPORT_DIR"
else
    echo "⚠️  报告目录不存在"
fi

echo ""
echo "🎯 测试总结:"
echo "- 基础功能测试: 检查所有页面和功能模块的可访问性"
echo "- 交互元素测试: 分析页面交互元素和API端点"
echo "- API端点验证: 验证所有API端点的功能正确性"
echo "- 自动化测试套件: 运行单元测试、集成测试和E2E测试"
echo "- 代码清理分析: 识别未使用的文件和代码优化建议"
echo "- 综合报告: 汇总所有测试结果和修复建议"

echo ""
echo "📖 查看详细报告:"
echo "- 打开 $REPORT_DIR/comprehensive-final-report.md 查看完整报告"
echo "- 各个JSON文件包含详细的测试数据"

echo ""
echo "✨ 测试脚本执行完成！"

# 如果我们启动了服务器，询问是否要停止它
if [ ! -z "$SERVER_PID" ]; then
    echo ""
    read -p "🤔 是否要停止测试服务器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🛑 停止服务器..."
        kill $SERVER_PID 2>/dev/null || true
        echo "✅ 服务器已停止"
    else
        echo "ℹ️  服务器继续运行 (PID: $SERVER_PID)"
        echo "   如需手动停止，请运行: kill $SERVER_PID"
    fi
fi
