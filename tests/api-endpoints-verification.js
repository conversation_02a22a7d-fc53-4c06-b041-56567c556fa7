/**
 * API端点验证测试
 * 验证所有新实现的API端点是否正常工作
 */

const axios = require('axios');

class APIEndpointsVerifier {
    constructor() {
        this.baseURL = 'http://localhost:3003';
        this.session = null;
        this.results = {
            passed: 0,
            failed: 0,
            endpoints: {},
            summary: {}
        };
    }

    async login() {
        try {
            const loginResponse = await axios.post(`${this.baseURL}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            }, {
                withCredentials: true,
                headers: { 'Content-Type': 'application/json' }
            });
            
            this.session = loginResponse.headers['set-cookie'];
            return true;
        } catch (error) {
            console.error('登录失败:', error.message);
            return false;
        }
    }

    async testEndpoint(endpoint, expectedFields = []) {
        try {
            console.log(`🔍 测试端点: ${endpoint}`);
            
            const response = await axios.get(`${this.baseURL}${endpoint}`, {
                headers: {
                    'Cookie': this.session ? this.session.join('; ') : ''
                },
                timeout: 10000
            });
            
            if (response.status === 200) {
                const data = response.data;
                
                // 验证响应结构
                const hasSuccess = data.hasOwnProperty('success');
                const hasData = data.hasOwnProperty('data');
                const isSuccessTrue = data.success === true;
                
                let fieldValidation = true;
                let missingFields = [];
                
                if (expectedFields.length > 0 && data.data && data.data.items) {
                    const firstItem = data.data.items[0];
                    if (firstItem) {
                        expectedFields.forEach(field => {
                            if (!firstItem.hasOwnProperty(field)) {
                                fieldValidation = false;
                                missingFields.push(field);
                            }
                        });
                    }
                }
                
                const result = {
                    status: response.status,
                    success: true,
                    hasValidStructure: hasSuccess && hasData && isSuccessTrue,
                    dataCount: data.data && data.data.items ? data.data.items.length : 0,
                    hasPagination: data.data && data.data.pagination ? true : false,
                    fieldValidation,
                    missingFields,
                    responseTime: response.headers['x-response-time'] || 'N/A'
                };
                
                this.results.endpoints[endpoint] = result;
                this.results.passed++;
                
                console.log(`✅ ${endpoint} - 状态: ${response.status}, 数据条数: ${result.dataCount}`);
                
                if (missingFields.length > 0) {
                    console.log(`   ⚠️  缺失字段: ${missingFields.join(', ')}`);
                }
                
                return result;
                
            } else {
                throw new Error(`意外的状态码: ${response.status}`);
            }
            
        } catch (error) {
            const result = {
                status: error.response?.status || 'ERROR',
                success: false,
                error: error.message,
                hasValidStructure: false,
                dataCount: 0,
                hasPagination: false,
                fieldValidation: false
            };
            
            this.results.endpoints[endpoint] = result;
            this.results.failed++;
            
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
            return result;
        }
    }

    async runVerification() {
        console.log('🚀 开始API端点验证测试...\n');
        
        const loginSuccess = await this.login();
        if (!loginSuccess) {
            console.log('❌ 登录失败，无法继续测试');
            return this.results;
        }

        // 定义要测试的API端点和预期字段
        const endpointsToTest = [
            {
                endpoint: '/api/users',
                expectedFields: ['id', 'username', 'name', 'email', 'role', 'status']
            },
            {
                endpoint: '/api/tenants',
                expectedFields: ['id', 'company_name', 'contact_name', 'email', 'status']
            },
            {
                endpoint: '/api/flocks',
                expectedFields: ['id', 'flock_name', 'breed', 'status']
            },
            {
                endpoint: '/api/production',
                expectedFields: ['id', 'flock_id', 'record_date', 'egg_count']
            },
            {
                endpoint: '/api/finance',
                expectedFields: ['id', 'type', 'category', 'amount', 'transaction_date']
            },
            {
                endpoint: '/api/inventory',
                expectedFields: ['id', 'item_name', 'category', 'current_stock', 'unit']
            },
            {
                endpoint: '/api/reports',
                expectedFields: []
            },
            {
                endpoint: '/api/goose-prices',
                expectedFields: ['id', 'region', 'price_per_kg', 'price_date']
            },
            {
                endpoint: '/api/mall/products',
                expectedFields: ['id', 'name', 'category', 'price', 'stock', 'status']
            },
            {
                endpoint: '/api/knowledge',
                expectedFields: ['id', 'title', 'category', 'content', 'author']
            },
            {
                endpoint: '/api/announcements',
                expectedFields: ['id', 'title', 'content', 'type', 'status']
            }
        ];

        // 测试所有端点
        for (const test of endpointsToTest) {
            await this.testEndpoint(test.endpoint, test.expectedFields);
            await new Promise(resolve => setTimeout(resolve, 500)); // 避免请求过快
        }

        return this.results;
    }

    generateSummary() {
        const totalTests = this.results.passed + this.results.failed;
        const successRate = totalTests > 0 ? ((this.results.passed / totalTests) * 100).toFixed(2) : 0;
        
        this.results.summary = {
            totalEndpoints: totalTests,
            passedEndpoints: this.results.passed,
            failedEndpoints: this.results.failed,
            successRate: `${successRate}%`,
            workingEndpoints: Object.keys(this.results.endpoints).filter(
                endpoint => this.results.endpoints[endpoint].success
            ),
            failedEndpoints: Object.keys(this.results.endpoints).filter(
                endpoint => !this.results.endpoints[endpoint].success
            )
        };
        
        return this.results.summary;
    }

    printReport() {
        const summary = this.generateSummary();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 API端点验证测试结果');
        console.log('='.repeat(60));
        console.log(`总端点数: ${summary.totalEndpoints}`);
        console.log(`通过数: ${summary.passedEndpoints}`);
        console.log(`失败数: ${summary.failedEndpoints}`);
        console.log(`成功率: ${summary.successRate}`);
        
        if (summary.workingEndpoints.length > 0) {
            console.log('\n✅ 正常工作的端点:');
            summary.workingEndpoints.forEach(endpoint => {
                const result = this.results.endpoints[endpoint];
                console.log(`  - ${endpoint} (${result.dataCount}条数据)`);
            });
        }
        
        if (summary.failedEndpoints.length > 0) {
            console.log('\n❌ 失败的端点:');
            summary.failedEndpoints.forEach(endpoint => {
                const result = this.results.endpoints[endpoint];
                console.log(`  - ${endpoint}: ${result.error}`);
            });
        }
        
        console.log('\n🎯 修复建议:');
        if (summary.failedEndpoints.length > 0) {
            console.log('- 检查失败端点的数据库表是否存在');
            console.log('- 验证数据库连接和权限设置');
            console.log('- 检查路由配置是否正确');
        } else {
            console.log('- 所有API端点都正常工作！');
        }
    }

    async saveResults() {
        const fs = require('fs');
        const path = require('path');
        
        const reportPath = path.join(__dirname, 'api-verification-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
        console.log(`\n📊 API验证报告已保存到: ${reportPath}`);
    }
}

// 运行验证测试
async function runAPIVerification() {
    const verifier = new APIEndpointsVerifier();
    
    try {
        await verifier.runVerification();
        verifier.printReport();
        await verifier.saveResults();
        
        return verifier.results;
        
    } catch (error) {
        console.error('API验证测试失败:', error);
    }
}

if (require.main === module) {
    runAPIVerification();
}

module.exports = APIEndpointsVerifier;
