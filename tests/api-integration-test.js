/**
 * API集成测试
 * 验证前后端数据对接是否正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4001';
const API_BASE = `${BASE_URL}/api/admin`;

// 测试结果收集
const testResults = [];

/**
 * 执行API测试
 */
async function runApiTests() {
  console.log('🚀 开始API集成测试...\n');
  
  // 测试1: 统计数据API
  await testStatisticsApi();
  
  // 测试2: 租户列表API
  await testTenantsApi();
  
  // 测试3: 用户列表API
  await testUsersApi();
  
  // 测试4: 待审批列表API
  await testPendingApi();
  
  // 测试5: 静态文件服务
  await testStaticFiles();
  
  // 输出测试结果
  printTestResults();
}

/**
 * 测试统计数据API
 */
async function testStatisticsApi() {
  try {
    console.log('📊 测试统计数据API...');
    const response = await axios.get(`${API_BASE}/statistics`);
    
    if (response.status === 200 && response.data.success) {
      const data = response.data.data;
      console.log('✅ 统计数据API测试通过');
      console.log(`   - 租户总数: ${data.tenants.total_tenants}`);
      console.log(`   - 活跃租户: ${data.tenants.active_tenants}`);
      console.log(`   - 用户总数: ${data.users.total_users}`);
      console.log(`   - 待审批总数: ${data.pending.total}`);
      
      testResults.push({
        test: '统计数据API',
        status: 'PASS',
        details: `获取到${data.tenants.total_tenants}个租户，${data.users.total_users}个用户`
      });
    } else {
      throw new Error('API响应格式错误');
    }
  } catch (error) {
    console.log('❌ 统计数据API测试失败:', error.message);
    testResults.push({
      test: '统计数据API',
      status: 'FAIL',
      error: error.message
    });
  }
  console.log('');
}

/**
 * 测试租户列表API
 */
async function testTenantsApi() {
  try {
    console.log('🏢 测试租户列表API...');
    const response = await axios.get(`${API_BASE}/tenants?page=1&limit=5`);
    
    if (response.status === 200 && response.data.success) {
      const data = response.data.data;
      console.log('✅ 租户列表API测试通过');
      console.log(`   - 返回租户数量: ${data.list.length}`);
      console.log(`   - 总租户数: ${data.pagination.total}`);
      console.log(`   - 分页信息: 第${data.pagination.page}页，共${data.pagination.pages}页`);
      
      if (data.list.length > 0) {
        console.log(`   - 示例租户: ${data.list[0].name} (${data.list[0].tenant_code})`);
      }
      
      testResults.push({
        test: '租户列表API',
        status: 'PASS',
        details: `获取到${data.list.length}个租户记录`
      });
    } else {
      throw new Error('API响应格式错误');
    }
  } catch (error) {
    console.log('❌ 租户列表API测试失败:', error.message);
    testResults.push({
      test: '租户列表API',
      status: 'FAIL',
      error: error.message
    });
  }
  console.log('');
}

/**
 * 测试用户列表API
 */
async function testUsersApi() {
  try {
    console.log('👥 测试用户列表API...');
    const response = await axios.get(`${API_BASE}/users?page=1&limit=5`);
    
    if (response.status === 200 && response.data.success) {
      const data = response.data.data;
      console.log('✅ 用户列表API测试通过');
      console.log(`   - 返回用户数量: ${data.list.length}`);
      console.log(`   - 总用户数: ${data.pagination.total}`);
      
      if (data.list.length > 0) {
        console.log(`   - 示例用户: ${data.list[0].username} (${data.list[0].nickname})`);
      }
      
      testResults.push({
        test: '用户列表API',
        status: 'PASS',
        details: `获取到${data.list.length}个用户记录`
      });
    } else {
      throw new Error('API响应格式错误');
    }
  } catch (error) {
    console.log('❌ 用户列表API测试失败:', error.message);
    testResults.push({
      test: '用户列表API',
      status: 'FAIL',
      error: error.message
    });
  }
  console.log('');
}

/**
 * 测试待审批列表API
 */
async function testPendingApi() {
  try {
    console.log('⏳ 测试待审批列表API...');
    const response = await axios.get(`${API_BASE}/pending?limit=10`);
    
    if (response.status === 200 && response.data.success) {
      const data = response.data.data;
      console.log('✅ 待审批列表API测试通过');
      console.log(`   - 待审批数量: ${data.total}`);
      console.log(`   - 返回记录数: ${data.list.length}`);
      
      testResults.push({
        test: '待审批列表API',
        status: 'PASS',
        details: `获取到${data.list.length}个待审批记录`
      });
    } else {
      throw new Error('API响应格式错误');
    }
  } catch (error) {
    console.log('❌ 待审批列表API测试失败:', error.message);
    testResults.push({
      test: '待审批列表API',
      status: 'FAIL',
      error: error.message
    });
  }
  console.log('');
}

/**
 * 测试静态文件服务
 */
async function testStaticFiles() {
  try {
    console.log('📁 测试静态文件服务...');
    const response = await axios.get(`${BASE_URL}/admin-ui`);
    
    if (response.status === 200 && response.data.includes('智慧养鹅平台')) {
      console.log('✅ 静态文件服务测试通过');
      console.log('   - Vue + Element Plus管理界面可正常访问');
      
      testResults.push({
        test: '静态文件服务',
        status: 'PASS',
        details: '管理界面HTML文件正常加载'
      });
    } else {
      throw new Error('静态文件内容异常');
    }
  } catch (error) {
    console.log('❌ 静态文件服务测试失败:', error.message);
    testResults.push({
      test: '静态文件服务',
      status: 'FAIL',
      error: error.message
    });
  }
  console.log('');
}

/**
 * 输出测试结果
 */
function printTestResults() {
  console.log('📋 测试结果汇总:');
  console.log('='.repeat(50));
  
  const passedTests = testResults.filter(r => r.status === 'PASS').length;
  const failedTests = testResults.filter(r => r.status === 'FAIL').length;
  
  testResults.forEach((result, index) => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.test}`);
    if (result.details) {
      console.log(`   ${result.details}`);
    }
    if (result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`总测试数: ${testResults.length}`);
  console.log(`通过: ${passedTests}`);
  console.log(`失败: ${failedTests}`);
  console.log(`成功率: ${((passedTests / testResults.length) * 100).toFixed(2)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 所有API集成测试通过！前后端数据对接正常工作。');
  } else {
    console.log('\n⚠️  部分测试失败，需要检查相关功能。');
  }
}

// 运行测试
if (require.main === module) {
  runApiTests().catch(console.error);
}

module.exports = { runApiTests };
