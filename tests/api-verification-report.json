{"passed": 10, "failed": 1, "endpoints": {"/api/users": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 1, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/tenants": {"status": 500, "success": false, "error": "Request failed with status code 500", "hasValidStructure": false, "dataCount": 0, "hasPagination": false, "fieldValidation": false}, "/api/flocks": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 8, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/production": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 2, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/finance": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/inventory": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/reports": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 0, "hasPagination": false, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/goose-prices": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/mall/products": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/knowledge": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}, "/api/announcements": {"status": 200, "success": true, "hasValidStructure": true, "dataCount": 3, "hasPagination": true, "fieldValidation": true, "missingFields": [], "responseTime": "N/A"}}, "summary": {"totalEndpoints": 11, "passedEndpoints": 10, "failedEndpoints": ["/api/tenants"], "successRate": "90.91%", "workingEndpoints": ["/api/users", "/api/flocks", "/api/production", "/api/finance", "/api/inventory", "/api/reports", "/api/goose-prices", "/api/mall/products", "/api/knowledge", "/api/announcements"]}}