const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅SAAS平台管理后台 - 正确的功能测试
 * 基于实际的SAAS平台管理后台结构进行测试
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  }
};

// 测试结果记录
let testResults = {
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  menuTests: [],
  buttonTests: [],
  issues: []
};

// 登录辅助函数
async function performLogin(page) {
  console.log('🔐 执行登录操作...');
  
  await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
  await page.waitForLoadState('networkidle');
  
  await page.waitForSelector('input[name="username"]', { timeout: 10000 });
  await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
  await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
  await page.click('button[type="submit"]');
  await page.waitForURL(/.*\/dashboard/, { timeout: 15000 });
  
  console.log('✅ 登录成功');
  return true;
}

// 截图辅助函数
async function takeScreenshot(page, name) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    
    await page.screenshot({ 
      path: `test-results/screenshots/${filename}`,
      fullPage: true 
    });
    
    console.log(`📸 截图保存: ${filename}`);
    return filename;
  } catch (error) {
    console.log(`⚠️ 截图失败: ${error.message}`);
    return null;
  }
}

// 记录测试结果
function recordTest(category, name, passed, details = '') {
  const result = {
    category,
    name,
    passed,
    details,
    timestamp: new Date().toISOString()
  };
  
  if (category === 'menu') {
    testResults.menuTests.push(result);
  } else if (category === 'button') {
    testResults.buttonTests.push(result);
  }
  
  testResults.totalTests++;
  if (passed) {
    testResults.passedTests++;
    console.log(`✅ ${category} - ${name}: ${details}`);
  } else {
    testResults.failedTests++;
    testResults.issues.push(`${category} - ${name}: ${details}`);
    console.log(`❌ ${category} - ${name}: ${details}`);
  }
}

test.describe('智慧养鹅SAAS平台管理后台 - 正确功能测试', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始SAAS平台管理后台正确功能测试');
    
    // 创建截图目录
    const fs = require('fs');
    const path = require('path');
    const screenshotDir = path.join(process.cwd(), 'test-results', 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
  });

  // 1. SAAS平台主导航菜单测试
  test('SAAS平台主导航菜单测试', async ({ page }) => {
    console.log('\n📋 开始SAAS平台主导航菜单测试...');
    
    try {
      await performLogin(page);
      await page.waitForTimeout(2000);
      
      await takeScreenshot(page, 'saas-dashboard');
      
      // 测试实际存在的SAAS平台菜单项
      const saasMenuItems = [
        { name: '平台仪表盘', selector: 'a[href="/dashboard"]' },
        { name: '租户列表', selector: 'a[href="/tenants"]' },
        { name: '订阅管理', selector: 'a[href="/tenants/subscriptions"]' },
        { name: '使用统计', selector: 'a[href="/tenants/usage"]' },
        { name: '价格管理', selector: 'a[href="/goose-prices"]' },
        { name: '地区配置', selector: 'a[href="/goose-prices/regions"]' },
        { name: '价格趋势', selector: 'a[href="/goose-prices/trends"]' },
        { name: '知识文章', selector: 'a[href="/knowledge"]' },
        { name: '分类管理', selector: 'a[href="/knowledge/categories"]' },
        { name: '标签管理', selector: 'a[href="/knowledge/tags"]' },
        { name: '公告列表', selector: 'a[href="/announcements"]' },
        { name: '发布公告', selector: 'a[href="/announcements/create"]' },
        { name: '商品管理', selector: 'a[href="/mall/products"]' },
        { name: '订单管理', selector: 'a[href="/mall/orders"]' },
        { name: '库存管理', selector: 'a[href="/mall/inventory"]' },
        { name: '平台用户', selector: 'a[href="/platform-users"]' }
      ];
      
      for (const menuItem of saasMenuItems) {
        try {
          console.log(`🔍 测试SAAS菜单项: ${menuItem.name}`);
          
          // 查找菜单链接
          const menuLink = page.locator(menuItem.selector).first();
          const linkExists = await menuLink.count() > 0;
          
          if (linkExists) {
            // 检查链接是否可见（可能在折叠的子菜单中）
            const isVisible = await menuLink.isVisible();
            
            if (!isVisible) {
              // 尝试展开父级菜单
              const parentMenu = page.locator('.nav-item:has(.nav-treeview)').filter({ has: menuLink });
              if (await parentMenu.count() > 0) {
                const parentLink = parentMenu.locator('> .nav-link').first();
                if (await parentLink.count() > 0) {
                  await parentLink.click();
                  await page.waitForTimeout(500);
                }
              }
            }
            
            // 现在尝试点击菜单项
            if (await menuLink.isVisible()) {
              await menuLink.click();
              await page.waitForLoadState('networkidle');
              await page.waitForTimeout(1000);
              
              // 验证页面是否正确跳转
              const currentURL = page.url();
              const correctNavigation = currentURL.includes(menuItem.selector.match(/href="([^"]+)"/)[1]);
              
              recordTest('menu', menuItem.name, correctNavigation, 
                `菜单链接存在且可点击，跳转到: ${currentURL}`);
              
              if (correctNavigation) {
                await takeScreenshot(page, `saas-menu-${menuItem.name.replace(/[^a-zA-Z0-9]/g, '-')}`);
              }
            } else {
              recordTest('menu', menuItem.name, false, '菜单链接存在但不可见');
            }
            
          } else {
            recordTest('menu', menuItem.name, false, '菜单链接不存在');
          }
          
        } catch (menuError) {
          recordTest('menu', menuItem.name, false, `菜单测试失败: ${menuError.message}`);
        }
      }
      
    } catch (error) {
      recordTest('menu', 'SAAS导航菜单', false, `SAAS导航菜单测试失败: ${error.message}`);
    }
  });

  // 2. 租户管理页面功能测试
  test('租户管理页面功能测试', async ({ page }) => {
    console.log('\n🏢 开始租户管理页面功能测试...');
    
    try {
      await performLogin(page);
      
      // 导航到租户管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/tenants`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      await takeScreenshot(page, 'tenants-page');
      
      // 测试租户管理页面的按钮
      const tenantButtons = [
        { name: '添加租户按钮', selector: 'button:has-text("添加"), button:has-text("新增"), .btn-primary' },
        { name: '搜索功能', selector: 'input[type="search"], input[placeholder*="搜索"], .search-input' },
        { name: '筛选功能', selector: 'select, .form-select' },
        { name: '导出功能', selector: 'button:has-text("导出"), .btn-export' }
      ];
      
      for (const button of tenantButtons) {
        try {
          const buttonElement = page.locator(button.selector).first();
          const buttonExists = await buttonElement.count() > 0;
          
          if (buttonExists) {
            if (button.name === '搜索功能') {
              // 测试搜索输入
              await buttonElement.fill('test');
              await page.waitForTimeout(500);
              recordTest('button', button.name, true, '搜索输入框存在且可输入');
            } else if (button.name === '添加租户按钮') {
              // 测试添加按钮点击
              await buttonElement.click();
              await page.waitForTimeout(1500);
              
              // 检查是否打开了模态框或跳转页面
              const modal = page.locator('.modal.show, .modal:visible');
              const modalVisible = await modal.count() > 0;
              const currentURL = page.url();
              const isCreatePage = currentURL.includes('create') || currentURL.includes('add');
              
              if (modalVisible || isCreatePage) {
                recordTest('button', button.name, true, '添加租户按钮点击成功');
                await takeScreenshot(page, 'tenants-add-interface');
                
                // 关闭模态框
                if (modalVisible) {
                  const closeBtn = page.locator('.modal .btn-close, .modal .close').first();
                  if (await closeBtn.count() > 0) {
                    await closeBtn.click();
                    await page.waitForTimeout(500);
                  }
                }
              } else {
                recordTest('button', button.name, false, '添加租户按钮点击后没有响应');
              }
            } else {
              recordTest('button', button.name, true, `${button.name}存在`);
            }
          } else {
            recordTest('button', button.name, false, `${button.name}不存在`);
          }
          
        } catch (buttonError) {
          recordTest('button', button.name, false, `${button.name}测试失败: ${buttonError.message}`);
        }
      }
      
    } catch (error) {
      recordTest('button', '租户管理按钮', false, `租户管理按钮测试失败: ${error.message}`);
    }
  });

  // 3. 平台用户管理测试
  test('平台用户管理测试', async ({ page }) => {
    console.log('\n👥 开始平台用户管理测试...');
    
    try {
      await performLogin(page);
      
      // 导航到平台用户页面
      await page.goto(`${TEST_CONFIG.baseURL}/platform-users`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      await takeScreenshot(page, 'platform-users-page');
      
      // 检查页面是否正常加载
      const pageContent = page.locator('body');
      const hasContent = await pageContent.isVisible();
      recordTest('menu', '平台用户页面', hasContent, '平台用户页面正常加载');
      
      // 测试平台用户管理的功能
      const userButtons = [
        { name: '用户搜索', selector: 'input[type="search"], input[placeholder*="搜索"]' },
        { name: '添加用户', selector: 'button:has-text("添加"), button:has-text("新增")' },
        { name: '用户表格', selector: 'table, .table' }
      ];
      
      for (const button of userButtons) {
        const element = page.locator(button.selector).first();
        const exists = await element.count() > 0;
        recordTest('button', button.name, exists, exists ? `${button.name}存在` : `${button.name}不存在`);
      }
      
    } catch (error) {
      recordTest('button', '平台用户管理', false, `平台用户管理测试失败: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 SAAS平台管理后台测试结果汇总:');
    
    const totalTests = testResults.totalTests;
    const passedTests = testResults.passedTests;
    const failedTests = testResults.failedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过测试: ${passedTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    console.log('\n=== SAAS平台菜单测试结果 ===');
    testResults.menuTests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });
    
    console.log('\n=== SAAS平台按钮测试结果 ===');
    testResults.buttonTests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });
    
    if (testResults.issues.length > 0) {
      console.log('\n=== 发现的问题 ===');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    console.log('\n🎉 SAAS平台管理后台测试完成！');
  });

});
