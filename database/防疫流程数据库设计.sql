-- 防疫流程管理数据库表设计
-- 适用于智慧养鹅全栈系统

-- 1. 防疫流程模板表 (vaccination_templates)
CREATE TABLE IF NOT EXISTS vaccination_templates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '流程名称，如：狮头鹅1600只防疫流程',
  description TEXT COMMENT '流程描述',
  total_days INT DEFAULT 30 COMMENT '流程总天数',
  breed VARCHAR(50) DEFAULT '狮头鹅' COMMENT '鹅品种',
  total_quantity INT DEFAULT 1600 COMMENT '适用数量',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '模板状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '防疫流程模板表';

-- 2. 防疫流程步骤表 (vaccination_steps)
CREATE TABLE IF NOT EXISTS vaccination_steps (
  id INT PRIMARY KEY AUTO_INCREMENT,
  template_id INT NOT NULL COMMENT '关联防疫模板ID',
  day_age VARCHAR(20) NOT NULL COMMENT '日龄，如：1日龄, 2-5日龄, 6日龄等',
  start_day INT NOT NULL COMMENT '开始天数',
  end_day INT COMMENT '结束天数（如果是范围）',
  prevention_disease TEXT COMMENT '预防疾病',
  medication TEXT COMMENT '用药方案',
  dosage_instructions TEXT COMMENT '用药说明和注意事项',
  cost DECIMAL(10,2) DEFAULT 0 COMMENT '单次用药成本',
  priority ENUM('high', 'medium', 'low') DEFAULT 'medium' COMMENT '重要程度',
  category ENUM('vaccination', 'medication', 'care', 'feeding') DEFAULT 'medication' COMMENT '类型：疫苗、药物、护理、饲喂',
  sort_order INT DEFAULT 0 COMMENT '排序',
  FOREIGN KEY (template_id) REFERENCES vaccination_templates(id) ON DELETE CASCADE,
  INDEX idx_template_day (template_id, start_day)
) COMMENT '防疫流程步骤表';

-- 3. 鹅群批次防疫记录表 (flock_vaccination_records)
CREATE TABLE IF NOT EXISTS flock_vaccination_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  flock_id INT NOT NULL COMMENT '鹅群ID',
  template_id INT NOT NULL COMMENT '使用的防疫模板ID',
  start_date DATE NOT NULL COMMENT '开始日期（入栏日期）',
  current_day INT DEFAULT 1 COMMENT '当前天数',
  total_geese INT NOT NULL COMMENT '鹅群总数',
  status ENUM('active', 'completed', 'paused') DEFAULT 'active' COMMENT '执行状态',
  completion_rate DECIMAL(5,2) DEFAULT 0 COMMENT '完成率',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (template_id) REFERENCES vaccination_templates(id),
  INDEX idx_flock_status (flock_id, status),
  INDEX idx_start_date (start_date)
) COMMENT '鹅群批次防疫记录表';

-- 4. 防疫任务执行记录表 (vaccination_task_records)
CREATE TABLE IF NOT EXISTS vaccination_task_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  flock_record_id INT NOT NULL COMMENT '鹅群防疫记录ID',
  step_id INT NOT NULL COMMENT '防疫步骤ID',
  scheduled_date DATE NOT NULL COMMENT '计划执行日期',
  actual_date DATE COMMENT '实际执行日期',
  status ENUM('pending', 'in_progress', 'completed', 'skipped', 'failed') DEFAULT 'pending' COMMENT '任务状态',
  executor_id INT COMMENT '执行人员ID',
  executor_name VARCHAR(50) COMMENT '执行人员姓名',
  actual_dosage TEXT COMMENT '实际用药量',
  mortality_count INT DEFAULT 0 COMMENT '死亡数量',
  side_effects TEXT COMMENT '不良反应记录',
  effectiveness_score INT COMMENT '效果评分(1-10)',
  cost DECIMAL(10,2) DEFAULT 0 COMMENT '实际成本',
  photos JSON COMMENT '照片记录',
  notes TEXT COMMENT '执行备注',
  weather_info JSON COMMENT '天气信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (flock_record_id) REFERENCES flock_vaccination_records(id) ON DELETE CASCADE,
  FOREIGN KEY (step_id) REFERENCES vaccination_steps(id),
  INDEX idx_scheduled_date (scheduled_date),
  INDEX idx_status (status),
  INDEX idx_flock_step (flock_record_id, step_id)
) COMMENT '防疫任务执行记录表';

-- 5. 防疫提醒表 (vaccination_reminders)
CREATE TABLE IF NOT EXISTS vaccination_reminders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_record_id INT NOT NULL COMMENT '任务记录ID',
  remind_date DATE NOT NULL COMMENT '提醒日期',
  remind_time TIME DEFAULT '08:00:00' COMMENT '提醒时间',
  title VARCHAR(200) NOT NULL COMMENT '提醒标题',
  content TEXT COMMENT '提醒内容',
  type ENUM('vaccination', 'medication', 'observation', 'preparation') DEFAULT 'medication' COMMENT '提醒类型',
  status ENUM('pending', 'sent', 'read', 'completed') DEFAULT 'pending' COMMENT '提醒状态',
  user_id INT COMMENT '接收用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (task_record_id) REFERENCES vaccination_task_records(id) ON DELETE CASCADE,
  INDEX idx_remind_date (remind_date, remind_time),
  INDEX idx_user_status (user_id, status)
) COMMENT '防疫提醒表';

-- 6. 药品库存表 (medication_inventory)
CREATE TABLE IF NOT EXISTS medication_inventory (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '药品名称',
  type ENUM('vaccine', 'medicine', 'supplement', 'disinfectant') DEFAULT 'medicine' COMMENT '药品类型',
  specification VARCHAR(100) COMMENT '规格',
  unit VARCHAR(20) DEFAULT '瓶' COMMENT '单位',
  stock_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '库存数量',
  min_stock DECIMAL(10,2) DEFAULT 0 COMMENT '最低库存预警',
  unit_price DECIMAL(10,2) DEFAULT 0 COMMENT '单价',
  supplier VARCHAR(100) COMMENT '供应商',
  expiry_date DATE COMMENT '过期日期',
  storage_conditions TEXT COMMENT '储存条件',
  usage_instructions TEXT COMMENT '使用说明',
  status ENUM('active', 'inactive', 'expired') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_expiry (expiry_date),
  INDEX idx_stock (stock_quantity, min_stock)
) COMMENT '药品库存表';

-- 7. 用药统计表 (medication_usage_stats)
CREATE TABLE IF NOT EXISTS medication_usage_stats (
  id INT PRIMARY KEY AUTO_INCREMENT,
  medication_id INT NOT NULL COMMENT '药品ID',
  flock_record_id INT NOT NULL COMMENT '鹅群记录ID',
  usage_date DATE NOT NULL COMMENT '使用日期',
  quantity_used DECIMAL(10,2) NOT NULL COMMENT '使用数量',
  unit_cost DECIMAL(10,2) NOT NULL COMMENT '单位成本',
  total_cost DECIMAL(10,2) NOT NULL COMMENT '总成本',
  effectiveness_rating INT COMMENT '效果评级(1-10)',
  notes TEXT COMMENT '使用备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (medication_id) REFERENCES medication_inventory(id),
  FOREIGN KEY (flock_record_id) REFERENCES flock_vaccination_records(id) ON DELETE CASCADE,
  INDEX idx_usage_date (usage_date),
  INDEX idx_medication_flock (medication_id, flock_record_id)
) COMMENT '用药统计表';

-- 插入默认防疫模板
INSERT INTO vaccination_templates (name, description, total_days, breed, total_quantity) VALUES 
('鼎晶1600只鹅科学防疫养殖流程', '山东鼎晶生物狮头鹅（1600只）养殖用药流程表，预防用药程序参考，根据季节和地区适当的做变动', 30, '狮头鹅', 1600);

-- 插入默认药品库存
INSERT INTO medication_inventory (name, type, specification, unit, unit_price, usage_instructions) VALUES
('呼肠清', 'medicine', '瓶装', '瓶', 95.00, '上午用，每天1瓶，连用两天，集中俩小时饮水'),
('亿消2号', 'medicine', '袋装', '袋', 160.00, '每天半袋，连用两天，上午集中2-3小时饮用'),
('鸭乐2号', 'medicine', '袋装', '袋', 140.00, '配合亿消2号使用'),
('呼畅', 'medicine', '瓶装', '瓶', 60.00, '每天1瓶，连喂2天，下午集中3小时内饮用'),
('肠速清', 'medicine', '袋装', '袋', 95.00, '一天用1包，连用两天，上午集中3小时饮用'),
('痛清', 'medicine', '瓶装', '瓶', 95.00, '每天2瓶，用两天，共4瓶，第7天上午集中2小时用'),
('增强素', 'supplement', '袋装', '袋', 120.00, '每天各1包，共2包上午集中3小时饮用'),
('浆速', 'medicine', '袋装', '袋', 150.00, '每天各1包，共2包上午集中3小时饮用'),
('小鹅瘟抗体', 'vaccine', '毫升', 'ml', 2.00, '每只打1.2毫升'),
('痛风+呼肠孤+小鹅瘟疫苗+浆膜炎', 'vaccine', '毫升', 'ml', 1.50, '每只打1毫升'),
('副粘+禽流感H9+安卡拉（三联苗）', 'vaccine', '毫升', 'ml', 1.80, '每只打1毫升'),
('3%葡萄糖', 'supplement', '袋装', '袋', 10.00, '饮水用，增强抵抗力，补充水分，减少应激'),
('电解多维', 'supplement', '袋装', '袋', 25.00, '配合葡萄糖使用'),
('小苏打', 'supplement', '袋装', '袋', 5.00, '晚上10点以后使用千分之三小苏打通肝肾');