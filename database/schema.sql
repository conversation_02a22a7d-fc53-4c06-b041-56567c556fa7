-- 智慧养鹅平台数据库表结构
-- 权限系统与用户认证完整解决方案数据库设计

-- 1. 平台管理员表
CREATE TABLE IF NOT EXISTS platform_admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('super_admin', 'admin') DEFAULT 'admin' COMMENT '角色:超级管理员/管理员',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '平台管理员表';

-- 2. 租户(养殖场)表
CREATE TABLE IF NOT EXISTS tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_code VARCHAR(20) NOT NULL UNIQUE COMMENT '租户代码',
    farm_name VARCHAR(100) NOT NULL COMMENT '养殖场名称',
    legal_representative VARCHAR(50) NOT NULL COMMENT '法定代表人',
    business_license VARCHAR(50) COMMENT '营业执照号',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    province VARCHAR(20) NOT NULL COMMENT '省份',
    city VARCHAR(20) NOT NULL COMMENT '城市',
    district VARCHAR(20) COMMENT '区县',
    detailed_address VARCHAR(200) NOT NULL COMMENT '详细地址',
    farm_scale ENUM('small', 'medium', 'large') NOT NULL COMMENT '养殖规模:小型/中型/大型',
    scale_description VARCHAR(100) COMMENT '规模描述',
    breed_types JSON COMMENT '养殖品种(JSON数组)',
    breeding_years INT DEFAULT 0 COMMENT '养殖年限',
    facilities JSON COMMENT '设施设备(JSON对象)',
    certifications JSON COMMENT '认证资质(JSON数组)',
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending' COMMENT '状态',
    rejection_reason TEXT COMMENT '拒绝原因',
    approved_at DATETIME COMMENT '审批时间',
    approved_by BIGINT COMMENT '审批人员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (approved_by) REFERENCES platform_admins(id) ON DELETE SET NULL
) COMMENT '租户(养殖场)表';

-- 3. 微信用户表
CREATE TABLE IF NOT EXISTS wechat_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信openid',
    unionid VARCHAR(100) COMMENT '微信unionid',
    nickname VARCHAR(100) COMMENT '微信昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别:0未知1男2女',
    language VARCHAR(10) COMMENT '语言',
    city VARCHAR(50) COMMENT '城市',
    province VARCHAR(50) COMMENT '省份',
    country VARCHAR(50) COMMENT '国家',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '微信用户表';

-- 4. 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    wechat_user_id BIGINT COMMENT '关联微信用户ID',
    user_code VARCHAR(20) NOT NULL COMMENT '用户编码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    id_card VARCHAR(18) COMMENT '身份证号',
    department VARCHAR(50) COMMENT '部门',
    position VARCHAR(50) COMMENT '职位',
    role ENUM('admin', 'manager', 'finance', 'employee') NOT NULL DEFAULT 'employee' COMMENT '角色',
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending' COMMENT '状态',
    is_farm_admin BOOLEAN DEFAULT FALSE COMMENT '是否为养殖场管理员',
    permissions JSON COMMENT '特殊权限配置',
    rejection_reason TEXT COMMENT '拒绝原因',
    approved_at DATETIME COMMENT '审批时间',
    approved_by BIGINT COMMENT '审批人员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_user_code (tenant_id, user_code),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (wechat_user_id) REFERENCES wechat_users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES platform_admins(id) ON DELETE SET NULL
) COMMENT '用户表';

-- 5. 用户登录记录表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    login_type ENUM('wechat', 'password') NOT NULL COMMENT '登录方式',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL COMMENT '登出时间',
    session_duration INT COMMENT '会话时长(秒)',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '用户登录记录表';

-- 6. 权限配置表
CREATE TABLE IF NOT EXISTS permission_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    action VARCHAR(50) NOT NULL COMMENT '操作',
    role_admin BOOLEAN DEFAULT TRUE COMMENT '管理员权限',
    role_manager BOOLEAN DEFAULT TRUE COMMENT '经理权限',
    role_finance BOOLEAN DEFAULT FALSE COMMENT '财务权限',
    role_employee BOOLEAN DEFAULT FALSE COMMENT '员工权限',
    description VARCHAR(200) COMMENT '权限描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_module_action (module, action)
) COMMENT '权限配置表';

-- 7. 审批流程记录表
CREATE TABLE IF NOT EXISTS approval_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    application_type ENUM('tenant', 'user') NOT NULL COMMENT '申请类型:租户/用户',
    application_id BIGINT NOT NULL COMMENT '申请ID',
    applicant_type ENUM('tenant', 'user') NOT NULL COMMENT '申请人类型',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    approver_id BIGINT COMMENT '审批人ID',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '状态',
    comment TEXT COMMENT '审批意见',
    approved_at DATETIME COMMENT '审批时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (approver_id) REFERENCES platform_admins(id) ON DELETE SET NULL
) COMMENT '审批流程记录表';

-- 8. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

-- 9. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    operator_type ENUM('admin', 'user') NOT NULL COMMENT '操作者类型',
    operator_id BIGINT NOT NULL COMMENT '操作者ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc VARCHAR(200) NOT NULL COMMENT '操作描述',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT '操作日志表';

-- 插入初始数据

-- 初始化平台超级管理员
INSERT INTO platform_admins (username, password, name, email, role) VALUES 
('superadmin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LEa52PVxmKT/6oVjG', '超级管理员', '<EMAIL>', 'super_admin'),
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LEa52PVxmKT/6oVjG', '系统管理员', '<EMAIL>', 'admin');

-- 初始化权限配置
INSERT INTO permission_configs (module, action, role_admin, role_manager, role_finance, role_employee, description) VALUES
('finance', 'ai-comprehensive', TRUE, TRUE, TRUE, FALSE, 'AI财务综合分析'),
('finance', 'overview', TRUE, TRUE, TRUE, FALSE, '财务总览'),
('finance', 'analysis', TRUE, TRUE, TRUE, FALSE, '财务分析'),
('finance', 'forecast', TRUE, TRUE, TRUE, FALSE, '财务预测'),
('finance', 'suggestions', TRUE, TRUE, TRUE, FALSE, '财务建议'),
('finance', 'export', TRUE, TRUE, TRUE, FALSE, '财务导出'),
('production', 'overview', TRUE, TRUE, FALSE, TRUE, '生产总览'),
('production', 'batch-management', TRUE, TRUE, FALSE, TRUE, '批次管理'),
('production', 'health-monitoring', TRUE, TRUE, FALSE, TRUE, '健康监控'),
('health', 'overview', TRUE, TRUE, FALSE, TRUE, '健康总览'),
('health', 'vaccination', TRUE, TRUE, FALSE, TRUE, '疫苗接种'),
('health', 'disease-prevention', TRUE, TRUE, FALSE, TRUE, '疾病预防'),
('market', 'price-monitoring', TRUE, TRUE, TRUE, TRUE, '价格监控'),
('market', 'trend-analysis', TRUE, TRUE, TRUE, FALSE, '趋势分析'),
('approval', 'procurement', TRUE, TRUE, FALSE, FALSE, '采购审批'),
('approval', 'expense', TRUE, TRUE, TRUE, FALSE, '费用审批'),
('user', 'management', TRUE, FALSE, FALSE, FALSE, '用户管理'),
('system', 'settings', TRUE, FALSE, FALSE, FALSE, '系统设置');

-- 初始化系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('wechat.app_id', '', 'string', '微信小程序AppID', FALSE),
('wechat.app_secret', '', 'string', '微信小程序AppSecret', FALSE),
('approval.auto_approve_tenant', 'false', 'boolean', '自动审批租户申请', FALSE),
('approval.auto_approve_user', 'false', 'boolean', '自动审批用户申请', FALSE),
('system.platform_name', '智慧养鹅管理平台', 'string', '平台名称', TRUE),
('system.contact_phone', '************', 'string', '客服电话', TRUE),
('system.contact_email', '<EMAIL>', 'string', '客服邮箱', TRUE);

-- 创建索引
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_tenant_code ON tenants(tenant_code);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_wechat_users_openid ON wechat_users(openid);
CREATE INDEX idx_user_login_logs_user_id ON user_login_logs(user_id);
CREATE INDEX idx_user_login_logs_login_time ON user_login_logs(login_time);
CREATE INDEX idx_approval_records_application_type ON approval_records(application_type, application_id);
CREATE INDEX idx_approval_records_status ON approval_records(status);
CREATE INDEX idx_operation_logs_operator ON operation_logs(operator_type, operator_id);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);