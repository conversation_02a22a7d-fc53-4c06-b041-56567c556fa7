#!/bin/bash

# 快速三端联调测试脚本
# 深度测试每个页面和按键

BASE_URL="http://localhost:4000"
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_PAGES=()
MISSING_PAGES=()

echo "🚀 开始深度三端联调测试"
echo "目标服务器: $BASE_URL"
echo "=================================="

# 函数：测试页面访问
test_page() {
    local path="$1"
    local name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "📄 测试 ${name} 页面 (${path})..."
    
    local response=$(curl -s -w "HTTPSTATUS:%{http_code};SIZE:%{size_download};TIME:%{time_total}" "$BASE_URL$path")
    local http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local size=$(echo "$response" | grep -o "SIZE:[0-9]*" | cut -d: -f2)
    local time=$(echo "$response" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        echo "✅ ${name} 正常加载 (${size}字节, ${time}秒)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 检查内容质量
        local content=$(echo "$response" | sed 's/HTTPSTATUS.*//g')
        if echo "$content" | grep -q "error\|Error\|错误\|异常"; then
            echo "⚠️ ${name} 页面包含错误信息"
            FAILED_PAGES+=("$name: 包含错误信息")
        fi
        
        return 0
    elif [ "$http_code" = "302" ]; then
        echo "🔄 ${name} 重定向 (HTTP $http_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo "❌ ${name} 访问失败 (HTTP $http_code)"
        MISSING_PAGES+=("$name (${path}): HTTP $http_code")
        return 1
    fi
}

# 函数：测试登录功能
test_login() {
    echo -e "\n🔐 测试登录功能..."
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    local login_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}' \
        -w "HTTPSTATUS:%{http_code}" \
        "$BASE_URL/auth/login")
    
    local http_code=$(echo "$login_response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        local response_body=$(echo "$login_response" | sed 's/HTTPSTATUS.*//g')
        if echo "$response_body" | grep -q '"success":true'; then
            echo "✅ 登录功能正常工作"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo "❌ 登录响应异常: $response_body"
            FAILED_PAGES+=("登录功能: 响应异常")
        fi
    else
        echo "❌ 登录请求失败 (HTTP $http_code)"
        FAILED_PAGES+=("登录功能: HTTP $http_code")
    fi
}

# 1. 测试登录功能
test_login

# 2. 测试核心管理页面
echo -e "\n📱 测试核心管理页面..."
test_page "/" "首页"
test_page "/auth/login" "登录页面"
test_page "/dashboard" "仪表板"
test_page "/users" "用户管理"
test_page "/tenants" "租户管理"
test_page "/flocks" "鹅群管理"
test_page "/production" "生产记录"
test_page "/health" "健康管理"
test_page "/finance" "财务管理"
test_page "/inventory" "库存管理"
test_page "/reports" "报表中心"
test_page "/system" "系统设置"

# 3. 测试SAAS特色功能页面
echo -e "\n🏪 测试SAAS特色功能页面..."
test_page "/mall" "商城管理"
test_page "/goose-prices" "鹅价管理"
test_page "/announcements" "公告管理"
test_page "/knowledge" "知识库"

# 4. 测试子功能页面
echo -e "\n🔧 测试子功能页面..."
test_page "/tenants/create" "创建租户"
test_page "/tenants/subscriptions" "订阅管理"
test_page "/tenants/usage" "使用统计"
test_page "/goose-prices/trends" "价格趋势"
test_page "/mall/products" "商品管理"
test_page "/mall/orders" "订单管理"
test_page "/mall/categories" "分类管理"
test_page "/mall/inventory" "库存管理"
test_page "/announcements/create" "创建公告"

# 5. 测试API端点
echo -e "\n🔌 测试API端点..."
api_endpoints=(
    "/health:健康检查"
    "/api/dashboard/stats:仪表板统计"
    "/api/users:用户API"
    "/api/tenants:租户API"
)

for endpoint_info in "${api_endpoints[@]}"; do
    IFS=':' read -r endpoint name <<< "$endpoint_info"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo "🔗 测试API: $name ($endpoint)..."
    local api_response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$BASE_URL$endpoint")
    local api_code=$(echo "$api_response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    
    if [ "$api_code" = "200" ]; then
        echo "✅ $name API正常"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    elif [ "$api_code" = "302" ] || [ "$api_code" = "401" ]; then
        echo "🔒 $name API需要认证 (HTTP $api_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ $name API异常 (HTTP $api_code)"
        FAILED_PAGES+=("$name API: HTTP $api_code")
    fi
done

# 6. 深度内容检查
echo -e "\n🔍 深度内容检查..."
content_checks=(
    "/dashboard:仪表板:仪表盘,统计,数据"
    "/users:用户管理:用户,管理,列表"
    "/tenants:租户管理:租户,企业,订阅"
    "/mall:商城管理:商城,产品,订单"
)

for check_info in "${content_checks[@]}"; do
    IFS=':' read -r check_path check_name check_keywords <<< "$check_info"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo "🔎 检查 $check_name 页面内容..."
    local page_content=$(curl -s "$BASE_URL$check_path")
    
    local has_keywords=false
    IFS=',' read -ra KEYWORDS <<< "$check_keywords"
    for keyword in "${KEYWORDS[@]}"; do
        if echo "$page_content" | grep -qi "$keyword"; then
            has_keywords=true
            break
        fi
    done
    
    if [ "$has_keywords" = true ]; then
        echo "✅ $check_name 内容完整"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "⚠️ $check_name 缺少关键内容"
        FAILED_PAGES+=("$check_name: 缺少关键内容")
    fi
done

# 7. 输出最终测试报告
echo -e "\n" 
echo "=================================="
echo "📊 深度三端联调测试报告"
echo "=================================="
echo "🎯 总测试数: $TOTAL_TESTS"
echo "✅ 通过测试: $PASSED_TESTS"
echo "❌ 失败测试: $((TOTAL_TESTS - PASSED_TESTS))"

success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "📈 成功率: ${success_rate}%"

if [ ${#MISSING_PAGES[@]} -gt 0 ]; then
    echo -e "\n📄 缺失或异常页面:"
    for i in "${!MISSING_PAGES[@]}"; do
        echo "  $((i+1)). ${MISSING_PAGES[$i]}"
    done
fi

if [ ${#FAILED_PAGES[@]} -gt 0 ]; then
    echo -e "\n⚠️ 问题页面:"
    for i in "${!FAILED_PAGES[@]}"; do
        echo "  $((i+1)). ${FAILED_PAGES[$i]}"
    done
fi

echo -e "\n🌐 服务器状态: http://localhost:4000"
echo "🕒 测试完成时间: $(date)"

if [ "$success_rate" -ge 90 ]; then
    echo -e "\n🎉 系统状态优秀！大部分功能正常运行"
    exit_code=0
elif [ "$success_rate" -ge 80 ]; then
    echo -e "\n✅ 系统状态良好，有少数问题需要处理"
    exit_code=0
else
    echo -e "\n⚠️ 系统存在较多问题，需要重点修复"
    exit_code=1
fi

echo "=================================="

exit $exit_code