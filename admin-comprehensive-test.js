/**
 * 智慧养鹅管理后台 - 全面功能测试脚本
 * 使用Playwright进行端到端测试，验证所有功能模块
 */

const { chromium } = require('playwright');

class AdminSystemTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:4000';
    this.apiUrl = 'http://localhost:3000';
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  async init() {
    console.log('🚀 启动管理后台全面测试...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000 // 减慢操作速度以便观察
    });
    this.page = await this.browser.newPage();
    
    // 设置视口
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    
    // 监听控制台错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ 控制台错误:', msg.text());
        this.testResults.errors.push(`Console Error: ${msg.text()}`);
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.log('❌ 页面错误:', error.message);
      this.testResults.errors.push(`Page Error: ${error.message}`);
    });
  }

  async testLogin() {
    console.log('\n📝 测试登录功能...');
    
    try {
      // 访问首页，应该重定向到登录页
      await this.page.goto(this.baseUrl);
      await this.page.waitForLoadState('networkidle');
      
      // 检查是否重定向到登录页
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/auth/login')) {
        throw new Error(`未重定向到登录页，当前URL: ${currentUrl}`);
      }
      
      // 检查登录表单元素
      await this.page.waitForSelector('input[name="username"]', { timeout: 5000 });
      await this.page.waitForSelector('input[name="password"]', { timeout: 5000 });
      await this.page.waitForSelector('button[type="submit"]', { timeout: 5000 });
      
      console.log('✅ 登录页面加载正常');
      
      // 测试登录
      await this.page.fill('input[name="username"]', 'admin');
      await this.page.fill('input[name="password"]', 'admin123');
      await this.page.click('button[type="submit"]');
      
      // 等待登录完成
      await this.page.waitForURL('**/dashboard', { timeout: 10000 });
      
      console.log('✅ 登录成功');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 登录测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Login Test: ${error.message}`);
    }
  }

  async testDashboard() {
    console.log('\n📊 测试仪表板页面...');
    
    try {
      // 确保在仪表板页面
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查仪表板关键元素
      const dashboardTitle = await this.page.textContent('h1, .page-title, .dashboard-title');
      console.log('📊 仪表板标题:', dashboardTitle);
      
      // 检查统计卡片
      const statsCards = await this.page.locator('.card, .stat-card, .dashboard-card').count();
      console.log('📈 统计卡片数量:', statsCards);
      
      // 检查图表或数据展示
      const hasCharts = await this.page.locator('canvas, .chart, .graph').count() > 0;
      console.log('📊 是否有图表:', hasCharts);
      
      console.log('✅ 仪表板页面正常');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 仪表板测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Dashboard Test: ${error.message}`);
    }
  }

  async testNavigationMenu() {
    console.log('\n🧭 测试导航菜单...');
    
    try {
      // 测试主要导航菜单项
      const menuItems = [
        { name: '用户管理', url: '/users', selector: 'a[href*="users"]' },
        { name: '鹅群管理', url: '/flocks', selector: 'a[href*="flocks"]' },
        { name: '健康管理', url: '/health', selector: 'a[href*="health"]' },
        { name: '生产管理', url: '/production', selector: 'a[href*="production"]' },
        { name: '库存管理', url: '/inventory', selector: 'a[href*="inventory"]' },
        { name: '系统设置', url: '/system', selector: 'a[href*="system"]' }
      ];
      
      for (const item of menuItems) {
        try {
          console.log(`🔍 测试菜单项: ${item.name}`);
          
          // 查找菜单项
          const menuLink = await this.page.locator(item.selector).first();
          if (await menuLink.count() > 0) {
            await menuLink.click();
            await this.page.waitForLoadState('networkidle');
            
            // 检查页面是否正确加载
            const currentUrl = this.page.url();
            console.log(`   ✅ ${item.name} 页面加载成功: ${currentUrl}`);
            
            // 返回仪表板
            await this.page.goto(`${this.baseUrl}/dashboard`);
            await this.page.waitForLoadState('networkidle');
            
          } else {
            console.log(`   ⚠️ ${item.name} 菜单项未找到`);
          }
          
        } catch (error) {
          console.log(`   ❌ ${item.name} 测试失败: ${error.message}`);
          this.testResults.errors.push(`Menu ${item.name}: ${error.message}`);
        }
      }
      
      console.log('✅ 导航菜单测试完成');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 导航菜单测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Navigation Test: ${error.message}`);
    }
  }

  async testUserManagement() {
    console.log('\n👥 测试用户管理功能...');
    
    try {
      await this.page.goto(`${this.baseUrl}/users`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查用户列表
      const userTable = await this.page.locator('table, .user-list, .data-table').count();
      console.log('📋 用户表格存在:', userTable > 0);
      
      // 检查添加用户按钮
      const addButton = await this.page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add').count();
      console.log('➕ 添加按钮存在:', addButton > 0);
      
      // 检查搜索功能
      const searchInput = await this.page.locator('input[type="search"], input[placeholder*="搜索"], .search-input').count();
      console.log('🔍 搜索功能存在:', searchInput > 0);
      
      console.log('✅ 用户管理页面正常');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 用户管理测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`User Management Test: ${error.message}`);
    }
  }

  async testHealthManagement() {
    console.log('\n🏥 测试健康管理功能...');
    
    try {
      await this.page.goto(`${this.baseUrl}/health`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查健康记录列表
      const healthRecords = await this.page.locator('.health-record, .record-item, table tr').count();
      console.log('📋 健康记录数量:', healthRecords);
      
      // 检查添加记录按钮
      const addRecordBtn = await this.page.locator('button:has-text("添加"), button:has-text("记录"), .btn-add').count();
      console.log('➕ 添加记录按钮存在:', addRecordBtn > 0);
      
      console.log('✅ 健康管理页面正常');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 健康管理测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Health Management Test: ${error.message}`);
    }
  }

  async testProductionManagement() {
    console.log('\n🏭 测试生产管理功能...');
    
    try {
      await this.page.goto(`${this.baseUrl}/production`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查生产记录
      const productionData = await this.page.locator('.production-record, .record-item, table tr').count();
      console.log('📊 生产记录数量:', productionData);
      
      // 检查统计图表
      const charts = await this.page.locator('canvas, .chart, .graph').count();
      console.log('📈 图表数量:', charts);
      
      console.log('✅ 生产管理页面正常');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 生产管理测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Production Management Test: ${error.message}`);
    }
  }

  async testApiEndpoints() {
    console.log('\n🔌 测试API端点...');
    
    const endpoints = [
      { name: '健康检查', url: '/api/health', method: 'GET' },
      { name: '用户列表', url: '/api/v1/auth/users', method: 'GET' },
      { name: '鹅群列表', url: '/api/v1/flocks', method: 'GET' },
      { name: '健康记录', url: '/api/v1/health/records', method: 'GET' },
      { name: '生产记录', url: '/api/v1/production-records', method: 'GET' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 测试API: ${endpoint.name}`);
        
        const response = await this.page.request.get(`${this.apiUrl}${endpoint.url}`);
        const status = response.status();
        
        if (status === 200) {
          console.log(`   ✅ ${endpoint.name} API正常 (${status})`);
        } else if (status === 401 || status === 403) {
          console.log(`   ⚠️ ${endpoint.name} API需要认证 (${status})`);
        } else {
          console.log(`   ❌ ${endpoint.name} API异常 (${status})`);
          this.testResults.errors.push(`API ${endpoint.name}: Status ${status}`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${endpoint.name} API测试失败: ${error.message}`);
        this.testResults.errors.push(`API ${endpoint.name}: ${error.message}`);
      }
    }
    
    this.testResults.passed++;
  }

  async generateReport() {
    console.log('\n📊 生成测试报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.passed + this.testResults.failed,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        success_rate: `${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(2)}%`
      },
      errors: this.testResults.errors,
      recommendations: []
    };
    
    // 生成建议
    if (this.testResults.errors.length > 0) {
      report.recommendations.push('修复发现的错误和异常');
    }
    if (this.testResults.failed > 0) {
      report.recommendations.push('检查失败的功能模块');
    }
    
    console.log('\n🎯 测试报告:');
    console.log('=====================================');
    console.log(`📊 总测试数: ${report.summary.total}`);
    console.log(`✅ 通过: ${report.summary.passed}`);
    console.log(`❌ 失败: ${report.summary.failed}`);
    console.log(`📈 成功率: ${report.summary.success_rate}`);
    
    if (report.errors.length > 0) {
      console.log('\n❌ 发现的问题:');
      report.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 建议:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
    
    return report;
  }

  async runAllTests() {
    try {
      await this.init();
      
      // 执行所有测试
      await this.testLogin();
      await this.testDashboard();
      await this.testNavigationMenu();
      await this.testUserManagement();
      await this.testHealthManagement();
      await this.testProductionManagement();
      await this.testApiEndpoints();
      
      // 生成报告
      const report = await this.generateReport();
      
      return report;
      
    } catch (error) {
      console.log('❌ 测试执行失败:', error.message);
      throw error;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 执行测试
async function main() {
  const tester = new AdminSystemTester();
  
  try {
    const report = await tester.runAllTests();
    
    console.log('\n🎉 测试完成！');
    
    // 保存报告到文件
    const fs = require('fs');
    fs.writeFileSync('admin-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 测试报告已保存到: admin-test-report.json');
    
  } catch (error) {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = AdminSystemTester;
