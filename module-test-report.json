{"timestamp": "2025-08-26T14:20:46.382Z", "summary": {"total": 19, "success": 19, "failed": 0, "errors": 0}, "results": {"success": [{"name": "仪表盘", "path": "/dashboard", "status": 200, "type": "page"}, {"name": "用户管理", "path": "/users", "status": 200, "type": "page"}, {"name": "租户管理", "path": "/tenants", "status": 200, "type": "page"}, {"name": "鹅群管理", "path": "/flocks", "status": 200, "type": "page"}, {"name": "生产管理", "path": "/production", "status": 200, "type": "page"}, {"name": "健康管理", "path": "/health", "status": 200, "type": "page"}, {"name": "财务管理", "path": "/finance", "status": 200, "type": "page"}, {"name": "库存管理", "path": "/inventory", "status": 200, "type": "page"}, {"name": "统计报告", "path": "/reports", "status": 200, "type": "page"}, {"name": "系统管理", "path": "/system", "status": 200, "type": "page"}, {"name": "鹅价管理", "path": "/goose-prices", "status": 200, "type": "page"}, {"name": "商城管理", "path": "/mall", "status": 200, "type": "page"}, {"name": "知识库", "path": "/knowledge", "status": 200, "type": "page"}, {"name": "公告管理", "path": "/announcements", "status": 200, "type": "page"}, {"name": "API管理", "path": "/api-management", "status": 200, "type": "page"}, {"name": "平台用户", "path": "/platform-users", "status": 200, "type": "page"}, {"name": "仪表盘统计", "path": "/api/dashboard/stats", "status": 200, "type": "api"}, {"name": "用户列表", "path": "/api/users/list", "status": 200, "type": "api"}, {"name": "健康检查", "path": "/api/health", "status": 200, "type": "api"}], "failed": [], "errors": []}}