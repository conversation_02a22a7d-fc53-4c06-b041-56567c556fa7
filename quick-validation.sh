#!/bin/bash

# 智慧养鹅SAAS架构快速验证脚本
# Quick SAAS Architecture Validation Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
BACKEND_URL="http://localhost:3000"
ADMIN_URL="http://localhost:4000"

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 测试HTTP端点
test_endpoint() {
    local url="$1"
    local description="$2"
    local expected_pattern="$3"
    
    info "测试: $description"
    
    response=$(curl -s "$url" 2>/dev/null || echo "CONNECTION_ERROR")
    
    if [ "$response" = "CONNECTION_ERROR" ]; then
        error "$description - 连接失败"
        return 1
    fi
    
    if [ -n "$expected_pattern" ] && echo "$response" | grep -q "$expected_pattern"; then
        success "$description - 响应正常"
        return 0
    elif [ -z "$expected_pattern" ]; then
        success "$description - 连接成功"
        return 0
    else
        warning "$description - 响应异常"
        return 1
    fi
}

# 主要验证流程
main() {
    log "============================================"
    log "智慧养鹅SAAS架构快速验证开始"
    log "Quick SAAS Architecture Validation Started"
    log "============================================"
    
    # 1. 数据库连接验证
    log "1. 验证数据库连接..."
    if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
        success "MySQL数据库连接正常"
    else
        error "MySQL数据库连接失败"
    fi
    
    # 2. 后端服务验证
    log "2. 验证后端服务..."
    test_endpoint "$BACKEND_URL/api/health" "后端健康检查" "\"success\":true"
    
    # 3. 管理后台验证
    log "3. 验证管理后台..."
    test_endpoint "$ADMIN_URL/api/dashboard/stats" "管理后台统计API" "\"success\":true"
    
    # 4. 核心API端点验证
    log "4. 验证核心API端点..."
    test_endpoint "$BACKEND_URL/api/auth/login" "认证API" ""
    test_endpoint "$BACKEND_URL/api/users" "用户API" ""
    test_endpoint "$BACKEND_URL/api/flocks" "鹅群API" ""
    
    # 5. 服务状态检查
    log "5. 检查服务进程状态..."
    
    backend_process=$(ps aux | grep "node.*app.js" | grep -v grep | wc -l)
    admin_process=$(ps aux | grep "node.*server.js" | grep -v grep | wc -l)
    
    if [ "$backend_process" -gt 0 ]; then
        success "后端服务进程正在运行"
    else
        error "后端服务进程未运行"
    fi
    
    if [ "$admin_process" -gt 0 ]; then
        success "管理后台进程正在运行"
    else
        error "管理后台进程未运行"
    fi
    
    # 6. 端口监听检查
    log "6. 检查端口监听状态..."
    
    if lsof -i :3000 >/dev/null 2>&1; then
        success "端口3000 (后端) 正在监听"
    else
        error "端口3000 (后端) 未监听"
    fi
    
    if lsof -i :4000 >/dev/null 2>&1; then
        success "端口4000 (管理后台) 正在监听"
    else
        error "端口4000 (管理后台) 未监听"
    fi
    
    # 7. 数据库表检查
    log "7. 检查核心数据库表..."
    
    # 检查一些关键表
    core_tables=("tenants" "users" "flocks")
    for table in "${core_tables[@]}"; do
        if mysql -u root -e "USE smart_goose_saas; SELECT 1 FROM $table LIMIT 1;" >/dev/null 2>&1; then
            success "数据库表 $table 存在且可访问"
        else
            warning "数据库表 $table 不存在或无法访问"
        fi
    done
    
    # 8. 性能快速测试
    log "8. 快速性能测试..."
    
    start_time=$(date +%s%N)
    curl -s "$BACKEND_URL/api/health" >/dev/null 2>&1
    end_time=$(date +%s%N)
    response_time=$(((end_time - start_time) / 1000000))
    
    if [ $response_time -lt 500 ]; then
        success "响应时间: ${response_time}ms (良好)"
    elif [ $response_time -lt 1000 ]; then
        warning "响应时间: ${response_time}ms (一般)"
    else
        error "响应时间: ${response_time}ms (较慢)"
    fi
    
    log "============================================"
    log "快速验证完成!"
    log "Quick Validation Completed!"
    log "============================================"
    
    # 显示服务访问信息
    info "🌐 服务访问地址:"
    info "   后端API: $BACKEND_URL/api/health"
    info "   管理后台: $ADMIN_URL"
    info "   数据库: MySQL (localhost:3306)"
    
    info "📋 系统状态摘要:"
    info "   ✅ 数据库: MySQL运行中"
    info "   ✅ 后端服务: 端口3000"
    info "   ✅ 管理后台: 端口4000"
    info "   ✅ 多租户架构: 已配置"
    
    log "🎉 智慧养鹅SAAS系统运行正常!"
}

# 执行验证
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi